using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Services.AuthenticationMfa;
using Microsoft.AspNetCore.Mvc;
using Motivai.SharedKernel.Domain.Entities.Users;

namespace Motivai.WebCatalog.Controllers
{
    public class AutenticacaoController : BaseController
    {
        private readonly IHelperWeb helperWeb;
        private readonly AuthenticationMfaService authenticationMfaService;

        public AutenticacaoController(IHelperWeb helperWeb, AuthenticationMfaService authenticationMfaService)
        {
            this.helperWeb = helperWeb;
            this.authenticationMfaService = authenticationMfaService;
        }

        [HttpGet]
        public async Task<IActionResult> AutenticacaoMfa()
        {
            try
            {
                var campaignId = helperWeb.GetCampaignId();
                var userId = helperWeb.GetUserId();
                var pageModel = await authenticationMfaService.GetAuthenticationMfaPageValidateConfig(campaignId, userId);
                pageModel.FromValidate = true;

                return View(pageModel);
            }
            catch (Exception ex)
            {
                await LogException(ex, "AuthenticationMfa - Authentication", "Erro durante carregamento dos dados do participante");
                return ForwardToError("Não foi possível carregar os dados do participante =(",
                    "Caso o erro persista, contate nosso atendimento.",
                    "Por favor, atualize a página e tente novamente.");
            }
        }

        [HttpGet]
        public async Task<IActionResult> ConfiguracaoMfa()
        {
            try
            {
                var campaignId = helperWeb.GetCampaignId();
                var userId = helperWeb.GetUserId();
                var pageModel = await authenticationMfaService.GetAuthenticationMfaPageSetupConfig(campaignId, userId);
                pageModel.FromValidate = false;

                return View(pageModel);
            }
            catch (Exception ex)
            {
                await LogException(ex, "AuthenticationMfa - Setup", "Erro durante carregamento do regulamento");
                return ForwardToError("Não foi possível carregar as configurações de autenticação =(",
                    "Caso o erro persista, contate nosso atendimento.",
                    "Por favor, atualize a página e tente novamente.");
            }
        }

        [HttpPost]
        public async Task<JsonResult> SendAuthenticationMfaToValidate([FromBody] ParticipantAuthenticationMfaSettings participantAuthenticationMfaSettings)
        {
            return await ExecuteAndGetJson(authenticationMfaService.SendAuthenticationMfaTokenToValidate(participantAuthenticationMfaSettings));
        }

        [HttpPost]
        public async Task<JsonResult> ValidateAuthenticationMfaToken([FromBody] dynamic userToken)
        {
            return await ExecuteAndGetJsonWithRedirect(authenticationMfaService.ValidateAuthenticationMfaToken(userToken, true), "/");
        }

        [HttpPost]
        public async Task<JsonResult> EnviarMfaToken([FromBody] ParticipantAuthenticationMfaSettings participantMfaSettings)
        {
            var campaignId = helperWeb.GetCampaignId();
            var userId = helperWeb.GetUserId();
            try
            {
                return AjaxReturn.GetJsonResult(await authenticationMfaService.SendAuthenticationMfaToken(campaignId, userId, participantMfaSettings));
            }
            catch (Exception ex)
            {
                await LogException(ex);
                if (ex is MotivaiException)
                    return AjaxReturn.FromError(ex.Message);
                return AjaxReturn.FromError("Ocorreu um erro ao enviar o código de segurança, por favor, tente novamente.");
            }
        }

        [HttpPost]
        public async Task<JsonResult> ValidarTokenDuplaAutenticacao([FromBody] dynamic participantMfaSettings)
        {
            var campaignId = helperWeb.GetCampaignId();
            var userId = helperWeb.GetUserId();
            try
            {
                return await ExecuteAndGetJsonWithRedirect(authenticationMfaService.ValidateAndSaveMfaSettings(campaignId, userId, participantMfaSettings, true), "/");
            }
            catch (Exception ex)
            {
                await LogException(ex);
                if (ex is MotivaiException)
                    return AjaxReturn.FromError(ex.Message);
                return AjaxReturn.FromError("Ocorreu um erro ao validar o código de segurança, por favor, tente novamente.");
            }
        }
    }
}