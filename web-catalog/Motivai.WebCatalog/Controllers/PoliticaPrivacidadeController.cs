using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.FirstAccess;
using Motivai.WebCatalog.Services.Terms;

namespace Motivai.WebCatalog.Controllers
{
    public class PoliticaPrivacidadeController : BaseController
    {
        private readonly NewPrivacyPolicyService newPrivacyPolicyService;
        private readonly IHelperWeb helperWeb;

        public PoliticaPrivacidadeController(NewPrivacyPolicyService newPrivacyPolicyService, IHelperWeb helperWeb)
        {
            this.newPrivacyPolicyService = newPrivacyPolicyService;
            this.helperWeb = helperWeb;
        }
        [HttpGet]
        public IActionResult Privacidade()
        {
            return View("Privacidade");
        }

        [HttpGet]
        public async Task<JsonResult> PrivacidadeLoad()
        {
            return await ExecuteAndGetJson(newPrivacyPolicyService.CreateNewPrivacyPageModel());
        }

        [HttpPost]
        public async Task<JsonResult> RegistraAceitePolitica([FromBody] PrivacyPolicyAcceptanceResult privacyPolicyAcceptanceResult)
        {
            var participantSession = await helperWeb.GetParticipantSessionAsync();
            var nextUrl = participantSession.NextUrl ?? "/";
            this.helperWeb.CleanNewPrivacyPolicy();
            return await ExecuteAndGetJsonWithRedirect(
                newPrivacyPolicyService.RegistraAceitePolitica(privacyPolicyAcceptanceResult, CreateLocationInfo(participantSession)),
                nextUrl
            );
        }
    }
}
