using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Repositories;
using Motivai.WebCatalog.Services.ExtraServices;
using Newtonsoft.Json.Linq;
using Motivai.WebCatalog.Models.Pages.Cashback;

namespace Motivai.WebCatalog.Controllers
{
	[Produces("application/json")]
	[Route("Cashback")]
	public class CashbackController : BaseController
	{
		private readonly IHelperWeb helperWeb;
		private readonly CampaignRepository campaignRepository;
		private readonly CashbackService cashbackService;

		public CashbackController(IHelperWeb helperWeb, CampaignRepository campaignRepository, CashbackService cashbackService)
		{
			this.helperWeb = helperWeb;
			this.campaignRepository = campaignRepository;
			this.cashbackService = cashbackService;
		}

		private async Task<CampaignSettingsModel> GetCampaignConfiguration()
		{
			return await campaignRepository.GetCampaignSettings(await helperWeb.GetCampaignIdForCurrentDomain());
		}

		[HttpGet]
		public async Task<IActionResult> Index()
		{
			var campaignSettings = await GetCampaignConfiguration();
			if (!campaignSettings.Parametrizations.EnableBankTransfer)
			{
				return ForwardToNotFound();
			}
			return View("Index", new CashbackViewModel(campaignSettings));
		}

		[HttpGet("configuracoes")]
		public async Task<JsonResult> GetPageConfiguration()
		{
			return await ExecuteAndGetJson(cashbackService.GetPageConfiguration(await helperWeb.GetCampaignIdForCurrentDomain()),
				"Não foi possível carregar as configurações, por favor, tente novamente.");
		}

		[HttpPost("taxas")]
		public async Task<JsonResult> CalculateBankTransferOrderFees([FromBody] dynamic order)
		{
			return await ExecuteAndGetJson(cashbackService.CalculateBankTransferOrderFees(await helperWeb.GetCampaignIdForCurrentDomain(),
				helperWeb.GetUserId(), order));
		}

		[HttpPost("transferir")]
		public async Task<JsonResult> CreateOrderUsingWithdrawableCard([FromBody] dynamic order)
		{
			var campaignSettings = await GetCampaignConfiguration();
			if (!campaignSettings.Parametrizations.EnableBankTransfer)
			{
				return AjaxReturn.FromError("Transferência bancária não está ativado nesta campanha");
			}
			var participant = helperWeb.GetParticipantSession();
			if (participant.IsAccountOperator())
			{
				var accountOperator = participant.GetAccountOperator();
				order.accountOperator = JObject.FromObject(new
				{
					accountOperatorDocument = accountOperator.AccountOperatorDocument,
					accountOperatorLoginId = accountOperator.AccountOperatorLoginId,
					accountOperatorId = accountOperator.AccountOperatorId,
					accountOperatorEmail = accountOperator.AccountOperatorEmail
				});
			}
			if (order.locationInfo == null)
			{
				order.locationInfo = JObject.FromObject(new
				{
					connectionInfo = GetConnectionInfo(),
					timezone = ""
				});
			}
			else
			{
				string tz = order.locationInfo.timezone;
				order.locationInfo = JObject.FromObject(new
				{
					connectionInfo = GetConnectionInfo(),
					timezone = tz
				});
			}
			return await ExecuteAndGetJson(cashbackService.CreateBankTransferOrder(await helperWeb.GetCampaignIdForCurrentDomain(),
				participant, order));
		}
	}
}
