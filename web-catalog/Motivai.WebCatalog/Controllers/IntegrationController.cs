using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Integrations;
using Motivai.WebCatalog.Models.Login;
using Motivai.WebCatalog.Repositories;
using Motivai.WebCatalog.Models.MyAccount;

namespace Motivai.WebCatalog.Controllers
{
	public class IntegrationController : BaseController {
		private readonly IHelperWeb _helperWeb;
		private readonly ICryptography _cryptography;
		private readonly LoginRepository _loginRepository;
		private readonly CampaignRepository _campaignRepository;

		public IntegrationController(IHelperWeb helperWeb, ICryptography cryptography,
			LoginRepository loginRepository, CampaignRepository campaignRepository) {
			this._helperWeb = helperWeb;
			this._cryptography = cryptography;
			this._loginRepository = loginRepository;
			this._campaignRepository = campaignRepository;
		}

		[HttpPost]
		public async Task<ActionResult> AuthenticateForm(IntegrationLogin login) {
			return await Authenticate(login);
		}

		[HttpPost]
		public async Task<ActionResult> Authenticate([FromBody] IntegrationLogin login) {
			var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			if (campaignId == Guid.Empty) {
				return ForwardToError("Campanha inválida", "Contate o atendimento para maiores informações", "Campanha não está configurada corretamente.");
			}
			if (login == null) {
				return ForwardToError("Dados inválidos", "Contate o administrador para maiores informações.", "Dados de autenticação estão inválidos.");
			}

			try {
				var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);
				// Verifica se a página de login está ativa
				if (!campaignSettings.Parametrizations.EnableLoginIntegration) {
					return ForwardToError("Login integrado está desabilitado nesta campanha", "Contate o atendimento para maiores informações", "Não é permitido efetuar login via integração.");
				}
				if (campaignSettings.Parametrizations.Token != login.Token) {
					return ForwardToError("Solicitação não autorizada.", "Contate o atendimento para maiores informações", "Não é permitido efetuar login via integração.");
				}

				login.Origin = "Integração Catálogo";
				login.ConnectionInfo = ConnectionInfo.OfHttpContext(HttpContext);
				var user = await _loginRepository.AuthenticateParticipantByIntegration(campaignId, login);
				return await CreateSession(campaignId, user, login.ProductId);
			}
			catch (Exception ex) {
				await LogException(ex);
				string message = "Não foi possível efetuar a autenticação.";
				if (ex is MotivaiException)
					message = ex.Message;
				return ForwardToError("Ocorreu um erro durante a autenticação", "Caso o erro persista, por favor, entre em contato com o administrador", message);
			}
		}

		[HttpPost]
		public async Task<JsonResult> Sso([FromBody] IntegrationLogin login) {
			if (login == null) {
				return new JsonResult(new {
					error = true,
					message = "Preencha os campos corretamente para prosseguir."
				});
			}

			try {
				var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
				if (campaignId == Guid.Empty) {
					return new JsonResult(new {
						error = true,
						message = "Token de acesso inválido."
					});
				}

				var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);
				if (!campaignSettings.Parametrizations.EnableLoginIntegration) {
					return new JsonResult(new {
						error = true,
						message = "Login integrado está desabilitado nesta campanha."
					});
				}
				if (campaignSettings.Parametrizations.Token != login.Token) {
					return new JsonResult(new {
						error = true,
						message = "Solicitação não autorizada."
					});
				}

				login.Origin = "SSO Catálogo";
				login.ConnectionInfo = ConnectionInfo.OfHttpContext(HttpContext);
				Guid token = await _loginRepository.AuthenticateByPlatformSso(campaignId, login);
				return new JsonResult(new {
					auth_token = token,
					access_token = token
				});
			} catch (Exception ex) {
				await LogException(ex, "Erro durante a requisição de solicitação do token de acesso.");
				return new JsonResult(new {
					error = true,
					message = "Ocorreu um erro durante a autenticação. Caso o erro persista, por favor, entre em contato com o administrador."
				});
			}
		}


		[HttpGet]
		public async Task<ActionResult> Sso([FromQuery] Guid token, [FromQuery] string productId) {
			var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			if (campaignId == Guid.Empty) {
				return ForwardToError("Campanha inválida", "Contate o atendimento para maiores informações", "Campanha não está configurada corretamente.");
			}

			ConnectionInfo connectionInfo = ConnectionInfo.OfHttpContext(HttpContext);
			LoginSsoEndingRequest loginSso = LoginSsoEndingRequest.Of(token, "SSO Catálogo", null, connectionInfo);

			try {
				var user = await _loginRepository.FinalizeAuthenticatedUserUsingSsoToken(campaignId, loginSso);
				if (user == null) {
					throw MotivaiException.ofValidation("Acesso expirado.");
				}
				return await CreateSession(campaignId, user, productId);
			} catch (Exception ex) {
				await LogException(ex);
				string message = "Não foi possível efetuar a autenticação.";
				if (ex is MotivaiException)
					message = ex.Message;
				return ForwardToError("Ocorreu um erro durante a autenticação", "Caso o erro persista, por favor, entre em contato com o administrador", message);
			}
		}

		[HttpPost]
		public async Task<ActionResult> AuthenticateCallcenter(CallcenterIntegrationLogin login) {
			if (login == null) {
				return ForwardToErrorWithMessage("Dados de autenticação não informados.");
			}
			if (!login.DecryptAndValidate(_cryptography))
			{
				return ForwardToErrorWithMessage("Sessão inválida.");
			}
			_helperWeb.ClearSession();

			Guid campaignId = Guid.Parse(login.campaign);
			Guid currentCampaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			if (campaignId != currentCampaignId) {
				return ForwardToErrorWithMessage("Campanha informada inválida.");
			}

			Guid callcenterUserId = Guid.Parse(login.callcenter);
			if (callcenterUserId == Guid.Empty) {
				return ForwardToErrorWithMessage("Usuário do call center inválido.");
			}

			try {
				var callcenterUsername = await _loginRepository.GetAdminUsername(callcenterUserId);
				var callcenterLogin = CallcenterLogin.NewSession(
					callcenterUserId, callcenterUsername,
					Guid.Parse(login.user),
					Guid.Parse(login.participant),
					ConnectionInfo.OfHttpContext(HttpContext)
				);
				var user = await _loginRepository.AuthenticateParticipantForCallcenter(campaignId, callcenterLogin);
				_helperWeb.CreateParticipantSession(campaignId, user);
				_helperWeb.StartCallcenterSession(callcenterUserId, callcenterUsername);
				return RedirectToAction("Index", "Home");
			} catch (Exception ex) {
				await LogException(ex);
				return ForwardToErrorWithMessage(ex is MotivaiException ? ex.Message : "Não foi possível efetuar a autenticação.");
			}
		}

		[HttpPost]
		public async Task<ActionResult> AuthenticateCampaignSite(CampaignSiteLogin campaignSiteLogin) {
			if (campaignSiteLogin == null) {
				return ForwardToErrorWithMessage("Dados de autenticação não informados.");
			}
			campaignSiteLogin.Validate();

			Guid campaignId = ParserHelper.DecryptGuid(_cryptography, campaignSiteLogin.Campaign);
			Guid currentCampaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			if (campaignId != currentCampaignId) {
				return ForwardToErrorWithMessage("Campanha informada inválida.");
			}

			if (!IsTokenValid(campaignSiteLogin.Token)) {
				return ForwardToErrorWithMessage("Sessão expirada, por favor, efetue login novamente.");
			}

			PlatformIntegrationLogin userLogin;
			try {
				if (campaignSiteLogin.IsAccountOperator()) {
					userLogin = PlatformIntegrationLogin.OfAccountOperator(
						ParserHelper.DecryptGuid(_cryptography, campaignSiteLogin.User),
						ParserHelper.DecryptGuid(_cryptography, campaignSiteLogin.AccountOperatorId),
						ParserHelper.DecryptGuid(_cryptography, campaignSiteLogin.AccountOperatorLoginId),
						campaignSiteLogin.Timezone,
						ConnectionInfo.OfHttpContext(HttpContext)
					);
				} else {
					userLogin = PlatformIntegrationLogin.OfUser(
						ParserHelper.DecryptGuid(_cryptography, campaignSiteLogin.User),
						campaignSiteLogin.Timezone,
						ConnectionInfo.OfHttpContext(HttpContext)
					);
				}

				if (!string.IsNullOrEmpty(campaignSiteLogin.Session))
				{
					userLogin.SessionId = _cryptography.Decrypt(campaignSiteLogin.Session);
				}
			} catch (Exception ex) {
				await LogException(ex, "Preparação de Login", true);
				return ForwardToErrorWithMessage(ex.Message);
			}

			try {
				var user = await _loginRepository.AuthenticateParticipantForCampaignSite(campaignId, userLogin);
				_helperWeb.CreateParticipantSession(campaignId, user);

				if (!string.IsNullOrEmpty(campaignSiteLogin.Callcenter))
				{
					var callcenterUserId = ParserHelper.DecryptGuid(_cryptography, campaignSiteLogin.Callcenter);
					var callcenterUsername = await _loginRepository.GetAdminUsername(callcenterUserId);
					_helperWeb.StartCallcenterSession(callcenterUserId, callcenterUsername);
				}

				// Verificar se o primeiro acesso está habilitado na campanha
				if (user.FirstAccess) {
					var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);
					if (campaignSettings.Parametrizations.EnableFirstAccess) {
						_helperWeb.SetSessionAsFirstAccess();
						var catalogSettings = await _campaignRepository.GetPagesSettings(campaignId);
						if (catalogSettings.PagesSettings.EnableRegulation) {
							return RedirectToAction("Regulamento", "PrimeiroAcesso");
						}
						return RedirectToAction("Cadastro", "PrimeiroAcesso");
					}
				} else if (campaignSiteLogin.HasProductId()) {
					return RedirectToAction("Detalhes", "Produto", new { id = campaignSiteLogin.ProductId });
				}
				return RedirectToAction("Index", "Home");
			} catch (Exception ex) {
				await LogException(ex, "Login via Campaign Site", true);
				return ForwardToErrorWithMessage(ex is MotivaiException ? ex.Message : "Não foi possível efetuar a autenticação.");
			}
		}

		private async Task<ActionResult> CreateSession(Guid campaignId, UserParticipantModel user, string productId = null) {
			if (user == null) {
				throw MotivaiException.ofValidation("Usuário inválido.");
			}
			var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);
			// Verifica se a página de login está ativa
			if (!campaignSettings.Parametrizations.EnableLoginIntegration) {
				return ForwardToError("Login integrado está desabilitado nesta campanha", "Contate o atendimento para maiores informações", "Não é permitido efetuar login via integração.");
			}
			_helperWeb.ClearSession();
			_helperWeb.CreateParticipantSession(campaignId, user);

			// Verificar se o primeiro acesso está habilitado na campanha
			if (user.FirstAccess && campaignSettings.Parametrizations.EnableFirstAccess) {
				_helperWeb.SetSessionAsFirstAccess();
				if (!string.IsNullOrEmpty(productId)) {
					var participantSession = await _helperWeb.GetParticipantSessionAsync();
					participantSession.NextUrl = $"/Produto/Detalhes/{productId}";
					_helperWeb.SetParticipantSession(participantSession);
				}
				var catalogSettings = await _campaignRepository.GetPagesSettings(campaignId);
				if (catalogSettings.PagesSettings.EnableRegulation) {
					return RedirectToAction("Regulamento", "PrimeiroAcesso");
				}
				else {
					return RedirectToAction("Cadastro", "PrimeiroAcesso");
				}
			}
			else if (!string.IsNullOrEmpty(productId)) {
				return RedirectToAction("Detalhes", "Produto", new { id = productId });
			}

			return RedirectToAction("Index", "Home");
		}


		private bool IsTokenValid(string encryptedToken) {
			var token = _cryptography.Decrypt(encryptedToken);
			var ticks = (long) 0;
			if (!long.TryParse(token, out ticks)) {
				return false;
			}
			var expireAt = new DateTime(ticks);
			return expireAt > DateTime.UtcNow;
		}

		private ActionResult ForwardToErrorWithMessage(string message) {
			return ForwardToError("Não foi possível efetuar autenticação",
				"Caso o erro persista, por favor, entre em contato com o administrador",
				message
			);
		}
	}
}
