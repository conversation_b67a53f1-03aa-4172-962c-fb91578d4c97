using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Models.ExtraServices;
using Motivai.WebCatalog.Repositories;
using Motivai.WebCatalog.Services.ExtraServices;

namespace Motivai.WebCatalog.Controllers
{
	public class PagueContasController : BaseController
	{
		private readonly IHelperWeb helperWeb;
		private readonly CampaignRepository campaignRepository;
		private readonly UserParticipantRepository participantRepository;
		private readonly BillPaymentService billPaymentService;

		public PagueContasController(IHelperWeb helperWeb, CampaignRepository campaignRepository,
			UserParticipantRepository participantRepository, BillPaymentService billPaymentService)
		{
			this.helperWeb = helperWeb;
			this.campaignRepository = campaignRepository;
			this.participantRepository = participantRepository;
			this.billPaymentService = billPaymentService;
		}

		private async Task<CampaignSettingsModel> GetCampaignSettings()
		{
			return await campaignRepository.GetCampaignSettings(await helperWeb.GetCampaignIdForCurrentDomain());
		}

		public async Task<IActionResult> Index()
		{
			var settings = await GetCampaignSettings();
			if (!settings.Parametrizations.EnableBillPayment)
			{
				return ForwardToNotFound();
			}
			return View("~/Views/Servicos/PagueContas.cshtml", settings);
		}

		public async Task<JsonResult> Detalhes([FromRoute(Name = "id")] string barCode)
		{
			var settings = await GetCampaignSettings();
			if (!settings.Parametrizations.EnableBillPayment)
			{
				return AjaxReturn.FromError("Pagamento de contas está desabilitado nesta campanha.");
			}
			try
			{
				return AjaxReturn.GetJsonResult(await billPaymentService.GetBillDetails(await helperWeb.GetCampaignIdForCurrentDomain(),
					helperWeb.GetParticipantSession(), barCode));
			}
			catch (Exception ex)
			{
				string message = null;
				if (ex is MotivaiException)
				{
					(ex as MotivaiException).Loggable = true;
					message = ex.Message;
				}
				else
				{
					message = "Não foi possível carregar os detalhes da conta para pagamento, por favor, tente novamente.";
				}
				await base.LogException(ex, "Ocorreu um erro ao carregar os detalhes da conta a pagar.", true);
				return AjaxReturn.FromError(message);
			}
		}

		private async Task<decimal?> RefreshParticipantBalance(Models.Session.UserPrincipal participant, Guid campaignId)
		{
			try
			{
				participant.Balance = await participantRepository.GetAvailableBalance(campaignId, participant.UserId);
				helperWeb.SetParticipantSession(participant);
				return participant.Balance;
			}
			catch (Exception ex)
			{
				await LogException(ex, "Erro ao atualizar saldo após pagto de conta.");
				return null;
			}
		}

		[HttpPost]
		public async Task<JsonResult> Inicia([FromBody] BillDetails bill)
		{
			var settings = await GetCampaignSettings();
			if (!settings.Parametrizations.EnableBillPayment)
			{
				return AjaxReturn.FromError("Pagamento de contas está desabilitado nesta campanha.");
			}
			try
			{
				var campaignId = await helperWeb.GetCampaignIdForCurrentDomain();
				var participant = helperWeb.GetParticipantSession();

				if (participant.IsAccountOperator())
				{
					bill.AccountOperator = participant.GetAccountOperator();
				}
				bill.LocationInfo = SetConnectionInfo(bill.LocationInfo);

				return AjaxReturn.GetJsonResult(await billPaymentService.IssueBillPaymentTicket(campaignId, participant, bill));
			}
			catch (Exception ex)
			{
				await LogException(ex, "Erro ao iniciar pagto conta.", true);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Não possível concluir a operação, por favor, tente novamente.");
			}
		}

		[HttpPost]
		public async Task<JsonResult> Confirma([FromBody] ConfirmationTicket ticket)
		{
			var settings = await GetCampaignSettings();
			if (!settings.Parametrizations.EnableBillPayment)
			{
				return AjaxReturn.FromError("Pagamento de contas está desabilitado nesta campanha.");
			}
			try
			{
				var campaignId = await helperWeb.GetCampaignIdForCurrentDomain();
				var participant = helperWeb.GetParticipantSession();

				if (participant.IsAccountOperator())
				{
					ticket.AccountOperator = participant.GetAccountOperator();
				}
				ticket.LocationInfo = SetConnectionInfo(ticket.LocationInfo);
				var confirmationResult = await billPaymentService.ConfirmBillPaymentTicket(campaignId, participant, ticket);
				if (confirmationResult.IsConfirmed)
					confirmationResult.UpdatedBalance = await RefreshParticipantBalance(participant, campaignId);
				return AjaxReturn.GetJsonResult(confirmationResult);
			}
			catch (Exception ex)
			{
				await LogException(ex, "Erro ao confirmar pagto conta.", true);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Não possível concluir a operação, por favor, tente novamente.");
			}
		}

		[HttpPost]
		public async Task<JsonResult> Agendar([FromBody] BillDetails bill)
		{
			try
			{
				var campaignId = await helperWeb.GetCampaignIdForCurrentDomain();
				var participant = helperWeb.GetParticipantSession();

				if (participant.IsAccountOperator())
				{
					bill.AccountOperator = participant.GetAccountOperator();
				}
				bill.LocationInfo = SetConnectionInfo(bill.LocationInfo);

				var scheduled = await billPaymentService.SchedulePayment(campaignId, participant, bill);
				if (!scheduled)
				{
					return AjaxReturn.FromError("Não possível concluir a operação, por favor, tente novamente.");
				}
				return AjaxReturn.GetJsonResult(new
				{
					Scheduled = scheduled,
					UpdatedBalance = await RefreshParticipantBalance(participant, campaignId)
				});
			}
			catch (Exception ex)
			{
				await LogException(ex, "Erro ao agendar o pagamento da conta.", true);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Não possível concluir a operação, por favor, tente novamente.");
			}
		}

		[HttpPost]
		public async Task<JsonResult> Cancela([FromBody] ConfirmationTicket ticket)
		{
			var settings = await GetCampaignSettings();
			if (!settings.Parametrizations.EnableBillPayment)
			{
				return AjaxReturn.FromError("Pagamento de contas está desabilitado nesta campanha.");
			}
			try
			{
				var campaignId = await helperWeb.GetCampaignIdForCurrentDomain();
				var participant = helperWeb.GetParticipantSession();
				if (participant.IsAccountOperator())
				{
					ticket.AccountOperator = participant.GetAccountOperator();
				}
				ticket.LocationInfo = SetConnectionInfo(ticket.LocationInfo);
				return AjaxReturn.GetJsonResult(await billPaymentService.CancelBillPaymentTicket(campaignId, participant, ticket));
			}
			catch (Exception ex)
			{
				await LogException(ex, "Erro ao cancelar pagto conta.", true);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Não possível concluir a operação, por favor, tente novamente.");
			}
		}
	}
}