using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Integrations;
using Motivai.WebCatalog.Repositories;
using Motivai.WebCatalog.Models.MyAccount;
using Motivai.WebCatalog.Services.Security;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.WebCatalog.Controllers
{
	// [Route("/marketplace")]
	public class MarketplaceController : BaseController
	{
		private readonly IHelperWeb _helperWeb;
		private readonly LoginRepository _loginRepository;
		private readonly CampaignRepository _campaignRepository;

		public MarketplaceController(IHelperWeb helperWeb, LoginRepository loginRepository,
			CampaignRepository campaignRepository)
		{
			this._helperWeb = helperWeb;
			this._loginRepository = loginRepository;
			this._campaignRepository = campaignRepository;
		}

		[HttpGet]
		[ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
		public async Task<ActionResult> Sso(
			[FromQuery(Name = "X-USER-ID")] string userId,
			[FromQuery(Name = "X-TRACE-ID")] string traceId,
			[FromQuery(Name = "app-id")] Guid appId,
			[FromQuery] string timestamp,
			[FromQuery] string signature
		)
		{
			try
			{
				// LoggerFactory.GetLogger().Info(
				// 	"XUserId: {} - XTraceId: {} - AppId: {} - Timestamp: {} - Signature: {}",
				// 	userId, traceId, appId, timestamp, signature
				// );
				// Valida os dados recebidos, bem como a assinatura dos dados
				ValidateSsoData(traceId, userId, appId, timestamp, signature);

				var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
				if (campaignId == Guid.Empty)
					throw MotivaiException.ofValidation("Campanha inválida.");
				var campaignToken = await VerifySsoEnviroment(campaignId);

				var login = new IntegrationLogin
				{
					Token = campaignToken,
					SkipDocumentValidation = true,
					Login = userId,
					ClientUserId = userId,
					TraceId = traceId,
					ConnectionInfo = ConnectionInfo.OfHttpContext(HttpContext),
				};

				Guid token = await _loginRepository.AuthenticateByPlatformSso(campaignId, login);
				LoginSsoEndingRequest loginSso = LoginSsoEndingRequest.Of(token, "SSO Wellness", null, login.ConnectionInfo);

				var user = await _loginRepository.FinalizeAuthenticatedUserUsingSsoToken(campaignId, loginSso);
				if (user == null)
					throw MotivaiException.ofValidation("Acesso expirado.");

				return await CreateSession(campaignId, user);

			}
			catch (MotivaiException ex)
			{
				return ForwardToError("Ocorreu um erro durante a autenticação", "Caso o erro persista, por favor, entre em contato com o administrador", ex.Message);

			}
			catch (Exception ex)
			{
				// Log assincrono para redirecionar usuario para tela de erro sem esperar
				await LogException(ex, "Erro durante o processamento de SSO com assinatura.", true);
				return ForwardToError("Ocorreu um erro durante a autenticação", "Caso o erro persista, por favor, entre em contato com o administrador");
			}
		}


		private void ValidateSsoData(string traceId, string userId, Guid appId, string timestamp, string signature)
		{
			// var offset = DateTimeOffset.FromUnixTimeSeconds(long.Parse(timestamp));
			// if (offset.UtcDateTime < DateTime.UtcNow.AddMinutes(-30))
			// {
			// 	throw MotivaiException.ofValidation("Token expirado.");
			// }

			if (string.IsNullOrEmpty(signature))
				throw MotivaiException.ofValidation("Requisição não autorizada.");
			// O dotnet substitui o caractere + por espaço, o mesmo é gerado pela carevoice pois a assinatura
			// nos é enviada como base64, replace "temporário" adicionado para corrigir.
			signature = signature.Replace(" ", "+");

			// Monta o conteudo a ser validado conforme especificação da wellness;
			var dataToValidate = appId + timestamp + userId;
			LoggerFactory.GetLogger().Info("Trace {} - Validação de assinatura: {}", traceId, dataToValidate);
			dataToValidate = dataToValidate.Replace(Environment.NewLine, "");

			if (!RsaSignHelper.ValidateSignedData(dataToValidate, signature))
			{
				LoggerFactory.GetLogger().Error("Trace {} - Tentativa de login via SSO com assinatura inválida.", traceId);
				throw MotivaiException.ofValidation("Requisição não autorizada.");
			}
		}

		private async Task<string> VerifySsoEnviroment(Guid campaignId)
		{
			try
			{
				var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);
				if (!campaignSettings.Parametrizations.EnableLoginIntegration)
					throw MotivaiException.ofValidation("Login integrado está desabilitado nesta campanha.");
				return campaignSettings.Parametrizations.Token;
			}
			catch (Exception ex)
			{
				await LogException(ex, "Erro durante a requisição de solicitação do token de acesso.");
				throw ex;
			}
		}


		private async Task<ActionResult> CreateSession(Guid campaignId, UserParticipantModel user, string productId = null)
		{
			if (user == null)
			{
				throw MotivaiException.ofValidation("Usuário inválido.");
			}
			var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);
			// Verifica se a página de login está ativa
			if (!campaignSettings.Parametrizations.EnableLoginIntegration)
			{
				return ForwardToError("Login integrado está desabilitado nesta campanha", "Contate o atendimento para maiores informações", "Não é permitido efetuar login via integração.");
			}
			_helperWeb.CreateParticipantSession(campaignId, user);

			// Verificar se o primeiro acesso está habilitado na campanha
			if (user.FirstAccess && campaignSettings.Parametrizations.EnableFirstAccess)
			{
				_helperWeb.SetSessionAsFirstAccess();
				if (!string.IsNullOrEmpty(productId))
				{
					var participantSession = await _helperWeb.GetParticipantSessionAsync();
					participantSession.NextUrl = $"/Produto/Detalhes/{productId}";
					_helperWeb.SetParticipantSession(participantSession);
				}
				var catalogSettings = await _campaignRepository.GetPagesSettings(campaignId);
				if (catalogSettings.PagesSettings.EnableRegulation)
				{
					return RedirectToAction("Regulamento", "PrimeiroAcesso");
				}
				else
				{
					return RedirectToAction("Cadastro", "PrimeiroAcesso");
				}
			}
			else if (!string.IsNullOrEmpty(productId))
			{
				return RedirectToAction("Detalhes", "Produto", new { id = productId });
			}

			return RedirectToAction("Index", "Home");
		}
	}
}
