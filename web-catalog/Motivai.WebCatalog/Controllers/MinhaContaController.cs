using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Enums.Orders;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Values;
using Motivai.WebCatalog.Domain;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Addresses;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Models.Catalog.Menu;
using Motivai.WebCatalog.Models.MyAccount;
using Motivai.WebCatalog.Models.Pages;
using Motivai.WebCatalog.Repositories;
using Motivai.WebCatalog.Services.Accounts;
using Motivai.WebCatalog.Services.AuthenticationMfa;
using Motivai.WebCatalog.Services.CampaignSettingsService;
using Motivai.WebCatalog.Services.Catalog;
using Motivai.WebCatalog.Services.Order;
using Microsoft.AspNetCore.Mvc;
using Motivai.SharedKernel.Domain.Entities.Users;

namespace Motivai.WebCatalog.Controllers
{
    public class MinhaContaController : BaseController
	{
		private readonly IHelperWeb _helperWeb;
		private readonly ICryptography _cryptography;
		private readonly CampaignSettingsService campaignSettingsService;
		private readonly OrderManagerService orderService;
		private readonly AccountService accountService;
		private readonly CampaignRepository _campaignRepository;
		private readonly UserParticipantRepository _participantRepository;
		private readonly CatalogRepository _catalogRepository;
		private readonly WebCatalogGateway catalogGateway;
		private readonly MenuCreator menuCreator;
        private readonly AuthenticationMfaService authenticationMfaService;

        public MinhaContaController(IHelperWeb helperWeb, ICryptography cryptography,
			CampaignSettingsService campaignSettingsService,
			OrderManagerService orderService, AccountService accountService,
			CampaignRepository campaignRepository, UserParticipantRepository participantRepository,
			CatalogRepository catalogRepository, WebCatalogGateway catalogGateway, MenuCreator menuCreator,
			AuthenticationMfaService authenticationMfaService)
		{
			this._helperWeb = helperWeb;
			this._cryptography = cryptography;
			this.campaignSettingsService = campaignSettingsService;
			this.orderService = orderService;
			this.accountService = accountService;
			this._campaignRepository = campaignRepository;
			this._participantRepository = participantRepository;
			this._catalogRepository = catalogRepository;
			this.catalogGateway = catalogGateway;
			this.menuCreator = menuCreator;
            this.authenticationMfaService = authenticationMfaService;
        }

		private async Task<CampaignSettingsModel> GetCampaignSettings()
		{
			return await _campaignRepository.GetCampaignSettings(await _helperWeb.GetCampaignIdForCurrentDomain());
		}

		private async Task<CampaignCatalogSettings> GetPagesSettings()
		{
			return await _campaignRepository.GetPagesSettings(await _helperWeb.GetCampaignIdForCurrentDomain());
		}

		[HttpGet]
		public JsonResult Operadoras()
		{
			var ajaxReturn = new AjaxReturn<List<string>>();
			ajaxReturn.Return = EnumHelper<Operadoras>.GetDisplayValues().ToList();
			if (ajaxReturn.Return == null || !ajaxReturn.Return.Any())
				ajaxReturn.SetError("Ocorreu um erro ao carregar operadoras.");
			return ajaxReturn.GetJsonResult();
		}

		[HttpGet]
		public async Task<JsonResult> PesquisarPessoaPeloDocumento([FromQuery] string document)
		{
			try
			{

				if (String.IsNullOrEmpty(document))
				{
					return AjaxReturn.GetJsonResult(null);
				}
				var person = await _participantRepository.SearchPersonByDocument(document);
				if (person == null)
				{
					return AjaxReturn.GetJsonResult(null);
				}

				return AjaxReturn.GetJsonResult(new
				{
					Name = person.name,
					Document = person.document
				});
			}
			catch (Exception ex)
			{
				await LogException(ex);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao carregar os dados, por favor, tente novamente.");
			}
		}

		[HttpGet]
		public JsonResult EstadosCivil()
		{
			var ajaxReturn = new AjaxReturn<List<string>>();
			ajaxReturn.Return = EnumHelper<EstadosCivil>.GetDisplayValues().ToList();
			if (ajaxReturn.Return == null || !ajaxReturn.Return.Any())
				ajaxReturn.SetError("Ocorreu um erro ao carregar os estados civis.");
			return ajaxReturn.GetJsonResult();
		}

		[HttpGet]
		public async Task<JsonResult> MetadataHeaders()
		{
			return await ExecuteAndGetJson(_campaignRepository.GetCampaignUsersMetadataHeaders(await _helperWeb.GetCampaignIdForCurrentDomain()));
		}

		public async Task<IActionResult> Index()
		{
			var catalogSettings = await GetPagesSettings();
			if (!catalogSettings.PagesSettings.EnableMyAccount)
			{
				return ForwardToNotFound();
			}
			var campaignSettings = await GetCampaignSettings();
			campaignSettings.Pages = catalogSettings.PagesSettings;
			var canLoggedParticipantAccessMenuService = await this.menuCreator.CanLoggedParticipantAccessServiceMenu();
			var pageModel = MyAccountMenuViewModel.InitViewModel(campaignSettings, canLoggedParticipantAccessMenuService);
			return View(pageModel);
		}

		public IActionResult Logout()
		{
			var userSession = _helperWeb.GetParticipantSession();
			if (userSession != null)
				_helperWeb.ClearSession();
			return RedirectToAction("Index", "Login");
		}

		[HttpGet]
		public async Task<IActionResult> Template(string id)
		{
			if (id == null || !Regex.IsMatch(id, @"^[-\w]+$"))
				throw new ArgumentException(@"Template inválido", "id");
			var campaignSettings = await campaignSettingsService.GetCampaignSettings();

			if (!campaignSettings.Pages.EnableMyAccount)
			{
				return ForwardToNotFound(true);
			}
			else if (id == "minhaconta-cadastro" && !campaignSettings.Pages.EnableParticipantData)
			{
				return ForwardToNotFound(true);
			}
			else if (id == "minhaconta-extrato" && !campaignSettings.Pages.EnableExtract)
			{
				return ForwardToNotFound(true);
			}
			else if (id == "minhaconta-comprarpontos" && !campaignSettings.Parametrizations.AllowBuyPoints)
			{
				return ForwardToNotFound(true);
			}
			else if (id == "minhaconta-enderecos" && !campaignSettings.Pages.EnableAddresses)
			{
				return ForwardToNotFound(true);
			}
			else if ((id == "minhaconta-pedidos" || id == "minhaconta-pedido-detalhes") && !campaignSettings.Pages.EnableOrders)
			{
				return ForwardToNotFound(true);
			}
			else if ((id == "minhaconta-bloqueados" || id == "minhaconta-expiracao") && !campaignSettings.Pages.EnablePointsToExpireAndBlocked)
			{
				return ForwardToNotFound(true);
			}
			else if (id == "minhaconta-enderecos-editar" && !campaignSettings.Parametrizations.AllowChangeShippingAddress)
			{
				return ForwardToNotFound(true);
			}
			else if (id == "minhaconta-senha" && !campaignSettings.Pages.EnablePassword)
			{
				return ForwardToNotFound(true);
			}
			return View(
				string.Format("~/Views/MinhaConta/Templates/{0}.cshtml", id),
				CatalogPageViewModel.OfCampaignSettings(campaignSettings)
			);
		}

		[HttpGet("MinhaConta/PrincipalData")]
		public async Task<JsonResult> GetPrincialData()
		{
			Guid userId = _helperWeb.GetUserId();
			if (userId == Guid.Empty)
			{
				return AjaxReturn.FromError("Sessão inválida.");
			}
			try
			{
				var participantData = await _participantRepository.GetPrincipalContact(await _helperWeb.GetCampaignIdForCurrentDomain(), userId);
				if (participantData == null)
				{
					return AjaxReturn.GetJsonResult(null);
				}
				return AjaxReturn.GetJsonResult(new
				{
					name = participantData.Name,
					document = participantData.Document,
					email = participantData.Email,
					mobilePhone = TelephoneHelper.FormatNumber(participantData.MobilePhone)
				});
			}
			catch (Exception ex)
			{
				await LogException(ex);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao carregar os dados, por favor, tente novamente.");
			}
		}

		[HttpGet]
		public async Task<JsonResult> DadosCadastro()
		{
			var userId = _helperWeb.GetUserId();
			var campaignId = _helperWeb.GetCampaignId();

			Motivai.SharedKernel.Domain.Entities.References.Users.ParticipantDataModel personalData = null;

			try
			{
				personalData = await _participantRepository.GetPersonalData(campaignId, userId);
			}
			catch (Exception ex)
			{
				await LogException(ex);
			}

			if (personalData == null)
			{
				return AjaxReturn.FromError("Ocorreu um erro ao carregar os dados do cadastro.");
			}
			if (personalData.Contact == null)
			{
				personalData.Contact = new Contact();
			}
			return AjaxReturn.GetJsonResult(new
			{
				Type = personalData.Type == PersonType.Fisica ? "Fisica" : "Juridica",
				Cpf = personalData.Cpf,
				Cnpj = personalData.Cnpj,
				Rg = personalData.Rg,
				CompanyName = personalData.CompanyName,
				StateInscriptionExempt = personalData.StateInscriptionExempt,
				StateInscription = personalData.StateInscription,
				StateInscriptionUf = personalData.StateInscriptionUf,
				Name = personalData.Name,
				BirthDate = personalData.BirthDate,
				Gender = personalData.Gender,
				MaritalStatus = personalData.MaritalStatus,
				GpInf = personalData.GpInf,
				GpPartnerInf = personalData.GpPartnerInf,
				PhotoUrl = personalData.PhotoUrl,
				Contact = personalData.Contact,
				Metadata = personalData.Metadata,
				AccountRepresentative = personalData.AccountRepresentative
			});
		}

		[HttpPost]
		public async Task<JsonResult> DadosCadastro([FromBody] Motivai.SharedKernel.Domain.Entities.References.Users.ParticipantDataModel userData)
		{
			var userId = _helperWeb.GetUserId();
			var campaignId = _helperWeb.GetCampaignId();
			var participantSession = _helperWeb.GetParticipantSession();

			bool result = false;
			try
			{
				// userData.Contact.GetPhoneDdds();
				if (!ModelState.IsValid)
					throw MotivaiException.ofValidation("Campos inválidos.");

				if (participantSession.IsAccountOperator())
				{
					userData.AccountOperator = participantSession.GetAccountOperator();
				}
				userData.LocationInfo = SetConnectionInfo(userData.LocationInfo);

				result = await _participantRepository.UpdatePersonalData(campaignId, userId, userData);
				if (!result)
					return AjaxReturn.FromError("Não foi possível salvar os dados, por favor, tente novamente.");
			}
			catch (Exception ex)
			{
				await LogException(ex);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao salvar os dados, por favor, tente novamente mais tarde.");
			}

			// Atualiza a sessão do usuário
			var participant = _helperWeb.GetParticipantSession();
			participant.Name = userData.Name;
			participant.Email = userData.Contact.MainEmail;
			_helperWeb.SetParticipantSession(participant);

			return AjaxReturn.GetJsonResult("Dados atualizados com sucesso.");
		}

		[HttpGet]
		public async Task<JsonResult> EnderecosEntrega([FromQuery] bool? resumed)
		{
			var userId = _helperWeb.GetUserId();
			var campaignId = _helperWeb.GetCampaignId();

			List<Address> shippingAddresses = null;
			try
			{
				if (resumed.HasValue && resumed.Value)
				{
					shippingAddresses = await _participantRepository.GetResumedShippingAddresses(campaignId, userId);
				}
				else
				{
					shippingAddresses = await _participantRepository.GetShippingAddresses(campaignId, userId);
				}
			}
			catch (Exception ex)
			{
				await LogException(ex);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao carregar os endereços de entrega.");
			}
			if (shippingAddresses == null)
				return AjaxReturn.GetJsonResult(null);


			return AjaxReturn.GetJsonResult(shippingAddresses.Select(address =>
			{
				var model = AddressModel.Of(address);
				model.Id = _cryptography.Encrypt(model.Id);
				return model;
			}).ToList());
		}

		[HttpPost]
		public async Task<JsonResult> CadastrarEndereco([FromBody] AddressModel model)
		{
			var campaignSettings = await GetCampaignSettings();
			if (!campaignSettings.Parametrizations.AllowChangeShippingAddress)
			{
				return AjaxReturn.FromError("Alteração de endereço está desabilitado neste campanha.");
			}
			if (model == null)
				return AjaxReturn.FromError("Preencha todos os campos do endereço corretamente.");
			var campaignId = _helperWeb.GetCampaignId();
			var userId = _helperWeb.GetUserId();
			var participant = _helperWeb.GetParticipantSession();
			try
			{
				var address = AddressUpdate.FromModel(model);
				if (participant.IsAccountOperator())
				{
					address.AccountOperator = participant.GetAccountOperator();
				}
				address.LocationInfo = SetConnectionInfo(address.LocationInfo);


				var createdAddress = await _participantRepository.CreateAddress(campaignId, userId, address);
				if (createdAddress != null && createdAddress.Id != Guid.Empty)
				{
					return AjaxReturn.GetJsonResult(_cryptography.Encrypt(createdAddress.Id.ToString()));
				}

				return AjaxReturn.FromError("Não foi possível cadastrar o endereço, por favor, tente novamente.");
			}
			catch (Exception ex)
			{
				await LogException(ex, "Account - Addresses", "Erro ao cadastrar endereço");
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao cadastrar o endereço de entrega, por favor, tente novamente.");
			}
		}

		[HttpGet]
		public async Task<JsonResult> GetEndereco(string id)
		{
			var userId = _helperWeb.GetUserId();
			var campaignId = _helperWeb.GetCampaignId();
			var addressId = ParserHelper.DecryptGuid(_cryptography, id);

			AddressModel model = null;
			try
			{
				var address = await _participantRepository.GetAddressById(campaignId, userId, addressId);
				if (address == null)
				{
					return AjaxReturn.FromError("Endereço não encontrado.");
				}
				model = AddressModel.Of(address);
				model.Id = id;
			}
			catch (Exception ex)
			{
				await LogException(ex, "Account - Addresses", "Erro ao carregar o endereço");
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao carregar o endereço de entrega, por favor, tente novamente.");
			}
			return AjaxReturn.GetJsonResult(model);
		}

		[HttpPut]
		public async Task<JsonResult> AtualizarEndereco([FromBody] AddressModel model)
		{
			var campaignSettings = await GetCampaignSettings();
			if (!campaignSettings.Parametrizations.AllowChangeShippingAddress)
			{
				return AjaxReturn.FromError("Alteração de endereço está desabilitado neste campanha.");
			}

			var campaignId = _helperWeb.GetCampaignId();
			var userId = _helperWeb.GetUserId();
			var participant = _helperWeb.GetParticipantSession();
			try
			{
				var address = AddressUpdate.FromModel(model);
				if (participant.IsAccountOperator())
				{
					address.AccountOperator = participant.GetAccountOperator();
				}
				address.LocationInfo = SetConnectionInfo(address.LocationInfo);

				address.Id = ParserHelper.DecryptGuid(_cryptography, model.Id);
				if (await _participantRepository.UpdateAddress(campaignId, userId, address))
				{
					return AjaxReturn.GetJsonResult("Endereço atualizado com sucesso.");
				}
				return AjaxReturn.FromError("Não foi possível atualizar o endereço, por favor, tente novamente.");
			}
			catch (Exception ex)
			{
				await LogException(ex, "Account - Addresses", "Erro ao atualizar endereço");
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao atualizar o endereço de entrega, por favor, tente novamente.");
			}
		}

		[HttpDelete]
		public async Task<JsonResult> ExcluirEndereco(string id)
		{
			var campaignSettings = await GetCampaignSettings();
			if (!campaignSettings.Parametrizations.AllowChangeShippingAddress)
			{
				return AjaxReturn.FromError("Alteração de endereço está desabilitado neste campanha.");
			}

			var userId = _helperWeb.GetUserId();
			var campaignId = _helperWeb.GetCampaignId();
			var addressId = ParserHelper.DecryptGuid(_cryptography, id);

			try
			{
				if (await _participantRepository.DeleteAddress(campaignId, userId, addressId))
				{
					return AjaxReturn.GetJsonResult("Endereço excluído com sucesso.");
				}
				else
				{
					return AjaxReturn.FromError("Não foi possível excluir o endereço, por favor, tente novamente.");
				}
			}
			catch (Exception ex)
			{
				await LogException(ex, "Account - Addresses", "Erro ao excluir endereço");
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao excluir o endereço de entrega, por favor, tente novamente.");
			}
		}

		[HttpGet]
		public async Task<JsonResult> GetResumo()
		{
			var userId = _helperWeb.GetUserId();
			var campaignId = _helperWeb.GetCampaignId();

			BalanceResumeModel summary = null;
			try
			{
				summary = await _participantRepository.GetSummary(campaignId, userId);
			}
			catch (Exception ex)
			{
				await LogException(ex);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao carregar o resumo, por favor, tente novamente.");
			}
			if (summary == null)
				return null;

			if (summary.Balance >= 0)
			{
				var usuarioLogado = _helperWeb.GetParticipantSession();
				usuarioLogado.Balance = summary.Balance;
				_helperWeb.SetParticipantSession(usuarioLogado);
			}

			if (summary.Balance <= 0)
			{
				summary.BlockedPoints = PointsSummary.Zero();
				summary.ExpiringPoints = PointsSummary.Zero();
			}

			CoinName coinName = null;
			try
			{
				coinName = await _campaignRepository.GetCoinName(campaignId);
			}
			catch (Exception ex)
			{
				await LogException(ex, "Account - Summary", $"Erro ao carregar nome da moeda - CMP {campaignId}");
				coinName = CoinName.Of("", "");
			}

			return AjaxReturn.GetJsonResult<dynamic>(new
			{
				CoinName = coinName,
				AvailableBalance = summary.Balance,
				LastAccessDate = summary.LastAccess,
				summary.BlockedPoints,
				summary.ExpiringPoints
			});
		}

		[HttpGet]
		public async Task<JsonResult> GetUltimosAcumulos()
		{
			var catalogSettings = await GetPagesSettings();
			if (!catalogSettings.PagesSettings.EnableExtract)
			{
				return AjaxReturn.FromError("Extrato está desabilitado neste campanha.");
			}
			var campaignId = _helperWeb.GetCampaignId();
			var userId = _helperWeb.GetUserId();
			List<TransactionSummaryModel> lastAcumullations = null;
			try
			{
				lastAcumullations = await _participantRepository.GetLastAccumulations(campaignId, userId);
			}
			catch (Exception ex)
			{
				await LogException(ex);
				return AjaxReturn.FromError("Ocorreu um erro ao carregar os últimos acúmulos, por favor, tente novamente.");
			}
			return AjaxReturn.GetJsonResult(lastAcumullations);
		}

		[HttpGet]
		public async Task<JsonResult> GetUltimosResgates()
		{
			var catalogSettings = await GetPagesSettings();
			if (!catalogSettings.PagesSettings.EnableExtract)
			{
				return AjaxReturn.FromError("Extrato está desabilitado neste campanha.");
			}
			var campaignId = _helperWeb.GetCampaignId();
			var userId = _helperWeb.GetUserId();
			List<TransactionSummaryModel> lastRedeems = null;
			try
			{
				lastRedeems = await _participantRepository.GetLastRedeems(campaignId, userId);
			}
			catch (Exception ex)
			{
				await LogException(ex);
				return AjaxReturn.FromError("Ocorreu um erro ao carregar os últimos resgates, por favor, tente novamente.");
			}
			return AjaxReturn.GetJsonResult(lastRedeems);
		}

		[HttpGet]
		public async Task<JsonResult> GetPontosExpirar()
		{
			var campaignId = _helperWeb.GetCampaignId();
			var userId = _helperWeb.GetUserId();

			BalanceResumeModel summary = null;
			MechanicsSummary expiringPoints = null;

			try
			{
				summary = await _participantRepository.GetSummary(campaignId, userId);
			}
			catch (Exception ex)
			{
				await LogException(ex);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao carregar o saldo, por favor, tente novamente.");
			}

			try
			{
				expiringPoints = await _participantRepository.GetExpirePoints(campaignId, userId);
				if (expiringPoints == null)
				{
					expiringPoints = new MechanicsSummary();
				}
				expiringPoints.CoinName = await _campaignRepository.GetCoinName(campaignId);

				if (summary.Balance <= 0)
				{
					expiringPoints = MechanicsSummary.Zero();
				}

				return AjaxReturn.GetJsonResult(new
				{
					CoinName = await _campaignRepository.GetCoinName(campaignId),
					TotalPoints = expiringPoints.TotalPoints,
					ExpiringPoints = expiringPoints.Mechanics
				});
			}
			catch (Exception ex)
			{
				await LogException(ex);
				return AjaxReturn.FromError("Ocorreu um erro ao carregar os pontos a expirar, por favor, tente novamente.");
			}
		}

		[HttpGet]
		public async Task<JsonResult> GetPontosBloqueados()
		{
			var campaignId = _helperWeb.GetCampaignId();
			var userId = _helperWeb.GetUserId();
			BlockedPointsSummary blockedPoints = null;
			try
			{
				blockedPoints = await _participantRepository.GetBlockedPoints(campaignId, userId);
				if (blockedPoints != null)
				{
					blockedPoints.CoinName = await _campaignRepository.GetCoinName(campaignId);
				}
			}
			catch (Exception ex)
			{
				await LogException(ex);
				return AjaxReturn.FromError("Ocorreu um erro ao carregar os pontos bloqueados, por favor, tente novamente.");
			}
			return AjaxReturn.GetJsonResult(blockedPoints);
		}

		[HttpGet]
		public async Task<JsonResult> GetParametrizacaoMetadata()
		{
			return await ExecuteAndGetJson(_campaignRepository.GetCampaignPointsMetadataParametrization(_helperWeb.GetCampaignId()));
		}

		[HttpGet]
		public async Task<JsonResult> GetExtrato(string t, DateTime? di, DateTime? df)
		{
			var catalogSettings = await GetPagesSettings();
			if (!catalogSettings.PagesSettings.EnableExtract)
			{
				return AjaxReturn.FromError("Extrato está desabilitado neste campanha.");
			}
			TransactionType? transactionType = null;
			TransactionOrigin? transactionOrigin = null;
			switch (t)
			{
				case "C": transactionType = TransactionType.Credit; break;
				case "R": transactionType = TransactionType.Debt; break;
				case "E":
					transactionType = TransactionType.Credit;
					transactionOrigin = TransactionOrigin.OrderRefund;
					break;
			}
			var campaignId = _helperWeb.GetCampaignId();
			var userId = _helperWeb.GetUserId();

			ExtractModel extract = new ExtractModel();
			try
			{

				extract = await catalogGateway.GetTransactions(userId, campaignId, null, transactionType, transactionOrigin, di, df);

				if (extract != null)
					extract.CoinName = await _campaignRepository.GetCoinName(campaignId);
				return AjaxReturn.GetJsonResult(extract);
			}
			catch (Exception ex)
			{
				await LogException(ex);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao carregar o extrato de pontos, por favor, tente novamente.");
			}
		}

		[HttpPost]
		public async Task<JsonResult> AlterarSenha([FromBody] dynamic payload)
		{
			var campaignId = _helperWeb.GetCampaignId();
			var userId = _helperWeb.GetUserId();
			try
			{
				var result = await accountService.UpdatePassword(campaignId, _helperWeb.GetParticipantSession(),
					PasswordUpdate.Of((string)payload.senhaAntiga, (string)payload.novaSenha, (string)payload.confirmaSenha));
				if (result)
				{
					return AjaxReturn.GetJsonResult(true);
				}
				return AjaxReturn.FromError("Não foi possível alterar a senha, por favor, tente novamente.");
			}
			catch (Exception ex)
			{
				await LogException(ex);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro durante a atualização da senha, por favor, tente novamente.");
			}
		}

		[HttpGet]
		public bool GetParticipantMigrated()
		{
			var userSession = _helperWeb.GetParticipantSession();
			if (userSession == null) {
				return false;
			}
			return userSession.IsParticipantMigrated();
		}

		[HttpGet]
		public async Task<JsonResult> GetPedidos([FromQuery] string s = null, [FromQuery] DateTime? di = null, [FromQuery] DateTime? df = null)
		{
			OrderStatus? status = null;
			switch (s)
			{
				case "P": status = OrderStatus.Processed; break;
				case "T": status = OrderStatus.Shipping; break;
				case "E": status = OrderStatus.Delivered; break;
				case "C": status = OrderStatus.Canceled; break;
			}
			return await ExecuteAndGetJson(orderService.GetOrders(status, di, df));
		}

		[HttpGet]
		public async Task<JsonResult> GetPedido(string id, [FromQuery] OrderType? type = null)
		{
			Guid orderId = ParserHelper.DecryptGuid(_cryptography, id);
			if (orderId == Guid.Empty || type == null || !type.HasValue)
				return AjaxReturn.FromError("Pedido inválido.");
			return await ExecuteAndGetJson(orderService.GetOrderByType(orderId, type.Value));
		}

		[HttpPost]
		public async Task<JsonResult> AprovaPedido(string id)
		{
			Guid orderId = ParserHelper.DecryptGuid(_cryptography, id);
			if (orderId == Guid.Empty)
				return AjaxReturn.FromError("Pedido inválido.");
			var campaignId = _helperWeb.GetCampaignId();
			try
			{
				return AjaxReturn.GetJsonResult(await _catalogRepository.ApproveOrder(campaignId, orderId));
			}
			catch (Exception ex)
			{
				await LogException(ex);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao aprovar o pedido, por favor, tente novamente.");
			}
		}

		[HttpPost]
		public async Task<JsonResult> RecusaPedido(string id)
		{
			Guid orderId = ParserHelper.DecryptGuid(_cryptography, id);
			if (orderId == Guid.Empty)
				return AjaxReturn.FromError("Pedido inválido.");
			var campaignId = _helperWeb.GetCampaignId();
			try
			{
				return AjaxReturn.GetJsonResult(await _catalogRepository.RefuseOrder(campaignId, orderId));
			}
			catch (Exception ex)
			{
				await LogException(ex);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao reprovar o pedido, por favor, tente novamente.");
			}
		}

		[HttpGet]
		public async Task<JsonResult> ConsultLinkVouchers(string id, string partnerId)
		{
			Guid orderId = ParserHelper.DecryptGuid(_cryptography, id);
			Guid itemGrouperId = ParserHelper.DecryptGuid(_cryptography, partnerId);

			if (orderId == Guid.Empty)
				return AjaxReturn.FromError("Pedido inválido.");

			if (itemGrouperId == Guid.Empty)
				return AjaxReturn.FromError("Pedido do parceiro inválido.");

			try
			{
				return AjaxReturn.GetJsonResult(await orderService.ConsultLinkVouchers(orderId, itemGrouperId));
			}
			catch (Exception ex)
			{
				await LogException(ex);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao consultar os links dos vales, por favor, tente novamente.");
			}
		}

		[HttpGet]
		public async Task<JsonResult> BuscarDadosDuplaAutenticacao()
		{
			var campaignId = _helperWeb.GetCampaignId();
			var userId = _helperWeb.GetUserId();
			try
			{
				return AjaxReturn.GetJsonResult(await authenticationMfaService.FindParticipantAuthenticationMfaSettings(campaignId, userId));
			}
			catch (Exception ex)
			{
				await LogException(ex);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao consultar os dados, por favor, tente novamente.");
			}
		}

		[HttpPost]
		public async Task<JsonResult> EnviarMfaToken([FromBody] ParticipantAuthenticationMfaSettings participantMfaSettings)
		{
			var campaignId = _helperWeb.GetCampaignId();
			var userId = _helperWeb.GetUserId();
			try
			{
				return AjaxReturn.GetJsonResult(await authenticationMfaService.SendAuthenticationMfaToken(campaignId, userId, participantMfaSettings));
			}
			catch (Exception ex)
			{
				await LogException(ex);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao enviar o código de segurança, por favor, tente novamente.");
			}
		}

		[HttpPost]
		public async Task<JsonResult> ValidarTokenDuplaAutenticacao([FromBody] dynamic participantMfaSettings)
		{
			var campaignId = _helperWeb.GetCampaignId();
			var userId = _helperWeb.GetUserId();
			try
			{
				return AjaxReturn.GetJsonResult(await authenticationMfaService.ValidateAndSaveMfaSettings(campaignId, userId, participantMfaSettings));
			}
			catch (Exception ex)
			{
				await LogException(ex);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao validar o código de segurança, por favor, tente novamente.");
			}
		}
	}
}
