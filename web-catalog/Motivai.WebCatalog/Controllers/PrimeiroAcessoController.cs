using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Models.FirstAccess;
using Motivai.WebCatalog.Models.FirstAccess.Card;
using Motivai.WebCatalog.Models.Pages.FirstAccess;
using Motivai.WebCatalog.Repositories;
using Motivai.WebCatalog.Services.CampaignSettingsService;
using Motivai.WebCatalog.Services.Terms;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.WebCatalog.Controllers
{
	public class PrimeiroAcessoController : BaseController
	{
		private readonly IHelperWeb _helperWeb;
		private readonly ICryptography cryptography;
		private readonly CampaignSettingsService campaignSettingsService;
		private readonly FirstAccessService firstAccessService;
		private readonly UserParticipantRepository _userParticipantRepository;

		public PrimeiroAcessoController(IHelperWeb helperWeb, ICryptography cryptography,
				CampaignSettingsService campaignSettingsService, FirstAccessService firstAccessService,
				UserParticipantRepository userParticipantRepository)
		{
			this.campaignSettingsService = campaignSettingsService;
			_helperWeb = helperWeb;
			this.cryptography = cryptography;
			this.firstAccessService = firstAccessService;
			_userParticipantRepository = userParticipantRepository;
		}

		private async Task<IActionResult> SendToView(string viewName, FirstAccessSteps currentStep, object pageModel = default)
		{
			var campaignSettings = await campaignSettingsService.GetCampaignSettings();
			var firstAccessModel = new FirstAccessStepsViewModel(campaignSettings, currentStep, pageModel);
			return View(viewName, firstAccessModel);
		}

		private async Task<IActionResult> ResolveNextStepFrom(FirstAccessSteps currentStep)
		{
			var firstAccessModel = await CreateFirstAccessStepsModel(currentStep);

			if (currentStep == FirstAccessSteps.START)
			{
				if (firstAccessModel.IsRegulationStepActive)
				{
					return RedirectToAction("Regulamento");
				}
				return RedirectToAction("Privacidade");
			}

			if (firstAccessModel.IsRegulationStep)
			{
				return RedirectToAction("Privacidade");
			}

			if (firstAccessModel.IsPrivacyPolicyStep)
			{
				if (firstAccessModel.IsRegistrationDataStepActive)
				{
					return RedirectToAction("Cadastro");
				}
				else if (firstAccessModel.IsCardStep)
				{
					return RedirectToAction("Cartao");
				}
			}

			if (firstAccessModel.IsRegistrationDataStep)
			{
				if (firstAccessModel.IsCardStep)
				{
					return RedirectToAction("Cartao");
				}
			}

			var partipantSession = await _helperWeb.GetParticipantSessionAsync();
			if (!string.IsNullOrEmpty(partipantSession.NextUrl))
			{
				return Redirect(partipantSession.NextUrl);
			}
			return RedirectToAction("Index", "Home");
		}

		private async Task<FirstAccessStepsViewModel> CreateFirstAccessStepsModel(FirstAccessSteps currentStep)
		{
			var campaignSettings = await campaignSettingsService.GetCampaignSettings();
			return new FirstAccessStepsViewModel(campaignSettings, currentStep, null);
		}

		public Task<IActionResult> Index()
		{
			return ResolveNextStepFrom(FirstAccessSteps.START);
		}

		[HttpGet]
		public async Task<IActionResult> Regulamento()
		{
			try
			{
				var pageModel = await firstAccessService.GetRegulationPageConfig();
				if (pageModel.Disabled)
				{
					return await ResolveNextStepFrom(FirstAccessSteps.REGULATION);
				}
				return await SendToView("Regulamento", FirstAccessSteps.REGULATION, pageModel);
			}
			catch (DisabledFirstAccessException)
			{
				return RedirectToAction("/");
			}
			catch (Exception ex)
			{
				await LogException(ex, "FirstAccess - Regulation", "Erro durante carregamento do regulamento");
				return ForwardToError("Não foi possível carregar o regulamento =(",
					"Caso o erro persista, contate nosso atendimento.",
					"Por favor, atualize a página e tente novamente.");
			}
		}

		[HttpPost]
		public async Task<IActionResult> Regulamento(string regulation, string version, string acceptedTerms)
		{
			try
			{
				await firstAccessService.ProccessRegulationAcceptance(regulation, version, acceptedTerms == "on");
				return await ResolveNextStepFrom(FirstAccessSteps.REGULATION);
			}
			catch (RegulationNotAcceptedException)
			{
				return RedirectToAction("Regulamento");
			}
			catch (Exception)
			{
				return RedirectToAction("Regulamento");
			}

		}

		[HttpGet]
		public Task<IActionResult> Privacidade()
		{
			return SendToView("Privacidade", FirstAccessSteps.PRIVACY_POLICY);
		}

		[HttpPost]
		public async Task<JsonResult> Privacidade([FromBody] PrivacyPolicyAcceptanceResult privacyPolicyAcceptanceResult)
		{
			var ajaxReturn = new AjaxReturn<string>();
			try
			{
				var campaignSettings = await campaignSettingsService.GetCampaignSettings();
				var locationInfo = CreateLocationInfo(privacyPolicyAcceptanceResult.Timezone);
				var skipRegistrationData =  await firstAccessService.ProccessPrivacyAcceptance(privacyPolicyAcceptanceResult, locationInfo, campaignSettings);

				if (skipRegistrationData) {
					ajaxReturn.Redirect = Url.Action("Index", "Home");
				} else {
					ajaxReturn.Redirect = Url.Action("Cadastro");
				}
			}
			catch (PrivacyPolicyNotAcceptedException)
			{
				ajaxReturn.SetError("Erro ao salvar aceite nas politicas de privacidade");
				return ajaxReturn.GetJsonResult();
			}
			catch (Exception ex)
			{
				await LogException(ex, "Erro durante o processamento do aceite", true);
				ajaxReturn.SetError("Não foi possível processar o aceite =(" +
					"Caso o erro persista, contate nosso atendimento." +
					"Por favor, atualize a página e tente novamente.");

				return ajaxReturn.GetJsonResult();
			}
			return ajaxReturn.GetJsonResult();
		}

		[HttpGet]
		public async Task<IActionResult> Cadastro()
		{
			try
			{
				var campaignSettings = await campaignSettingsService.GetCampaignSettings();
				var pageModel = await firstAccessService.GetRegistrationPageConfig();
				if (pageModel.SkipFirstAccessRegistrationData)
				{
					return await ResolveNextStepFrom(FirstAccessSteps.REGISTRATION_DATA);
				}
				return await SendToView("Cadastro", FirstAccessSteps.REGISTRATION_DATA, pageModel);
			}
			catch (RegulationNotAcceptedException)
			{
				return RedirectToAction("Regulamento");
			}
			catch (DisabledFirstAccessException)
			{
				return RedirectToAction("/");
			}
			catch (Exception ex)
			{
				await LogException(ex, "Erro durante carregamento do cadastro");
				return ForwardToError("Não foi possível processar o aceite =(",
					"Caso o erro persista, contate nosso atendimento.",
					"Por favor, atualize a página e tente novamente.");
			}
		}

		[HttpPost]
		public async Task<JsonResult> Completar([FromBody] FirstAccessDataModel dataModel)
		{
			var campaignSettings = await campaignSettingsService.GetCampaignSettings();
			var hasCard = campaignSettings.Parametrizations.EnableCardRegisterAtFirstAccess == true;
			if (hasCard)
			{
				return await ExecuteAndGetJsonWithRedirect(firstAccessService.CompleteRegistration(dataModel), "Cartao");
			}
			return await ExecuteAndGetJsonWithRedirect(firstAccessService.CompleteRegistration(dataModel), "/");
		}

		[HttpGet]
		public async Task<IActionResult> Cartao()
		{
			try
			{
				var firstAccessModel = await CreateFirstAccessStepsModel(FirstAccessSteps.CARD);
				if (!firstAccessModel.IsCardStepActive)
				{
					return await ResolveNextStepFrom(FirstAccessSteps.CARD);
				}
				return View("Cartao", firstAccessModel);
			}
			catch (DisabledFirstAccessException)
			{
				return RedirectToAction("/");
			}
			catch (Exception ex)
			{
				await LogException(ex, "Erro durante carregamento do cadastro");
				return ForwardToError("Não foi possível processar o aceite =(",
					"Caso o erro persista, contate nosso atendimento.",
					"Por favor, atualize a página e tente novamente.");
			}
		}

		#region Política de Privacidade

		[HttpGet]
		public async Task<JsonResult> GetPrivacyPolicy()
		{
			return await ExecuteAndGetJson(firstAccessService.CreatePrivacyPageModel());
		}

		#endregion

		#region Atualização Cadastral

		[HttpGet]
		public async Task<JsonResult> Configuracoes()
		{
			return await ExecuteAndGetJson(firstAccessService.GetRegistrationPageConfig());
		}

		[HttpGet]
		public async Task<JsonResult> Dados()
		{
			return await ExecuteAndGetJson(firstAccessService.GetParticipantDataToComplete());
		}

		[HttpGet]
		public async Task<JsonResult> PesquisarPessoaPeloDocumento([FromQuery] string document)
		{
			try
			{
				if (string.IsNullOrEmpty(document))
				{
					return AjaxReturn.GetJsonResult(null);
				}
				var person = await _userParticipantRepository.SearchPersonByDocument(document);
				if (person == null)
				{
					return AjaxReturn.GetJsonResult(null);
				}
				return AjaxReturn.GetJsonResult(new
				{
					Name = person.name,
					Document = person.document
				});
			}
			catch (Exception ex)
			{
				await LogException(ex);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro ao carregar os dados, por favor, tente novamente.");
			}
		}

		#endregion

		#region Cartão

		[HttpGet]
		public async Task<dynamic> TokenizacaoInfo()
		{
			return await ExecuteAndGetJson(firstAccessService.GetTokenizationInfo());
		}

		[HttpPost]
		public async Task<IActionResult> Cartao([FromBody] UserCard card)
		{
			return await ExecuteAndGetJsonWithRedirect(firstAccessService.SaveUserCard(card), "/");
		}
		#endregion
	}
}
