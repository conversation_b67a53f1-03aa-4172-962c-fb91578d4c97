using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Models.Pages;
using Motivai.WebCatalog.Models.Pages.Produto;
using Motivai.WebCatalog.Models.Product;
using Motivai.WebCatalog.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.WebCatalog.Controllers
{
    public class ProdutoController : BaseController {
        private readonly IHelperWeb _helperWeb;
        private readonly CampaignRepository _campaignRepository;
        private readonly ICryptography _cryptography;
        private readonly ProductRepository _productRepository;

        public ProdutoController(IHelperWeb helperWeb, ICryptography cryptography, CampaignRepository campaignRepository,
            ProductRepository productRepository) {
            this._helperWeb = helperWeb;
            this._campaignRepository = campaignRepository;
            this._productRepository = productRepository;
            this._cryptography = cryptography;
        }

        [HttpGet]
        public IActionResult Index() => View();

        [HttpGet]
        public IActionResult TabelaMedidas() => View();

        [HttpGet]
        public async Task<IActionResult> Detalhes([FromRoute(Name = "id")] string elasticId, [FromRoute(Name = "idAux")] string productName) {
            var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
            var participant = _helperWeb.GetParticipantSession();
            ProductDetailsModel product = null;

            try {
                product = await _productRepository.GetProductByElasticId(campaignId, participant.UserId, participant.ParticipantId, elasticId, null, participant.Balance);
            } catch (Exception ex) {
                await LogException(ex, "Erro durante carregamento de detalhes do produto");
                return ForwardToError("Ocorreu um erro ao carregar o produto =(",
                    "Se o erro persistir, por favor, contate o administrador.",
                    "Por favor, atualize a página.");
            }

            if (product == null) {
                return RedirectToAction("PaginaNaoEncontrada", "Erro");
            }

            // Se não estiver disponível então pega o e-mail principal do usuário
            if (!product.Available) {
                ViewBag.email = _helperWeb.GetParticipantSession().Email;
            }

            // Verifica se teve erro durante uma tentativa de adicionar ao carrinho
            var cartSession = _helperWeb.GetSessionCart();
            if (cartSession != null && cartSession.HasErrorToShow()) {
                product.OccurredError = true;
                product.ErrorDescription = cartSession.GetErrorDescription();
                cartSession.HasShownError = true;
                _helperWeb.SetSessionCart(cartSession);
            }

            product.SetMainImage();
            product.Id = _cryptography.Encrypt(product.Id);
            product.PartnerId = _cryptography.Encrypt(product.PartnerId);
            product.DepartmentId = _cryptography.Encrypt(product.DepartmentId);
            product.CategoryId = _cryptography.Encrypt(product.CategoryId);

            var pageModel = new ProductPageModel(product);
            var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);
            pageModel.SetParameters(campaignSettings);
            return View(new ProductViewModel(campaignSettings, pageModel));
        }

        [HttpGet]
        public async Task<JsonResult> Marketplace([FromRoute(Name = "id")] string ean) {
            var participant = _helperWeb.GetParticipantSession();
            return await ExecuteAndGetJson(_productRepository.GetMarketplacesByEan(await _helperWeb.GetCampaignIdForCurrentDomain(),
                participant.UserId, participant.ParticipantId, ean, participant.Balance));
        }

        [HttpGet]
        public async Task<JsonResult> ProdutosSimilares(string id) {
            Guid categoryId = ParserHelper.DecryptGuid(_cryptography, id);
            var participant = _helperWeb.GetParticipantSession();
            return await ExecuteAndGetJson(_productRepository.GetSimilarProducts(await _helperWeb.GetCampaignIdForCurrentDomain(),
                participant.UserId, participant.ParticipantId, categoryId, participant.Balance));
        }

        [HttpPost]
        public async Task<JsonResult> GetAtributos([FromBody] dynamic payload) {
            string elasticId = null;
            string model = null, color = null, voltage = null, size = null;
            if (String.IsNullOrEmpty((string) payload.i))
                return AjaxReturn.FromError("Produto inválido.");
            elasticId = (string) payload.i;
            if (!String.IsNullOrEmpty((string) payload.m)) model = (string) payload.m;
            if (!String.IsNullOrEmpty((string) payload.t)) size = (string) payload.t;
            if (!String.IsNullOrEmpty((string) payload.c)) color = (string) payload.c;
            if (!String.IsNullOrEmpty((string) payload.v)) voltage = (string) payload.v;

            var participant = _helperWeb.GetParticipantSession();
            return await ExecuteAndGetJson(_productRepository.GetSkusAttributes(await _helperWeb.GetCampaignIdForCurrentDomain(),
                participant.UserId, participant.ParticipantId, elasticId, model, voltage, color, size), null);
        }

        [HttpPost]
        public async Task<JsonResult> ConsultaDisponibilidade([FromBody] dynamic payload) {
            string elasticId = null, skuCode = null;
            int quantity = 1;
            decimal? priceDefinedByParticipant = null;

            if (String.IsNullOrEmpty((string) payload.id))
                return AjaxReturn.FromError("Produto inválido.");
            elasticId = (string) payload.id;
            if (String.IsNullOrEmpty((string) payload.md))
                return AjaxReturn.FromError("Modelo inválido.");
            skuCode = (string) payload.md;
            if (payload.qt != null && !int.TryParse((string) payload.q, out quantity))
                quantity = 1;

            try {
                if (payload.priceDefinedByParticipant != null && !string.IsNullOrEmpty(payload.priceDefinedByParticipant.ToString())) {
                    priceDefinedByParticipant = DecimalHelper.ConvertStringToDecimalPtBr(payload.priceDefinedByParticipant.ToString(), "Preço informado");
                }

                var participant = _helperWeb.GetParticipantSession();
                var result = await _productRepository.VerifyAvailability(await _helperWeb.GetCampaignIdForCurrentDomain(), participant.UserId,
                    participant.ParticipantId, elasticId, skuCode, quantity, priceDefinedByParticipant);
                if (result != null && result.Available)
                    return AjaxReturn.GetJsonResult(result);
                return AjaxReturn.GetJsonResult(new {
                    Available = result?.Available ?? false
                });
            } catch (Exception ex) {
                await LogException(ex);
                if (ex is MotivaiException)
                    return AjaxReturn.FromError(ex.Message);
                return AjaxReturn.FromError("Não foi possível verificar a disponibilidade do produto.");
            }
        }

        [HttpPost]
        public async Task<JsonResult> RegistrarAvisoDisponibilidade([FromBody] dynamic payload) {
            if (payload == null || payload.p == null || string.IsNullOrEmpty((string) payload.p))
                return AjaxReturn.FromError("Produto inválido.");
            string p = payload.p;
            var jsonResult = new AjaxReturn();
            var productId = ParserHelper.DecryptGuid(_cryptography, p);
            try {
                var participant = _helperWeb.GetParticipantSession();
                jsonResult.Success = await _productRepository.RegisterAvailableNotification(await _helperWeb.GetCampaignIdForCurrentDomain(),
                    participant.UserId, participant.ParticipantId, productId);
            } catch (Exception ex) {
                if (ex is MotivaiException)
                    jsonResult.SetError(ex.Message);
                else
                    jsonResult.SetError("Não foi possível completar a operação, por favor, tente novamente.");
                await LogException(ex);
            }
            return jsonResult.GetJsonResult();
        }
    }
}