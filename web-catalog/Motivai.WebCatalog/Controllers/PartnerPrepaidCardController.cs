using System.Threading.Tasks;
using Motivai.WebCatalog.Helpers;
using Microsoft.AspNetCore.Mvc;
using Motivai.WebCatalog.Models.ExtraServices.Cards.Partners;
using Motivai.WebCatalog.Services.ExtraServices.Partners;

namespace Motivai.WebCatalog.Controllers
{
	[Produces("application/json")]
	[Route("partners/prepaidcards")]
	public class PartnerPrepaidCardController : BaseController
	{
		private readonly PartnerPrepaidCardService cardsService;
		private readonly IHelperWeb helperWeb;

		public PartnerPrepaidCardController(PartnerPrepaidCardService cardsService, IHelperWeb helperWeb)
		{
			this.cardsService = cardsService;
			this.helperWeb = helperWeb;
		}

		[HttpGet("search-one-by-query")]
		public async Task<JsonResult> FindPrepaidCard([FromQuery] string birthDate, [FromQuery] string firstDigits, [FromQuery] string lastDigits, [FromQuery] string expirationDate)
		{
			return await ExecuteAndGetJson(this.cardsService.FindPrepaidCard(helperWeb.GetUserId(), await helperWeb.GetCampaignIdForCurrentDomain(), birthDate, firstDigits, lastDigits, expirationDate));
		}


		[HttpGet("in-use")]
		public async Task<JsonResult> FindPrepaidCardsInUse()
		{
			return await ExecuteAndGetJson(this.cardsService.FindPrepaidCardsInUse(helperWeb.GetUserId(), await helperWeb.GetCampaignIdForCurrentDomain()),
                "Não foi possível carregar as configurações do cartão, por favor, tente novamente.");
		}

		[HttpGet("{cardId}")]
		public async Task<JsonResult> FindPrepaidCardById(string cardId)
		{
			return await ExecuteAndGetJson(this.cardsService.FindPrepaidCardById(helperWeb.GetUserId(), await helperWeb.GetCampaignIdForCurrentDomain(), cardId),
                "Não foi possível carregar as configurações do cartão, por favor, tente novamente.");
		}

		[HttpPut("{cardId}/active")]
		public async Task<JsonResult> ActiveCard(string cardId)
		{
			return await ExecuteAndGetJson(this.cardsService.ActiveCard(helperWeb.GetUserId(), await helperWeb.GetCampaignIdForCurrentDomain(), cardId),
                "Não foi possível desbloquear o cartão, por favor, tente novamente.");
		}

		[HttpPut("{cardId}/block")]
		public async Task<JsonResult> BlockCard(string cardId)
		{
			return await ExecuteAndGetJson(this.cardsService.BlockCard(helperWeb.GetUserId(), await helperWeb.GetCampaignIdForCurrentDomain(), cardId),
                "Não foi possível bloquear o cartão, por favor, tente novamente.");
		}

		[HttpPut("{cardId}/use")]
		public async Task<JsonResult> UseCard(string cardId)
		{
			return await ExecuteAndGetJson(this.cardsService.UseCard(helperWeb.GetUserId(), await helperWeb.GetCampaignIdForCurrentDomain(), cardId),
                "Não foi possível desbloquear o cartão para uso, por favor, tente novamente.");
		}

		[HttpPut("{cardId}/pin")]
		public async Task<JsonResult> ResetPin(string cardId, [FromBody] PinRequest request)
		{
			return await ExecuteAndGetJson(this.cardsService.ResetPin(helperWeb.GetUserId(), await helperWeb.GetCampaignIdForCurrentDomain(), cardId, request),
                "Não foi possível alterar a senha do cartão, por favor, tente novamente.");
		}

		[HttpGet("{cardId}/statement")]
		public async Task<JsonResult> GetPrepaidCardStatement(string cardId, [FromQuery] string startPeriod, [FromQuery]  string endPeriod, [FromQuery]  int limit)
		{
			return await ExecuteAndGetJson(this.cardsService.GetPrepaidCardStatement(helperWeb.GetUserId(), await helperWeb.GetCampaignIdForCurrentDomain(), cardId, startPeriod, endPeriod, limit),
                "Não foi possível carregar o extrato, por favor, tente novamente.");
		}

		[HttpGet("{cardId}/balance")]
		public async Task<JsonResult> GetBalance(string cardId)
		{
			return await ExecuteAndGetJson(this.cardsService.GetBalance(helperWeb.GetUserId(), await helperWeb.GetCampaignIdForCurrentDomain(), cardId),
                "Não foi possível carregar o saldo, por favor, tente novamente.");
		}

		[HttpGet("{cardId}/tracking")]
		public async Task<JsonResult> RetrieveCardTracking(string cardId)
		{
			return await ExecuteAndGetJson(this.cardsService.RetrieveCardTracking(cardId),
                "Não foi possível rastrear o cartão, por favor, tente novamente.");
		}
	}
}