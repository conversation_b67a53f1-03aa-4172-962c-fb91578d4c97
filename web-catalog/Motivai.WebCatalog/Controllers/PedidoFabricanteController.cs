using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Models.Order;
using Motivai.WebCatalog.Models.Pages;
using Motivai.WebCatalog.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.WebCatalog.Controllers {
    [Route("PedidoFabricante")]
    public class PedidoFabricanteController : BaseController {
        private readonly IHelperWeb _helperWeb;
        private readonly ICryptography _cryptography;
        private readonly CampaignRepository _campaignRepository;
        private readonly UserParticipantRepository _participantRepository;
        private readonly CatalogRepository _catalogRepository;

        public PedidoFabricanteController(IHelperWeb helperWeb, ICryptography cryptography,
            CampaignRepository campaignRepository, UserParticipantRepository participantRepository,
            CatalogRepository catalogRepository) {
            this._helperWeb = helperWeb;
            this._cryptography = cryptography;
            this._catalogRepository = catalogRepository;
            this._participantRepository = participantRepository;
            this._campaignRepository = campaignRepository;
        }

        private void EncryptFactoryOrder(FactoryOrder order) {
            order.OrderId = _cryptography.Encrypt(order.OrderId);
            order.ItemGrouperId = _cryptography.Encrypt(order.ItemGrouperId);
            order.Items.ForEach(item => {
                item.SkuId = _cryptography.Encrypt(item.SkuId);
            });
        }

        private void DecryptFactoryOrder(FactoryOrderPrice order) {
            order.OrderId = _cryptography.Decrypt(order.OrderId);
            order.ItemGrouperId = _cryptography.Decrypt(order.ItemGrouperId);
            order.Items.ForEach(item => {
                item.SkuId = _cryptography.Decrypt(item.SkuId);
            });
        }

        private async Task<FactoryOrder> GetFactoryOrder(Guid campaignId, Guid orderId, Guid factoryId, Guid userId, Guid participantId) {
            var orderCart = await _catalogRepository.GetOrder(campaignId, userId, participantId, orderId);
            if (orderCart == null)
                throw MotivaiException.ofValidation("Pedido não encontrado pelo link informado.");
            // Remove os itens que nao é do fornecedor
            orderCart.ChildrenCarts.ForEach(pc => pc.Products.RemoveAll(i => !i.FactoryId.HasValue || i.FactoryId.Value != factoryId));
            if (orderCart.IsEmpty())
                throw MotivaiException.ofValidation("Pedido não possui produtos desse fornecedor. " +
                    "Caso você tenha certeza que o pedido tem um produto deste fornecedor, por favor, entre em contato com o administrador do sistema.");

            var contact = await _participantRepository.GetPrincipalContact(campaignId, orderCart.UserId);
            orderCart.Participant = new ParticipantDataModel() {
                // Id = contact.Id.ToString(),
                Nome = contact.Name,
                Telefone = contact.Telephone,
                Celular = contact.Cellphone,
                Email = contact.Email
            };
            var order = new FactoryOrder(factoryId, orderCart);
            EncryptFactoryOrder(order);
            return order;
        }

        [HttpGet("{encryptedOrderId}/fabricante/{encryptedFactoryId}/{orderNumber?}")]
        public async Task<ActionResult> GetPedido(string encryptedOrderId, string encryptedFactoryId, string orderNumber) {
            var orderId = ParserHelper.DecryptGuid(_cryptography, encryptedOrderId);
            var factoryId = ParserHelper.DecryptGuid(_cryptography, encryptedFactoryId);

            if (orderId == Guid.Empty)
                return ForwardToError("Pedido inválido.", "Entre em contato com o administrador do sistema.", "", null, false, true);
            try {
                return View("~/Views/Pedido/ConsultaPedidoFabricante.cshtml", new FactoryOrder() {
                    Token = _cryptography.Encrypt(string.Format("{0}|{1}", orderId, factoryId)),
                    OrderNumber = orderNumber
                });
            } catch (Exception ex) {
                if (ex is MotivaiException)
                    (ex as MotivaiException).Loggable = true;
                await LogException(ex);
                if (ex is MotivaiException)
                    return ForwardToError(ex.Message, "", "", null, false, true);
                return ForwardToError("Ocorreu um erro ao carregar o pedido, por favor, tente novamente.",
                    "Se o erro persistir, por favor, contate o administrador do sistema.", null, null, false, true);
            }
        }

        [HttpGet("{token}")]
        public async Task<JsonResult> GetOrderByToken(string token) {
            if (string.IsNullOrEmpty(token))
                return AjaxReturn.FromError("Token do pedido inválido.");

            Guid orderId = Guid.Empty, factoryId = Guid.Empty;
            try {
                var decryptedToken = _cryptography.Decrypt(token);
                var tokenIds = decryptedToken.Split('|');
                orderId = Guid.Parse(tokenIds[0]);
                factoryId = Guid.Parse(tokenIds[1]);
            } catch (Exception) {
                return AjaxReturn.FromError("Token do pedido inválido.");
            }

            var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
            try {
                var participant = _helperWeb.GetParticipantSession();
                return AjaxReturn<FactoryOrder>.GetJsonResult(await GetFactoryOrder(campaignId, orderId, factoryId, participant.UserId, participant.ParticipantId));
            } catch (Exception ex) {
                await LogException(ex);
                if (ex is MotivaiException)
                    return AjaxReturn.FromError(ex.Message);
                return AjaxReturn.FromError("Ocorreu um erro ao carregar o pedido do fabricante.");
            }
        }

        [HttpPut("precificar")]
        public async Task<JsonResult> Price([FromBody] FactoryOrderPrice order) {
            if (order == null)
                return AjaxReturn.FromError("Preencha os campos corretamente para prosseguir.");
            try {
                order.Validate();
                DecryptFactoryOrder(order);
                var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
                return AjaxReturn<bool>.GetJsonResult(await _catalogRepository.PriceItems(campaignId, order));
            } catch (Exception ex) {
                await LogException(ex);
                if (ex is MotivaiException)
                    return AjaxReturn.FromError(ex.Message);
                return AjaxReturn.FromError("Ocorreu um erro ao efetuar a precificação dos itens, por favor, tente novamente.");
            }
        }
    }
}