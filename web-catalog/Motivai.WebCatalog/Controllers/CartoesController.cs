using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Models.ExtraServices.Cards;
using Motivai.WebCatalog.Repositories;
using Motivai.WebCatalog.Services.ExtraServices;
using Motivai.WebCatalog.Models.Pages.Cards;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;

namespace Motivai.WebCatalog.Controllers {
    [Produces("application/json")]
    [Route("Cartoes")]
    public class CartoesController : BaseController {
        private readonly IHelperWeb helperWeb;
        private readonly CampaignRepository campaignRepository;
        private readonly CardsService cardsService;

        public CartoesController(IHelperWeb helperWeb, CampaignRepository campaignRepository, CardsService cardsService) {
            this.helperWeb = helperWeb;
            this.campaignRepository = campaignRepository;
            this.cardsService = cardsService;
        }

        private async Task<CampaignSettingsModel> GetCampaignConfiguration() {
            return await campaignRepository.GetCampaignSettings(await helperWeb.GetCampaignIdForCurrentDomain());
        }

		private bool IsPersonTypeAllowedToAccess(PersonTypeAllowed prepaidCardPersonTypeAllowed)
		{
			var participantSession = helperWeb.GetParticipantSession();
			if (prepaidCardPersonTypeAllowed != PersonTypeAllowed.BOTH && prepaidCardPersonTypeAllowed .ToString() != participantSession.Type.ToString())
			{
                return false;
			}
			return true;
		}

        [HttpGet("comsaque")]
        public async Task<IActionResult> WithdrawablePage() {
            var campaignSettings = await GetCampaignConfiguration();
            if (!campaignSettings.Parametrizations.EnablePrepaidCard || !campaignSettings.Parametrizations.EnableWithdrawableCard) {
                return ForwardToNotFound();
            }
			var personTypeAllowed = campaignSettings.Parametrizations.AllowDrawablePrepaidCardOnlyForPersonType;
			if (!IsPersonTypeAllowedToAccess(personTypeAllowed))
			{
                return ForwardToNotFound();
			}
            var parametrizations = await this.cardsService.GetCardParametrizations(await helperWeb.GetCampaignIdForCurrentDomain());
            return View("Index", PrepaidCardPageModel.Of(PrepaidCardType.WITHDRAWABLE, campaignSettings, parametrizations));;
        }

        [HttpGet("semsaque")]
        public async Task<IActionResult> NoDrawablePage() {
            var campaignSettings = await campaignRepository.GetCampaignSettings(await helperWeb.GetCampaignIdForCurrentDomain());
            if (!campaignSettings.Parametrizations.EnablePrepaidCard || !campaignSettings.Parametrizations.EnableNoDrawableCard) {
                return ForwardToNotFound();
            }
			var personTypeAllowed = campaignSettings.Parametrizations.AllowNoDrawablePrepaidCardOnlyForPersonType;
			if (!IsPersonTypeAllowedToAccess(personTypeAllowed))
			{
                return ForwardToNotFound();
			}
            var parametrizations = await this.cardsService.GetCardParametrizations(await helperWeb.GetCampaignIdForCurrentDomain());
            return View("Index", PrepaidCardPageModel.Of(PrepaidCardType.NODRAWABLE, campaignSettings, parametrizations));;
        }

        private async Task<JsonResult> GetPageConfigurationByType(PrepaidCardType cardType) {
            return await ExecuteAndGetJson(cardsService.GetPageConfiguration(await helperWeb.GetCampaignIdForCurrentDomain(), cardType),
                "Não foi possível carregar as configurações, por favor, tente novamente.");
        }

        private async Task<JsonResult> GetParticipantCardsByType(PrepaidCardType cardType) {
            return await ExecuteAndGetJson(cardsService.GetParticipantCardsByType(await helperWeb.GetCampaignIdForCurrentDomain(),
                helperWeb.GetUserId(), cardType));
        }

        private async Task<JsonResult> CalculateFeesByCardType(PrepaidCardType cardType, dynamic payload) {
            return await ExecuteAndGetJson(this.cardsService.CalculateCardOrderFees(await helperWeb.GetCampaignIdForCurrentDomain(),
                helperWeb.GetUserId(), cardType, payload));
        }

        private async Task<JsonResult> CreatePrepaidCardOrder(PrepaidCardType cardType, PrepaidCardOrder order) {
            var campaignSettings = await GetCampaignConfiguration();
            if (!campaignSettings.Parametrizations.EnablePrepaidCard) {
                return AjaxReturn.FromError("Cartão pré-pago não está ativado nesta campanha");
            }
            return await ExecuteAndGetJson(cardsService.CreatePrepaidCardOrder(await helperWeb.GetCampaignIdForCurrentDomain(),
                helperWeb.GetParticipantSession(), cardType, order));
        }

        [HttpGet("withdrawable/configuracoes")]
        public async Task<JsonResult> GetPageConfiguration() {
            return await GetPageConfigurationByType(PrepaidCardType.WITHDRAWABLE);
        }

        [HttpGet("withdrawable/gerados")]
        public async Task<JsonResult> GetParticipantWithdrawableCards() {
            return await GetParticipantCardsByType(PrepaidCardType.WITHDRAWABLE);
        }

        [HttpPost("withdrawable/taxas")]
        public async Task<JsonResult> CalculateWithdrawableCardFees([FromBody] dynamic payload) {
            return await CalculateFeesByCardType(PrepaidCardType.WITHDRAWABLE, payload);
        }

        [HttpPost("withdrawable/transferir")]
        public async Task<JsonResult> CreateOrderUsingWithdrawableCard([FromBody] PrepaidCardOrder order) {
            var participant = helperWeb.GetParticipantSession();
            if (participant.IsAccountOperator()) {
                order.AccountOperator = participant.GetAccountOperator();
            }
            order.LocationInfo = SetConnectionInfo(order.LocationInfo);
            return await CreatePrepaidCardOrder(PrepaidCardType.WITHDRAWABLE, order);
        }

        [HttpGet("nodrawable/configuracoes")]
        public async Task<JsonResult> GetNoDrawablePageConfiguration() {
            return await GetPageConfigurationByType(PrepaidCardType.NODRAWABLE);
        }

        [HttpGet("nodrawable/gerados")]
        public async Task<JsonResult> GetParticipantNoDrawableCards() {
            return await GetParticipantCardsByType(PrepaidCardType.NODRAWABLE);
        }

        [HttpPost("nodrawable/taxas")]
        public async Task<JsonResult> CalculateNoDrawableCardFees([FromBody] dynamic payload) {
            return await CalculateFeesByCardType(PrepaidCardType.NODRAWABLE, payload);
        }

        [HttpPost("nodrawable/transferir")]
        public async Task<JsonResult> CreateOrderUsingNoDrawableCard([FromBody] PrepaidCardOrder order) {
            var participant = helperWeb.GetParticipantSession();
            if (participant.IsAccountOperator()) {
                order.AccountOperator = participant.GetAccountOperator();
            }
            order.LocationInfo = SetConnectionInfo(order.LocationInfo);
            return await CreatePrepaidCardOrder(PrepaidCardType.NODRAWABLE, order);
        }
    }
}
