using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Models.ExtraServices;
using Motivai.WebCatalog.Repositories;
using Motivai.WebCatalog.Services.ExtraServices;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.WebCatalog.Controllers {
    public class RecargaCelularController : BaseController {
        private readonly IHelperWeb helperWeb;
        private readonly ICryptography cryptography;
        private readonly MobileRechargeService rechargeService;
        private readonly CampaignRepository campaignRepository;
        private readonly UserParticipantRepository participantRepository;
        private readonly CatalogRepository catalogRepository;

        public RecargaCelularController(IHelperWeb helperWeb, ICryptography cryptography, MobileRechargeService rechargeService,
                CampaignRepository campaignRepository,
                UserParticipantRepository participantRepository, CatalogRepository catalogRepository) {
            this.helperWeb = helperWeb;
            this.cryptography = cryptography;
            this.rechargeService = rechargeService;
            this.campaignRepository = campaignRepository;
            this.participantRepository = participantRepository;
            this.catalogRepository = catalogRepository;
        }

        private async Task<CampaignSettingsModel> GetCampaignSettings() {
            return await campaignRepository.GetCampaignSettings(await helperWeb.GetCampaignIdForCurrentDomain());
        }

        public async Task<IActionResult> Index() {
            var settings = await GetCampaignSettings();
            if (!settings.Parametrizations.EnableMobileRecharge) {
                return ForwardToNotFound();
            }
            return View("~/Views/Servicos/RecargaCelular.cshtml");
        }

        [HttpGet]
        public async Task<JsonResult> CelularPrincipal() {
            var settings = await GetCampaignSettings();
            if (!settings.Parametrizations.EnableMobileRecharge) {
                return AjaxReturn.FromError("Recarga de celular está desabilitado nesta campanha.");
            }
            try {
                var contact = await participantRepository.GetPrincipalContact(await helperWeb.GetCampaignIdForCurrentDomain(), helperWeb.GetUserId());
                if (contact == null || contact.Cellphone == null || contact.Cellphone.Length < 9) return AjaxReturn.GetJsonResult(null);
                return AjaxReturn.GetJsonResult((dynamic) new {
                    ddd = contact.Cellphone.Substring(0, 2),
                    number = contact.Cellphone.Substring(2)
                });
            } catch (Exception ex) {
                await LogException(ex);
                return AjaxReturn.FromError("Não foi possível carregar o celular cadastrado, por favor, insira manualmente o DDD e o número.");
            }
        }

        [HttpGet]
        [Route("recargacelular/ddds/{ddd}/operadoras")]
        public async Task<JsonResult> Operadoras([FromRoute] string ddd) {
            var settings = await GetCampaignSettings();
            if (!settings.Parametrizations.EnableMobileRecharge) {
                return AjaxReturn.FromError("Recarga de celular está desabilitado nesta campanha.");
            }
            try {
                var campaignId = await helperWeb.GetCampaignIdForCurrentDomain();

                return AjaxReturn<List<MobileOperator>>.GetJsonResult(await rechargeService.GetOperatorsByDdd(campaignId, ddd));
            } catch (Exception ex) {
                await LogException(ex, $"Erro ao carregar operadoras do DDD {ddd}.", true);
                return AjaxReturn.FromError("Não possível carregar as operadoras.");
            }
        }

        [HttpGet]
        [Route("recargacelular/ddds/{ddd}/operadoras/{providerId}/options")]
        public async Task<JsonResult> Operadoras([FromRoute] string ddd, [FromRoute] string providerId) {
            var settings = await GetCampaignSettings();
            if (!settings.Parametrizations.EnableMobileRecharge) {
                return AjaxReturn.FromError("Recarga de celular está desabilitado nesta campanha.");
            }
            try {
                var campaignId = await helperWeb.GetCampaignIdForCurrentDomain();
                return AjaxReturn<List<dynamic>>.GetJsonResult(await rechargeService.GetOptionsForOperator(campaignId, ddd, providerId));
            } catch (Exception ex) {
                await LogException(ex, $"Erro ao carregar opções de recarga do DDD {ddd} e operadora {providerId}.", true);
                return AjaxReturn.FromError("Não possível carregar as opções de recarga da operadora.");
            }
        }

        [HttpPost]
        public async Task<JsonResult> Inicia([FromBody] MobileRecharge recharge) {
            var settings = await GetCampaignSettings();
            if (!settings.Parametrizations.EnableMobileRecharge) {
                return AjaxReturn.FromError("Recarga de celular está desabilitado nesta campanha.");
            }
            try {
                var participant = helperWeb.GetParticipantSession();
                var campaignId = await helperWeb.GetCampaignIdForCurrentDomain();
                if (participant.IsAccountOperator()) {
                    recharge.AccountOperator = participant.GetAccountOperator();
                    recharge.LocationInfo = SetConnectionInfo(recharge.LocationInfo);
                }
                return AjaxReturn<OperationTicket>.GetJsonResult(await rechargeService.IssueRechargeTicket(campaignId, participant, recharge));
            } catch (MotivaiException ex) {
                return AjaxReturn.FromError(ex.Message);
            } catch (Exception ex) {
                await LogException(ex, "Erro ao iniciar recarga.", true);
                return AjaxReturn.FromError("Não possível concluir a operação, por favor, tente novamente.");
            }
        }

        [HttpPost]
        public async Task<JsonResult> Confirma([FromBody] ConfirmationTicket ticket) {
            var settings = await GetCampaignSettings();
            if (!settings.Parametrizations.EnableMobileRecharge) {
                return AjaxReturn.FromError("Recarga de celular está desabilitado nesta campanha.");
            }
            if (ticket == null)
                return AjaxReturn.FromError("Preencha todos os campos para continuar.");
            try {
                var campaignId = await helperWeb.GetCampaignIdForCurrentDomain();
                var participant = helperWeb.GetParticipantSession();

                 if (participant.IsAccountOperator()) {
                    ticket.AccountOperator = participant.GetAccountOperator();
                    ticket.LocationInfo = SetConnectionInfo(ticket.LocationInfo);
                }
                var confirmationResult = await rechargeService.ConfirmRechargeTicket(campaignId, participant, ticket);
                if (confirmationResult.IsConfirmed)
                    confirmationResult.UpdatedBalance = await RefreshParticipantBalance(participant, campaignId);
                return AjaxReturn<OperationTicket>.GetJsonResult(confirmationResult);
            } catch (Exception ex) {
                await LogException(ex, "Erro ao confirmar recarga.", true);
                if (ex is MotivaiException)
                    return AjaxReturn.FromError(ex.Message);
                return AjaxReturn.FromError("Não possível concluir a operação, por favor, tente novamente.");
            }
        }

        [HttpPost]
        public async Task<JsonResult> Cancela([FromBody] ConfirmationTicket ticket) {
            var settings = await GetCampaignSettings();
            if (!settings.Parametrizations.EnableMobileRecharge) {
                return AjaxReturn.FromError("Recarga de celular está desabilitado nesta campanha.");
            }
            if (ticket == null)
                return AjaxReturn.FromError("Preencha todos os campos para continuar.");
            try {
                var campaignId = await helperWeb.GetCampaignIdForCurrentDomain();
                var participant = helperWeb.GetParticipantSession();

                return AjaxReturn<OperationTicket>.GetJsonResult(await rechargeService.CancelRechargeTicket(campaignId, participant, ticket));
            } catch (Exception ex) {
                await LogException(ex, "Erro ao cancelar recarga.", true);
                if (ex is MotivaiException)
                    return AjaxReturn.FromError(ex.Message);
                return AjaxReturn.FromError("Não possível concluir a operação, por favor, tente novamente.");
            }
        }

        private async Task<decimal?> RefreshParticipantBalance(Models.Session.UserPrincipal participant, Guid campaignId) {
            try {
                participant.Balance = await participantRepository.GetAvailableBalance(campaignId, participant.UserId);
                helperWeb.SetParticipantSession(participant);
                return participant.Balance;
            } catch (Exception ex) {
                await LogException(ex, "Erro ao atualizar saldo após recarga.");
                return null;
            }
        }
    }
}