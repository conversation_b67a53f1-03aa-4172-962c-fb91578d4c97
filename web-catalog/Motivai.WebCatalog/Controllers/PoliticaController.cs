using System.Threading.Tasks;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.WebCatalog.Controllers {
    public class PoliticaController : BaseController {
        private readonly IHelperWeb _helperWeb;
        private readonly CampaignRepository _campaignRepository;

        public PoliticaController(IHelperWeb helperWeb, CampaignRepository campaignRepository) {
            this._helperWeb = helperWeb;
            this._campaignRepository = campaignRepository;
        }

        [HttpGet]
        public async Task<ActionResult> Privacidade() {
            var campaignSettings = await _campaignRepository.GetPagesSettings(await _helperWeb.GetCampaignIdForCurrentDomain());
            if (!campaignSettings.PagesSettings.EnablePolicy) {
                return ForwardToNotFound();
            }
            return View();
        }

        [HttpGet]
        public async Task<ActionResult> Entrega() {
            var campaignSettings = await _campaignRepository.GetPagesSettings(await _helperWeb.GetCampaignIdForCurrentDomain());
            if (!campaignSettings.PagesSettings.EnableShippingPolicy) {
                return ForwardToNotFound();
            }
            return View();
        }

        [HttpGet]
        public async Task<JsonResult> CarregaPoliticaPrivacidade() {
            var campaignSettings = await _campaignRepository.GetPagesSettings(await _helperWeb.GetCampaignIdForCurrentDomain());
            if (!campaignSettings.PagesSettings.EnablePolicy) {
                return AjaxReturn.FromError("Política de privacidade está desabilitada.");
            }
            return await ExecuteAndGetJson(_campaignRepository.GetPrivacyPolicy(await _helperWeb.GetCampaignIdForCurrentDomain(),
                _helperWeb.GetUserId()));
        }

        [HttpGet]
        public async Task<JsonResult> CarregaPoliticaEntrega() {
            var campaignSettings = await _campaignRepository.GetPagesSettings(await _helperWeb.GetCampaignIdForCurrentDomain());
            if (!campaignSettings.PagesSettings.EnableShippingPolicy) {
                return AjaxReturn.FromError("Política de entrega e troca está desabilitada.");
            }
            return await ExecuteAndGetJson(_campaignRepository.GetShippingPolicy(await _helperWeb.GetCampaignIdForCurrentDomain(),
                _helperWeb.GetUserId()));
        }
    }
}