using System;
using System.IO;
using System.Text;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Logger;
using Org.BouncyCastle.Asn1.Pkcs;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.OpenSsl;
using Org.BouncyCastle.Security;

namespace Motivai.WebCatalog.Services.Security
{
	public class RsaSignHelper
	{
		private static RsaKeyParameters publicKeyParameters;

		private static RsaKeyParameters GetPublicKey()
		{
			if (publicKeyParameters != null)
				return publicKeyParameters;
			try
			{
				string publicKeyPath = ConfigurationHelper.GetValue("Settings:Wellness:CareVoicePublicKey");
				using (var reader = new StreamReader(publicKeyPath))
				{
					var publicReader = new PemReader(reader);
					publicKeyParameters = (RsaKeyParameters) publicReader.ReadObject();
				}
				return publicKeyParameters;
			}
			catch (IOException ex)
			{
				ExceptionLogger.LogException(ex, "Chave pública não encontrada", true);
				throw;
			}
			catch (Exception ex)
			{
				ExceptionLogger.LogException(ex, "Erro na leitura da chave pública", true);
				throw;
			}
		}

		public static bool ValidateSignedData(string data, string signature)
		{
			try
			{
				byte[] dataBytes = Encoding.UTF8.GetBytes(data);
				byte[] signatureBytes = Convert.FromBase64String(signature);

				ISigner signVerifier = SignerUtilities.GetSigner(PkcsObjectIdentifiers.Sha256WithRsaEncryption.Id);
				signVerifier.Init(false, GetPublicKey());
				signVerifier.BlockUpdate(dataBytes, 0, dataBytes.Length);
				return signVerifier.VerifySignature(signatureBytes);
			}
			catch (Exception ex)
			{
				LoggerFactory.GetLogger().Error("Erro durante a validação da assinatura: {}", ex.Message);
				return false;
			}
		}
	}
}