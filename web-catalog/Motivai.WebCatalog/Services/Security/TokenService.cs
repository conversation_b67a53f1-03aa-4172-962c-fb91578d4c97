using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Strings;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Services.Security {
    public class TokenService {
        private readonly IHelperWeb helperWeb;
        private readonly UserParticipantRepository participantRepository;
        private readonly CatalogRepository catalogRepository;

        public TokenService(IHelperWeb helperWeb,
            UserParticipantRepository participantRepository, CatalogRepository catalogRepository) {
            this.helperWeb = helperWeb;
            this.participantRepository = participantRepository;
            this.catalogRepository = catalogRepository;
        }

        public async Task<dynamic> GetTokenSecurityInfo() {
            var contact = await participantRepository.GetPrincipalContact(await helperWeb.GetCampaignIdForCurrentDomain(), helperWeb.GetUserId());
            if (contact == null)
                throw MotivaiException.ofValidation("Participante não possui nenhuma informação de contato cadastrada.");
            return new {
                email = Obfuscator.ObfuscateText(contact.Email, 4, contact.Email.IndexOf('@') + 4),
                cellphone = Obfuscator.ObfuscateMobilePhone(contact.Cellphone)
            };
        }

        public async Task<bool> IssueShoppingCartSecurityToken() {
            var token = await catalogRepository.IssueSecurityToken(await helperWeb.GetCampaignIdForCurrentDomain(), helperWeb.GetUserId());
            if (string.IsNullOrEmpty(token))
                throw MotivaiException.ofValidation("Não foi possível enviar o código de segurança, por favor, tente novamente.");
            var cart = helperWeb.GetSessionCart();
            cart.StoreToken(token);
            helperWeb.SetSessionCart(cart);
            return true;
        }

        public void ConfirmShoppingCartSecurityToken(string token) {
            var cart = helperWeb.GetSessionCart();
            cart.ValidateToken(token);
            helperWeb.SetSessionCart(cart);
        }
    }
}