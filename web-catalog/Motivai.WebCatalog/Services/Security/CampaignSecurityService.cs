using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Services.Security
{
	public class CampaignSecurityService
    {
        private readonly CampaignRepository campaignRepository;

        public CampaignSecurityService(CampaignRepository campaignRepository)
        {
            this.campaignRepository = campaignRepository;
        }

        public async Task<dynamic> GetAuthenticationMfaParameters(Guid campaignId)
        {
            var security = await campaignRepository.GetCampaignSecurityParameters(campaignId);

            if (security == null)
                throw MotivaiException.ofValidation("Não foi possível consultar os parametros da campanha, por favor, tente novamente.");

            return new {
                requireMfaValidationAtRegistration = security.RequireMfaValidationAtRegistration,
                registrationMfaValidationMethod = security.RegistrationMfaValidationMethod
            };
        }
    }
}