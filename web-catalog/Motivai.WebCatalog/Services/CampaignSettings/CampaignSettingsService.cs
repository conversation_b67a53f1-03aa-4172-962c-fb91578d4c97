using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Services.CampaignSettingsService
{
	public class CampaignSettingsService
	{
		private readonly IHelperWeb _helperWeb;
		private readonly CampaignRepository _campaignRepository;

		public CampaignSettingsService(IHelperWeb helperWeb, CampaignRepository campaignRepository)
		{
			this._helperWeb = helperWeb;
			this._campaignRepository = campaignRepository;
		}

		public async Task<CampaignSettingsModel> GetCampaignSettings()
		{
			Guid campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);
			var catalogSettings = await _campaignRepository.GetPagesSettings(campaignId);
			campaignSettings.Pages = catalogSettings.PagesSettings;
			return campaignSettings;
		}
	}

}