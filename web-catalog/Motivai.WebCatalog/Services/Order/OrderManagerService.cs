using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Enums.Orders;
using Motivai.SharedKernel.Helpers.Api;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Order;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Services.Order {
	public class OrderManagerService {
		private readonly ICryptography cryptography;
		private readonly IHelperWeb helperWeb;
		private readonly CampaignRepository campaignRepository;
		private readonly CatalogRepository catalogRepository;
		private readonly UserParticipantRepository participantRepository;

		public OrderManagerService(ICryptography cryptography, IHelperWeb helperWeb, CampaignRepository campaignRepository,
			CatalogRepository catalogRepository, UserParticipantRepository participantRepository) {
			this.cryptography = cryptography;
			this.helperWeb = helperWeb;
			this.campaignRepository = campaignRepository;
			this.catalogRepository = catalogRepository;
			this.participantRepository = participantRepository;
		}

		public async Task<List<dynamic>> GetOrders(OrderStatus? status, DateTime? di = null, DateTime? df = null) {
			try {
				var orders = await participantRepository.GetOrders(await helperWeb.GetCampaignIdForCurrentDomain(), helperWeb.GetUserId(), di, df, status);
				if (orders == null)
					return null;
				return orders.Select(o => (dynamic) new {
					Id = cryptography.Encrypt(o.Id.ToString()),
					o.OrderType,
					o.InternalOrderNumber,
					o.CreationDate,
					o.TotalAmountPoints,
					Status = OrderStatusHelper.FromExternalStatusToCatalogStatus(o.Status)
				}).ToList();
			} catch (Exception ex) {
				await ExceptionLoggerMiddleware.HandleException(ex);
				throw MotivaiException.ofValidation("Ocorreu um erro ao carregar os pedidos, por favor, tente novamente.");
			}
		}

		public async Task<dynamic> GetOrderByType(Guid orderId, OrderType type) {
			switch (type) {
				case OrderType.PRODUCT_ORDER:
					return await GetProductOrderById(orderId);
				case OrderType.CARD_ORDER:
					return await GetCardOrderById(orderId);
				case OrderType.BANK_TRANSFER_ORDER:
					return await GetBankTransferOrderById(orderId);
				case OrderType.BILL_PAYMENT_ORDER:
					return await GetBillPaymentsOrderById(orderId);
				case OrderType.RECHARGE_ORDER:
					return await GetRechargeOrderById(orderId);
				default:
					throw MotivaiException.ofValidation("Tipo de pedido inválido.");
			}
		}

		private async Task<dynamic> GetProductOrderById(Guid orderId) {
			var campaignId = await helperWeb.GetCampaignIdForCurrentDomain();

			try {
				var participant = helperWeb.GetParticipantSession();
				var orderCart = await catalogRepository.GetOrder(campaignId, participant.UserId, participant.ParticipantId, orderId);
				if (orderCart == null)
					throw MotivaiException.ofValidation("Pedido não encontrado.");

				// Carrega os dados do premiado
				var contact = await participantRepository.GetPrincipalContact(campaignId, helperWeb.GetUserId());
				orderCart.Participant = new ParticipantDataModel() {
					Nome = contact.Name,
					Telefone = contact.Telephone,
					Celular = contact.Cellphone,
					Email = contact.Email
				};
				return CartViewModel.From(orderCart, await campaignRepository.GetCoinName(campaignId), cryptography);
			} catch (Exception ex) {
				await ExceptionLoggerMiddleware.HandleException(ex);
				if (ex is MotivaiException) throw ex;
				throw MotivaiException.ofException("Ocorreu um erro ao carregar o pedido, por favor, tente novamente.", ex);
			}
		}

		private async Task<dynamic> GetCardOrderById(Guid orderId) {
			return await catalogRepository.GetCardOrder(await helperWeb.GetCampaignIdForCurrentDomain(), orderId);
		}

		private async Task<dynamic> GetBankTransferOrderById(Guid orderId) {
			return await catalogRepository.GetBankTransferOrderById(await helperWeb.GetCampaignIdForCurrentDomain(), orderId);
		}

		private async Task<dynamic> GetRechargeOrderById(Guid orderId) {
			return await catalogRepository.GetRechargeOrderById(await helperWeb.GetCampaignIdForCurrentDomain(), orderId);
		}

		private async Task<dynamic> GetBillPaymentsOrderById(Guid orderId) {
			return await catalogRepository.GetBillPaymentsOrderById(await helperWeb.GetCampaignIdForCurrentDomain(), orderId);
		}

		public async Task<List<dynamic>> ConsultLinkVouchers(Guid orderId, Guid itemGrouperId) {
			return await catalogRepository.ConsultLinkVouchers(await helperWeb.GetCampaignIdForCurrentDomain(), orderId, itemGrouperId);
		}
	}
}
