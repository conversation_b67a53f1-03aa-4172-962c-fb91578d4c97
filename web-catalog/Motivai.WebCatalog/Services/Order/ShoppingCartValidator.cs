using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Addresses;
using Motivai.WebCatalog.Models.Order;
using Motivai.WebCatalog.Repositories;
using Motivai.WebCatalog.Services.Cart;

namespace Motivai.WebCatalog.Services.Order {
	public class ShoppingCartValidator {

		private readonly IHelperWeb _helperWeb;
		private readonly ICryptography _cryptography;
		private readonly AddressService addressService;
		private readonly CampaignRepository _campaignRepository;
		private readonly UserParticipantRepository _participantRepository;

		public ShoppingCartValidator(IHelperWeb helperWeb, ICryptography cryptography, AddressService addressService,
				CampaignRepository campaignRepository, UserParticipantRepository participantRepository)
		{
			this._helperWeb = helperWeb;
			this._cryptography = cryptography;
			this.addressService = addressService;
			this._campaignRepository = campaignRepository;
			this._participantRepository = participantRepository;
		}

		public async Task ValidateCartForOrder(CartModel cart, string selectedShippingAddressId = null, bool requiresAllChildren = false) {
			try {
				cart.RequiresAllChildren = requiresAllChildren;
				cart.VerifyErrors();

				if (cart.HasOccurredError()) {
					_helperWeb.SetSessionCart(cart);
					throw new InvalidCardException(cart.ErrorMessage);
				}

				var campaignSettings = await _campaignRepository.GetCampaignSettings(cart.CampaignId);

				if (campaignSettings.Parametrizations.RequireMinimumOrderTotalAmount && campaignSettings.Parametrizations.MinimumOrderTotalAmount.HasValue) {
					if (cart.GetCartTotal() < campaignSettings.Parametrizations.MinimumOrderTotalAmount) {
						throw new InvalidCardException($"Para finalizar o pedido é necessário ter um valor mínimo de {campaignSettings.Parametrizations.MinimumOrderTotalAmount.Value.ToCurrency()}.");
					}
				}

				// Valida se o token foi confirmado
				if (campaignSettings.Parametrizations.RequireRedeemToken && !cart.WasTokenConfirmed()) {
					throw MotivaiException.ofValidation("É necessário confirmar o código de segurança para prosseguir com o pedido.");
				}

				if (campaignSettings.Parametrizations.RequireCepToFinish()) {
					await ValidateShippingAddress(campaignSettings, cart, selectedShippingAddressId);
				}

				// Verifica se o participante tem saldo suficiente se não permite resgate sem saldo
				if(!campaignSettings.IsMarketplace() && !campaignSettings.Parametrizations.AllowRedeemWithoutBalance) {
					decimal clientBalance = await _participantRepository.GetAvailableBalance(cart.CampaignId, cart.UserId);
					if (clientBalance < cart.GetCartTotal()) {
						throw MotivaiException.ofValidation("Saldo insuficiente para resgatar o carrinho.");
					}
				}
			} catch (InvalidCardException) {
				throw;
			} catch (MotivaiException) {
				throw;
			} catch (Exception ex) {
				await ExceptionLogger.LogException(ex, "Order Creator", "Erro ao iniciar resgate");
			}
		}

		private async Task ValidateShippingAddress(CampaignSettingsModel campaignSettings, CartModel cart, string shippingZipcode) {
			Address address = null;
			// Valida a seleção do CEP de entrega caso seja obrigatório
			if (string.IsNullOrEmpty(shippingZipcode)) {
				if (!campaignSettings.Parametrizations.UseDefaultShippingAddress) {
					throw new InvalidCardException("Selecione um endereço para entraga.");
				}
				address = await _participantRepository.GetMainShippingAddress(cart.CampaignId, cart.UserId);
			} else if (shippingZipcode != "other") {
				try {
					var shippingAddressId = Guid.Parse(_cryptography.Decrypt(shippingZipcode));
					address = await _participantRepository.GetAddressById(cart.CampaignId, cart.UserId, shippingAddressId);
				} catch (FormatException) {
					throw new InvalidCardException("Selecione um endereço para entrega válido.");
				} catch {
					throw new InvalidCardException("Ocorreu um erro ao carregar o endereço de entrega, por favor, tente novamente.");
				}
			}

			// Carrega o endereço de entrega
			if (address != null) {
				cart.ShippingAddress = AddressModel.Of(address);
				await addressService.CorrectShippingAddress(cart);
			}

			if (cart.ShippingAddress == null) {
				throw new InvalidCardException("Selecione um endereço para entraga.");
			} else if (cart.ShippingAddress.Receiver == null) {
				// Instância um destinatário para poder preencher na tela seguinte (Confirmação do Endereço)
				cart.ShippingAddress.Receiver = new ReceiverModel();
			}
		}
	}
}