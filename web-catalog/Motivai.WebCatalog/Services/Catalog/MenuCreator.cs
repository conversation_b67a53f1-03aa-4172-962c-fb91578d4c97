using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.SharedKernel.Helpers.Cache;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Catalog.Menu;
using Motivai.WebCatalog.Models.Session;
using Motivai.WebCatalog.Repositories;
using Motivai.WebCatalog.Services.Cache;
using Newtonsoft.Json;

namespace Motivai.WebCatalog.Services.Catalog
{
	public class MenuCreator
	{
		private readonly CustomCache cache;
		private readonly CampaignRepository campaignRepository;
		private readonly IHelperWeb helperWeb;

		public MenuCreator(CustomCache cache, CampaignRepository campaignRepository, IHelperWeb helperWeb)
		{
			this.cache = cache;
			this.campaignRepository = campaignRepository;
			this.helperWeb = helperWeb;
		}

		public async Task<CatalogMenu> GetMenuForCampaign(Guid campaignId)
		{
			var campaignSettings = await campaignRepository.GetCampaignSettings(campaignId);

			var participantSession = helperWeb.GetParticipantSession();
			CatalogMenu campaignMenu = await GetMenuByCampaignId(campaignId);

			if (campaignMenu == null || campaignMenu.Menu == null)
			{
				campaignMenu = new CatalogMenu();
				if (participantSession == null)
					return campaignMenu;

				if (participantSession.IsMigratedOperator()) {
					return HandleMigrateMenu(participantSession.GetRedirectUrlByAuthenticationType(), campaignMenu);
				}
				return campaignMenu;
			}

			if (helperWeb.HasParticipantSession())
			{
				await HandleVisibleServiceMenu(campaignSettings, participantSession, campaignMenu);
			}

			if (participantSession != null && participantSession.IsMigratedOperator())
			{
				campaignMenu = HandleMigrateMenu(participantSession.GetRedirectUrlByAuthenticationType(), campaignMenu);
			}
			return campaignMenu;
		}

		public CatalogMenu HandleMigrateMenu(string redirectUrl, CatalogMenu catalogMenu) {
			if (catalogMenu == null || catalogMenu.Menu == null) {
				CatalogMenu _catalogMenu = new CatalogMenu();
				_catalogMenu.Menu = new List<MenuItem>();
				_catalogMenu.Menu.Insert(0, MenuItem.OfMigratedOperator(MenuItemType.DEPARTMENT, "Voltar ao SuperPortal", redirectUrl));
				return _catalogMenu;
			}
			catalogMenu.Menu.Insert(0, MenuItem.OfMigratedOperator(MenuItemType.DEPARTMENT, "Voltar ao SuperPortal", redirectUrl));
			return catalogMenu;
		}

		public async Task<bool> CanLoggedParticipantAccessServiceMenu()
		{
			var campaignId = await helperWeb.GetCampaignIdForCurrentDomain();
			var participantSession = helperWeb.GetParticipantSession();

			CatalogMenu campaignMenu = await GetMenuByCampaignId(campaignId);
			var serviceMenu = campaignMenu?.Menu?.FirstOrDefault(m => m.Type == MenuItemType.SERVICES);
			if (serviceMenu == null)
			{
				return false;
			}

			return await CanParticipantAccessServiceMenu(serviceMenu, participantSession);
		}

		private async Task HandleVisibleServiceMenu(CampaignSettingsModel campaignSettings, UserPrincipal participantSession, CatalogMenu campaignMenu)
		{
			var serviceMenu = campaignMenu.Menu.FirstOrDefault(m => m.Type == MenuItemType.SERVICES);
			if (serviceMenu == null)
			{
				return;
			}

			bool serviceMenuEnabled = true;
			if (serviceMenu.HasSegmentations())
			{
				if (!await CanParticipantAccessServiceMenu(serviceMenu, participantSession))
				{
					serviceMenuEnabled = false;
					campaignMenu.Menu.Remove(serviceMenu);
				}
			}

			if (serviceMenuEnabled && serviceMenu.HasSubMenus())
			{
				SegmentationPrepaidCards(campaignSettings, participantSession, serviceMenu);

				serviceMenu.Submenus.ForEach(item => {
					if (item.Hide) return;

					campaignMenu.Menu.Add(MenuItem.Of(item.Type, item.Name, item.Url));
				});
			}
		}

		private async Task<bool> CanParticipantAccessServiceMenu(MenuItem serviceMenu, UserPrincipal participantSession)
		{
			if (serviceMenu.Segmentations == null)
			{
				return true;
			}

			var segmentations = serviceMenu.Segmentations;
			if (!segmentations.HasAnySegmentation())
			{
				return true;
			}

			if (segmentations.IsEnableByPersonTypeAndCanAcccess(participantSession.Type))
			{
				return true;
			}

			if (segmentations.IsEnableByParticipantsAndCanAcccess(participantSession.UserId))
			{
				return true;
			}

			if (segmentations.EnableByParticipantsGroups)
			{
				var userGroups = await this.campaignRepository.GetParticipantGroups(participantSession.CampaignId.Value, participantSession.UserId);
				if (segmentations.IsEnableByParticipantsGroupsAndCanAcccess(userGroups))
				{
					return true;
				}
			}

			return false;
		}

		private static void SegmentationPrepaidCards(CampaignSettingsModel campaignSettings, UserPrincipal participantSession, MenuItem serviceMenu)
		{
			if (!campaignSettings.Parametrizations.EnableNoDrawableCard && !campaignSettings.Parametrizations.EnableWithdrawableCard)
				return;

			serviceMenu.Submenus.ForEach(subItems =>
			{
				switch (subItems.Type)
				{
					case MenuItemType.SERVICE_WITHDRAWABLE_PREPAID_CARD:
						HandleWithdrawablePrepaidCardMenu(campaignSettings, participantSession, subItems);
						break;
					case MenuItemType.SERVICE_NODRAWABLE_PREPAID_CARD:
						HandleNoDrawablePrepaidCardMenu(campaignSettings, participantSession, subItems);
						break;
				}
			});
		}

		private static void HandleNoDrawablePrepaidCardMenu(CampaignSettingsModel campaignSettings, UserPrincipal participantSession, SubmenuItem prepaidCardMenuItem)
		{
			if (campaignSettings.Parametrizations.AllowNoDrawablePrepaidCardOnlyForPersonType == PersonTypeAllowed.BOTH)
				return;

			prepaidCardMenuItem.Hide = campaignSettings.Parametrizations.AllowNoDrawablePrepaidCardOnlyForPersonType.ToString() != participantSession.Type.ToString();
		}

		private static void HandleWithdrawablePrepaidCardMenu(CampaignSettingsModel campaignSettings, UserPrincipal participantSession, SubmenuItem prepaidCardMenuItem)
		{
			if (campaignSettings.Parametrizations.AllowDrawablePrepaidCardOnlyForPersonType == PersonTypeAllowed.BOTH)
				return;
			prepaidCardMenuItem.Hide = campaignSettings.Parametrizations.AllowDrawablePrepaidCardOnlyForPersonType.ToString() != participantSession.Type.ToString();
		}

		private async Task<CatalogMenu> GetMenuByCampaignId(Guid campaignId)
		{
			return await cache.GetOrCreate(SharedCacheKeysPrefix.WEB_CAMPAIGN_CATALOG_MENU + campaignId, async () =>
			{
				return await ConfigureMenuForCampaign(campaignId);
			});
		}

		private async Task<CatalogMenu> ConfigureMenuForCampaign(Guid campaignId)
		{
			var pageSettings = await campaignRepository.GetPagesSettings(campaignId);
			var campaignSettings = await campaignRepository.GetCampaignSettings(campaignId);
			var catalogMenu = new CatalogMenu();

			if (pageSettings.PagesSettings.HideMenu)
			{
				catalogMenu.Hide = pageSettings.PagesSettings.HideMenu;
				catalogMenu.Menu = new List<MenuItem>();
			}
			else
			{
				// var menu = await campaignRepository.GetCampaignMenu(campaignId);
				var customization = await campaignRepository.GetCampaignCustomization(campaignId);
				if (customization.Menu != null)
				{
					catalogMenu.Menu = customization.Menu.Select(dep => MenuItem.From(dep))
						.OrderBy(m => m.Name)
						.ToList();
				}
			}
			return catalogMenu;
		}
	}
}
