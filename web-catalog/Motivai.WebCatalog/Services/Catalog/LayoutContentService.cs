using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.WebCatalog.Models.Catalog.LayoutContent;
using Motivai.WebCatalog.Models.Catalog.Menu;
using Motivai.WebCatalog.Repositories;
using Microsoft.Extensions.Caching.Memory;

namespace Motivai.WebCatalog.Services.Catalog {
    public class LayoutContentService {
        private readonly IMemoryCache cache;
        private readonly MenuCreator menuCreator;
        private readonly CampaignRepository campaignRepository;

        public LayoutContentService(IMemoryCache cache, MenuCreator menuCreator, CampaignRepository campaignRepository) {
            this.cache = cache;
            this.menuCreator = menuCreator;
            this.campaignRepository = campaignRepository;
        }

        public async Task<FooterContent> GetFooterContentByCampaign(Guid campaignId) {
            return await cache.GetOrCreateAsync("cmp-footer-cnt-" + campaignId, async entry => {
                var catalogSettings = await campaignRepository.GetPagesSettings(campaignId);
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(catalogSettings == null ? 1 : 60);
                return FooterContent.BuildFromSettings(catalogSettings);
            });
        }

        public async Task<CoinName> GetCoinNameByCampaign(Guid campaignId) {
            if (campaignId == Guid.Empty)
                return CoinName.Of("", "");
            try {
                return await campaignRepository.GetCoinName(campaignId);
            } catch (Exception ex) {
                await ExceptionLogger.LogException(ex, "Catálogo - Moeda", $"Erro ao carregar moeda da campanha {campaignId}", true);
                return CoinName.Of("", "");
            }
        }

        public async Task<CatalogMenu> GetMenuByCampaign(Guid campaignId) {
            try {
                return await menuCreator.GetMenuForCampaign(campaignId);

            } catch (Exception ex) {
                await ExceptionLogger.LogException(ex, "Catálogo - Menu", $"Erro ao carregar menu da campanha {campaignId}", true);
                return new CatalogMenu();
            }
        }

        public async Task<Guid> GetCurrentThemeByCampaign(Guid campaignId) {
            if (campaignId == Guid.Empty)
                return Guid.Empty;
            try {
                return await campaignRepository.GetThemeByCampaign(campaignId);
            } catch (Exception ex) {
                await ExceptionLogger.LogException(ex, "Catálogo - Tema", $"Erro ao carregar tema da campanha {campaignId}", true);
                return Guid.Empty;
            }
        }

        public async Task<CampaignCatalogSettings> GetPagesSettingsByCampaign(Guid campaignId) {
            return await campaignRepository.GetPagesSettings(campaignId);
        }
    }
}
