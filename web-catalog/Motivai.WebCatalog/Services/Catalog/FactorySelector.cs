using System;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.WebCatalog.Models.Order;
using Motivai.WebCatalog.Models.Product;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Services.Catalog {
    public class FactorySelector {
        private readonly CatalogRepository catalogRepository;

        public FactorySelector(CatalogRepository catalogRepository) {
            this.catalogRepository = catalogRepository;
        }

        public async Task SelectFactory(CartModel cart, ProductDetailsModel product, Guid factoryId) {
            // Se não tiver selecionado no carrinho então seleciona um automaticamente
            if (factoryId == Guid.Empty) {
                await SelectFactoryUsingShippingAddressRegion(cart, product);
            } else {
                product.FactoryId = factoryId;
            }

            // Valida se tem uma fábrica para a região de entrega
            if (!product.HasFactory())
                throw MotivaiException.ofValidation("Produto não disponível para entrega na região.");
        }

        private async Task SelectFactoryUsingShippingAddressRegion(CartModel cart, ProductDetailsModel product) {
            var shippingAddressId = Guid.Empty;
            if (cart.HasShippingAddress()) {
                Guid.TryParse(cart.ShippingAddress.Id, out shippingAddressId);
            }

            var factories = await catalogRepository.GetFactoriesForItem(cart.CampaignId, cart.UserId, cart.ParticipantId,
                product.ElasticsearchId, product.SkuCode, shippingAddressId, cart.Cep);

            if (!factories.IsNullOrEmpty()) {
                var factory = factories.FirstOrDefault();
                product.FactoryId = Guid.Parse(factory.FactoryId);
                product.FactoryName = factory.FactoryName;
            }
        }
    }
}