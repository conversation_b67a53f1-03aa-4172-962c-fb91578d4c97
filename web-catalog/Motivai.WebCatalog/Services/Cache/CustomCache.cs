using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Logger;
using Newtonsoft.Json;
using StackExchange.Redis;

namespace Motivai.WebCatalog.Services.Cache
{
    public class CustomCache
    {
        private readonly IDatabase redisDatabase;

        public CustomCache()
        {
            var redisUri = ConfigurationHelper.GetValueFromAny("REDIS_CONNECTION_STRING_MANUAL", "REDIS_URI");
            if (!string.IsNullOrEmpty(redisUri))
            {
                try
                {
                    var redisConnection = ConnectionMultiplexer.Connect(redisUri);
                    this.redisDatabase = redisConnection.GetDatabase();
                }
                catch (Exception ex)
                {
                    LoggerFactory.GetLogger().Error("Cache - Redis - Erro durante conexão: {}", ex.Message);
                }
            }
        }

        public async Task<T> Get<T>(string key)
        {
            if (this.redisDatabase == null) return default;
            String value = null;
            try
            {
                var redisValue = await this.redisDatabase.StringGetAsync(key);
                if (redisValue.IsNullOrEmpty || !redisValue.HasValue)
                {
                    return default;
                }
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Cache - Redis", $"Erro ao ler com: {key}");
            }
            if (value == null)
                return default;
            return JsonConvert.DeserializeObject<T>(value);
        }

        public async Task Set<T>(string key, T value, long ttlSeconds = 3600)
        {
            if (this.redisDatabase == null) return;
            await Set<T>(key, value, TimeSpan.FromSeconds(ttlSeconds));
        }

        public async Task Set<T>(string key, T value, TimeSpan ttl)
        {
            if (this.redisDatabase == null) return;
            if (value == null) return;
            var objStr = JsonConvert.SerializeObject(value);
            try
            {
                await this.redisDatabase.StringSetAsync(key, objStr);
                await this.redisDatabase.KeyExpireAsync(key, ttl);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Cache - Redis", $"Erro ao setar com: {key}");
            }
        }

        public async Task Remove(string key)
        {
            if (this.redisDatabase == null) return;
            try
            {
                await this.redisDatabase.KeyDeleteAsync(key);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Cache - Redis", $"Erro ao remover key {key} no Redis.");
            }
        }

        public async Task<T> GetOrCreate<T>(string key, Func<Task<T>> factory, long ttlSeconds = 3600)
        {
            if (this.redisDatabase != null)
            {
                RedisValue redisValue = RedisValue.Null;
                try
                {
                    redisValue = await this.redisDatabase.StringGetAsync(key);
                }
                catch (Exception ex)
                {
                    await ExceptionLogger.LogException(ex, "Cache - Redis", "Erro ao ler com key " + key);
                }

                if (!redisValue.IsNullOrEmpty && redisValue.HasValue)
                    return JsonConvert.DeserializeObject<T>(redisValue.ToString());
            }

            var valueToCache = await factory();
            if (valueToCache != null && (!(valueToCache is Guid id) || id !=Guid.Empty))
            {
                await Set(key, valueToCache, ttlSeconds);
            }
            return valueToCache;
        }
    }
}
