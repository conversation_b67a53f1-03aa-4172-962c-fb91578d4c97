using System;
using System.Threading.Tasks;
using Motivai.WebCatalog.Models.Security;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Services.Auth
{
	public class AuthenticationService
    {
        private readonly AuthenticatorRepository _authenticatorRepository;

        public AuthenticationService(AuthenticatorRepository AuthenticatorRepository)
        {
            this._authenticatorRepository = AuthenticatorRepository;
        }

        public async Task<bool> SendAuthorizationCode(Guid campaignId, Guid userId, SimpleSecurityCodeRequest securityCodeRequest)
        {
            return await _authenticatorRepository.SendAuthorizationCode(campaignId, userId, securityCodeRequest);
        }

        public async Task<bool> ValidateAuthorizationCode(Guid campaignId, Guid userId, SimpleSecurityCodeValidation securityCodeRequest)
        {
            return await _authenticatorRepository.ValidateAuthorizationCode(campaignId, userId, securityCodeRequest);
        }
    }
}