using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.WebCatalog.Models.Order;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Services.Cart {
    public class AddressService {
        private readonly CorreiosRepository correiosRepository;

        public AddressService(CorreiosRepository correiosRepository) {
            this.correiosRepository = correiosRepository;
        }

        public async Task CorrectShippingAddress(CartModel cart) {
            var address = cart.ShippingAddress;
            cart.ClearNotifications();
            try {
                var correiosAddress = await correiosRepository.QueryCep(address.Cep);
                if (correiosAddress == null)
                    return;
                if (correiosAddress.Street != address.Street || correiosAddress.City != address.City) {
                    LoggerFactory.GetLogger().Info("Order - Correção de Endereço - Endereço de entrega no CEP {0} corrigido de '{1}' para '{2}'.",
                        address.Cep, address.Street, correiosAddress.Street);
                }

                if (!address.FilledManually) {
                    if (!string.IsNullOrEmpty(correiosAddress.Street) && address.Street != correiosAddress.Street) {
                        cart.AddNotification($"Logradouro de entrega do CEP {address.Cep} está diferente do Correios. Informado: '{address.Street}', Correios: '{correiosAddress.Street}'");
                        address.Street = correiosAddress.Street;
                    }
                    if (!string.IsNullOrEmpty(correiosAddress.Neighborhood) && address.Neighborhood != correiosAddress.Neighborhood) {
                        cart.AddNotification($"Bairro de entrega do CEP {address.Cep} está diferente do Correios. Informado: '{address.Neighborhood}', Correios: '{correiosAddress.Neighborhood}'");
                        address.Neighborhood = correiosAddress.Neighborhood;
                    }
                    if (!string.IsNullOrEmpty(correiosAddress.City) && address.City != correiosAddress.City) {
                        cart.AddNotification($"Cidade de entrega do CEP {address.Cep} está diferente do Correios. Informado: '{address.Neighborhood}', Correios: '{correiosAddress.Neighborhood}'");
                        address.City = correiosAddress.City;
                    }
                }
                if (!string.IsNullOrEmpty(correiosAddress.State)) {
                    address.State = correiosAddress.State;
                    address.InitialState = correiosAddress.Uf;
                }
            } catch (Exception ex) {
                await ExceptionLogger.LogException(ex, "Order - Correção Endereço", "Erro ao verificar endereço de entrega.", true);
            }
        }
    }
}