using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.SharedKernel.Helpers.Storage;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Addresses;
using Motivai.WebCatalog.Models.Order;
using Motivai.WebCatalog.Models.Product;
using Motivai.WebCatalog.Repositories;
using Motivai.WebCatalog.Services.Catalog;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Newtonsoft.Json;

namespace Motivai.WebCatalog.Services.Cart {
	public class CartManager {
		private readonly IHelperWeb _helperWeb;
		private readonly ICryptography _cryptography;
		private readonly IStorageIntegrator storage;

		private readonly CampaignRepository _campaignRepository;
		private readonly CatalogRepository _catalogRepository;
		private readonly FactorySelector factorySelector;
		private readonly AddressService addressService;
		private readonly UserParticipantRepository _participantRepository;
		private readonly ProductRepository _productRepository;

		public CartManager(IHelperWeb helperWeb, ICryptography cryptography, IStorageIntegrator storage,
			FactorySelector factorySelector, AddressService addressService,
			CampaignRepository campaignRepository, CatalogRepository catalogRepository,
			UserParticipantRepository participantRepository, ProductRepository productRepository) {
			this._helperWeb = helperWeb;
			this._cryptography = cryptography;
			this.storage = storage;
			this.factorySelector = factorySelector;
			this.addressService = addressService;
			this._campaignRepository = campaignRepository;
			this._catalogRepository = catalogRepository;
			this._participantRepository = participantRepository;
			this._productRepository = productRepository;
		}

		protected async Task<CartModel> GetOrCreateCart() {
			var cartModel = await _helperWeb.GetSessionCartAsync();
			if (cartModel != null)
				return cartModel;

			var campaignId = _helperWeb.GetCampaignId();
			var participant = _helperWeb.GetParticipantSession();
			var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);
			cartModel = CartModel.For(campaignId, campaignSettings.Type, participant);
			UpdateCartSession(cartModel);
			return cartModel;
		}

		private void UpdateCartSession(CartModel cartModel) {
			_helperWeb.SetSessionCart(cartModel);
		}

		private static string ValidateCep(string cep) {
			if (string.IsNullOrEmpty(cep))
				throw MotivaiException.ofValidation("CEP inválido.");
			cep = cep.Replace("-", "");
			if (cep.Length != 8)
				throw MotivaiException.ofValidation("CEP inválido.");
			return cep;
		}

		public async Task<ShippingCostResult> CalculateItemShippingCostForAddress(Guid addressId, int quantity, string elasticId, string skuCode) {
			var shippingCep = await _participantRepository.GetAddressCepById(await _helperWeb.GetCampaignIdForCurrentDomain(), _helperWeb.GetUserId(), addressId);
			if (String.IsNullOrEmpty(shippingCep))
				throw MotivaiException.ofValidation("CEP cadastrado é inválido");
			return await CalculateItemShippingCostForCep(shippingCep, quantity, elasticId, skuCode);
		}

		public async Task<ShippingCostResult> CalculateItemShippingCostForCep(string cep, int quantity, string elasticId, string skuCode) {
			cep = ValidateCep(cep);
			var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			var participant = _helperWeb.GetParticipantSession();

			var shippingPrice = await _catalogRepository.CalculateShippingForItem(campaignId, participant.UserId,
				participant.ParticipantId, elasticId, skuCode, quantity, cep);
			if (shippingPrice == null)
				throw MotivaiException.ofValidation("Não foi possível calcular o frete para o item, por favor, tente novamente.");
			return shippingPrice;
		}

		private async Task SetShippingAddress(CartModel cart, Address address) {
			if (cart == null || address == null) return;
			cart.SetShippingAddress(AddressModel.Of(address));
			await addressService.CorrectShippingAddress(cart);
		}

		public async Task SetShippingAddressById(CartModel cart, Guid addressId) {
			var address = await _participantRepository.GetAddressById(cart.CampaignId, cart.UserId, addressId);
			if (address == null) {
				throw MotivaiException.ofValidation("Endereço selecionado não foi encontrado no seu cadastro.");
			}
			await SetShippingAddress(cart, address);
		}

		private async Task LoadDefaultShippingAddress(CartModel cart) {
			// Se não selecionou e permite utilizar o endereço padrão
			Address address = null;
			try {
				address = await _participantRepository.GetMainShippingAddress(cart.CampaignId, cart.UserId);
			} catch (Exception ex) {
				await ExceptionLogger.LogException(ex, "Cart - Add", "Erro ao carregar o endereço padrão de entrega.");
			}
			if (address != null) {
				await SetShippingAddress(cart, address);
			}
		}

		/**
			Alterar calculo do carrinho para considerar se o produto tem preço dinamico
			Caso o produto possa ter o preço inserido pelo participante
			Validar range de preço do produto
		**/
		public async Task CalculateCart(CartModel cart) {
			// Se o carrinho estiver vazio então reseta o frete
			if (cart.IsEmpty()) {
				cart.ChildrenCarts.ForEach(cp => cp.ShippingCost = Amount.Of(0, 0));
				cart.ClearPartnersCartsErrors();
				return;
			}

			var campaignSettings = await _campaignRepository.GetCampaignSettings(cart.CampaignId);

			// Utiliza o endereço padrão quando habilitado caso não tenha outra seleção
			if (campaignSettings.Parametrizations.RequireCepToFinish() && !cart.HasShippingAddress() &&
				campaignSettings.Parametrizations.UseDefaultShippingAddress) {
				await LoadDefaultShippingAddress(cart);
				if (!cart.HasShippingAddress())
					return;
			}

			cart.ClearPartnersCartsErrors();
			// Se tiver endereco de entrega entao calcula o carrinho com frete
			if (campaignSettings.Parametrizations.EnableAnyShippingCalculation() &&
				(!campaignSettings.Parametrizations.RequireCepToFinish() || cart.HasShippingAddress())) {
				try {
					await _catalogRepository.CalculateCart(cart.CampaignId, cart.UserId, cart.ParticipantId, cart);
				} catch (Exception ex) {
					if (ex is MotivaiException) {
						if ((ex as MotivaiException).ErrorType == ErrorType.Timeout)
							cart.SetError("Tempo de espera máximo do cálculo excedido, por favor, tente novamente.");
						else
							cart.SetError(ex.Message);
					} else {
						cart.SetError("Não foi possível calcular o carrinho, por favor, tente novamente.");
					}
				}
			}
		}

		public async Task<CartModel> ApplyDiscountCoupon(string discountCoupon) {
			var cart = await GetOrCreateCart();
			cart.DiscountCoupon = string.IsNullOrEmpty(discountCoupon) ? null : discountCoupon;

			await CalculateCart(cart);

			cart.OccurredError = false;
			UpdateCartSession(cart);

			return cart;
		}

		// Alterar para considerar produto com preço dinamico inserido pelo participante
		public async Task<CartActionResult> AddItemToCart(string elasticsearchId, string skuCode, string factory, int quantity, string cep, IFormCollection formCollection, string priceDefinedByParticipant) {
			var cart = await GetOrCreateCart();
			decimal? productPriceDefinedByParticipant = null;
			if (priceDefinedByParticipant != null) {
				productPriceDefinedByParticipant = DecimalHelper.ConvertStringToDecimalPtBr(priceDefinedByParticipant.ToString(), null);
			}

			try {
				var campaignSettings = await _campaignRepository.GetCampaignSettings(cart.CampaignId);

				if (HasQuantityItemAboveLimit(cart, campaignSettings)) {
					throw MotivaiException.ofValidation($"Só é permitido {campaignSettings.Parametrizations.MaximumNumberCartItems} item(s) por pedido, finalize o pedido ou remova um item do carrinho para continuar.");
				}

				var product = await _productRepository.GetProductForCart(cart.CampaignId, cart.UserId, cart.ParticipantId, elasticsearchId, skuCode, quantity, productPriceDefinedByParticipant);
				if (product == null)
				{
					throw MotivaiException.ofValidation($"Produto não encontrado pelo SKU {skuCode}.");
				}
				if (!product.Available)
				{
					throw MotivaiException.ofValidation("Produto indisponível.");
				}
				product.SetMainImage();

				// Validação do ranking caso o produto seja específico
				if (product.HasRankings()) {
					await VerifyRanking(cart.CampaignId, cart.UserId, product);
				}

				if (!campaignSettings.IsMarketplace()) {
					await ValidateBalance(cart, product, campaignSettings);
				}

				if (product.HasCustomAttributes()) {
					await ExtractProductCustomAttributesFromForm(cart.CampaignId, product, formCollection);
				}

				await SelectShippingAddress(campaignSettings, cart, product, cep);

				// Verifica se está habilitado a seleção de fabricante
				if (campaignSettings.Parametrizations.EnableSelectProductFactory) {
					await SelectFactory(cart, product, factory);
				}

				// Adiciona o item no carrinho
				cart.AddProduct(product, quantity);

				await CalculateCart(cart);
			} catch (Exception ex) {
				await ExceptionLogger.LogException(ex, "Cart - Add", $"Erro ao adicionar {elasticsearchId} no carrinho");
				cart.SetErrorIfMotivaiException(ex, "Ocorreu um erro ao calcular o carrinho, por favor, tente novamente.");
				return CartActionResult.Error;
			}
			finally
			{
				UpdateCartSession(cart);
			}
			return CartActionResult.Ok;
		}

		public async Task<CartModel> RemoveItemFromCart(string itemId) {
			var cart = await GetOrCreateCart();
			cart.RemoveItem(itemId);
			await CalculateCart(cart);
			UpdateCartSession(cart);
			return cart;
		}

		private async Task SelectShippingAddress(CampaignSettingsModel campaignSettings, CartModel cart, ProductDetailsModel product, string encryptedAddressId) {
			// Se selecionou um endereço préviamente cadastrado
			if (!string.IsNullOrEmpty(encryptedAddressId) && encryptedAddressId != "-1") {
				Guid addressId = ParserHelper.DecryptGuid(_cryptography, encryptedAddressId);
				await SetShippingAddressById(cart, addressId);
			} else if (string.IsNullOrEmpty(encryptedAddressId) && campaignSettings.Parametrizations.UseDefaultShippingAddress) {
				await LoadDefaultShippingAddress(cart);
			}

			// Verifica se foi selecionado um CEP caso seja obrigatório
			if (product.ProductType != ProductType.ValeVirtual && campaignSettings.Parametrizations.RequireCepBeforeAddToCart()) {
				if (string.IsNullOrEmpty(encryptedAddressId) && !cart.HasShippingAddress()) {
					return;
				}
			}
		}

		private static bool HasQuantityItemAboveLimit(CartModel cart, CampaignSettingsModel campaignSettings) {
			return campaignSettings.Parametrizations.HasMaximumNumberCartItems() &&
				campaignSettings.Parametrizations.MaximumNumberCartItems <= cart.GetTotalItems();
		}

		private async Task SelectFactory(CartModel cart, ProductDetailsModel product, string encryptedFactoryId) {
			Guid factoryId = Guid.Empty;

			// Utiliza o fabricante selecionado
			if (!string.IsNullOrEmpty(encryptedFactoryId)) {
				factoryId = ParserHelper.DecryptGuid(_cryptography, encryptedFactoryId);
				if (factoryId == Guid.Empty) {
					throw MotivaiException.ofValidation("Selecione um fabricante para prosseguir.");
				}
			}

			await factorySelector.SelectFactory(cart, product, factoryId);
		}

		///<summary>
		/// Carrega o saldo para validar se o participante tem saldo suficiente para o
		/// Se ocorrer o error retorna, permitindo que o participante adicione no carrinho.
		///</summary>
		private async Task ValidateBalance(CartModel cart, ProductDetailsModel product, CampaignSettingsModel campaignSettings) {
			// valida o saldo reservado apenas se tiver habilitado e estiver desabilitado a compra de pontos
			if (campaignSettings.Parametrizations.EnableMechanicRedemptionRules && campaignSettings.Parametrizations.ShouldValidateBalance()) {
				// chamar Transactions para verificar se tem saldo específico para produto ou classificação
				var reservedBalance = await this._participantRepository.GetReservedBalanceFor(cart.CampaignId, cart.UserId,
					Product.Of(Guid.Parse(product.PartnerId), Guid.Parse(product.Id), product.SkuId));
				if (reservedBalance < product.GetSalePricePoints()) {
					throw MotivaiException.ofValidation("Saldo insuficiente para o produto (parte do saldo está reservado para produtos específicos).");
				}
			}

			var participant = await _helperWeb.GetParticipantSessionAsync();
			decimal availableBalance = 0m;

			try {
				availableBalance = await _participantRepository.GetAvailableBalance(cart.CampaignId, cart.UserId);
			} catch (Exception ex) {
				await ExceptionLogger.LogException(ex, "Cart - Add", "Erro ao carregar o saldo para validação.");
				return;
			}

			if (availableBalance != participant.Balance) {
				// Atualiza o saldo na sessão
				participant.Balance = availableBalance;
				_helperWeb.SetParticipantSession(participant);
			}

			// Se permite comprar pontos, não será validado se tem saldo
			if (availableBalance < product.GetSalePricePoints() && campaignSettings.Parametrizations.ShouldValidateBalance()) {
				throw MotivaiException.ofValidation("Saldo insuficiente para o produto.");
			}
		}

		private async Task VerifyRanking(Guid campaignId, Guid userId, ProductDetailsModel product) {
			// Se tiver ranking, carrega o ranking do participante e verifica se tem o do produto
			var participant = _helperWeb.GetParticipantSession();
			if (!participant.RankingId.HasValue) {
				throw MotivaiException.ofValidation("Você não participa do ranking requerido para este produto.");
			}

			// carrega os filhos do maior ranking do participante
			var participantRankings = await _participantRepository.GetRankingChildren(campaignId, participant.RankingId.Value);
			var commonRanking = product.Rankings.Select(r => Guid.Parse(r.Id)).Intersect(participantRankings).Distinct().ToList();
			if (!commonRanking.Any()) {
				throw MotivaiException.ofValidation("Você não participa do ranking requerido para este produto.");
			}

			// verifica se tem saldo em um dos rankings permitidos
			var rankingBalance = await _participantRepository.GetAvailableBalanceByLowestRankings(campaignId, userId, commonRanking);
			if (rankingBalance < product.GetSalePricePoints())
				throw MotivaiException.ofValidation("Saldo do ranking insuficiente para o produto.");

			// Atualiza os rankigns que serão usados
			product.Rankings = product.Rankings.Where(r => commonRanking.Contains(Guid.Parse(r.Id))).ToList();
		}

		private async Task ExtractProductCustomAttributesFromForm(Guid campaignId, ProductDetailsModel product, IFormCollection formCollection) {
			await product.CustomAttributes.ForEachAsync(async attr => {
				StringValues value = StringValues.Empty;
				if (attr.Type == CustomAttributeType.File) {
					IFormFile file = formCollection.Files.FirstOrDefault(f => f.Name == "p_" + attr.Identifier);
					if (file != null && file.Length > 0) {
						attr.Value = await UploadFileToStorage(campaignId, file);
						// Console.WriteLine($"URL: {attr.Value}");
					} else if (attr.Required) {
						throw MotivaiException.ofValidation($"Preencha o campo {attr.Name} do produto antes de adicionar no carrinho.");
					}
				} else {
					if (formCollection.TryGetValue("p_" + attr.GetIdentifier(), out value)) {
						attr.Value = value;
						if (attr.Type == CustomAttributeType.Number && !string.IsNullOrEmpty(attr.Value)) {
							int intVal = 0;
							if (!int.TryParse(attr.Value, out intVal)) {
								throw MotivaiException.ofValidation($"O campo {attr.Name} contém informações inválidas, por favor, preencha com um valor numérico.");
							}
						}
					}
					if (attr.Required && string.IsNullOrEmpty(attr.Value)) {
						throw MotivaiException.ofValidation($"Preencha o campo {attr.Name} do produto antes de adicionar no carrinho.");
					}
				}
			});
		}

		private async Task<string> UploadFileToStorage(Guid campaignId, IFormFile file) {
			string cloudStorageUrl = ConfigurationHelper.GetValue("GCS_URL");
			string bucketName = ConfigurationHelper.GetValue("GCS_BUCKET_NAME");
			string folderFormat = ConfigurationHelper.GetValue("StorageFolders:FileTemp");
			var partialUrl = await storage.UploadFile(bucketName, String.Format(folderFormat, campaignId), file, null, false);
			return String.Format("{0}/{1}/{2}", cloudStorageUrl, bucketName, partialUrl);
		}
	}
}

