using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Entities.Users;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Pages;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Services.AuthenticationMfa
{
    public class AuthenticationMfaService
    {
        private readonly IHelperWeb helperWeb;
        private readonly CampaignRepository campaignRepository;
        private readonly AuthenticationMfaRepository authenticationMfaRepository;

        public AuthenticationMfaService(IHelperWeb helperWeb, CampaignRepository campaignRepository, AuthenticationMfaRepository authenticationMfaRepository)
        {
            this.helperWeb = helperWeb;
            this.campaignRepository = campaignRepository;
            this.authenticationMfaRepository = authenticationMfaRepository;
        }

        private async Task<CampaignSettingsModel> GetCampaignSettings()
        {
            return await campaignRepository.GetCampaignSettings(await helperWeb.GetCampaignIdForCurrentDomain());
        }

        public async Task<ParticipantAuthenticationMfaSettings> FindParticipantAuthenticationMfaSettings(Guid campaignId, Guid userId)
        {
            return await this.authenticationMfaRepository.FindParticipantAuthenticationMfaSettings(campaignId, userId);
        }

        private AuthenticationMfaPageModel CreateAuthenticationMfaPageModel(ParticipantAuthenticationMfaSettings settings)
        {
            var authenticationMfaPageModel = new AuthenticationMfaPageModel();
            authenticationMfaPageModel.Email = settings.Email;
            authenticationMfaPageModel.MobilePhone = settings.MobilePhone;
            authenticationMfaPageModel.AuthenticationMfaFormat = settings.AuthenticationMfaFormat.Value;

            return authenticationMfaPageModel;
        }

        public async Task<AuthenticationMfaPageModel> GetAuthenticationMfaPageValidateConfig(Guid campaignId, Guid userId)
        {
            var participantAuthenticationMfaSettings = await authenticationMfaRepository.FindParticipantAuthenticationMfaSettingsToValidate(campaignId, userId);
            if (participantAuthenticationMfaSettings == null)
            {
                throw MotivaiException.ofValidation("Configuração de dupla autenticação não encontrada");
            }

            return CreateAuthenticationMfaPageModel(participantAuthenticationMfaSettings);
        }

        public async Task<AuthenticationMfaPageModel> GetAuthenticationMfaPageSetupConfig(Guid campaignId, Guid userId)
        {
            var pageModel = new AuthenticationMfaPageModel();
            pageModel.CampaignSettings = await GetCampaignSettings();

            return pageModel;
        }

        public async Task<bool> SendAuthenticationMfaToken(Guid campaignId, Guid userId, ParticipantAuthenticationMfaSettings userAuthenticationMfa)
        {
            if (userAuthenticationMfa == null)
            {
                throw MotivaiException.ofValidation("Dados inválidos");
            }
            if (userAuthenticationMfa.AuthenticationMfaFormat == null) {
                throw MotivaiException.ofValidation("Selecione o formato de autenticação");
            }
            if (userAuthenticationMfa.AuthenticationMfaFormat == ParticipantAuthenticationMfaFormat.EMAIL_ONLY)
            {
                if (string.IsNullOrEmpty(userAuthenticationMfa.Email))
                {
                    throw MotivaiException.ofValidation("E-mail não informado");
                }
            }
            else
            {
                if (string.IsNullOrEmpty(userAuthenticationMfa.MobilePhone))
                {
                    throw MotivaiException.ofValidation("Celular não informado");
                }
            }
            return await authenticationMfaRepository.SendAuthenticationMfaToken(campaignId, userId, userAuthenticationMfa);
        }

        public async Task<bool> SendAuthenticationMfaTokenToValidate(ParticipantAuthenticationMfaSettings userAuthenticationMfa)
        {
            Guid campaignId = await helperWeb.GetCampaignIdForCurrentDomain();
            Guid userId = helperWeb.GetUserId();
            return await authenticationMfaRepository.SendAuthenticationMfaTokenToValidate(campaignId, userId, userAuthenticationMfa);
        }

        public async Task<bool> ValidateAuthenticationMfaToken(dynamic userToken, bool updateSession = false)
        {
            Guid campaignId = await helperWeb.GetCampaignIdForCurrentDomain();
            Guid userId = helperWeb.GetUserId();
            if (userToken == null || string.IsNullOrEmpty(userToken.token.ToString()))
            {
                throw MotivaiException.ofValidation("Código de segurança inválido.");
            }

            if (updateSession)
            {
                await authenticationMfaRepository.ValidateAuthenticationMfaToken(campaignId, userId, userToken);
                return UpdateSession();
            }

            return await authenticationMfaRepository.ValidateAuthenticationMfaToken(campaignId, userId, userToken);
        }

        public async Task<bool> ValidateAndSaveMfaSettings(Guid campaignId, Guid userId, dynamic participantMfaSettings, bool updateSession = false)
        {
            if (participantMfaSettings == null || string.IsNullOrEmpty(participantMfaSettings.token.ToString()))
            {
                throw MotivaiException.ofValidation("Código de segurança inválido.");
            }

            var validated = await ValidateAuthenticationMfaToken(participantMfaSettings, updateSession);
            if (validated)
            {
				var participantAuthenticationMfaSettings = new ParticipantAuthenticationMfaSettings
				{
					AuthenticationMfaFormat = participantMfaSettings.authenticationMfaFormat,
					MobilePhone = participantMfaSettings.mobilePhone,
					Email = participantMfaSettings.email
				};

				return await authenticationMfaRepository.UpdateAuthenticationMfaSettings(campaignId, userId, participantAuthenticationMfaSettings);
            }

            return false;
        }

        private bool UpdateSession()
        {
            var participantSession = helperWeb.GetParticipantSession();
            participantSession.NeedAuthenticationMfa = false;
            participantSession.NeedSetupAuthenticationMfa = false;

            helperWeb.SetParticipantSession(participantSession);
            helperWeb.CleanAuthenticationMfa();

            return true;
        }
    }
}