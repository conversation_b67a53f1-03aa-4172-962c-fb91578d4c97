using System.Threading.Tasks;
using Motivai.WebCatalog.Models.Recaptcha;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers.Exceptions;
using Newtonsoft.Json;
using Motivai.SharedKernel.Helpers;
namespace Motivai.WebCatalog.Services.Recaptcha
{
    public class RecaptchaValidatorService
    {
        public readonly string GOOGLE_RECAPTCHA_SECRET;

        public RecaptchaValidatorService()
        {
            GOOGLE_RECAPTCHA_SECRET = ConfigurationHelper.GetValue("GOOGLE_RECAPTCHA_PRIVATE_API_KEY");
        }


        public async Task<bool> ValidateCaptchaToken(string host, string token)
        {
            if (string.IsNullOrEmpty(token))
            {
                throw MotivaiException.ofValidation("Token do Recaptcha inválido");
            }

            var recaptcha = await GetRecaptchaResponse(token);
            if (!recaptcha.IsSuccess())
            {
                throw MotivaiException.ofValidation("reCaptcha inválido");
            }

            recaptcha.ValidateHostName(host);

            return true;
        }

        private async Task<RecaptchaResponse> GetRecaptchaResponse(string token)
        {
            var result = await HttpClient.Create("https://www.google.com/recaptcha/api/siteverify")
                .UseFormUrlEncoded()
                .AddFormField("response", token)
                .AddFormField("secret", GOOGLE_RECAPTCHA_SECRET)
                .AsyncPost()
                .GetResponse();

            if (result == null)
            {
                throw MotivaiException.ofValidation("Erro durante chamada de validação do reCaptcha");
            }

            return JsonConvert.DeserializeObject<RecaptchaResponse>(result);
        }
    }
}
