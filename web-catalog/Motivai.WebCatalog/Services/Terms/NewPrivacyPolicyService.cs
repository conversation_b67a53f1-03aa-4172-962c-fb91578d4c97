using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.FirstAccess;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Services.Terms
{
    public class NewPrivacyPolicyService
    {
        private readonly IHelperWeb helperWeb;
        private readonly ICryptography cryptography;
        private readonly CampaignRepository campaignRepository;
        private readonly UserParticipantRepository userParticipantRepository;


        public NewPrivacyPolicyService(IHelperWeb helperWeb, ICryptography cryptography,
            CampaignRepository campaignRepository, UserParticipantRepository userParticipantRepository)
        {
            this.helperWeb = helperWeb;
            this.cryptography = cryptography;
            this.campaignRepository = campaignRepository;
            this.userParticipantRepository = userParticipantRepository;
        }

        public async Task<PrivacyDataModel> CreateNewPrivacyPageModel()
        {
            Guid campaignId = await helperWeb.GetCampaignIdForCurrentDomain();
            var privacy = await campaignRepository.GetFirstAccessPrivacyPolicy(campaignId, helperWeb.GetUserId());
            return new PrivacyDataModel(privacy).EncryptSensitiveData(this.cryptography);
        }

        public Task<dynamic> RegistraAceitePolitica(PrivacyPolicyAcceptanceResult privacyPolicyAcceptanceResult,
            LocationInfo locationInfo)
        {
            privacyPolicyAcceptanceResult.DecryptSensitiveData(cryptography);
            var privacyPolicyChange = PrivacyPolicyChangeModel.Of(privacyPolicyAcceptanceResult, locationInfo,
                helperWeb.GetParticipantSession().GetAccountOperator());
            return this.userParticipantRepository.UpdatePrivacyPolicyAcceptance(helperWeb.GetCampaignId(),
                helperWeb.GetUserId(), privacyPolicyChange);
        }
    }
}