using System;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Entities.References.Users.FirstAccess;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Api;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.FirstAccess;
using Motivai.WebCatalog.Models.FirstAccess.CampaignAcceptanceResult.CampaignAcceptancesResult;
using Motivai.WebCatalog.Models.FirstAccess.Card;
using Motivai.WebCatalog.Models.FirstAccess.FirstAccessAcceptancesModel;
using Motivai.WebCatalog.Models.FirstAccess.Regulation;
using Motivai.WebCatalog.Models.Pages;
using Motivai.WebCatalog.Repositories;
using Newtonsoft.Json;

namespace Motivai.WebCatalog.Services.Terms
{
	public class FirstAccessService
	{
		private const string CAMPAIGN_REGULATION_ID = "CMP_REG_ID";
		private const string CAMPAIGN_REGULATION_VERSION = "CMP_REG_VRS";
		private const string CAMPAIGN_PRIVACY_ID = "CMP_PRIV_ID";
		private const string CAMPAIGN_PRIVACY_VERSION = "CMP_PRIV_VRS";
		private const string CAMPAIGN_PRIVACY_ESSENTIALCOOKIES = "CMP_PRIV_ESS";
		private const string CAMPAIGN_PRIVACY_ANALYTICCOOKIES = "CMP_PRIV_ANL";
		private const string CAMPAIGN_PRIVACY_ACCEPTED = "CMP_PRIV_ACC";

		private readonly IHelperWeb _helperWeb;
		private readonly ICryptography cryptography;
		private readonly CampaignRepository _campaignRepository;
		private readonly UserParticipantRepository _userParticipantRepository;
		private readonly ClientsIntegrationsRepository integrationsRepository;

		public FirstAccessService(IHelperWeb helperWeb, ICryptography cryptography,
			CampaignRepository campaignRepository, UserParticipantRepository userParticipantRepository,
			ClientsIntegrationsRepository _integrationsRepository)
		{
			this._helperWeb = helperWeb;
			this.cryptography = cryptography;
			this._campaignRepository = campaignRepository;
			this._userParticipantRepository = userParticipantRepository;
			this.integrationsRepository = _integrationsRepository;
		}

		// Verificar se o primeiro acesso está habilitado na campanha
		private static void ThrowErrorIfFirstAccessDisabled(CampaignSettingsModel campaignSettings)
		{
			if (campaignSettings.Parametrizations.EnableFirstAccess)
			{
				return;
			}
			throw new DisabledFirstAccessException();
		}

		public async Task<RegulationPageModel> GetRegulationPageConfig()
		{
			Guid campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);
			ThrowErrorIfFirstAccessDisabled(campaignSettings);

			var catalogSettings = await _campaignRepository.GetPagesSettings(campaignId);
			// Se o Regulamento estiver desativado então redireciona para os dados de cadastro.
			if (!catalogSettings.PagesSettings.EnableRegulation)
			{
				return RegulationPageModel.DisabledPage();
			}

			var groupRegulation = await _campaignRepository.GetRegulation(campaignId, _helperWeb.GetUserId());
			var acceptanceRequired = true;
			if (campaignSettings.Parametrizations != null)
			{
				acceptanceRequired = campaignSettings.Parametrizations.AcceptRegulationRequired;
			}
			return new RegulationPageModel()
			{
				AcceptanceRequired = acceptanceRequired,
				RegulationId = cryptography.Encrypt(groupRegulation.regulationId.ToString()),
				Version = cryptography.Encrypt(groupRegulation.version.ToString()),
				Regulation = groupRegulation.regulationContent
			};
		}

		public async Task<PrivacyDataModel> ProccessRegulationAcceptance(string regulationId, string version, bool accepted)
		{
			Guid campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);

			if (accepted)
			{
				_helperWeb.AddToSession(CAMPAIGN_REGULATION_ID, cryptography.Decrypt(regulationId));
				_helperWeb.AddToSession(CAMPAIGN_REGULATION_VERSION, cryptography.Decrypt(version));
			}
			else if (campaignSettings.Parametrizations.AcceptRegulationRequired)
			{
				// Se é obrigatório aceitar o regulamento
				throw new RegulationNotAcceptedException();
			}
			return await CreatePrivacyPageModel();
		}

		public async Task<bool> ProccessPrivacyAcceptance(PrivacyPolicyAcceptanceResult privacyPolicyAcceptanceResult, LocationInfo location, CampaignSettingsModel campaignSettings)
		{
			try
			{
				privacyPolicyAcceptanceResult.Validate();
				PersistPrivacyPolicyInSession(privacyPolicyAcceptanceResult);

				Guid campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
				if (campaignSettings.Parametrizations.SkipFirstAccessRegistrationData == true)
				{
					var userId = _helperWeb.GetUserId();
					var hasCard = campaignSettings.Parametrizations.EnableCardRegisterAtFirstAccess == true;
					var campaignRegulationId = _helperWeb.GetFromSession<string>(CAMPAIGN_REGULATION_ID);
					var regulationVersion = _helperWeb.GetFromSession<string>(CAMPAIGN_REGULATION_VERSION);

					var regulation = new RegulationAcceptanceResult(campaignRegulationId, regulationVersion);
					var campaignAcceptancesResult = new CampaignAcceptancesResult(privacyPolicyAcceptanceResult, regulation);
					var firstAccessAcceptancesModel = new FirstAccessAcceptancesModel(hasCard, campaignAcceptancesResult, location);
					await _userParticipantRepository.RegisterPrivacyPolicyAcceptance(firstAccessAcceptancesModel, userId, campaignId);
					this.RemoveAndCleanSession();
					return true;
				}
				return false;
			}
			catch (Exception)
			{
				throw;
			}
		}

		public async Task<FirstAccessPageModel> GetFirstAccessPageModel()
		{
			Guid campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);
			return await CreateFirstAccessPageModel(campaignId, campaignSettings);
		}

		private void PersistPrivacyPolicyInSession(PrivacyPolicyAcceptanceResult privacyPolicyAcceptanceResult)
		{
			privacyPolicyAcceptanceResult.DecryptSensitiveData(this.cryptography);
			_helperWeb.AddToSession(CAMPAIGN_PRIVACY_ID, privacyPolicyAcceptanceResult.ContentId);
			_helperWeb.AddToSession(CAMPAIGN_PRIVACY_VERSION, privacyPolicyAcceptanceResult.Version);
			_helperWeb.AddToSession(CAMPAIGN_PRIVACY_ESSENTIALCOOKIES, privacyPolicyAcceptanceResult.EssentialsCookies);
			_helperWeb.AddToSession(CAMPAIGN_PRIVACY_ANALYTICCOOKIES, privacyPolicyAcceptanceResult.AnalyticsCookies);
			_helperWeb.AddToSession(CAMPAIGN_PRIVACY_ACCEPTED, privacyPolicyAcceptanceResult.Accepted);
		}

		public async Task<PrivacyDataModel> CreatePrivacyPageModel()
		{
			Guid campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			var privacy = await _campaignRepository.GetFirstAccessPrivacyPolicy(campaignId, _helperWeb.GetUserId());
			return new PrivacyDataModel(privacy).EncryptSensitiveData(this.cryptography);
		}

		public async Task<FirstAccessPageModel> GetRegistrationPageConfig()
		{
			Guid campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			var settings = await _campaignRepository.GetCampaignSettings(campaignId);
			ThrowErrorIfFirstAccessDisabled(settings);

			bool hasAccepted = _helperWeb.HasItemInSession(CAMPAIGN_REGULATION_ID);
			// Verifica se o regulamento é obrigatório e se não está aceito
			if (settings.Parametrizations.AcceptRegulationRequired && !hasAccepted)
			{
				var catalogSettings = await _campaignRepository.GetPagesSettings(campaignId);
				// Se não estiver aceito e a página está habilitada então lança erro
				if (catalogSettings.PagesSettings.EnableRegulation)
				{
					throw new RegulationNotAcceptedException();
				}
			}
			return await CreateFirstAccessPageModel(campaignId, settings);
		}

		private async Task<FirstAccessPageModel> CreateFirstAccessPageModel(Guid campaignId, CampaignSettingsModel campaignSettings)
		{
			return new FirstAccessPageModel(
				campaignSettings,
				await _campaignRepository.GetPagesSettings(campaignId),
				await _campaignRepository.GetFirstAccessSettings(campaignId)
			);
		}

		private async Task RegisterAcceptance(Guid campaignId, string regulationId, string version)
		{
			try
			{
				await _userParticipantRepository.RegisterRegulationAcceptance(campaignId, _helperWeb.GetUserId(), regulationId, version);
			}
			catch (Exception ex)
			{
				await ExceptionLoggerMiddleware.HandleException(ex, "Erro durante registro do aceito do regulamento", true);
			}
		}

		public async Task<FirstAccessDataModel> GetParticipantDataToComplete()
		{
			Guid campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			Guid userId = _helperWeb.GetUserId();
			try
			{
				var participantData = await _userParticipantRepository.GetPersonalData(campaignId, userId);
				if (participantData == null)
				{
					participantData = new ParticipantDataModel()
					{
						UserId = userId,
						CampaignId = campaignId
					};
				}
				var homeAddress = await _userParticipantRepository.GetMainShippingAddress(campaignId, userId);

				return FirstAccessDataModel.FromParticipantDataAndAddress(participantData, homeAddress);
			}
			catch (Exception ex)
			{
				await ExceptionLoggerMiddleware.HandleException(ex);
				if (ex is MotivaiException) throw;
				throw MotivaiException.ofException("Não foi possível carregar os dados pré-cadastrados.", ex);
			}
		}

		public async Task<dynamic> GetTokenizationInfo()
		{
			try
			{
				var campaignId = _helperWeb.GetCampaignId();
				var integrationSettings = await this._campaignRepository.GetCampaignIntegrationSettings(campaignId);
				var authToken = await this.integrationsRepository.FetchCampaignIntegrationToken(campaignId);

				if (authToken == null)
					throw MotivaiException.ofValidation("Não foi possivel consultar o token.");

				return new
				{
					endpoint = "",
					token = authToken,
					clientId = ""
				};
			}
			catch (Exception ex)
			{
				await ExceptionLoggerMiddleware.HandleException(ex);
				if (ex is MotivaiException) throw;
				throw MotivaiException.ofException("Não foi possível buscar os dados para a tokenização do cartão.", ex);
			}
		}

		public async Task<bool> SaveUserCard(UserCard card)
		{
			Guid campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			Guid userId = _helperWeb.GetUserId();
			try
			{
				var integrationSettings = await this._campaignRepository.GetCampaignIntegrationSettings(campaignId);
				var result = await _userParticipantRepository.SaveUserCard(campaignId, userId, card);
				if (result)
				{
					this.RemoveAndCleanSession();
				}
				return result;
			}
			catch (Exception ex)
			{
				await ExceptionLoggerMiddleware.HandleException(ex);
				if (ex is MotivaiException) throw;
				throw MotivaiException.ofException("Não foi possível cadastrar os dados do cartão.", ex);
			}
			finally
			{
				await UpdateBalance(campaignId, userId);
			}
		}

		private async Task UpdateBalance(Guid campaignId, Guid userId)
		{
			try
			{
				var balance = await _userParticipantRepository.GetAvailableBalance(campaignId, userId);
				var loggedUser = _helperWeb.GetParticipantSession();
				loggedUser.Balance = balance;
				_helperWeb.SetParticipantSession(loggedUser);
			}
			catch (Exception ex)
			{
				LoggerFactory.GetLogger().Error("Ocorreu erro durante a atualização do saldo: " + ex.Message);
			}
		}

		public async Task<bool> CompleteRegistration(FirstAccessDataModel dataModel)
		{
			if (!string.IsNullOrEmpty(dataModel.BirthDate))
			{
				if (!DateTime.TryParse(dataModel.BirthDate, out _))
				{
					throw MotivaiException.ofValidation("Data de Nascimento não é válida.");
				}
			}

			try
			{
				var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
				var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);
				var catalogSettings = await _campaignRepository.GetPagesSettings(campaignId);

				dataModel.Validate(catalogSettings.PagesSettings.ParticipantData);

				// Mantém a flag firstAccess até o próximo step
				dataModel.KeepFirstAccess = campaignSettings.Parametrizations.EnableCardRegisterAtFirstAccess == true;

				var userId = _helperWeb.GetUserId();
				if (dataModel.HomeAddress != null)
				{
					dataModel.HomeAddress.UserId = userId;
					dataModel.HomeAddress.CampaignId = campaignId;
				}
				if (dataModel.CommercialAddress != null)
				{
					dataModel.CommercialAddress.UserId = userId;
					dataModel.CommercialAddress.CampaignId = campaignId;
				}

				dataModel.CampaignAcceptancesResult = new CampaignAcceptancesResult(new PrivacyPolicyAcceptanceResult().Of(
				   _helperWeb.GetFromSession<string>(CAMPAIGN_PRIVACY_ID),
				   _helperWeb.GetFromSession<string>(CAMPAIGN_PRIVACY_VERSION),
				   _helperWeb.GetFromSession<bool>(CAMPAIGN_PRIVACY_ESSENTIALCOOKIES),
				   _helperWeb.GetFromSession<bool>(CAMPAIGN_PRIVACY_ANALYTICCOOKIES),
				   _helperWeb.GetFromSession<bool>(CAMPAIGN_PRIVACY_ACCEPTED)
			   ), new RegulationAcceptanceResult(_helperWeb.GetFromSession<string>(CAMPAIGN_REGULATION_ID), _helperWeb.GetFromSession<string>(CAMPAIGN_REGULATION_VERSION)));

				dataModel.CampaignAcceptancesResult.PrivacyPolicy.Validate();

				var firstAccessResult = await _userParticipantRepository.RegisterFirstAccess(campaignId, userId, dataModel);

				// Flag a sessão para não redirecionar para o Primeiro Acesso
				if (firstAccessResult.Completed)
				{
					UpdateSession(dataModel, firstAccessResult);
				}

				return firstAccessResult.Completed;
			}
			catch (Exception ex)
			{
				await ExceptionLogger.LogException(ex, "FirstAccess - Completar Cadastro", "Erro durante primeiro acesso.");
				if (ex is MotivaiException) throw;
				throw MotivaiException.ofException("Não foi possível completar o cadastro, por favor, tente novamente.", ex);
			}
		}

		private void UpdateSession(FirstAccessDataModel dataModel, FirstAccessResult firstAccessResult)
		{
			// Atualiza a sessão do usuário
			var participantSession = _helperWeb.GetParticipantSession();
			participantSession.Name = dataModel.Name;

			if (firstAccessResult.Migrated)
			{
				LoggerFactory.GetLogger().Warn("Cmp {} - Usr {} - Migrado para usuário {}",
						participantSession.CampaignId, participantSession.UserId, firstAccessResult.MigratedUserId);
				participantSession.Document = firstAccessResult.UserDocument;
				participantSession.UserId = firstAccessResult.MigratedUserId.Value;
				participantSession.ParticipantId = firstAccessResult.MigratedParticipantId.Value;
			}
			else if (string.IsNullOrEmpty(participantSession.Document))
			{
				participantSession.Document = dataModel.GetDocument();
			}

			if (dataModel.Contact != null)
			{
				participantSession.Email = dataModel.Contact.MainEmail;
			}
			_helperWeb.SetParticipantSession(participantSession);
			if (!dataModel.KeepFirstAccess)
			{
				this.RemoveAndCleanSession();
			}
		}

		private void RemoveAndCleanSession()
		{
			if (_helperWeb.HasItemInSession(CAMPAIGN_REGULATION_ID))
			{
				_helperWeb.RemoveFromSession(CAMPAIGN_REGULATION_ID);
				_helperWeb.RemoveFromSession(CAMPAIGN_REGULATION_VERSION);
			}
			_helperWeb.CleanNewPrivacyPolicy();
			_helperWeb.CleanFirstAccess();
		}
	}
}
