using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.SharedKernel.Helpers.Api;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Models.ExtraServices.Cards.Partners;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Services.ExtraServices.Partners
{
    public class PartnerPrepaidCardService
    {

        private readonly ICryptography cryptography;
        private readonly PartnerPrepaidCardRepository cardsRepository;

        public PartnerPrepaidCardService(ICryptography cryptography, PartnerPrepaidCardRepository cardsRepository)
        {
            this.cryptography = cryptography;
            this.cardsRepository = cardsRepository;
        }

        public async Task<PartnerPrepaidCard> FindPrepaidCard(Guid userId, Guid campaignId, string birthDate, string firstDigits, string lastDigits, string expirationDate)
        {
            if (userId == null)
			{
				throw MotivaiException.ofValidation("Usuário inválido, por favor realize o login novamente.");
			}

            return await cardsRepository.FindPrepaidCard(userId, campaignId, birthDate, firstDigits, lastDigits, expirationDate);
        }

		public async Task<List<MaskedPrepaidCard>> FindPrepaidCardsInUse(Guid userId, Guid campaignId)
        {
            if (userId == null)
			{
				throw MotivaiException.ofValidation("Usuário inválido, por favor realize o login novamente.");
			}

            return await cardsRepository.FindPrepaidCardsInUse(userId, campaignId);
        }

		public async Task<PartnerPrepaidCard> FindPrepaidCardById(Guid userId, Guid campaignId, string encryptedCardId)
        {

            if (userId == null)
			{
				throw MotivaiException.ofValidation("Usuário inválido, por favor realize o login novamente.");
			}

            return await cardsRepository.FindPrepaidCardById(userId, campaignId, encryptedCardId);
        }

        public async Task<dynamic> ActiveCard(Guid userId, Guid campaignId, string encryptedCardId)
		{
            if (userId == null)
			{
				throw MotivaiException.ofValidation("Usuário inválido, por favor realize o login novamente.");
			}

			return await cardsRepository.ActiveCard(userId, campaignId, encryptedCardId);
		}

		public async Task<dynamic> BlockCard(Guid userId, Guid campaignId, string encryptedCardId)
		{
            if (userId == null)
			{
				throw MotivaiException.ofValidation("Usuário inválido, por favor realize o login novamente.");
			}

			return await cardsRepository.BlockCard(userId, campaignId, encryptedCardId);
		}

		public async Task<dynamic> UseCard(Guid userId, Guid campaignId, string encryptedCardId)
		{
            if (userId == null)
			{
				throw MotivaiException.ofValidation("Usuário inválido, por favor realize o login novamente.");
			}

			return await cardsRepository.UseCard(userId, campaignId, encryptedCardId);
		}

        public async Task<dynamic> ResetPin(Guid userId, Guid campaignId, string encryptedCardId, PinRequest request)
		{
            if (userId == null)
			{
				throw MotivaiException.ofValidation("Usuário inválido, por favor realize o login novamente.");
			}

			return await cardsRepository.ResetPin(userId, campaignId, encryptedCardId, request);
		}

        public async Task<dynamic> GetPrepaidCardStatement(Guid userId, Guid campaignId, string encryptedCardId, string startPeriod, string endPeriod, int limit)
		{
            if (userId == null)
			{
				throw MotivaiException.ofValidation("Usuário inválido, por favor realize o login novamente.");
			}

			return await cardsRepository.GetPrepaidCardStatement(userId, campaignId, encryptedCardId, startPeriod, endPeriod, limit);
		}

		public async Task<dynamic> GetBalance(Guid userId, Guid campaignId, string encryptedCardId)
		{
			if (userId == null)
			{
				throw MotivaiException.ofValidation("Usuário inválido, por favor realize o login novamente.");
			}

			return await cardsRepository.GetBalance(userId, campaignId, encryptedCardId);
		}

		public async Task<dynamic> RetrieveCardTracking(string encryptedCardId)
		{
			if (encryptedCardId == null)
			{
				throw MotivaiException.ofValidation("Por favor selecione o cartão.");
			}

			return await cardsRepository.RetrieveCardTracking(encryptedCardId);
		}
    }
}