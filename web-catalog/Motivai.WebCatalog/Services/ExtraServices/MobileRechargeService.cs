using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Models.ExtraServices;
using Motivai.WebCatalog.Models.Session;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Services.ExtraServices {
    public class MobileRechargeService {
        private readonly ICryptography cryptography;
        private readonly CampaignRepository campaignRepository;
        private readonly UserParticipantRepository participantRepository;
        private readonly CatalogRepository catalogRepository;

        public MobileRechargeService(ICryptography cryptography, CampaignRepository campaignRepository,
            UserParticipantRepository participantRepository, CatalogRepository catalogRepository) {
            this.cryptography = cryptography;
            this.campaignRepository = campaignRepository;
            this.participantRepository = participantRepository;
            this.catalogRepository = catalogRepository;
        }

        public async Task<List<MobileOperator>> GetOperatorsByDdd(Guid campaignId, string ddd) {
            return await catalogRepository.GetOperatorsByDdd(campaignId, ddd);
        }

        public async Task<List<dynamic>> GetOptionsForOperator(Guid campaignId, string ddd, string providerId) {
            var options = await catalogRepository.GetOperatorOptions(campaignId, ddd, providerId);
            if (options == null) return null;
            return options.Select(opt => (dynamic) new {
                Description = opt.Description,
                Cost = opt.ParticipantCost.GetPointsOrZero(),
                Token = opt.CreateToken(cryptography)
            }).ToList();
        }

        public async Task<OperationTicket> IssueRechargeTicket(Guid campaignId, UserPrincipal participant, MobileRecharge recharge) {
            if (recharge == null)
                throw MotivaiException.ofValidation("Preencha todos os campos para continuar.");
            recharge.Validate();

            recharge.UserId = participant.UserId;
            recharge.ParticipantId = participant.ParticipantId;

            recharge.Option = MobileOperatorOption.FromToken(cryptography, recharge.RechargeValue);

            var rechargeTicket = await catalogRepository.IssueRechargeMobileTicket(campaignId, recharge);
            rechargeTicket.OperatorName = recharge.OperatorName;
            rechargeTicket.RechargeValue = recharge.Option.RechargeCost;
            rechargeTicket.RechargeCost = recharge.Option.ParticipantCost.GetPointsOrZero();

            rechargeTicket.ConfirmationToken = ConfirmationTicket.CreateConfirmationToken(cryptography, rechargeTicket);

            return rechargeTicket;
        }

        private async Task<ConfirmationResult> SendRechargeTicketConfirmation(Guid campaignId, UserPrincipal participant, ConfirmationTicket ticket, bool confirmRecharge) {
            if (ticket == null)
                throw MotivaiException.ofValidation("Preencha todos os campos para continuar.");
            ticket.ReadToken(cryptography);
            var rechargeOption = MobileOperatorOption.FromToken(cryptography, ticket.Token);
            ticket.RechargeValue = rechargeOption.RechargeCost;
            ticket.ParticipantCost = rechargeOption.ParticipantCost;
            ticket.Validate();

            ticket.UserId = participant.UserId;
            ticket.ParticipantId = participant.ParticipantId;
            return await catalogRepository.ConfirmRechargeMobile(campaignId, ticket, confirmRecharge);
        }

        public async Task<ConfirmationResult> ConfirmRechargeTicket(Guid campaignId, UserPrincipal participant, ConfirmationTicket ticket) {
            return await SendRechargeTicketConfirmation(campaignId, participant, ticket, true);
        }

        public async Task<ConfirmationResult> CancelRechargeTicket(Guid campaignId, UserPrincipal participant, ConfirmationTicket ticket) {
            return await SendRechargeTicketConfirmation(campaignId, participant, ticket, false);
        }
    }
}