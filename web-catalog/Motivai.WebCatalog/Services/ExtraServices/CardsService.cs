using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.SharedKernel.Helpers.Api;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.ExtraServices;
using Motivai.WebCatalog.Models.ExtraServices.Cards;
using Motivai.WebCatalog.Models.Pages.Cards;
using Motivai.WebCatalog.Models.Session;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Services.ExtraServices {
    public class CardsService {
        private readonly ICryptography cryptography;
        private readonly IHelperWeb helperWeb;
        private readonly CatalogRepository catalogRepository;
        private readonly UserParticipantRepository participantRepository;

        public CardsService(ICryptography cryptography, IHelperWeb helperWeb,
                CatalogRepository catalogRepository, UserParticipantRepository participantRepository) {
            this.cryptography = cryptography;
            this.helperWeb = helperWeb;
            this.catalogRepository = catalogRepository;
            this.participantRepository = participantRepository;
        }

        public async Task<dynamic> GetPageConfiguration(Guid campaignId, PrepaidCardType cardType) {
            var settings = await catalogRepository.GetCardsPageConfiguration(campaignId, cardType);
            if (settings == null)
                throw MotivaiException.ofValidation("Não foi possível carregar as configurações, por favor, tente novamente.");
            return new {
                cardDetailedImage = settings.cardDetailedImage,
                transactionLimit = settings.transactionLimit,
                regulation = settings.regulation,
                provider = settings.provider
            };
        }

        public async Task<List<PrepaidCard>> GetParticipantCardsByType(Guid campaignId, Guid userId, PrepaidCardType cardType) {
            var cards = await participantRepository.GetParticipantCardsByType(campaignId, userId, cardType);
            if (cards == null || cards.Count == 0) return cards;
            cards.ForEach(c => c.Id = cryptography.Encrypt(c.Id.ToString()));
            return cards;
        }

        public async Task<dynamic> CalculateCardOrderFees(Guid campaignId, Guid userId, PrepaidCardType cardType, dynamic transaction) {
            if (transaction == null)
                throw MotivaiException.ofValidation("Preencha os campos corretamente para efetuar o cálculo.");
            transaction.campaignId = campaignId;
            transaction.userId = userId;
            transaction.cardType = cardType;
            return await catalogRepository.CalculateCardOrderFees(campaignId, transaction);
        }

        public async Task<dynamic> CreatePrepaidCardOrder(Guid campaignId, UserPrincipal participant, PrepaidCardType cardType, PrepaidCardOrder order) {
            if (order == null)
                throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir com a transferência.");
            order.CardType = cardType;
            order.CampaignId = campaignId;
            order.UserId = participant.UserId;
            order.ParticipantId = participant.ParticipantId;

            if (string.IsNullOrEmpty(order.PrepaidCardId)) {
                order.Card = null;
            } else {
                order.PrepaidCardId = cryptography.Decrypt(order.PrepaidCardId);
                if (order.Card != null && !string.IsNullOrEmpty(order.Card.Id)) {
                    order.Card.Id = cryptography.Decrypt(order.Card.Id);
                }
            }
            var createdOrder = await catalogRepository.CreateCardOrder(campaignId, order);
            if (createdOrder.orderNumber == null || string.IsNullOrEmpty(createdOrder.orderNumber.ToString())) {
                throw MotivaiException.ofValidation("Não foi possível criar o pedido, por favor, tente novamente.");
            }
            await UpdateParticipantBalance(campaignId, participant.UserId);
            return createdOrder.orderNumber;
        }

        private async Task UpdateParticipantBalance(Guid campaignId, Guid userId) {
            // Atualiza o saldo disponível na sessão
            try {
                decimal balance = await participantRepository.GetAvailableBalance(campaignId, userId);
                // Atualiza o saldo na sessão
                var loggedUser = helperWeb.GetParticipantSession();
                loggedUser.Balance = balance;
                helperWeb.SetParticipantSession(loggedUser);
            } catch (Exception ex) {
                await ExceptionLoggerMiddleware.HandleException(ex, "Erro ao consultar e atualizar saldo disponível após o pedido.");
            }
        }

        public async Task<PrepaidCardParametrizations> GetCardParametrizations(Guid campaignId) {
            try {
                return await this.catalogRepository.GetCardParametrizations(campaignId);
            }
            catch (Exception ex) {
                throw MotivaiException.ofValidation("Não foi possível carregar as parametrizações do cartão. {0} ", ex.ToString());
            }
        }
    }
}