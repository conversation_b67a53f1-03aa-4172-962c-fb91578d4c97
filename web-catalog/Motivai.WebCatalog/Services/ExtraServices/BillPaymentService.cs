using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Models.ExtraServices;
using Motivai.WebCatalog.Models.Session;
using Motivai.WebCatalog.Repositories;
using Newtonsoft.Json;

namespace Motivai.WebCatalog.Services.ExtraServices
{
	public class BillPaymentService
	{
		private readonly ICryptography cryptography;
		private readonly CatalogRepository catalogRepository;

		public BillPaymentService(ICryptography cryptography, CatalogRepository catalogRepository)
		{
			this.cryptography = cryptography;
			this.catalogRepository = catalogRepository;
		}

		public async Task<BillDetails> GetBillDetails(Guid campaignId, UserPrincipal participant, string barCode)
		{
			var billDetails = await catalogRepository.GetBillDetails(campaignId, participant, barCode);
			if (billDetails != null)
				billDetails.CreateToken(cryptography);
			return billDetails;
		}

		public async Task<OperationTicket> IssueBillPaymentTicket(Guid campaignId, UserPrincipal participant, BillDetails bill)
		{
			if (bill == null)
				throw MotivaiException.ofValidation("Conta para pagamento inválida, por favor, preencha o código de barras corretamente.");
			bill.DecodeToken(cryptography);
			bill.Validate();

			bill.SetParticipantInfo(participant);

			var ticket = await catalogRepository.IssueBillPaymentTicket(campaignId, bill);
			ticket.ConfirmationToken = ConfirmationTicket.CreateConfirmationToken(cryptography, ticket);
			return ticket;
		}

		private async Task<ConfirmationResult> SendBillPaymentTicketConfirmation(Guid campaignId, UserPrincipal participant,
			ConfirmationTicket ticket, bool confirmPayment)
		{
			if (ticket == null)
				throw MotivaiException.ofValidation("Preencha todos os campos para continuar.");

			ticket.ReadToken(cryptography);
			ticket.ParticipantCost = BillDetails.ExtractCostFromToken(cryptography, ticket.Token);
			ticket.Validate();

			ticket.UserId = participant.UserId;
			ticket.ParticipantId = participant.ParticipantId;

			return await catalogRepository.ConfirmBillPayment(campaignId, ticket, confirmPayment);
		}

		public async Task<ConfirmationResult> ConfirmBillPaymentTicket(Guid campaignId, UserPrincipal participant, ConfirmationTicket ticket)
		{
			return await SendBillPaymentTicketConfirmation(campaignId, participant, ticket, true);
		}

		public async Task<ConfirmationResult> CancelBillPaymentTicket(Guid campaignId, UserPrincipal participant, ConfirmationTicket ticket)
		{
			return await SendBillPaymentTicketConfirmation(campaignId, participant, ticket, false);
		}

		public async Task<bool> SchedulePayment(Guid campaignId, UserPrincipal participant, BillDetails billDetails)
		{
			if (billDetails == null)
				throw MotivaiException.ofValidation("Conta para pagamento inválida, por favor, preencha o código de barras corretamente.");
			billDetails.DecodeToken(cryptography);
			billDetails.Validate();

			billDetails.SetParticipantInfo(participant);

			var result = await catalogRepository.ScheduleBillPayment(campaignId, billDetails);
			return result.SchedulingId != Guid.Empty;
		}
	}
}