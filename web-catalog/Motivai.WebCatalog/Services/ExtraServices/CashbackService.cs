using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Api;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Session;
using Motivai.WebCatalog.Repositories;
using Newtonsoft.Json;

namespace Motivai.WebCatalog.Services.ExtraServices
{
	public class CashbackService
	{
		private readonly IHelperWeb helperWeb;
		private readonly CatalogRepository catalogRepository;
		private readonly UserParticipantRepository participantRepository;

		public CashbackService(IHelperWeb helperWeb, CatalogRepository catalogRepository, UserParticipantRepository participantRepository)
		{
			this.helperWeb = helperWeb;
			this.catalogRepository = catalogRepository;
			this.participantRepository = participantRepository;
		}

		public async Task<object> GetPageConfiguration(Guid campaignId)
		{
			var settings = await catalogRepository.GetBankTransferConfiguration(campaignId);
			if (settings.enableBankTransfer == false)
				throw MotivaiException.ofValidation("Transferência bancária não está ativa na campanha.");
			if (settings == null)
				throw MotivaiException.ofValidation("Não foi possível carregar as configurações, por favor, tente novamente.");
			return new
			{
				settings.transferMethodAllowed,
				transactionLimit = settings.transactionLimit,
				regulation = settings.regulation
			};
		}

		public async Task<dynamic> CalculateBankTransferOrderFees(Guid campaignId, Guid userId, dynamic order)
		{
			if (order == null)
				throw MotivaiException.ofValidation("Preencha os campos corretamente para efetuar o cálculo.");
			order.campaignId = campaignId;
			order.userId = userId;
			return await catalogRepository.CalculateBankTransferOrderFees(campaignId, order);
		}

		public async Task<dynamic> CreateBankTransferOrder(Guid campaignId, UserPrincipal participant, dynamic order)
		{
			if (order == null)
				throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir com a transferência.");
			order.campaignId = campaignId;
			order.userId = participant.UserId;
			order.participantId = participant.ParticipantId;
			var orderNumber = await catalogRepository.CreateBankTransferOrder(campaignId, order);
			if (string.IsNullOrEmpty(orderNumber))
			{
				throw MotivaiException.ofValidation("Não foi possível criar o pedido, por favor, tente novamente.");
			}
			await UpdateParticipantBalance(campaignId, participant.UserId);
			return orderNumber;
		}

		private async Task UpdateParticipantBalance(Guid campaignId, Guid userId)
		{
			// Atualiza o saldo disponível na sessão
			try
			{
				decimal balance = await participantRepository.GetAvailableBalance(campaignId, userId);
				// Atualiza o saldo na sessão
				var loggedUser = helperWeb.GetParticipantSession();
				loggedUser.Balance = balance;
				helperWeb.SetParticipantSession(loggedUser);
			}
			catch (Exception ex)
			{
				await ExceptionLoggerMiddleware.HandleException(ex, "Erro ao consultar e atualizar saldo disponível após o pedido.");
			}
		}
	}
}
