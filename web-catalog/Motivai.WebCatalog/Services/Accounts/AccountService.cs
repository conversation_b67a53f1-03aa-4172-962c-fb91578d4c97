using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Models.MyAccount;
using Motivai.WebCatalog.Models.Session;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Services.Accounts {
    public class AccountService {
        private readonly UserParticipantRepository userParticipantRepository;

        public AccountService(UserParticipantRepository userParticipantRepository) {
            this.userParticipantRepository = userParticipantRepository;
        }

        public async Task<bool> UpdatePassword(Guid campaignId, UserPrincipal userPrincipal, PasswordUpdate passwordUpdate) {
            if (passwordUpdate == null) {
                throw MotivaiException.ofValidation("Alteração de senha inválida");
            }
            passwordUpdate.Validate();
            if (userPrincipal.IsAccountOperator()) {
                return await userParticipantRepository.UpdateAccountOperatorPassword(userPrincipal.AccountOperatorId.Value,
                    userPrincipal.AccountOperatorLoginId.Value, passwordUpdate);
            }

            return await userParticipantRepository.UpdatePassword(userPrincipal.UserId, campaignId, passwordUpdate);
        }
    }
}