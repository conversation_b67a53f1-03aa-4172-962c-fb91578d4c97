﻿using System;
using System.IO;
using System.Linq;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Logging;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Api;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Storage;
using Motivai.SharedKernel.Helpers.Storage.Impls;
using Motivai.WebCatalog.Configuration.Models;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Middlewares;
using Motivai.WebCatalog.Repositories;
using Motivai.WebCatalog.Services.Cache;
using Motivai.WebCatalog.Services.Cart;
using Motivai.WebCatalog.Services.Catalog;
using Motivai.WebCatalog.Services.ExtraServices;
using Motivai.WebCatalog.Services.Order;
using Motivai.WebCatalog.Services.Security;
using Motivai.WebCatalog.Services.Terms;
using Newtonsoft.Json;
using NLog.Extensions.Logging;
using NLog.Web;
using Motivai.WebCatalog.Services.Accounts;
using Motivai.WebCatalog.Middlewares.Security;
using Motivai.WebCatalog.Domain;
using Motivai.WebCatalog.Domain.Integrator;
using Motivai.WebCatalog.Domain.Integrator.PlatformImpl;
using Motivai.WebCatalog.Domain.Integrator.ClientsIntegratorImpl;
using Motivai.WebCatalog.Services.Recaptcha;
using Motivai.WebCatalog.Services.ExtraServices.Partners;
using Motivai.WebCatalog.Services.Auth;
using Motivai.WebCatalog.Services.AuthenticationMfa;
using Motivai.WebCatalog.Services.CampaignSettingsService;

namespace Motivai.WebCatalog
{
    public class Startup : ApiBaseStartup
    {
        public Startup(IHostingEnvironment env) : base(env)
        {
            env.ConfigureNLog("nlog.config");
        }

        public override void ConfigureServicesBeforeMvc(IServiceCollection services)
        {
            services.AddCors();

            // Configuração das Métricas (podemos usar o appsettings para configurar tudo isso)
            // services.AddMonitoring();
        }

        protected override void ConfigureMvc(IServiceCollection services)
        {
            services.AddSession(options =>
            {
                options.IdleTimeout = TimeSpan.FromMinutes(20);
                // options.CookieHttpOnly = true;
                // options.CookieName = "ssn-token";
                options.Cookie.HttpOnly = true;
                options.Cookie.Name = "ssn-token";
            });

            services.AddRin();

            services.AddMvc()
                .AddRinMvcSupport()
                .AddJsonOptions(opts =>
                {
                    // opts.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
                    opts.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Serialize;
                    opts.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                    opts.SerializerSettings.MissingMemberHandling = MissingMemberHandling.Ignore;
                })
                .SetCompatibilityVersion(CompatibilityVersion.Version_2_1);
        }

        public override void ConfigureIoC(IServiceCollection services)
        {
            services.Configure<CacheSettings>(options => Configuration.GetSection("CacheSettings").Bind(options));

            // Helpers
            services.AddSingleton<IHelperWeb, HelperWeb>();
            services.AddSingleton<ICryptography, Cryptography>();

            services.AddSingleton<CustomCache>();

            // Storage
            services.AddSingleton<IStorageIntegrator, GoogleStorageIntegrator>();

            // IRepository
            services.AddSingleton<LoginRepository>();
            services.AddSingleton<CampaignRepository>();
            services.AddSingleton<CatalogRepository>();
            services.AddSingleton<CorreiosRepository>();
            services.AddSingleton<ProductRepository>();
            services.AddSingleton<UserParticipantRepository>();
            services.AddSingleton<IntegrationsRepository>();
            services.AddSingleton<ClientsIntegrationsRepository>();

            // Services
            services.AddSingleton<AddressService>();
            services.AddSingleton<CampaignSettingsService>();
            services.AddSingleton<CartManager>();
            services.AddSingleton<ShoppingCartValidator>();
            services.AddSingleton<FactorySelector>();
            services.AddSingleton<BillPaymentService>();
            services.AddSingleton<MobileRechargeService>();
            services.AddSingleton<FirstAccessService>();
            services.AddSingleton<CardsService>();
            services.AddSingleton<CashbackService>();
            services.AddSingleton<OrderManagerService>();
            services.AddSingleton<AccountService>();
            services.AddSingleton<TokenService>();
            services.AddSingleton<MenuCreator>();
            services.AddSingleton<LayoutContentService>();
            services.AddSingleton<WebCatalogGateway>();
            services.AddSingleton<ApiResolver>();
            services.AddSingleton<PlatformIntegrator>();
            services.AddSingleton<ClientsIntegrator>();
            services.AddSingleton<RecaptchaValidatorService>();
            services.AddSingleton<PartnerPrepaidCardService>();
            services.AddSingleton<PartnerPrepaidCardRepository>();
            services.AddSingleton<AuthenticationService>();
            services.AddSingleton<AuthenticatorRepository>();
            services.AddSingleton<AuthenticationMfaService>();
            services.AddSingleton<AuthenticationMfaRepository>();
            services.AddSingleton<CampaignSecurityService>();
            services.AddSingleton<NewPrivacyPolicyService>();
        }

        public override void ConfigureBeforeUseMvc(IApplicationBuilder app, IHostingEnvironment env, ILoggerFactory loggerFactory)
        {
            RequestContextManager.Instance = new RequestContextManager(app.ApplicationServices.GetService<IHttpContextAccessor>());

            if (env.IsStaging() || env.IsProduction())
            {
                app.UseExceptionHandler("/Erro");
            }
            else
            {
                // Add: Enable request/response recording and serve a inspector frontend.
                // Important: `UseRin` (Middlewares) must be top of the HTTP pipeline.
                app.UseRin();
                // Add(option): Enable ASP.NET Core MVC support if the project built with ASP.NET Core MVC
                app.UseRinMvcSupport();
                app.UseDeveloperExceptionPage();
                // Add: Enable Exception recorder. this handler must be after `UseDeveloperExceptionPage`.
                app.UseRinDiagnosticsHandler();

                loggerFactory.AddDebug();
                app.UseDeveloperExceptionPage();
                app.UseBrowserLink();
            }

            ConfigureStaticFiles(app, env);
            app.UseSession();
            app.UseCors(options => options.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod());
            // app.UseMetricsAndReporting(lifetime);
            app.UseSecurityHeadersMiddleware(new SecurityHeadersBuilder().AddDefaultSecurePolicy());
            app.UseLoginMiddleware();
            app.UseReCaptchaMiddleware();
            app.UseTemplateMiddleware();
        }

        protected override void UseMvc(IApplicationBuilder app)
        {
            app.UseMvc(routes =>
            {
                routes.MapRoute(name: "default", template: "{controller=Home}/{action=Index}/{id?}/{idAux?}");
            });
        }

        private static void ConfigureStaticFiles(IApplicationBuilder app, IHostingEnvironment env)
        {
            var compositeProvider = new CompositeFileProvider(env.WebRootFileProvider, new PhysicalFileProvider(Path.Combine(Directory.GetCurrentDirectory(), @"public")));
            env.WebRootFileProvider = compositeProvider;
            var options = new StaticFileOptions()
            {
                FileProvider = compositeProvider,
                RequestPath = "/public"
            };
            app.UseStaticFiles(options);

            Action<Microsoft.AspNetCore.StaticFiles.StaticFileResponseContext> prepareResponse = ctx =>
            {
                ctx.Context.Response.Headers.Append("Cache-Control", "public,max-age=3600");
            };
            app.UseStaticFiles(new StaticFileOptions()
            {
                FileProvider = new PhysicalFileProvider(Path.Combine(Directory.GetCurrentDirectory(), @"public/js")),
                RequestPath = new PathString("/js"),
                OnPrepareResponse = prepareResponse
            });

            app.UseStaticFiles(new StaticFileOptions()
            {
                FileProvider = new PhysicalFileProvider(Path.Combine(Directory.GetCurrentDirectory(), @"public/css")),
                RequestPath = new PathString("/css"),
                OnPrepareResponse = prepareResponse
            });

            app.UseStaticFiles(new StaticFileOptions()
            {
                FileProvider = new PhysicalFileProvider(Path.Combine(Directory.GetCurrentDirectory(), @"public/themes")),
                RequestPath = new PathString("/themes"),
                OnPrepareResponse = prepareResponse
            });

            app.UseStaticFiles(new StaticFileOptions()
            {
                FileProvider = new PhysicalFileProvider(Path.Combine(Directory.GetCurrentDirectory(), @"public/assets")),
                RequestPath = new PathString("/assets"),
                OnPrepareResponse = prepareResponse
            });
        }
    }
}
