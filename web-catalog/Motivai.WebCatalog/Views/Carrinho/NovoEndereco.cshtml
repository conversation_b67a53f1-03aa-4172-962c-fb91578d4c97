@model Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations.CampaignSettingsModel
@{
	Layout = null;
}
<!DOCTYPE html>
<html lang="pt-br" prefix="og: http://ogp.me/ns#">
<head>
    @{ Html.RenderPartial("_Head"); }
	<style>
	html { height:480px; width:720px; }
	</style>
</head>
<body class="modal">
	<div id="page">
		<section id="content" ng-app="popupEnderecoApp" ng-controller="EnderecoController" ng-init="init()">
			<div class="container">
				<article class="modal">
					<header>
						<h1>{{ ::pageTitle }}</h1>
					</header>

					<div class="right-p1">
						<div class="scroll-container">
							<form name="enderecoForm">
								<fieldset class="addressName">
									<div class="field">
										<label for="nomeEndereco">Dê um nome para este endereço<span class="required">*</span></label>
										<input id="nomeEndereco" name="nomeEndereco" type="text" placeholder="Ex.: Casa" required
											ng-model="endereco.AddressName" validator="required">
									</div>
								</fieldset>
								<fieldset class="address">
									<div class="field">
										<label for="cep">CEP<span class="required">*</span></label>
										<input id="cep" name="cep" type="text" placeholder="Ex.: 01234-567" required ng-blur="searchAddressByCep(endereco.Cep)"
											ng-model="endereco.Cep" ng-minlength="9" ui-br-cep-mask validator="required,cep">
                						<span ng-class="{'CustomValidationError validationFieldRequired': endereco.cepSearchError}" style="display:inline;"
											ng-bind="endereco.cepSearchMessage"></span>
									</div>
									<div class="field">
										<label for="endereco">Endereço<span class="required">*</span></label>
										<input id="endereco" name="endereco" type="text" placeholder="Ex.: Av. Paulista" ng-disabled="!endereco.FilledManually"
											ng-model="endereco.Street" validator="required">
									</div>
									<div class="field">
										<label for="numero">Número<span class="required">*</span></label>
										<input id="numero" name="numero" type="text" placeholder="Ex.: 12.345" required
											ng-model="endereco.Number" validator="required">
									</div>
									<div class="field">
										<label for="complemento">Complemento @if(Model.Parametrizations.RequiredShippingAddressDetails){<span class="required">*</span>}</label>
										<input id="complemento" name="complemento" placeholder="Ex.: Apto. 12 Bloco A" type="text"
											ng-model="endereco.Complement" @(Model.Parametrizations.RequiredShippingAddressDetails ? "required validator=required" : "")>
									</div>
									<div class="field">
										<label for="bairro">Bairro<span class="required">*</span></label>
										<input id="bairro" name="bairro" type="text" placeholder="Ex.: Bela Vista" required ng-disabled="!endereco.FilledManually"
											ng-model="endereco.Neighborhood" validator="required">
									</div>
									<div class="field">
										<label for="estado">Estado<span class="required">*</span></label>
										@*<input id="estado" name="estado" type="text" placeholder="Ex.: São Paulo" required ng-disabled="!endereco.FilledManually"
											ng-model="endereco.State" validator="required">*@
										<select-state class="platform-chosen" select-id="estado" state="endereco.State" on-update="endereco.State=$value"
											required="true" disabled="!endereco.FilledManually"></select-state>
									</div>
									<div class="field">
										<label for="cidade">Cidade<span class="required">*</span></label>
										<input id="cidade" name="cidade" type="text" placeholder="Ex.: São Paulo" required ng-disabled="!endereco.FilledManually"
											ng-model="endereco.City" validator="required">
									</div>
									<div class="field">
										<label for="ponto-de-referencia">Ponto de referência @if(Model.Parametrizations.RequiredShippingAddressDetails){<span class="required">*</span>}</label>
										<input id="ponto-de-referencia" name="ponto-de-referencia" type="text" placeholder="Ex.: Condomínio Edifício Paulista"
											ng-model="endereco.Reference" @(Model.Parametrizations.RequiredShippingAddressDetails ? "required validator=required" : "")>
									</div>
								</fieldset>
								<fieldset class="recipient">
									<h2>Dados de quem receberá os pedidos neste endereço</h2>
									<div class="field">
										<label for="cpfDestinatario">CPF<span class="required">*</span></label>
											<input id="cpfDestinatario" name="cpfDestinatario" type="text" placeholder="123.456.789-01" required
												ng-model="endereco.Receiver.Cpf" ui-br-cpf-mask validator="required">
									</div>
									<div class="field">
										<label for="nomeDestinatario">Nome<span class="required">*</span></label>
										<input id="nomeDestinatario" name="nomeDestinatario" type="text" placeholder="Ex.: Nome Sobrenome" required
											ng-model="endereco.Receiver.Name" validator="required">
									</div>
									<div class="field">
										<label for="telefoneDestinatario">Telefone residencial</label>
										<input id="telefoneDestinatario" name="telefoneDestinatario" type="text" placeholder="(12) 3456-7890" maxlength="14"
											ng-model="endereco.Receiver.Telephone" ui-br-phone-number validator="optionalPhone">
									</div>
									<div class="field">
										<label for="celularDestinatario">Celular<span class="required">*</span></label>
										<input id="celularDestinatario" name="celularDestinatario" type="text" placeholder="(12) 98765-4321" maxlength="15"
											ng-model="endereco.Receiver.Cellphone" ui-br-phone-number validator="required,phone">
									</div>
									<div class="field">
										<label for="emailDestinatario">E-mail<span class="required">*</span></label>
										<input id="emailDestinatario" name="emailDestinatario" type="email" placeholder="Ex.: <EMAIL>" required
											ng-model="endereco.Receiver.Email" ng-maxlength="150" validator="required,email">
									</div>
								</fieldset>
								<button type="submit" class="button" style="margin-bottom:1em;" ng-click="cadastraEndereco(endereco)" ng-if="showButton" ng-disabled="disableButton">{{ ::buttonText }}</button>
							    <div ng-class="'validation-summary-' + classeMensagem" ng-show="mensagemFeedback"><p ng-bind="mensagemFeedback"></p></div>
							</form>
						</div>
					</div>
				</article>
			</div>
		</section>
	</div>
</body>
@{ Html.RenderPartial("~/Views/Shared/_Scripts.cshtml"); }
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation.js")" type="text/javascript"></script>
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation-rule.js")" type="text/javascript"></script>
<script src="@Url.Content("~/js/app/filters/brasilFilters.js")" type="text/javascript"></script>
<script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript"></script>
<script src="@Url.Content("~/js/endereco/enderecoApi.js")" type="text/javascript"></script>
<script src="@Url.Content("~/js/minhaconta/account-address-popup.js")" type="text/javascript"></script>
</html>