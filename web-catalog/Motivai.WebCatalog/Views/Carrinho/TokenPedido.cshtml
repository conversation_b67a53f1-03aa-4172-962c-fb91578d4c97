@{
	Layout = null;
}
<!DOCTYPE html>
<html lang="pt-br" prefix="og: http://ogp.me/ns#">
<head>
    @{ Html.RenderPartial("_Head"); }
	<style>
	    html { max-height:340px; max-width:720px; }
	</style>
</head>
<body class="modal">
	<div id="page" ng-app="tokenApp">
		<section id="content" ng-controller="tokenCtrl" ng-init="init()">
			<div class="container">
				<article class="modal">
					<header>
                        <h1>Confirme o Código de Segurança</h1>
                    </header>
					<form>
						<div loader-container is-loading="loading">
							<label>O código de segurança será enviado para o celular:</label>
							<h3>{{ contact.cellphone }}</h3>
						</div>
						<div ng-show="sendingToken">
							<p>Enviando código de segurança, por favor, aguarde.</p>
						</div>
						<div ng-show="allowInsertToken">
							<div class="field">
								<label>Informe o código de segurança:</label>
								<input type="text" id="token" name="token" class="discount-coupon col-4" maxlength="6" ng-model="token" />
							</div>
							<button type="submit" class="button" ng-click="confirmToken()" ng-disabled="confirmingToken"
								ng-class="{'processing': confirmingToken}">{{ confirmingToken ? 'Aguarde' : 'Confirmar' }}</button>
						</div>
					</form>
				</article>
			</div>
		</section>
	</div>
</body>
@{ Html.RenderPartial("~/Views/Shared/_Scripts.cshtml"); }
<script type="text/javascript">
	angular.module('tokenApp', ['platformComponents'])
	.service('TokenService', ['$http', function($http) {
        this.getMethodWay = function() {
            return $http.get('/token/methods/info');
        };
        this.issueToken = function() {
            return $http.post('/token/order/issue');
        };
        this.confirmToken = function(token) {
            return $http.put('/token/order/confirmation', { token: token });
        };
	}])
	.controller('tokenCtrl', ['TokenService', '$scope', '$location', function(service, $scope, $location) {
        $scope.loading = false;
		$scope.allowInsertToken = false;

		const getContactInfo = function() {
			$scope.loading = true;
			service.getMethodWay()
				.then(function(response) { return response.data; })
				.then(function(contactResult) {
					$scope.loading = false;
					if (contactResult && contactResult.Success) {
						$scope.contact = contactResult.Return;
						window.canSubmit = true;
						issueToken();
					} else {
						showErrorSwal('Ops', 'Não foi possível carregar os dados de contato, por favor, atualize a página.');
					}
				})
				.catch(function(resp) {
					$scope.loading = false;
					showErrorSwal('Ops', 'Não foi possível carregar os dados de contato, por favor, atualize a página.');
				});
		};

		const issueToken = function() {
			$scope.sendingToken = true;
			$scope.allowInsertToken = false;
			service.issueToken()
				.then(function(response) { return response.data; })
				.then(function(tokenResult) {
					$scope.sendingToken = false;
					if (tokenResult && tokenResult.Success) {
						$scope.allowInsertToken = true;
					} else {
						showErrorSwal('Ops', 'Não foi possível enviar o código de segurança, por favor, atualize a página.');
					}
				})
				.catch(function(resp) {
					$scope.sendingToken = false;
					showErrorSwal('Ops', 'Não foi possível enviar o código de segurança, por favor, atualize a página.');
				});
		};

		const confirmToken = function(token) {
			if (!token || !token.length) {
				showErrorSwal('Ops', 'Código de segurança inválido.');
				return;
			}
			$scope.confirmingToken = true;
			service.confirmToken(token)
				.then(function(response) { return response.data; })
				.then(function(tokenResult) {
					$scope.confirmingToken = false;
					if (tokenResult && tokenResult.Success) {
						showSuccessSwal('Info', 'Código de segurança confirmado com sucesso.')
							.then(function() {
								frames.top.parent.canSubmit = true;
								frames.top.$.fancybox.close(true);
							});
					} else {
						showErrorSwal('Ops', tokenResult.Error);
					}
				})
				.catch(function(resp) {
					$scope.confirmingToken = false;
					showErrorSwal('Ops', 'Não foi possível confirmar o código de segurança, por favor, atualize a página.');
				});
		};

		$scope.confirmToken = function() {
			confirmToken($scope.token);
		};

		$scope.init = function() {
			getContactInfo();
		};
	}]);
</script>
</html>