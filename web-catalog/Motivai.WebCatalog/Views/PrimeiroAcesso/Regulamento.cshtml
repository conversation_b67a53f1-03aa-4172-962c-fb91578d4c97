﻿@model FirstAccessStepsViewModel;
@using Motivai.WebCatalog.Models.Pages.FirstAccess;
@using Motivai.WebCatalog.Models.FirstAccess;
@{
	ViewBag.Title = "Primeiro Acesso - Regulamento";
	Layout = "~/Views/Shared/_LayoutPrimeiroAcesso.cshtml";
}
<article class="terms">
	<header>
		<h1>Leia atentamente as regras abaixo</h1>
		<h2>É necessário aceitá-las para prosseguir ao catálogo</h2>
	</header>
	<div class="text">
		<div class="scroll">
			<div class="scroll-container">
				@Html.Raw(Model.PageModel.Regulation)
			</div>
		</div>
	</div>
	@using(Html.BeginForm("Regulamento", "PrimeiroAcesso", FormMethod.Post, new { @class = "acceptTerms", @onsubmit = "addLoaderToButton()" })) {
		<input type="hidden" name="regulation" value="@Model.PageModel.RegulationId" />
		<input type="hidden" name="version" value="@Model.PageModel.Version" />
		<input id="acceptedTerms" name="acceptedTerms" type="checkbox" @(Model.PageModel.AcceptanceRequired ? "required" : "") onclick="habilita(this);">
		<label for="acceptedTerms">Li e aceito os termos do regulamento @if(Model.PageModel.AcceptanceRequired) { <span class="required">*</span> }</label>
		<button type="submit" @(Model.PageModel.AcceptanceRequired ? "disabled" : "") class="button">Continuar</button>
	}
</article>

@section ScriptsCustomizados {
	<script type="text/javascript">
		function habilita(value) {
			if ($(value).attr("checked") == "checked")
				$("button[type=submit]").removeAttr("disabled");
			else
				$("button[type=submit]").attr("disabled", "disabled");
		}

		window.onload = function() {
			habilita('#acceptedTerms')
		}
	</script>
}