﻿@model Motivai.WebCatalog.Models.FirstAccess.RegulationPageModel
@{
	ViewBag.Title = "Politica de Privacidade - Regulamento";
	Layout = "~/Views/Shared/_LayoutPrimeiroAcesso.cshtml";
}
<article class="terms">
	<header>
		<h1>Leia atentamente as regras abaixo</h1>
		<h2>É necessário aceitá-las para prosseguir ao catálogo</h2>
	</header>
	<div class="text">
		<div class="scroll">
			<div class="scroll-container">
				@Html.Raw(Model.Regulation)
			</div>
		</div>
	</div>
	@using(Html.BeginForm("Cadastro", "PrimeiroAcesso", FormMethod.Post, new { @class = "acceptTerms", @onsubmit = "addLoaderToButton()" })) {
		<input type="hidden" name="regulation" value="@Model.RegulationId" />
		<input type="hidden" name="version" value="@Model.Version" />
		<input id="acceptedTerms" name="acceptedTerms" type="checkbox" @(Model.AcceptanceRequired ? "required" : "") onclick="habilita(this);">
		<label for="acceptedTerms">Li e aceito os termos do regulamento @if(Model.AcceptanceRequired) { <span class="required">*</span> }</label>
		<button type="submit" @(Model.AcceptanceRequired ? "disabled" : "") class="button">Continuar</button>
	}
</article>