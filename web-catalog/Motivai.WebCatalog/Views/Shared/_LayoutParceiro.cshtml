@using Motivai.WebCatalog.Helpers;
@inject IHelperWeb _helper;
@{
    var httpContext = RequestContextManager.Instance.CurrentContext;
    var itemCoinPrefix = httpContext.Items["coinPrefix"];
    var itemCoinSufix = httpContext.Items["coinSufix"];
    var coinPrefix = itemCoinPrefix != null ? itemCoinPrefix.ToString() : "";
    var coinSufix = itemCoinSufix != null ? itemCoinSufix.ToString() : "";
    var participante = _helper.GetParticipantSession();
}
<!DOCTYPE html>
<html lang="pt-br" prefix="og: http://ogp.me/ns#">
<head>
    @{ Html.RenderPartial("_Head"); }
	@RenderSection("StylesCustomizados", false)
</head>
<body class="@ViewBag.ClassBody">
    <div id="page">
		<header role="banner" class="site-header">
			<div class="container">
				<h1 class="logo"><a href="/"></a></h1>
			</div>
		</header>
		<section id="content">
        	@RenderBody()
        </section>
        <div class="footer-fix"></div>
    </div>
    <footer>
		<div class="container">
			<div class="copyright">
				<h1 class="logo"></h1>
				<p>&copy; @DateTime.UtcNow.Year. Todos os direitos reservados.</p>
			</div>
		</div>
	</footer>
</body>
@{
    Html.RenderPartial("_Scripts");
	_helper.ClearSession();
}
@RenderSection("ScriptsCustomizados", false)
</html>