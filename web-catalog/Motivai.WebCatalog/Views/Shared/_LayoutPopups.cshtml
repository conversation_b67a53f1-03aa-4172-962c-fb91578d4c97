<!DOCTYPE html>
<html lang="pt-br" prefix="og: http://ogp.me/ns#">
<head>
    <cache expires-sliding="@TimeSpan.FromMinutes(30)">
    @{ Html.RenderPartial("_Head"); }
    </cache>
    <style>
        .chosen-container { display: inline-block !important; }
        .align-left { text-align: left; }
        .chosen-container { margin-bottom: 0px !important; }
    </style>
	@RenderSection("StylesCustomizados", false)
</head>
<body class="modal">
    @RenderBody()
</body>
@{ Html.RenderPartial("_Scripts"); }
@RenderSection("ScriptsCustomizados", false)
</html>