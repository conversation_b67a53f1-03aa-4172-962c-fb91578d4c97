﻿@using Motivai.WebCatalog.Helpers;
@using Motivai.SharedKernel.Helpers;
@inject IHelperWeb _helper;
@{
    var httpContext = RequestContextManager.Instance.CurrentContext;
    var itemCoinPrefix = httpContext.Items["coinPrefix"];
    var itemCoinSufix = httpContext.Items["coinSufix"];
    var coinPrefix = itemCoinPrefix != null ? itemCoinPrefix.ToString() : "";
    var coinSufix = itemCoinSufix != null ? itemCoinSufix.ToString() : "";
    var participante = _helper.GetParticipantSession();
}
<!DOCTYPE html>
<html lang="pt-br" prefix="og: http://ogp.me/ns#">
<head>
	@{ Html.RenderPartial("_Head"); }
	@RenderSection("StylesCustomizados", false)
</head>
<body class="primeiro-acesso">
    <div id="page">
		<header role="banner" class="site-header">
			@if(_helper.IsCallcenterSession()) {
			<div class="info alert-stock" style="text-align:center;max-height:3em;padding:0.70em 1em;">
				<p></p><span style="font-size:24px"><strong>Teleresgate: Iniciado em @participante.SessionStart.ToString("dd/MM/yyyy HH:mm")
				por @_helper.GetCallcenterUsername()</strong></span>
			</div>
			}
			<div class="container">
				<h1 class="logo"><a href="/"></a></h1>
				<div class="search"></div>
				<div class="profile">
					<div class="photo"><img src="/assets/img/usuarios/profile-noPhoto.jpg"></div>
					<div class="text">
						<h2>Olá, @(participante != null && !participante.NeedAuthenticationMfa && !participante.NeedSetupAuthenticationMfa ? participante.Name : "Cliente")
							<span>
								<div class="over" style="width:150px;">
									<ul>
										<li><a href="@Url.Action("Logout", "MinhaConta")">Sair</a></li>
									</ul>
								</div>
							</span>
						</h2>
                		<p>Saldo: <strong>@coinPrefix @(participante != null ? participante.Balance.ToCurrency() : "0,00") @coinSufix</strong></p>
					</div>
				</div>
			</div>
		</header>
		<section id="content">
        	<div class="container">
        		@RenderBody()
        	</div>
        </section>
        <div class="footer-fix"></div>
    </div>
    <footer>
		<div class="container footer-container">
			<cache expires-sliding="@TimeSpan.FromDays(1)">
				<div class="copyright">
					<h1 class="logo"></h1>
					<p>@DateTime.UtcNow.Year. Todos os direitos reservados.</p>
				</div>
			</cache>
		</div>
	</footer>
</body>
@{ Html.RenderPartial("_Scripts"); }
@RenderSection("ScriptsCustomizados", false)
</html>
