@model Motivai.WebCatalog.Models.Pages.CatalogPageViewModel
<article class="pointsLocked" ng-init="init()">
    <header>
        <h1>@Model.ViewStrings.BalanceDescription Bloqueados</h1>
    </header>
    <form>
        <div class="validation-summary-errors" ng-show="ocorreuErro"><span ng-bind="descricaoErro"></span></div>
        <div loader-container is-loading="loadingPontos" div-style=""></div>
        <table ng-hide="loadingPontos">
            <thead>
                <tr>
                    <th>Data</th>
                    <th>Descrição</th>
                    <th>@Model.ViewStrings.BalanceDescription</th>
                </tr>
            </thead>
            <tbody>
                <tr ng-show="!pontosBloqueados || pontosBloqueados.length == 0">
                    <td colspan="3">Não existem lançamentos.</td>
                </tr>
                <tr ng-repeat="ponto in pontosBloqueados">
                    <td><span ng-bind="::(ponto.ProcessingDate | date:'dd/MM/yyyy')"></span></td>
                    <td><span ng-bind="::ponto.Description"></span></td>
                    <td><span ng-bind="::(ponto.Amount | currency:'')"></span></td>
                </tr>
            </tbody>
            <tfoot>
            <tr>
                <td colspan="2">@Model.ViewStrings.BalanceDescriptionLowerCase a expirar</td>
                <td colspan="2"><span ng-bind="moeda.Prefix"></span> <span ng-bind="(totalPontosBloqueados | currency:'')">0</span> <span ng-bind="moeda.Sufix"></span></td>
            </tr>
            </tfoot>
        </table>
    </form>
</article>