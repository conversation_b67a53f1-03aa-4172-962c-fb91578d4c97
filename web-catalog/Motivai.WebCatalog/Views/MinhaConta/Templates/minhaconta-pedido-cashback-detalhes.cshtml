@using Motivai.SharedKernel.Helpers;
@using Motivai.WebCatalog.Helpers;
@model Motivai.WebCatalog.Models.Pages.CatalogPageViewModel
@{
    var httpContext = RequestContextManager.Instance.CurrentContext;
    var itemCoinPrefix = httpContext.Items["coinPrefix"];
    var itemCoinSufix = httpContext.Items["coinSufix"];
    var coinPrefix = itemCoinPrefix != null ? itemCoinPrefix.ToString() : "";
    var coinSufix = itemCoinSufix != null ? itemCoinSufix.ToString() : "";
}
<article class="cart" ng-init="init()">
    <div loader-container is-loading="loading" div-style=""></div>

	<div ng-if="order && order.orderNumber" ng-show="!loading">
        <header>
            <h1>Pedido número {{ order.orderNumber }}</h1>
        </header>
        <div>
            <p><strong>Data do pedido:</strong> {{ order.formattedDate }}</p>
            <p><strong>Status:</strong> <span ng-bind="::order.resumedStatus"></span></p>
        </div>
        <div class="cart-products">
            <h2>Dados da transferência</h2>
            <table class="cashback-details" style="display:table">
                <thead>
                    <tr>
                        <th colspan="2">Conta a ser Creditada</th>
                        <th>@Model.ViewStrings.BalanceDescription p/ Transferir</th>
                        <th>Custo do TED</th>
                        <th>Taxa de Serviço</th>
                        <th>Valor a creditar</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="2" class="bank-account">
                            <span>{{ order.bankAccount.bankName }}</span><br>
                            <span>{{ order.bankAccount.typeDescription }}</span><br>
                            <span>Ag: {{ order.bankAccount.agencyNumber }}</span><br>
                            <span>Cc: {{ order.bankAccount.accountNumber }}-{{ order.bankAccount.accountDigit }}</span>
                        </td>
                        <td>@coinPrefix {{ order.pointsAmount | currency:'' }} @coinSufix</td>
                        <td>R$ {{ order.detailedFees.bankTransferFee | currency:'' }}</td>
                        <td>R$ {{ (order.detailedFees.additionalFee + order.detailedFees.governmentFee) | currency:'' }}</td>
                        <td>R$ {{ order.transferAmount | currency:'' }}</td>
                    </tr>
                    <tr ng-if="order.showTransferReceipt">
                        <td colspan="3" style="text-align:left">
                            <h3>Data de Transferência: {{ order.formattedTransferDate }}</h3><br>
                        </td>
                        <td colspan="3">
                            <a class="button modal btn-tracking" target="_blank" href="{{ order.transferReceiptImageUrl }}">Ver Comprovante</a>
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="6">
                            <table class="subtotal" style="display:table">
                                <tr>
                                    <td>Total das Taxas</td>
                                    <td>R$ {{ order.totalFeesAmount | currency:'' }}</td>
                                </tr>
                                <tr>
                                    <td>Total de @Model.ViewStrings.BalanceDescriptionLowerCase a debitar</td>
                                    <td>@coinPrefix {{ order.totalCost.points | currency:'' }} @coinSufix</td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</article>