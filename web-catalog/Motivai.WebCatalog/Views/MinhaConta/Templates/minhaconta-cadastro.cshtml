@model Motivai.WebCatalog.Models.Pages.CatalogPageViewModel
<article class="register" ng-init="init(@(Model.CampaignSettings.Parametrizations.EnableMetadataFields ? "true": "false"))"
    ng-if="!requireMfaValidationAtRegistration() || !isMfaValidationStep()">
    <div class="validation-summary-errors" ng-if="erroGeral">
        <p><span ng-bind="mensagemErroGeral"></span></p>
    </div>
    <div id="div-load" loader-container is-loading="loading() || loadingDados">
    </div>
    <form id="registerForm" name="registerForm" novalidate>
        <input type="hidden" id="id" name="id" ng-model="dadosCadastro.id" ng-readonly="true" />
        <fieldset class="personalData" ng-if="IsPessoaFisica">
            <h1>Dados pessoais</h1>
            <div ng-if="requireMfaValidationAtRegistration() && disabled()" id="auth-mfa-content">
                <div class="col-12 instructions">
                    <a class="mfa-require-information">Necessário autenticação do usuário para
                        alteração do cadastro. </a>
                </div>

                <div class="col-12">
                    <a class="button" id="auth" href="#/auth">Autenticar</a>
                </div>
            </div>
            <div class="field">
                <label for="nome">Nome<span class="required">*</span></label>
                <input id="nome" name="nome" type="text" placeholder="Ex.: Nome Sobrenome" maxlength="150"
                    ng-model="dadosCadastro.Name" validator="required" ng-disabled="disabled()">
            </div>
            <div class="field">
                <label for="cpf">CPF<span class="required">*</span></label>
                <input id="cpf" name="cpf" type="text" placeholder="123.456.789-01" class="cpf" maxlength="14"
                    ng-model="dadosCadastro.Cpf" ng-readonly="notAllowChangeDocument" validator="required,cpf"
                    ng-disabled="disabled()">
            </div>
            <div class="field">
                <label for="rg">RG<span></span></label>
                <input id="rg" name="rg" type="text" placeholder="1234567890" class="rg" maxlength="15"
                    ng-model="dadosCadastro.Rg" ng-disabled="disabled()">
            </div>
            <div class="field">
                <label for="data-nascimento">Data de Nascimento<span class="required">*</span></label>
                <input id="data-nascimento" name="data-nascimento" type="text" maxlength="10"
                    ng-model="dadosCadastro.BirthDate" moment-picker="dadosCadastro.BirthDate"
                    start-date="dadosCadastro.BirthDate" ng-model-options="{updateOn:'blur'}" locale="pt-br"
                    format="DD/MM/YYYY" start-view="decade" keyboard="true" ng-disabled="disabled()">
            </div>
            <div class="field">
                <label for="estadocivil">Estado Civil<span></span></label>
                <select id="estadocivil" name="MaritalStatus" placeholder="Escolha" data-placeholder="Escolha" ng-model="dadosCadastro.MaritalStatus"
                    ng-options="estado for estado in estadosCivil | orderBy:estado" ng-disabled="disabled()">
                    <option value="" selected="selected" disabled="disabled">Escolha</option>
                </select>
                <span ng-show="dirtyWithError(registerForm.MaritalStatus, 'required')"
                    class="CustomValidationError validationFieldRequired val-msg">Estado civil é obrigatório</span>
            </div>
            <div class="field" ng-class="{'chosen-required':dirtyWithError(registerForm.Gender, 'required')}">
                <label for="sexo">Sexo<span></span></label>
                <select class="autostart" id="sexo" name="Gender" placeholder="Escolha" data-placeholder="Escolha"
                    ng-model="dadosCadastro.Gender" ng-disabled="disabled()">
                    <option value="" selected="selected">Escolha</option>
                    <option value="M">Masculino</option>
                    <option value="F">Feminino</option>
                </select>
                <span ng-show="dirtyWithError(registerForm.Gender, 'required')"
                    class="CustomValidationError validationFieldRequired">Sexo é obrigatório</span>
            </div>
        </fieldset>

        <fieldset class="corporateData" ng-if="IsPessoaJuridica">
            <h1>Dados empresariais</h1>
            <div class="field">
                <label for="cnpj">CNPJ<span class="required">*</span></label>
                <input id="cnpj" name="cnpj" type="text" placeholder="Ex.: 12345678900019" class="cnpj" maxlength="18"
                    ui-br-cnpj-mask ng-model="dadosCadastro.Cnpj" ng-readonly="notAllowChangeDocument"
                    validator="required,cnpj" ng-disabled="disabled()" />
            </div>
            <div class="field">
                <label for="nome-fantasia">Nome Fantasia<span class="required">*</span></label>
                <input id="nome-fantasia" name="nome-fantasia" type="text" placeholder="Ex.: Noma da empresa"
                    maxlength="150" ng-model="dadosCadastro.Name" validator="required" ng-disabled="disabled()">
            </div>
            <div class="field">
                <label for="razao-social">Razão Social<span class="required">*</span></label>
                <input id="razao-social" name="razao-social" type="text" placeholder="Ex.: Noma da empresa Ltda"
                    maxlength="150" ng-model="dadosCadastro.CompanyName" validator="required" ng-disabled="disabled()">
            </div>
            @if (Model.CampaignSettings.Parametrizations.EnableAccountRepresentative)
            {
                <div class="field">
                    <label for="cpf-pedidos">CPF para pedidos<span class="required">*</span></label>
                    <input id="cpf-pedidos" name="cpf-pedidos" type="text" placeholder="123.456.789-01" class="cpf"
                    maxlength="14" ng-model="dadosCadastro.AccountRepresentative.Document"
                    ng-blur="pesquisarPessoaPeloDocumento()" validator="required,cpf" ng-disabled="disabled()">
                </div>
                <div class="field">
                    <label for="nome-pedidos">Nome<span class="required">*</span></label>
                    <input id="nome-pedidos" name="nome-pedidos" type="text"
                    ng-model="dadosCadastro.AccountRepresentative.Name" ng-disabled="disabled()">
                </div>
                <div class="field">
                    <label for="email-pedidos">E-mail<span class="required">*</span></label>
                    <input id="email-pedidos" name="email-pedidos" type="text"
                    ng-model="dadosCadastro.AccountRepresentative.Email" ng-disabled="disabled()">
                </div>
                <div class="field">
                    <label for="telefone-pedidos">Telefone<span class="required">*</span></label>
                    <input id="telefone-pedidos" name="telefone-pedidos" type="text"
                    ng-model="dadosCadastro.AccountRepresentative.Telephone" ng-disabled="disabled()">
                </div>
            }
            <div class="field state-inscription-exempt">
                <input id="stateInscriptionExempt" name="stateInscriptionExempt" type="checkbox" value="true"
                    ng-model="dadosCadastro.StateInscriptionExempt" ng-change="onExemptChange()"
                    ng-disabled="disabled()">
                <label for="stateInscriptionExempt">Inscrição estadual isenta?</label>
            </div>
            <div class="field">
                <label for="ie">Estado</label>
                <select id="ieEstado" name="ieEstado" ng-model="dadosCadastro.StateInscriptionUf" ng-disabled="exempt"
                    ng-disabled="disabled()">
                    <option value="AC">AC</option>
                    <option value="AL">AL</option>
                    <option value="AP">AP</option>
                    <option value="AM">AM</option>
                    <option value="BA">BA</option>
                    <option value="CE">CE</option>
                    <option value="DF">DF</option>
                    <option value="ES">ES</option>
                    <option value="GO">GO</option>
                    <option value="MA">MA</option>
                    <option value="MT">MT</option>
                    <option value="MS">MS</option>
                    <option value="MG">MG</option>
                    <option value="PR">PR</option>
                    <option value="PB">PB</option>
                    <option value="PA">PA</option>
                    <option value="PE">PE</option>
                    <option value="PI">PI</option>
                    <option value="RJ">RJ</option>
                    <option value="RN">RN</option>
                    <option value="RS">RS</option>
                    <option value="RO">RO</option>
                    <option value="RR">RR</option>
                    <option value="SC">SC</option>
                    <option value="SE">SE</option>
                    <option value="SP">SP</option>
                    <option value="TO">TO</option>
                </select>
            </div>
            <div class="field">
                <label for="ie">Inscrição estadual</label>
                <input id="ie" name="ie" type="text" placeholder="Ex.: 1234567890" maxlength="20"
                    ng-model="dadosCadastro.StateInscription" ui-br-ie-mask="dadosCadastro.StateInscriptionUf"
                    ng-disabled="exempt" ng-disabled="disabled()">
            </div>
        </fieldset>

        @if (Model.CampaignSettings.Parametrizations.EnableMetadataFields)
        {
            <fieldset class="personalData" ng-if="metadataHeaders && metadataHeaders.length">
                <div class="field field-metadata" ng-repeat="field in metadataHeaders">
                    <label for="{{ field.property }}">{{ field.description }}</label>
                    <input type="text" name="{{field.property}}" placeholder="{{field.property}}"
                    ng-disabled="field.readOnly" ng-required="field.required"
                    ng-model="::(dadosCadastro.Metadata[field.property])"
                    ng-change="dadosCadastro.Metadata[field.property]=$event" ng-disabled="disabled()" />
                </div>
            </fieldset>
        }

        <fieldset class="phones">
            <h1>Telefones</h1>
            <div class="field">
                <label for="fonePrincipal">Telefone principal<span></span></label>
                <input id="fonePrincipal" name="fonePrincipal" type="tel" maxlength="14" placeholder="(01) 2345-6789"
                    ui-br-phone-number ng-model="dadosCadastro.Contact.MainPhone"
                    ng-disabled="disabled()">
            </div>
            <div class="field">
                <label for="foneResidencial">Telefone residencial</label>
                <input id="foneResidencial" name="foneResidencial" type="tel" maxlength="14"
                    placeholder="(01) 2345-6789" ui-br-phone-number ng-model="dadosCadastro.Contact.HomePhone"
                    validator="optionalPhone" ng-disabled="disabled()">
            </div>
            <div class="field">
                <label for="foneComercial">Telefone comercial</label>
                <input id="foneComercial" name="foneComercial" type="tel" maxlength="14" placeholder="(01) 2345-6789"
                    ui-br-phone-number ng-model="dadosCadastro.Contact.CommercialPhone" validator="optionalPhone"
                    ng-disabled="disabled()">
            </div>
            <div class="field">
                <label for="falarCom">Falar com</label>
                <input id="falarCom" name="falarCom" type="text" placeholder="Ex.: Fulano" maxlength="20"
                    ng-model="dadosCadastro.Contact.TalkTo" ng-disabled="disabled()">
            </div>
            <div class="field">
                <label for="celular">Celular<span class="required">*</span></label>
                <input id="celular" name="celular" type="tel" class="celular" maxlength="15"
                    placeholder="(01) 2345-67890" ng-model="dadosCadastro.Contact.MobilePhone"
                    validator="required,phone" ui-br-phone-number ng-disabled="disabled()">
            </div>
            <div class="field">
                <label for="operadora">Operadora<span></span></label>
                <select id="operadora" name="operadora" placeholder="Escolha" data-placeholder="Escolha"
                    ng-model="dadosCadastro.Contact.MobileOperator"
                    ng-options="oper for oper in operadoras | orderBy:oper" ng-disabled="disabled()">
                    <option value="" selected="selected" disabled="disabled">Escolha</option>
                </select>
            </div>
        </fieldset>

        <fieldset class="emails">
            <h1>Formas de contato</h1>
            <div class="field">
                <label for="Email">E-mail principal<span class="required">*</span></label>
                <input id="Email" name="Email" type="email" placeholder="Ex.: <EMAIL>" maxlength="150"
                    ng-model="dadosCadastro.Contact.MainEmail" ng-maxlength="150" validator="required,email"
                    ng-disabled="disabled()">
            </div>
            <div class="field" ng-if="IsPessoaFisica">
                <label for="Email">E-mail pessoal</label>
                <input id="Email" name="Email" type="email" placeholder="Ex.: <EMAIL>" maxlength="150"
                    ng-model="dadosCadastro.Contact.PersonalEmail" ng-maxlength="150"
                    validator="nonrequired,optionalEmail" ng-disabled="disabled()">
            </div>
            <div class="field">
                <label for="com-Email">E-mail comercial</label>
                <input id="com-Email" name="com-Email" type="email" placeholder="Ex.: <EMAIL>"
                    maxlength="150" ng-model="dadosCadastro.Contact.CommercialEmail"
                    validator="nonrequired,optionalEmail" ng-disabled="disabled()">
            </div>
            <div class="field">
                @if (Model.CampaignSettings.Parametrizations.HasNewsletters)
                {
                    <input id="promo-GP" name="promo-GP" type="checkbox" value="true" ng-model="dadosCadastro.GpInf">
                    <label for="promo-GP">Desejo receber informações e promoções por e-mail.</label><br>
                    <input id="promo-partners" name="promo-partners" type="checkbox" value="true" ng-disabled="disabled()"
                    ng-model="dadosCadastro.GpPartnerInf">
                    <label for="promo-partners">Desejo receber informa&ccedil;&otilde;s e promo&ccedil;&otilde;es de
                        parceiros por e-mail.</label>
                }
            </div>
        </fieldset>

        @if (Model.CampaignSettings.Parametrizations.CanUploadPicture)
        {
            <fieldset class="profilePhoto">
                <h2>Sua foto</h2>
                <p>Você pode enviar arquivos JPG, GIF, BMP ou PNG (tamanho máximo de 1 MB). Não envie fotos que contenham
                    imagens de personagens de desenho animado, pessoas famosas, nudez, trabalho artístico ou material
                    protegido por direitos autorais. O tamanho mínimo de imagem é de 75x75 pixels.</p>
                <div class="profile">
                    <div class="photo"><img id="fotoParticipante" src="/assets/img/usuarios/profile-noPhoto.jpg"></div>
                </div>
                <div class="field">
                    <input id="profilePhoto" name="profilePhoto" type="file">
                    <button type="button" class="button">Selecionar foto</button>
                </div>
            </fieldset>
        }

        <fieldset>
            <button type="submit" class="button" style="margin-bottom:1em;" ng-click="salvarDados(registerForm)" ng-disabled="disabled()">Confirmar dados</button>
        </fieldset>

        <div ng-class="'validation-summary-' + classeMensagem" ng-show="mensagemFeedback">
            <p><span ng-bind="mensagemFeedback"></span></p>
        </div>
    </form>
</article>