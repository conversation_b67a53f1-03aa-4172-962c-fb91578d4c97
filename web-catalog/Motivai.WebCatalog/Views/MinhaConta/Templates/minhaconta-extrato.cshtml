@model Motivai.WebCatalog.Models.Pages.CatalogPageViewModel
@{
  var showExtractDetails = Model.CampaignSettings.Parametrizations.ExtractType == "D";
}
<article class="pointsSummary" ng-init="init()">
    <header>
        <h1>Meu <PERSON></h1>
    </header>
    <form>
        <div class="filters">
            <div class="field">
                <label for="periodo">Período</label>
                <select class="autostart" id="periodo" name="periodo" placeholder="Escolha" data-placeholder="Escolha">
                    <option value="" selected="selected" disabled="disabled">Escolha</option>
                    <option value="7">Últimos 7 dias</option>
                    <option value="14">Últimos 14 dias</option>
                    <option value="30">Último mês</option>
                    <option value="90">Últimos 3 meses</option>
                    <option value="other">Outro período</option>
                </select>
            </div>
            <div class="other">
                <div class="field">
                    <label for="dataInicial">De</label>
                    <input id="dataInicial" name="dataInicial" type="text" maxlength="10" locale="pt-br"
                        moment-picker ng-model="dataInicialRange" ng-model-options="{updateOn:'blur'}"
                        start-view="day" format="DD/MM/YYYY" keyboard="true" />
                </div>
                <div class="field">
                    <label for="dataFinal">Até</label>
                    <input id="dataFinal" name="dataFinal" type="text" maxlength="10" locale="pt-br"
                        moment-picker ng-model="dataFinalRange" ng-model-options="{updateOn:'blur'}"
                        start-view="day" format="DD/MM/YYYY" keyboard="true" />
                </div>
            </div>
            <div class="field">
                <label for="tipoTransacao">Tipo de transação</label>
                <select class="autostart" id="tipoTransacao" name="tipoTransacao" placeholder="Escolha" data-placeholder="Escolha" ng-model="filtros.tipoExtrato">
                    <option value="" selected="selected">Todos</option>
                    <option value="C">Crédito</option>
                    <option value="R">@Model.ViewStrings.BuyActionTypeDescription</option>
                    <option value="E">Estorno</option>
                </select>
            </div>
            <div class="field">
                <button type="submit" class="button" ng-click="filtra()"></button>
            </div>
        </div>
        <div class="validation-summary-errors" ng-show="ocorreuErro"><span ng-bind="descricaoErro"></span></div>
        <div loader-container is-loading="loadingExtrato || loadingParamertizacaoMetadata" div-style=""></div>
        <table ng-hide="loadingExtrato">
            <thead>
                <tr>
                    <th>Data</th>
                    <th>Descrição</th>
                    <th>@Model.ViewStrings.OrderTotalDescription</th>
					@if(showExtractDetails) {
     	               <th>Detalhes</th>
					}
                </tr>
            </thead>
            <tbody>
                <tr ng-show="!extrato || extrato.length == 0">
                    <td colspan="@(showExtractDetails ? "4" : "3")">Não existem lançamentos.</td>
                </tr>
                <tr ng-repeat="reg in extrato">
                    <td><span ng-bind="::(reg.ProcessingDate | date:'dd/MM/yyyy')"></span></td>
                    <td><span ng-bind="::reg.Description"></span></td>
                    <td><span ng-bind="::(reg.Amount | currency:'')">0</span></td>
                    @if(showExtractDetails) {
                    <td>
                        <a ng-if="reg.ExtraData" class="btn-icon" title="Detalhes" ng-click="showDetails(reg.ExtraData)">
						    <i class="fa fa-search fa-2x"></i>
                        </a>
                    </td>
                    }
                </tr>
            </tbody>
            <tfoot>
            <tr>
                <td colspan="@(showExtractDetails ? "3" : "2")">Saldo total disponível</td>
                <td>
                    <span ng-bind="moeda.Prefix"></span> <span ng-bind="(saldoPeriodo | currency:'')">0</span> <span ng-bind="moeda.Sufix"></span>
                </td>
            </tr>
            </tfoot>
        </table>
    </form>

	@if(showExtractDetails) {
    <div style="visibility:hidden;position:absolute;">
		<div id="modal-extradata" class="container" style="width: metadataOrientationType == 'HORIZONTAL' ? '1000px;' : '720px;'">
            <article class="modal">
                <header>
                    <h1>Detalhes da pontuação</h1>
                </header>
                    <div class="container">
                        <div class="col-md-12" ng-show="metadataOrientationType == 'HORIZONTAL'">
                            <table class="modalTableH">
                                <thead>
                                    <tr>
                                        <th ng-repeat="h in headers">{{h}}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-show="!body || body.length == 0">
                                        <td colspan="3">Não existem Detalhes.</td>
                                    </tr>
                                    <tr>
                                        <td ng-repeat="b in body">{{ b.value }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-md-12" ng-show="metadataOrientationType == 'VERTICAL'">
                            <table class="modalTableV">
                                <tr ng-repeat="b in body">
                                    <th>{{ b.key }}</th>
                                    <td>{{ b.value }}</td>
                                </tr>
                            </table>
                        </div>
                </div>
            </article>
		</div>
	</div>
	}
</article>