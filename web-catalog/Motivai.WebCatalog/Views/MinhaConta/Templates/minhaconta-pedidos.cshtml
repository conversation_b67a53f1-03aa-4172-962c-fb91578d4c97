@model Motivai.WebCatalog.Models.Pages.CatalogPageViewModel
<article class="ordersSummary" ng-init="init()">
	<header>
		<h1><PERSON>u <PERSON>edido<PERSON></h1>
	</header>
	<form>
		<div class="filters">
			<div class="field">
				<label for="periodo">Período</label>
				<select class="autostart" id="periodo" name="periodo" placeholder="Escolha" data-placeholder="Escolha">
					<option value="" selected="selected" disabled="disabled">Escolha</option>
					<option value="7">Últimos 7 dias</option>
					<option value="14">Últimos 14 dias</option>
                    <option value="30">Último mês</option>
                    <option value="90">Últimos 3 meses</option>
					<option value="other">Outro período</option>
				</select>
			</div>
            <div class="other">
                <div class="field">
                    <label for="dataInicial">De</label>
                    <input id="dataInicial" name="dataInicial" type="text" maxlength="10" locale="pt-br"
                        moment-picker ng-model="dataInicialRange" ng-model-options="{updateOn:'blur'}"
                        start-view="day" format="DD/MM/YYYY" keyboard="true" />
                </div>
                <div class="field">
                    <label for="dataFinal">Até</label>
                    <input id="dataFinal" name="dataFinal" type="text" maxlength="10" locale="pt-br"
                        moment-picker ng-model="dataFinalRange" ng-model-options="{updateOn:'blur'}"
                        start-view="day" format="DD/MM/YYYY" keyboard="true" />
                </div>
            </div>
			<div class="field">
				<label for="status">Status</label>
				<select class="autostart" id="status" name="status" placeholder="Escolha" data-placeholder="Escolha">
					<option value="" selected="selected">Todos</option>
					<option value="P">Andamento</option>
					<option value="T">Em Transporte</option>
					<option value="E">Entregue</option>
					<option value="C">Cancelado</option>
				</select>
			</div>
			<div class="field">
				<button type="submit" class="button" ng-click="filtra()"></button>
			</div>
		</div>

        <div class="validation-summary-errors" ng-show="ocorreuErro"><span ng-bind="descricaoErro"></span></div>

        <div loader-container is-loading="loadingPedidos" div-style=""></div>

		<table ng-hide="loadingPedidos">
			<thead>
				<tr>
					<th>Data</th>
					<th>Nº pedido</th>
					<th>Tipo</th>
					<th>Status</th>
					<th>@Model.ViewStrings.OrderTotalDescription</th>
					<th></th>
				</tr>
			</thead>
			<tbody>
				<tr ng-show="!pedidos || pedidos.length == 0">
					<td colspan="3">Não existem lançamentos.</td>
				</tr>
				<tr ng-repeat="pedido in pedidos | orderBy:'CreationDate':true ">
					<td><span ng-bind="::(pedido.CreationDate|date:'dd/MM/yyyy')"></span></td>
					<td><a href="#/pedido/{{::pedido.Id}}" ng-bind="::pedido.InternalOrderNumber">0</a></td>
					<td>{{ pedido.descricaoTipo }}</td>
					<td><span ng-bind="::pedido.Status"></span></td>
					<td><span ng-bind="::(pedido.TotalAmountPoints | currency:'')">0</span></td>
					<td>
						<a class="orderDetails" href="#/pedido/{{::pedido.Id}}" ng-if="pedido.OrderType == 'PRODUCT_ORDER'">Detalhes</a>
						<a class="orderDetails" href="#/pedido/cartao/{{::pedido.Id}}" ng-if="pedido.OrderType == 'CARD_ORDER'">Detalhes</a>
						<a class="orderDetails" href="#/pedido/cashback/{{::pedido.Id}}" ng-if="pedido.OrderType == 'CASHBACK_ORDER'">Detalhes</a>
						<a class="orderDetails" href="#/pedido/recarga/{{::pedido.Id}}" ng-if="pedido.OrderType == 'RECHARGE_ORDER'">Detalhes</a>
						<a class="orderDetails" href="#/pedido/pague-contas/{{::pedido.Id}}" ng-if="pedido.OrderType == 'BILL_PAYMENT_ORDER'">Detalhes</a>
					</td>
				</tr>
			</tbody>
		</table>
	</form>
</article>