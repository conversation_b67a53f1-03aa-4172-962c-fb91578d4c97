@model Motivai.WebCatalog.Models.Pages.CatalogPageViewModel
<article class="register" ng-if="!requireMfaValidationAtRegistration() || !isMfaValidationStep()">
  <header>
    <h1>Meus endereços</h1>
  </header>
  <div ng-if="requireMfaValidationAtRegistration() && disabled()" id="auth-mfa-content">
    <div class="col-12 instructions">
      <div class="col-12 instructions">
        <a class="mfa-require-information">Necessário autenticação do usuário para alteração do(s) endereço(s). </a>
      </div>

      <div class="col-12">
        <a class="button" id="auth" href="#/auth">Autenticar</a>
      </div>
    </div>
  </div>
  <p>Abaixo estão os endereços cadastros que podem ser utilizados como endereço de entrega.</p>
  <div class="validation-summary-errors" ng-if="erroGeral">
    <p><span ng-bind="mensagemErroGeral"></span></p>
  </div>
  <div ng-class="'validation-summary-' + classeMensagem" ng-show="mensagemFeedback">
    <p ng-bind="mensagemFeedback"></p>
  </div>
  <div loader-container is-loading="loadingEnderecos || loading()">
    <table>
      <thead>
        <tr>
          <th>Título</th>
          <th>Endereço</th>
          <th>Destinatário</th>
          @if (Model.Parametrizations.AllowChangeShippingAddress)
          {
            <th ng-if="!disabled()"></th>
          }
        </tr>
      </thead>
      <tbody>
        <tr ng-repeat="end in enderecos">
          <td>1
            <span ng-bind="::end.AddressName"></span>
            <span ng-if="::end.MainAddress"><br /><small>(Principal)</small></span>
          </td>
          <td>
            <span ng-bind="::end.Street"></span>, <span ng-bind="::end.Number"></span>, <span
              ng-bind="::end.Complement"></span>
            <br><span ng-bind="::end.Neighborhood"></span> - <span ng-bind="::end.City"></span> - <span
              ng-bind="::end.State"></span>
            <br>CEP <span ng-bind="::(end.Cep | cep)"></span><br>Ref.: <span ng-bind="::end.Reference"></span>
          </td>
          <td>
            <div ng-if="::end.Receiver.Name">
              <span ng-bind="::end.Receiver.Name"></span><br>CPF <span ng-bind="::(end.Receiver.Cpf | cpf)"></span>
              <br><span ng-bind="::(end.Receiver.Telephone | telefone)"></span> / <span
                ng-bind="::(end.Receiver.Cellphone | telefone)"></span>
              <br><span ng-bind="::end.Receiver.Email"></span>
            </div>
          </td>
          @if (Model.Parametrizations.AllowChangeShippingAddress)
          {
            <td ng-if="!disabled()">
              <a href="#/editar-endereco/{{::end.Id}}" class="edit"
              @Html.Raw(!Model.Parametrizations.AllowChangeIntegrationShippingAddress ?
              "ng-if=\"::(end.Integration==false)\"" : "")>Editar</a>
              <a href="javascript:;" class="delete" ng-click="apagaEndereco(end.Id)"
              @Html.Raw(!Model.Parametrizations.AllowChangeIntegrationShippingAddress ?
              "ng-if=\"::(end.Integration==false)\"" : "")>Apagar</a>
            </td>
          }
        </tr>
      </tbody>
    </table>
  </div>
  @if (Model.Parametrizations.AllowChangeShippingAddress)
  {
    <a href="#/novo-endereco" class="button" ng-if="!disabled()">Cadastrar novo endereço</a>
  }
</article>