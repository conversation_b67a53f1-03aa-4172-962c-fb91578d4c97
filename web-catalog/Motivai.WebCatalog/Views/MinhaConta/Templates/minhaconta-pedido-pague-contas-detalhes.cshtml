@using Motivai.SharedKernel.Helpers;
@using Motivai.WebCatalog.Helpers;
@{
    var httpContext = RequestContextManager.Instance.CurrentContext;
    var itemCoinPrefix = httpContext.Items["coinPrefix"];
    var itemCoinSufix = httpContext.Items["coinSufix"];
    var coinPrefix = itemCoinPrefix != null ? itemCoinPrefix.ToString() : "";
    var coinSufix = itemCoinSufix != null ? itemCoinSufix.ToString() : "";
}
@section StylesCustomizados {
    <style>
        .cart-receipt pre { font-family: Courier, monospace; }
    </style>
}
<article class="cart" ng-init="init()">
    <div loader-container is-loading="loading" div-style=""></div>

	<div ng-if="order && order.protocol" ng-show="!loading">
        <header>
            <h1>Pedido número {{ order.protocol }}</h1>
        </header>
        <div>
            <p><strong>Data do pedido:</strong> {{ order.formattedDate }}</p>
            <p><strong>Status:</strong> <span ng-bind="::order.resumedStatus"></span></p>
        </div>
        <div class="cart-products">
            <h2>Dados da Conta</h2>
            <table class="cashback-details" style="display:table">
                <thead>
                    <tr>
                        <th>Codigo de Barras</th>
                        <th>Emissor</th>
                        <th>Valor da Conta</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{{ order.billPayment != null && order.billPayment.barcode ? order.billPayment.barcode : '-' }}</td>
                        <td>{{ order.billPayment != null && order.billPayment.assignor ? order.billPayment.assignor : '-' }}</td>
                        <td>@coinPrefix {{( order.deteiledCost != null && order.deteiledCost.participantCost ? order.deteiledCost.participantCost.points : '-' ) | currency:'' }} @coinSufix</td>
                    </tr>
                </tbody>
            </table>
            <div ng-if="order.confirmed && order.proofPayment" class="cart-receipt">
                <h2>Comprovante do Pagamento</h2>
                <pre>
                    <p ng-bind="order.proofPayment"></p>
                </pre>
                 <a href="javascript:" class="button print" ng-click="printProof()">Imprimir comprovante</a>
            </div>
        </div>
    </div>
</article>