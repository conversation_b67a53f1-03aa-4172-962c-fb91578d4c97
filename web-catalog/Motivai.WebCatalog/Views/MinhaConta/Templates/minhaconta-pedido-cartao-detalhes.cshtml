@model Motivai.WebCatalog.Models.Pages.CatalogPageViewModel
@{
    var balanceLabelUpperCase = "Pontos";
    var balanceLabelLowerCase = "pontos";
    if (Model.IsMarketplace) {
        balanceLabelUpperCase = "Saldos";
        balanceLabelLowerCase = "saldos";
    }
}
<article class="cart" ng-init="init()">
    <div loader-container is-loading="loading" div-style=""></div>

	<div ng-if="cardOrder && cardOrder.orderNumber" ng-show="!loading">
        <header>
            <h1>Pedido número {{ cardOrder.orderNumber }}</h1>
        </header>
        <div>
            <p><strong>Data do pedido:</strong> {{ cardOrder.formattedDate }}</p>
            <p><strong>Status:</strong> <span ng-bind="::cardOrder.resumedStatus"></span></p>
        </div>
        <div class="order-info card-order-info">
            <div class="box" ng-if="cardOrder.issueNewCard || cardOrder.reissueCard">
                <div class="address" ng-if="cardOrder.shippingAddress">
                    <h3>Endereço de entrega: {{ cardOrder.shippingAddress.addressName }}</h3>
                    <p>{{ cardOrder.shippingAddress.street }}, {{ cardOrder.shippingAddress.number }} - {{ cardOrder.shippingAddress.complement }}
                    <br>{{ cardOrder.shippingAddress.neighborhood }} - {{ cardOrder.shippingAddress.city }} - {{ cardOrder.shippingAddress.state }}
                    <br>CEP {{ cardOrder.shippingAddress.cep }}<br>Referência: {{ cardOrder.shippingAddress.reference }}</p>
                </div>
                <div class="recipient" ng-if="cardOrder.shippingAddress.receiver">
                    <h3>Quem receberá o cartão</h3>
                    <p>{{ cardOrder.shippingAddress.receiver.name }}<br>CPF {{ cardOrder.shippingAddress.receiver.cpf }}
                    <br>{{ cardOrder.shippingAddress.receiver.telephone }} / {{ cardOrder.shippingAddress.receiver.cellphone }}
                    <br>{{ cardOrder.shippingAddress.receiver.email }}</p>
                </div>
            </div>
        </div>
        <div class="cart-products">
            <h2>Dados da transferência</h2>
            <table style="display:table">
                <thead>
                    <tr>
                        <th colspan="2">Cartão</th>
                        <th>@balanceLabelUpperCase p/ Transferir</th>
                        <th>Custo do Plástico</th>
                        <th>Custo do Frete</th>
                        <th>Taxa de Carga</th>
                        <th>Taxa de Serviço</th>
                        <th>Valor a creditar</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="2"><strong>{{ cardOrder.card.number }}</strong> <span ng-if="cardOrder.reissueCard">(2ª Via)</span></td>
                        <td style="width:100px">{{ cardOrder.pointsToTransfer | number:'2' }}</td>
                        <td>R$ {{ cardOrder.detailedFees.plasticCost | number:'2' }}</td>
                        <td>R$ {{ cardOrder.detailedFees.shippingCost | number:'2' }}</td>
                        <td>R$ {{ cardOrder.detailedFees.chargeCost | number:'2' }}</td>
                        <td>R$ {{ cardOrder.detailedFees.serviceFee | number:'2' }}</td>
                        <td>R$ {{ cardOrder.creditAmount | number:'2' }}</td>
                    </tr>
                </tbody>
                <tfoot>
                <tr>
                    <td colspan="4"></td>
                    <td colspan="4">
                        <table class="subtotal" style="display:table">
                            <tr>
                                <td>Total das Taxas</td>
                                <td>R$ {{ cardOrder.totalFeesAmount | number:'2' }}</td>
                            </tr>
                            <tr>
                                <td>Total de @balanceLabelLowerCase a debitar</td>
                                <td>{{ cardOrder.orderTotalCost.points | number:'2' }}</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                </tfoot>
            </table>
        </div>
    </div>
</article>