<div id="div-load" loader-container is-loading="loading()"></div>
<fieldset ng-controller="RegistrationMfaValidationCtrl" ng-init="init()" ng-hide="loading()">
    <form>
        <fieldset ng-if="isRecaptchaStep()">
            <div>
                <h1>Autenticação</h1>
                <p>Preencha o Recaptcha abaixo. </p>
            </div>
            <form method="POST" name="loginForm" autocomplete="off">
                <div class="field">
                    <div id="userValidationRecaptcha"></div>
                </div>
                <div class="field">
                    <button type="submit" class="button pull-left" ng-class="{'processing': processing}"
                        ng-click="validateReCaptcha()">Continuar</button>

                    <aside class="sidebar pull-left button-line">
                        <a class="back" class="button pull-left" ng-click="cancelAuth()"
                            href="javascript:history.back()"> Cancelar </a>
                    </aside>
                </div>
            </form>
        </fieldset>

        <fieldset ng-if="isTokenOptionStep()">
            <div>
                <h1>Código de Segurança</h1>
                <p ng-if="canChoose()">Selecione a forma que deseja receber o código de segurança.</p>
            </div>
            <div class="field no-bottom" ng-if="user.email && canSendEmail()">
                <input id="email" name="sendMethod" type="radio" value="EMAIL" ng-model="user.sendMethod" />
                <label for="email">Por e-mail: {{ user.email }}</label>
            </div>
            <div class="field no-bottom" ng-if="user.mobilePhone && canSendSMS()">
                <input id="cellphone" name="sendMethod" type="radio" value="SMS" ng-model="user.sendMethod" />
                <label for="cellphone">Por SMS: {{ user.mobilePhone }}</label>
            </div>
            <div class="field text-left">
                <button type="submit" class="button pull-left" ng-class="{'processing': processing}"
                    ng-click="sendAuthorizationCode()">{{!alreadySentToken ? "Enviar Token" : "Reenviar
                    Token"}}</button>

                <aside class="sidebar pull-left button-line">
                    <a class="back" class="button pull-left" ng-click="cancelAuth()" href="javascript:history.back()">
                        Cancelar </a>
                </aside>
            </div>
        </fieldset>
        <fieldset ng-if="isTokenConfirmationStep()">
            <form class="form-input">
                <div>
                    <h1>Confirmação do código</h1>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div class="field col-12">
                            <label for="token">Código de segurança recebido</label>
                            <input id="token" type="text" name="token" ng-model="user.code" required>
                        </div>
                    </div>
                </div>
                <div class="field text-left">
                    <button type="submit" class="button pull-left" ng-class="{'processing': processing}"
                        ng-click="validateAuthorizationCode()">Confirmar</button>

                    <aside class="sidebar pull-left button-line">
                        <a class="back" class="button pull-left" ng-click="backToTokenOption()"> Voltar </a>
                    </aside>
                </div>
            </form>
        </fieldset>
    </form>
</fieldset>
