@model Motivai.WebCatalog.Models.Pages.CatalogPageViewModel
<article class="register" ng-init="init('@(Model.Parametrizations.AuthenticationMfaFormat)')">
    <form id="mfaSettings" name="mfaSettings" ng-hide="loadingMfaSettings" autocomplete="off">
        <fieldset class="password">
            <h1>Dados de Dupla Autenticação</h1>
            <div class="field">
                <label for="authFormat">Formato de autenticação<span class="required">*</span></label>
                <select id="authFormat" class="autostart" name="auth_format"
                    ng-model="participantMfaSettings.AuthenticationMfaFormat"
                    ng-disabled="!candEditAuthenticationFormat">
                    <option value="">Selecione</option>
                    <option value="SMS_ONLY">Celular</option>
                    <option value="EMAIL_ONLY">E-mail</option>
                </select>
            </div>
            <div class="field" ng-if="participantMfaSettings.AuthenticationMfaFormat == 'EMAIL_ONLY'">
                <label for="email">Email</label>
                <input id="email" name="email" type="text" ng-model="participantMfaSettings.Email">
            </div>
            <div class="field" ng-if="participantMfaSettings.AuthenticationMfaFormat == 'SMS_ONLY'">
                <label for="mobilePhoneMfa">Celular</label>
                <input id="mobilePhoneMfa" name="mobilePhoneMfa" type="text"
                    ng-model="participantMfaSettings.MobilePhone">
            </div>
            <div class="field top-p29" ng-if="!tokenSended || counter == 0">
                <button type="submit" class="button pull-left" ng-click="sendToken()">{{ tokenSended ? 'Reenviar':
                    'Enviar'
                    }}</button>
            </div>

            <div ng-if="tokenSended">
                <div class="field" ng-if="participantMfaSettings.AuthenticationMfaFormat == 'SMS_ONLY'">
                    <label for="mobilePhoneSender">Informe o código de segurança recebido</label>
                    <input type="text" autocomplete="off" class="form-control" id="token" name="token"
                        ng-model="userToken.token" placeholder="Digite o Código de segurança recebido por SMS">
                </div>
                <div class="field" ng-if="participantMfaSettings.AuthenticationMfaFormat == 'EMAIL_ONLY'">
                    <label for="emailSender">Informe o código de segurança recebido</label>
                    <input type="text" autocomplete="off" class="form-control" id="token" name="token"
                        ng-model="userToken.token" placeholder="Digite o Código de segurança recebido por E-mail">
                </div>

                <div class="field">
                    <button type="submit" class="button" ng-click="validateToken()"> Validar
                    </button>
                </div>

                <div class="field" ng-if="counter !== 0">
                    <span>Você pode solicitar um novo envio em: <strong> {{ counter * 1000 | date:'mm:ss'}}
                        </strong>
                    </span>
                </div>

            </div>
        </fieldset>
    </form>
    <div loader-container is-loading="loadingMfaSettings" div-style=""></div>
</article>