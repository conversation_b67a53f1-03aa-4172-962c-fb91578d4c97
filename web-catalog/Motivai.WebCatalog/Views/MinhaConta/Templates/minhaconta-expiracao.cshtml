@model Motivai.WebCatalog.Models.Pages.CatalogPageViewModel
<article class="pointsExpired" ng-init="init()">
    <header>
        <h1>@Model.ViewStrings.BalanceDescription a Expirar</h1>
    </header>
    <form>
        <div class="validation-summary-errors" ng-show="ocorreuErro"><span ng-bind="descricaoErro"></span></div>
        <div loader-container is-loading="loadingPontos" div-style=""></div>
        <table ng-hide="loadingPontos">
            <thead>
                <tr>
                    <th>Data de expiração</th>
                    <th>Descrição</th>
                    <th>@Model.ViewStrings.BalanceDescription</th>
                    <th>Utilizados</th>
                    <th>Disponíveis</th>
                    <th>A expirar</th>
                </tr>
            </thead>
            <tbody>
                <tr ng-show="!pontosExpirar || pontosExpirar.length == 0">
                    <td colspan="6">Não existem lançamentos.</td>
                </tr>
                <tr ng-repeat="ponto in pontosExpirar">
                    <td><span ng-bind="::(ponto.ExpirationDate | date:'dd/MM/yyyy')"></span></td>
                    <td><span ng-bind="::ponto.Description"></span></td>
                    <td><span ng-bind="::(ponto.TotalPoints|currency:'')"></span></td>
                    <td><span ng-bind="::(ponto.UsedPoints|currency:'')"></span></td>
                    <td><span ng-bind="::(ponto.AvailablePoints|currency:'')"></span></td>
                    <td><span ng-bind="::(ponto.ExpiringPoints|currency:'')"></span></td>
                </tr>
            </tbody>
            <tfoot>
            <tr>
                <td colspan="3">@Model.ViewStrings.BalanceDescription expirar</td>
                <td colspan="3"><span ng-bind="moeda.Prefix"></span> <span ng-bind="(totalPontosExpirar|currency:'')">0</span> <span ng-bind="moeda.Sufix"></span></td>
            </tr>
            </tfoot>
        </table>
    </form>
</article>