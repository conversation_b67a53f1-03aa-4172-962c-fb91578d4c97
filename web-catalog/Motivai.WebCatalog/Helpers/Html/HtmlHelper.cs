using System.Text;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace Motivai.WebCatalog.Helpers.Html {
    public static class NgHtml {
        private static StringBuilder AppendLabel(StringBuilder builder, string labelText, string labelFor, bool required) {
            builder.Append("<label for=\"")
                .Append(labelFor)
                .Append("\">")
                .Append(labelText);
            if (required) {
                builder.Append("<span class=\"required\">*</span>");
            }
            builder.Append("</label>");
            return builder;
        }

        public static IHtmlContent NgLabel(this IHtmlHelper htmlHelper, string labelText, string labelFor = null, bool required = false) {
            StringBuilder builder = new StringBuilder();
            AppendLabel(builder, labelText, labelFor, required);
            return new HtmlString(builder.ToString());
        }

        public static IHtmlContent Label(this IHtmlHelper htmlHelper, ParticipantFieldSettings field, string labelFor = null) {
            return htmlHelper.NgLabel(field.Label, labelFor, field.Required);
        }

        private static void AppendNgInput(StringBuilder builder, string ngModel, string name, string placeholder, string maxlength,
                bool required = false, bool disabled = false, FieldType type = FieldType.Text) {
            AppendNgInput(builder, ngModel, name, placeholder, maxlength, required, disabled.ToString(), type);
        }

        private static void AppendNgInput(StringBuilder builder, string ngModel, string name, string placeholder, string maxlength,
            bool required = false, string disabled = null, FieldType type = FieldType.Text) {
            builder.Append("<input type=\"text\" id=\"")
                .Append(name)
                .Append("\" name=\"")
                .Append(name)
                .Append("\" placeholder=\"")
                .Append(placeholder)
                .Append("\" maxlength=\"")
                .Append(maxlength)
                .Append("\" ng-model=\"")
                .Append(ngModel);

            if (disabled != null && disabled.ToLower() == "true") {
                builder.Append($"\" disabled=\"{disabled}\" ng-readonly=\"{disabled}");
            }

            if (type == FieldType.Text && required) {
                builder.Append("\" validator=\"required");
            } else if (type == FieldType.Phone) {
                builder.Append("\" ui-br-phone-number ");
                if (required) {
                    builder.Append("validator=\"required,phone");
                } else {
                    builder.Append("validator=\"nonrequired,optionalPhone");
                }
            } else if (type == FieldType.Email) {
                if (required) {
                    builder.Append("\" validator=\"required,email");
                } else {
                    builder.Append("\" validator=\"nonrequired,optionalEmail");
                }
            } else if (type == FieldType.Cpf) {
                builder.Append("\" ui-br-cpf-mask ");
                if (required) {
                    builder.Append("validator=\"required,cpf");
                }
            } else if (type == FieldType.Cnpj) {
                builder.Append("\" ui-br-cnpj-mask ");
                if (required) {
                    builder.Append("validator=\"required,cnpj");
                }
            } else if (type == FieldType.Number) {
                builder.Append("\" ui-hide-group-sep ui-number-mask=\"0");
                if (required) {
                    builder.Append("\" validator=\"required,number");
                }
            }
            builder.Append("\">");
        }

        public static IHtmlContent NgInput(this IHtmlHelper htmlHelper, string ngModel, string name, string placeholder = null,
            string maxlength = null, bool required = false, bool disabled = false) {
            StringBuilder builder = new StringBuilder();
            AppendNgInput(builder, ngModel, name, placeholder, maxlength, required, disabled);
            return new HtmlString(builder.ToString());
        }

        public static IHtmlContent Field(this IHtmlHelper htmlHelper, ParticipantFieldSettings field, string ngModel, string name,
            string placeholder = null, string maxlength = null, string disabled = null) {
            StringBuilder builder = new StringBuilder();
            builder.Append("<div class=\"field\">");
            AppendLabel(builder, field.Label, name, field.Required);
            AppendNgInput(builder, ngModel, name, placeholder, maxlength, field.Required, disabled ?? field.Disabled.ToString());
            builder.Append("</div>");
            return new HtmlString(builder.ToString());
        }

        public static IHtmlContent NumberField(this IHtmlHelper htmlHelper, ParticipantFieldSettings field, string ngModel, string name,
            string placeholder = null, string maxlength = null, string disabled = null) {
            StringBuilder builder = new StringBuilder();
            builder.Append("<div class=\"field\">");
            AppendLabel(builder, field.Label, name, field.Required);
            AppendNgInput(builder, ngModel, name, placeholder, maxlength, field.Required, disabled ?? field.Disabled.ToString(), FieldType.Number);
            builder.Append("</div>");
            return new HtmlString(builder.ToString());
        }

        public static IHtmlContent PhoneField(this IHtmlHelper htmlHelper, ParticipantFieldSettings field, string ngModel, string name,
            string placeholder = null, string maxlength = null) {
            StringBuilder builder = new StringBuilder();
            builder.Append("<div class=\"field\">");
            AppendLabel(builder, field.Label, name, field.Required);
            AppendNgInput(builder, ngModel, name, placeholder, maxlength, field.Required, field.Disabled, FieldType.Phone);
            builder.Append("</div>");
            return new HtmlString(builder.ToString());
        }

        public static IHtmlContent EmailField(this IHtmlHelper htmlHelper, ParticipantFieldSettings field, string ngModel, string name) {
            StringBuilder builder = new StringBuilder();
            builder.Append("<div class=\"field\">");
            AppendLabel(builder, field.Label, name, field.Required);
            AppendNgInput(builder, ngModel, name, "<EMAIL>", "150", field.Required, field.Disabled, FieldType.Email);
            builder.Append("</div>");
            return new HtmlString(builder.ToString());
        }

        public static IHtmlContent CpfField(this IHtmlHelper htmlHelper, ParticipantFieldSettings field, string ngModel, string name) {
            StringBuilder builder = new StringBuilder();
            builder.Append("<div class=\"field\">");
            AppendLabel(builder, field.Label, name, field.Required);
            AppendNgInput(builder, ngModel, name, "123.456.789-09", "14", field.Required, field.Disabled, FieldType.Cpf);
            builder.Append("</div>");
            return new HtmlString(builder.ToString());
        }

        public static IHtmlContent CnpjField(this IHtmlHelper htmlHelper, ParticipantFieldSettings field, string ngModel, string name) {
            StringBuilder builder = new StringBuilder();
            builder.Append("<div class=\"field\">");
            AppendLabel(builder, field.Label, name, field.Required);
            AppendNgInput(builder, ngModel, name, "12.345.678/0000-19", "19", field.Required, field.Disabled, FieldType.Cnpj);
            builder.Append("</div>");
            return new HtmlString(builder.ToString());
        }

        public static IHtmlContent NgInputCpf(this IHtmlHelper htmlHelper, string ngModel, string name, string placeholder = null,
            bool required = false, bool disabled = false) {
            var builder = new StringBuilder()
                .Append("<input type=\"text\" id=\"")
                .Append(name)
                .Append("\" name=\"")
                .Append(name)
                .Append("\" placeholder=\"")
                .Append(placeholder)
                .Append("\" maxlength=\"15\" ng-model=\"")
                .Append(ngModel);

            if (disabled) {
                builder.Append("\" ng-readonly=\"true");
            }

            builder.Append("\" validator=\"");
            if (required) {
                builder.Append("required,");
            }
            builder.Append("cpf\" ui-br-cpf-mask>");
            return new HtmlString(builder.ToString());
        }
    }

    public enum FieldType {
        Text,
        Number,
        Phone,
        Email,
        Date,
        Cpf,
        Cnpj
    }
}