using System;
using Motivai.SharedKernel.Helpers.Cryptography;

namespace Motivai.WebCatalog.Helpers
{
    public static class ParserHelper
    {
        public static Guid DecryptGuid(ICryptography _cryptography, string encryptedGuid) {
            Guid parsedGuid;
            if (string.IsNullOrEmpty(encryptedGuid))
                return Guid.Empty;
            string decryptedGuid = null;
            try {
                decryptedGuid = _cryptography.Decrypt(encryptedGuid);
            } catch (Exception) {
                return Guid.Empty;
            }
            if (Guid.TryParse(decryptedGuid, out parsedGuid))
                return parsedGuid;
            return Guid.Empty;
        }
    }
}