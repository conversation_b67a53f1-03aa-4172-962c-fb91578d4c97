﻿using System;
using System.Threading.Tasks;
using Motivai.WebCatalog.Models.MyAccount;
using Motivai.WebCatalog.Models.Order;
using Motivai.WebCatalog.Models.Session;

namespace Motivai.WebCatalog.Helpers {
    public interface IHelperWeb {
        Task<Guid> GetCampaignIdForCurrentDomain();

        void ClearSession();
        void AddToSession<T>(string sessionKey, T value);
        bool HasItemInSession(string sessionKey);
        T GetFromSession<T>(string sessionKey);
        void RemoveFromSession(string sessionKey);

        #region Call Center
        void StartCallcenterSession(Guid callcenterUserId, string callcenterUsername);
        Guid GetCallCenterUserId();
        bool IsCallcenterSession();
        void RemoveCallcenterSession();
        string GetCallcenterUsername();
        #endregion

        #region Primeiro Acesso
        void SetSessionAsFirstAccess();
        void SetNewPrivacyPolicy();
        bool IsFirstAccessSession();
        bool IsNewPrivacyPolicySession();
        void CleanFirstAccess();
        void CleanNewPrivacyPolicy();
        #endregion

        #region MFA
        void SetSessionAsNeedAuthenticationMfa();
        bool IsNeedAuthenticationMfaSession();

        void SetSessionAsNeedSetupAuthenticationMfa();
        bool IsNeedSetupAuthenticationMfaSession();

        #endregion

        #region Participante
        bool HasParticipantSession();
        Guid GetUserId();
        Guid GetParticipantId();
        ///<summary>
        /// Carregar o ID da campanha do usuário logado.
        ///</summary>
        Guid GetCampaignId();
        void CreateParticipantSession(Guid campaignId, UserParticipantModel user);
        void SetParticipantSession(UserPrincipal user);
        UserPrincipal GetParticipantSession();
        Task<UserPrincipal> GetParticipantSessionAsync();
        #endregion

        #region Carrinho
        CartModel GetSessionCart();
        Task<CartModel> GetSessionCartAsync();
        void SetSessionCart(CartModel cartModel);
        void RemoveCartFromSession();
        void CleanAuthenticationMfa();
        #endregion
    }
}
