﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>netcoreapp2.1</TargetFramework>
  </PropertyGroup>
  <PropertyGroup>
    <EnableDefaultContentItems>false</EnableDefaultContentItems>
    <MvcRazorCompileOnPublish>true</MvcRazorCompileOnPublish>
  </PropertyGroup>

  <Target Name="BundlerMinifier" BeforeTargets="Build">
    <Exec Command="dotnet bundle" />
  </Target>

  <ItemGroup>
    <!-- <Content Include="Certificates\**\*" CopyToPublishDirectory="Always" /> -->
    <Content Include="public\**\*" CopyToPublishDirectory="Always" />
    <Content Include="Views\**\*" CopyToPublishDirectory="Always" />
    <Content Include="appsettings.json" CopyToPublishDirectory="Always" />
    <Content Include="nlog.config" CopyToPublishDirectory="Always" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Motivai.SharedKernel.Domain" Version="2.0.0" />
    <PackageReference Include="Motivai.SharedKernel.Helpers" Version="2.0.0" />

    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.0.0" />
    <PackageReference Include="Microsoft.AspNetCore" Version="2.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.1.3" />
    <PackageReference Include="Microsoft.AspNetCore.Session" Version="2.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.StaticFiles" Version="2.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Localization" Version="2.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.ViewCompilation" Version="2.1.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="2.1.1" />
    <PackageReference Include="Microsoft.Extensions.Caching.Redis" Version="2.2.0" />

    <PackageReference Include="Rin" Version="1.0.5" />
    <PackageReference Include="Rin.Mvc" Version="1.0.5" />

    <!-- https://github.com/bcgit/bc-csharp -->
    <PackageReference Include="Portable.BouncyCastle" Version="1.9.0" />
  </ItemGroup>

  <ItemGroup>
    <DotNetCliToolReference Include="BuildBundlerMinifier" Version="2.8.391" />
    <DotNetCliToolReference Include="BundlerMinifier.Core" Version="2.8.391" />
    <PackageReference Include="Microsoft.VisualStudio.Web.BrowserLink" Version="2.1.1" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="2.1.1" />
    <DotNetCliToolReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Tools" Version="2.0.1" />
  </ItemGroup>
</Project>
