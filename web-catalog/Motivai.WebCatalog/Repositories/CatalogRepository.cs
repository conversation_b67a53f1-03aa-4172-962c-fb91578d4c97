using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.PaymentGateway;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Domain.Model.Structures;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Models.PointsPurchase;
using Motivai.WebCatalog.Models.Catalog;
using Motivai.WebCatalog.Models.ExtraServices;
using Motivai.WebCatalog.Models.ExtraServices.BillPayments;
using Motivai.WebCatalog.Models.Order;
using Motivai.WebCatalog.Models.Pages.Cards;
using Motivai.WebCatalog.Models.Product;
using Motivai.WebCatalog.Models.Session;
using Motivai.WebCatalog.Services.Cache;

namespace Motivai.WebCatalog.Repositories
{
	public class CatalogRepository {
		public const string USER_ID_QUERY_NAME = "uid";
		public const string PARTICIPANT_ID_QUERY_NAME = "pid";

		private readonly CustomCache cache;
		private readonly ICryptography _cryptography;
		private readonly CampaignRepository _campaignRepository;

		public CatalogRepository(CustomCache customCache, ICryptography cryptography,
			CampaignRepository campaignRepository) {
			this.cache = customCache;
			this._cryptography = cryptography;
			this._campaignRepository = campaignRepository;
		}

		#region Mediaboxes

		private List<MediaBoxModel> HandleMediaboxAndCommunications(List<MediaBoxModel> mediaBoxes) {
			if (mediaBoxes == null) return null;
			mediaBoxes.ForEach(m => {
				if (m.Communication) {
					if (m.Location == CommunicationLocation.Mediabox) {
						m.Id = _cryptography.Encrypt(m.Id);
						m.Message = null;
						m.Link = string.Format("/comunicacao/detalhes/{0}", m.Id);
					} else if (m.Location == CommunicationLocation.Modal) {
						m.Id = m.Identifier;
						m.Identifier = null;
					}
				} else {
					m.Id = null;
					if (m.ContentType == "PRODUCT" && m.FeaturedProduct != null) {
						m.FeaturedProduct.EncodeUrl();
					} else {
						m.FeaturedProduct = null;
					}
				}
			});
			return mediaBoxes;
		}

		public async Task<List<MediaBoxModel>> GetMediaBoxesForHome(Guid campaignId, Guid userId, Guid participantId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("mediaboxes/home")
				.Query(USER_ID_QUERY_NAME, userId)
				.Query(PARTICIPANT_ID_QUERY_NAME, participantId)
				.Query("catalog", "true")
				.AsyncGet()
				.GetApiReturn<List<MediaBoxModel>>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os media boxes da campanha.");
			return HandleMediaboxAndCommunications(apiReturn.GetReturnOrError());
		}

		public async Task<List<CampaignPagesCustomScriptsModel>> GetCustomScripts(Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("campaigns").Path(campaignId)
				.Path("pages/customscripts")
				.AsyncGet()
				.GetApiReturn<List<CampaignPagesCustomScriptsModel>>();

			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os scripts da campanha.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<MediaBoxModel>> GetMediaBoxesForDepartment(Guid campaignId, Guid departmentId, Guid userId, Guid participantId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("mediaboxes").Path(departmentId)
				.Query(USER_ID_QUERY_NAME, userId)
				.Query(PARTICIPANT_ID_QUERY_NAME, participantId)
				.Query("catalog", "true")
				.AsyncGet()
				.GetApiReturn<List<MediaBoxModel>>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os media boxes da campanha.");
			return HandleMediaboxAndCommunications(apiReturn.GetReturnOrError());
		}
		#endregion

		#region Loja Especial

		public async Task<List<SpecialShopModel>> GetSpecialShops(Guid campaignId, Guid userId, Guid participantId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("specialshops")
				.Query(USER_ID_QUERY_NAME, userId)
				.Query(PARTICIPANT_ID_QUERY_NAME, participantId)
				.AsyncGet()
				.Timeout(15_000)
				.GetApiReturn<List<SpecialShopModel>>();

			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as lojas especiais da campanha.");
			var specialShops = apiReturn.GetReturnOrError();
			if (specialShops != null) {
				specialShops.ForEach(sp => {
					sp.Id = _cryptography.Encrypt(sp.Id);
					sp.EncodeUrl();
                });
			}
			return specialShops;
		}

		public async Task<SpecialShopModel> GetSpecialShopById(Guid campaignId, Guid userId, Guid participantId, Guid specialShopId, CampaignParametrizationOrigin origin) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("specialshops").Path(specialShopId)
				.Query(USER_ID_QUERY_NAME, userId)
				.Query(PARTICIPANT_ID_QUERY_NAME, participantId)
				.Query("origin", origin.ToString())
				.AsyncGet()
				.GetApiReturn<SpecialShopModel>();


			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar a loja especial da campanha.");
			var specialShop = apiReturn.GetReturnOrError();
			if (specialShop != null) {
				specialShop.Id = _cryptography.Encrypt(specialShop.Id);
				specialShop.EncodeUrl();
				if (specialShop.Products != null) {
					specialShop.Products.ForEach(p => p.EncodeUrl());
				}
			}
			return specialShop;
		}

		public async Task<SpecialShopModel> GetRandomSpecialShop(Guid campaignId, Guid userId, Guid participantId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("specialshops/random")
				.Query(USER_ID_QUERY_NAME, userId)
				.Query(PARTICIPANT_ID_QUERY_NAME, participantId)
				.AsyncGet()
				.Timeout(10_000)
				.GetApiReturn<SpecialShopModel>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar a loja especial da campanha.");
			var specialShop = apiReturn.GetReturnOrError();
			if (specialShop != null) {
				specialShop.Id = _cryptography.Encrypt(specialShop.Id);
				specialShop.EncodeUrl();
			}
			return specialShop;
		}

		#endregion

		#region Home

		public async Task<List<ProductShowcaseModel>> GetFeaturedProductsForHome(Guid campaignId, Guid userId,
				Guid participantId, decimal? balance = null) {
			// return await cache.GetOrCreate($"web-cmp-{campaignId}-home-prods", async () => {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("home/products")
				.Query(USER_ID_QUERY_NAME, userId)
				.Query(PARTICIPANT_ID_QUERY_NAME, participantId)
				.Query("balance", balance)
				.AsyncGet()
				.GetApiReturn<List<ProductShowcaseModel>>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os produtos da campanha.");
			var products = apiReturn.GetReturnOrError();
			if (products != null) {
				products.ForEach(prod => {
					prod.EncodeUrl();
					if (prod.Rankings != null) {
						prod.Rankings.ForEach(r => {
							r.Id = _cryptography.Encrypt(r.Id);
						});
					}
				});
			}
			return products;
			// });
		}

		#endregion

		#region Pesquisa de Produtos

		public async Task<SearchModel> SearchProducts(Guid campaignId, Guid userId, Guid participantId,
            Guid? departmentId, string query, string[] departments, string[] categories, string[] subcategories,
            string[] partners, string[] manufacturers, string[] colors, string[] voltagens, string productType = null,
            string sortBy = null, decimal? fromPoints = null, decimal? toPoints = null, decimal? balance = null,
            int from = 0, int size = 8)
        {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("products/search")
				.Query(USER_ID_QUERY_NAME, userId)
				.Query(PARTICIPANT_ID_QUERY_NAME, participantId)
				.Query("dpi", departmentId.HasValue ? departmentId.Value.ToString() : null)
				.Query("q", query)
				.Query("dp", string.Join("|", departments ?? Array.Empty<string>()))
				.Query("ct", string.Join("|", categories ?? Array.Empty<string>()))
				.Query("sb", string.Join("|", subcategories ?? Array.Empty<string>()))
				.Query("pr", string.Join("|", partners ?? Array.Empty<string>()))
				.Query("mn", string.Join("|", manufacturers ?? Array.Empty<string>()))
				.Query("cr", string.Join("|", colors ?? Array.Empty<string>()))
				.Query("vl", string.Join("|", voltagens ?? Array.Empty<string>()))
				.Query("srt", sortBy)
				.Query("fp", fromPoints.HasValue ? fromPoints.Value.ToString() : "")
				.Query("tp", toPoints.HasValue ? toPoints.Value.ToString() : "")
				.Query("balance", balance)
				.Query("fr", from.ToString())
				.Query("sz", size.ToString())
				.Query("prt", productType)
				.AsyncGet()
				.GetApiReturn<SearchModel>();

			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os produtos da campanha.");

			var searchResult = apiReturn.GetReturnOrError();
			if (searchResult != null && searchResult.HasResult()) {
				searchResult.Result.ForEach(prod => {
					prod.EncodeUrl();
					if (prod.Rankings != null) {
						prod.Rankings.ForEach(r => {
							r.Id = _cryptography.Encrypt(r.Id);
						});
					}
				});
			}

			return searchResult;
		}

		#endregion

		#region Departamentos e Categorias

		public async Task<DepartmentModel> GetDepartmentById(Guid campaignId, Guid departmentId, Guid participantId) {
			return await cache.GetOrCreate($"web-cmp-{campaignId}-dpt-{departmentId}", async () => {
				var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
					.Path("catalogs").Path(campaignId).Path("departments").Path(departmentId)
					.Query(PARTICIPANT_ID_QUERY_NAME, participantId)
					.AsyncGet()
					.Timeout(20_000)
					.GetApiReturn<DepartmentModel>();
				if (apiReturn == null)
					throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o departamento.");
				return apiReturn.GetReturnOrError();
			}, 300);
		}

		public async Task<CategoryModel> GetCategoryById(Guid campaignId, Guid categoryId, Guid participantId) {
			return await cache.GetOrCreate($"web-cmp-{campaignId}-cat-{categoryId}", async () => {
				var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
					.Path("catalogs").Path(campaignId).Path("departments/categories").Path(categoryId)
					.Query(PARTICIPANT_ID_QUERY_NAME, participantId)
					.AsyncGet()
					.Timeout(20_000)
					.GetApiReturn<CategoryModel>();
				if (apiReturn == null)
					throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar a categoria.");
				return apiReturn.GetReturnOrError();
			}, 300);
		}

		public Task<List<ProductShowcaseModel>> GetMostRedeemedProducts(Guid campaignId, Guid userId, Guid participantId, Guid departmentId, decimal? balance = null) {
			return GetProductsByDepartment(campaignId, userId, participantId, departmentId, balance);
		}

		public Task<List<ProductShowcaseModel>> GetMostSeenProducts(Guid campaignId, Guid userId, Guid participantId, Guid departmentId, decimal? balance = null) {
			return GetProductsByDepartment(campaignId, userId, participantId, departmentId, balance);
		}

		public async Task<List<ProductShowcaseModel>> GetProductsOffersByDepartment(Guid campaignId, Guid userId, Guid participantId, Guid departmentId, decimal? balance = null) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("departments").Path(departmentId).Path("products/offers")
				.Query(USER_ID_QUERY_NAME, userId)
				.Query(PARTICIPANT_ID_QUERY_NAME, participantId)
				.Query("balance", balance)
				.AsyncGet()
				.GetApiReturn<List<ProductShowcaseModel>>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os produtos em oferta.");
			var products = apiReturn.GetReturnOrError();
			if (products != null) {
				var coinName = await _campaignRepository.GetCoinName(campaignId);
				products.ForEach(prod => {
					prod.CoinName = coinName;
					prod.EncodeUrl();
					if (prod.Rankings != null) {
						prod.Rankings.ForEach(r => {
							r.Id = _cryptography.Encrypt(r.Id);
						});
					}
				});
			}
			return products;
		}

		public async Task<List<ProductShowcaseModel>> GetProductsByDepartment(Guid campaignId, Guid userId, Guid participantId, Guid departmentId, decimal? balance = null) {
			// return await cache.GetOrCreate($"web-cmp-{campaignId}-dtp-{departmentId}-prods", async () => {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("departments").Path(departmentId).Path("products")
				.Query(USER_ID_QUERY_NAME, userId)
				.Query(PARTICIPANT_ID_QUERY_NAME, participantId)
				.Query("balance", balance)
				.AsyncGet()
				.GetApiReturn<List<ProductShowcaseModel>>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os produtos em oferta.");
			var products = apiReturn.GetReturnOrError();
			if (products != null) {
				var coinName = await _campaignRepository.GetCoinName(campaignId);
				products.ForEach(prod => {
					prod.CoinName = coinName;
					prod.EncodeUrl();
					if (prod.Rankings != null) {
						prod.Rankings.ForEach(r => {
							r.Id = _cryptography.Encrypt(r.Id);
						});
					}
				});
			}
			return products;
			// });
		}

		#endregion

		#region Fale Conosco

		public async Task<bool> SendContactUsFormMessage(Guid campaignId, ContactUsModel contact) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("contactus/form")
				.Entity(new {
					UserId = contact.UserId,
					Name = contact.Name,
					Document = contact.Document,
					Email = contact.Email,
					Phone = contact.Phone,
					Cellphone = contact.Cellphone,
					SubjectId = contact.Subject,
					Message = contact.Message
				})
				.AsyncPost()
				.GetApiReturn<bool>();
			return apiReturn.GetReturnOrError();
		}

		#endregion

		#region Carrinho

		/**
		Mapear novas props de preço dinamico do produto **/
		public async Task CalculateCart(Guid campaignId, Guid userId, Guid participantId, CartModel cart) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("users").Path(userId).Path("shoppingcart")
				.Query(USER_ID_QUERY_NAME, userId)
				.Query(PARTICIPANT_ID_QUERY_NAME, participantId)
				.Entity(new {
					sessionId = cart.SessionId,
					campaignId = cart.CampaignId,
					type = cart.Type,
					userId = userId,
					cep = cart.Cep,
					discountCoupon = cart.DiscountCoupon,
					childrenCarts = cart.ChildrenCarts.Select(pc => new {
						itemGrouperId = pc.ItemGrouperId,
						products = pc.Products.Select(i => new {
							ProductId = i.ProductId,
							SkuId = i.SkuId,
							ElasticId = i.ElasticId,
							Offline = i.Offline,
							ProcessType = i.ProcessType,
							ProductType = i.ProductType,
							LayoutType = i.LayoutType,
							SkuCode = i.SkuCode,
							SkuModel = i.SkuModel,
							UnitPrices = i.UnitPrices,
							Quantity = i.Quantity,
							StockParticipantGroupId = i.StockParticipantGroupId,
							DepartmentId = i.DepartmentId,
							CategoryId = i.CategoryId,
							SubcategoryId = i.SubcategoryId,
							PartnerSettings = i.PartnerSettings,
							DynamicPrice = i.DynamicPrice,
							DynamicPricingSetter = i.DynamicPricingSetter,
							DynamicPriceMinimumValue = i.DynamicPriceMinimumValue,
							DynamicPriceMaximumValue = i.DynamicPriceMaximumValue,
							EnableCustomPromotionalPrice = i.EnableCustomPromotionalPrice
						}).ToList()
					}).ToList()
				})
				.Timeout(65_000)
				.AsyncPost()
				.GetApiReturn<CartModel>();
			if (apiReturn == null || apiReturn.HasNullReturn())
				throw MotivaiException.ofValidation("Não foi possível calcular o carrinho.");
			var calculatedCart = apiReturn.GetReturnOrError();
			// Reseta as notificações e erros
			cart.ClearPartnersCartsErrors();
			if (calculatedCart.OccurredError) {
				cart.SetError(calculatedCart.ErrorMessage);
			} else {
				cart.ErrorMessage = calculatedCart.ErrorMessage;
			}
			cart.Notification = calculatedCart.Notification;
			if (calculatedCart.Notifications != null) {
				calculatedCart.Notifications.ForEach(not => cart.Notifications.Add(not));
			}
			calculatedCart.ChildrenCarts.ForEach(calcPartnerCart => {
				var partnerCart = cart.ChildrenCarts.FirstOrDefault(pc => pc.ItemGrouperId == calcPartnerCart.ItemGrouperId);
				if (partnerCart == null) return;
				partnerCart.ShippingCost = calcPartnerCart.ShippingCost;
				partnerCart.DetailedShippingCost = calcPartnerCart.DetailedShippingCost;
				partnerCart.EstimatedDeliveryDays = calcPartnerCart.EstimatedDeliveryDays;
				partnerCart.OccurredError = calcPartnerCart.OccurredError;
				partnerCart.ErrorMessage = calcPartnerCart.ErrorMessage;
				calcPartnerCart.Products.ForEach(calcProd => {
					var prod = partnerCart.Products.FirstOrDefault(p => p.ElasticId == calcProd.ElasticId && p.SkuCode == calcProd.SkuCode);
					if (prod == null) return;
					prod.Available = calcProd.Available;
					prod.DetailedPrice = calcProd.DetailedPrice;
					prod.ShippingCost = calcProd.ShippingCost;
					prod.DetailedShippingCost = calcProd.DetailedShippingCost;
					prod.EstimatedDeliveryDays = calcProd.EstimatedDeliveryDays;
					prod.OccurredError = calcProd.OccurredError;
					prod.ErrorMessage = calcProd.ErrorMessage;
					prod.Notifications = calcProd.Notifications;
					prod.Discount = calcProd.Discount;
					if (calcProd.PriceUpdate) {
						prod.PriceUpdate = true;
						prod.UnitPrices = calcProd.UnitPrices;
					}
				});
			});
			cart.Discount = calculatedCart.Discount;
			// Se retornar sem cupom significa que não é válido
			if (string.IsNullOrEmpty(calculatedCart.DiscountCoupon)) {
				cart.DiscountCoupon = null;
			}
			cart.EstimatedDeliveryDays = calculatedCart.EstimatedDeliveryDays;
		}

		public async Task<ShippingCostResult> CalculateShippingForItem(Guid campaignId, Guid userId,
			Guid participantId, string elasticId, string skuCode, int quantity, string shippingCep) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("users").Path(userId).Path("shoppingcart/item")
				.Query(USER_ID_QUERY_NAME, userId)
				.Query(PARTICIPANT_ID_QUERY_NAME, participantId)
				.Entity(new {
					cep = shippingCep,
					elasticsearchId = elasticId,
					skuCode = skuCode,
					quantity = quantity
				})
				.AsyncPost()
				.Timeout(35_000)
				.GetApiReturn<ShippingCostResult>();
			if (apiReturn == null || apiReturn.HasNullReturn())
				throw MotivaiException.ofValidation("Não foi possível calcular o frete do produto.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<ProductFactory>> GetFactoriesForItem(Guid campaignId, Guid userId,
				Guid participantId, string elasticId, string skuCode, Guid shippingAddressId, string shippingCep) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("users").Path(userId).Path("shoppingcart/item/factories")
				.Query(USER_ID_QUERY_NAME, userId)
				.Query(PARTICIPANT_ID_QUERY_NAME, participantId)
				.Entity(new {
					shippingAddressId = shippingAddressId,
					cep = shippingCep,
					elasticsearchId = elasticId,
					skuCode = skuCode,
				})
				.AsyncPost()
				.GetApiReturn<List<ProductFactory>>();
			if (apiReturn == null || apiReturn.HasNullReturn())
				throw MotivaiException.ofValidation("Não foi possível carregar a fábrica do produto.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<string> IssueSecurityToken(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("users").Path(userId).Path("shoppingcart/securitytoken")
				.AsyncPost()
				.GetApiReturn<string>();
			if (apiReturn == null || apiReturn.HasNullReturn())
				throw MotivaiException.ofValidation("Não foi possível enviar o token de segurança.");
			return apiReturn.GetReturnOrError();
		}
		#endregion

		#region Pedidos
		public async Task CreateOrder(Guid campaignId, CartModel cart) {
			cart.ClearPartnersCartsErrors();
			cart.ClearNotifications();
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("orders")
				.Entity(cart)
				.Timeout(65_000)
				.AsyncPost()
				.LogPayloadToLogger()
				.GetApiReturn<CartModel>();
			if (apiReturn == null || apiReturn.HasNullReturn())
				throw MotivaiException.ofValidation("Não foi possível criar o pedido.");
			var createdOrder = apiReturn.GetReturnOrError();
			cart.OrderId = createdOrder.OrderId;
			cart.InternalOrderNumber = createdOrder.InternalOrderNumber;
			cart.ShippingAddress = createdOrder.ShippingAddress;
			cart.Notifications = createdOrder.Notifications;
			createdOrder.ChildrenCarts.ForEach(calcPartnerCart => {
				var partnerCart = cart.ChildrenCarts.FirstOrDefault(pc => pc.ItemGrouperId == calcPartnerCart.ItemGrouperId);
				if (partnerCart == null) return;
				partnerCart.ShippingCost = calcPartnerCart.ShippingCost;
				partnerCart.DetailedShippingCost = calcPartnerCart.DetailedShippingCost;
				partnerCart.EstimatedDeliveryDays = calcPartnerCart.EstimatedDeliveryDays;
				partnerCart.OccurredError = calcPartnerCart.OccurredError;
				partnerCart.ErrorMessage = calcPartnerCart.ErrorMessage;
				calcPartnerCart.Products.ForEach(calcProd => {
					var prod = partnerCart.Products.FirstOrDefault(p => p.ElasticId == calcProd.ElasticId);
					if (prod == null) return;
					prod.Available = calcProd.Available;
					prod.ShippingCost = calcProd.ShippingCost;
					prod.DetailedShippingCost = calcProd.DetailedShippingCost;
					prod.EstimatedDeliveryDays = calcProd.EstimatedDeliveryDays;
					prod.OccurredError = calcProd.OccurredError;
					prod.ErrorMessage = calcProd.ErrorMessage;
					prod.Notifications = calcProd.Notifications;
					if (calcProd.PriceUpdate) {
						prod.PriceUpdate = true;
						prod.UnitPrices = calcProd.UnitPrices;
					}
				});
			});
		}

		public async Task<CartModel> GetOrder(Guid campaignId, Guid userId, Guid participantId, Guid orderId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("orders").Path(orderId)
				.Query(USER_ID_QUERY_NAME, userId)
				.Query(PARTICIPANT_ID_QUERY_NAME, participantId)
				.AsyncGet()
				.GetApiReturn<CartModel>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível consultar o pedido.");
			var order = apiReturn.GetReturnOrError();
			if (order != null) {
				order.ChildrenCarts.ForEach(cart => {
					if (cart.OccurredError && cart.Products != null) {
						cart.Products.ForEach(p => {
							if (!p.OccurredError) {
								p.OccurredError = cart.OccurredError;
								p.ErrorMessage = cart.ErrorMessage;
							}
						});
					}
				});
			}
			return order;
		}

		public async Task<bool?> PriceItems(Guid campaignId, FactoryOrderPrice order) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("orders").Path(order.OrderId).Path("items/price")
				.Entity(order)
				.Timeout(60_000)
				.AsyncPut()
				.GetApiReturn<bool?>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível precificar os itens do pedido.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> ApproveOrder(Guid campaignId, Guid orderId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("orders").Path(orderId).Path("approve")
				.AsyncPut()
				.GetApiReturn<bool?>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível aprovar o pedido, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> RefuseOrder(Guid campaignId, Guid orderId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("orders").Path(orderId).Path("refuse")
				.AsyncPut()
				.GetApiReturn<bool?>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível reprovar o pedido, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}
		#endregion

		#region Compra de Pontos

		public async Task<List<Entry<decimal, decimal>>> GetPointsOptionsToBuy(Guid campaignId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("points-purchase/options")
				.Timeout(60_000)
				.AsyncGet()
				.GetApiReturn<List<Entry<decimal, decimal>>>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as opções de pontos disponíveis para compra.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<PaymentOptions> GetPaymentOptionsFor(Guid campaignId, decimal pointsNeeded) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("points-purchase/payments")
				.Query("pointsNeeded", pointsNeeded.ToString())
				.Timeout(60_000)
				.AsyncGet()
				.GetApiReturn<PaymentOptions>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as opções de compra.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> CompletePointsPurchase(Guid campaignId, UserPrincipal participant, PointsPurchaseOrder pointsPurchaseOrder) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("points-purchase/payments")
				.Entity(new {
					UserId = participant.UserId,
					ParticipantId = participant.ParticipantId,
					AccountOperator = participant.GetAccountOperator(),
					LocationInfo = pointsPurchaseOrder.LocationInfo,
					PointsToBuy = pointsPurchaseOrder.Ticket.PointsToBuy,
					CurrencyValue = pointsPurchaseOrder.Ticket.CurrencyValue,
					PercentageAdditionalFee = pointsPurchaseOrder.Ticket.PercentageAdditionalFee,
					PointsConversionFactor = pointsPurchaseOrder.Ticket.PointsConversionFactor,
					EnableAntiFraud = pointsPurchaseOrder.Ticket.EnableAntiFraud,
					Name = pointsPurchaseOrder.Name,
					Cpf = pointsPurchaseOrder.Cpf,
					Email = pointsPurchaseOrder.Email,
					Phone = pointsPurchaseOrder.Phone,
					Installment = pointsPurchaseOrder.Installment.GetPaymentInstallment(),
					BillingAddress = pointsPurchaseOrder.BillingAddress,
					CardToken = pointsPurchaseOrder.CardToken
				})
				.Timeout(60_000)
				.AsyncPost()
				.LogPayloadToLogger()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi solicitar o processamento da compra, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}

		#endregion

		#region Recarga de Celular
		public async Task<List<MobileOperator>> GetOperatorsByDdd(Guid campaignId, string ddd) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("extraservices/mobiles/ddds").Path(ddd).Path("operators")
				.Timeout(60_000)
				.AsyncGet()
				.GetApiReturn<List<MobileOperator>>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as opções de compra.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<MobileOperatorOption>> GetOperatorOptions(Guid campaignId, string ddd, string providerId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("extraservices/mobiles/ddds").Path(ddd)
				.Path("operators").Path(providerId).Path("options")
				.Timeout(60_000)
				.AsyncGet()
				.GetApiReturn<List<MobileOperatorOption>>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as opções de compra.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<OperationTicket> IssueRechargeMobileTicket(Guid campaignId, MobileRecharge recharge) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("extraservices/mobiles/recharge/issue")
				.Entity(new {
					recharge.UserId,
					recharge.ParticipantId,
					recharge.AccountOperator,
					recharge.LocationInfo,
					CellphoneCountryCode = "+55",
					recharge.CellphoneDdd,
					CellphoneNumber = recharge.CellphoneNumber.Replace("-", ""),
					OperatorId = recharge.Operator,
					OperatorName = recharge.OperatorName,
					PointsFactor = recharge.Option.PointsFactor,
					RechargeCost = recharge.Option.RechargeCost,
					ParticipantCost = recharge.Option.ParticipantCost
				})
				.Timeout(60_000)
				.AsyncPost()
				.GetApiReturn<OperationTicket>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível efetuar a recarga, por favor, tente novamente.");
			var result = apiReturn.GetReturnOrError();
			if (result == null)
				throw MotivaiException.ofValidation("Não foi possível efetuar a recarga, por favor, tente novamente.");
			return result;
		}

		public async Task<ConfirmationResult> ConfirmRechargeMobile(Guid campaignId, ConfirmationTicket ticket, bool confirmRecharge) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("extraservices/mobiles/recharge/confirm")
				.Entity(new {
					confirm = confirmRecharge,
					ticket.UserId,
					ticket.ParticipantId,
					ticket.AccountOperator,
					ticket.LocationInfo,
					ticket.CellphoneDdd,
					CellphoneNumber = ticket.CellphoneNumber.Replace("-", ""),
					ticket.OperatorName,
					ticket.RechargeValue,
					CatalogExtraServiceId = ticket.CatalogExtraServiceId,
					Protocol = ticket.Protocol,
					ticket.ProofPayment,
					ParticipantCost = ticket.ParticipantCost
				})
				.Timeout(60_000)
				.AsyncPut()
				.LogPayloadToLogger()
				.LogResponseToLogger()
				.GetApiReturn<ConfirmationResult>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível efetuar a recarga, por favor, tente novamente.");
			var result = apiReturn.GetReturnOrError();
			if (result == null)
				throw MotivaiException.ofValidation("Não foi possível efetuar a recarga, por favor, tente novamente.");
			return result;
		}
		#endregion

		#region Pague Contas
		public async Task<BillDetails> GetBillDetails(Guid campaignId, UserPrincipal participant, string barCode) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("extraservices/bills").Path(barCode).Path("details")
				.Query(USER_ID_QUERY_NAME, participant.UserId)
				.Query("userDocument", participant.Document)
				.Query("userAccountOperatorDocument", participant.AccountOperatorDocument)
				.Query("accountOperatorId", participant.AccountOperatorId)
				.Query("accountOperatorLoginId", participant.AccountOperatorLoginId)
				.Timeout(60_000)
				.AsyncGet()
				.GetApiReturn<BillDetails>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os detalhes da conta para pagamento.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<OperationTicket> IssueBillPaymentTicket(Guid campaignId, BillDetails bill) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("extraservices/bills/issue")
				.Entity(bill)
				.Timeout(60_000)
				.AsyncPost()
				.GetApiReturn<OperationTicket>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível efetuar o pagamento da conta, por favor, tente novamente.");
			var result = apiReturn.GetReturnOrError();
			if (result == null)
				throw MotivaiException.ofValidation("Não foi possível efetuar o pagamento da conta, por favor, tente novamente.");
			return result;
		}

		public async Task<ConfirmationResult> ConfirmBillPayment(Guid campaignId, ConfirmationTicket ticket, bool confirmPayment) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("extraservices/bills/confirm")
				.Entity(new {
					confirm = confirmPayment,
					ticket.UserId,
					ticket.ParticipantId,
					ticket.AccountOperator,
					ticket.LocationInfo,
					ticket.Assignor,
					ticket.BarCode,
					ticket.BillingAmount,
					ticket.BillDetailsQueryPartner,
					ticket.BillPaymentPartner,
					CatalogExtraServiceId = ticket.CatalogExtraServiceId,
					Protocol = ticket.Protocol,
					ticket.ProofPayment,
					ParticipantCost = ticket.ParticipantCost
				})
				.Timeout(60_000)
				.AsyncPut()
				.LogPayloadToLogger()
				.LogResponseToLogger()
				.GetApiReturn<ConfirmationResult>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível confirmar o pagamento da conta, por favor, tente novamente.");
			var result = apiReturn.GetReturnOrError();
			if (result == null)
				throw MotivaiException.ofValidation("Não foi possível confirmar o pagamento da conta, por favor, tente novamente.");
			return result;
		}

		public async Task<ScheduledPaymentReceipt> ScheduleBillPayment(Guid campaignId, BillDetails billDetails) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("extraservices/billpayments/schedule")
				.Entity(billDetails)
				.Timeout(60_000)
				.AsyncPost()
				.LogPayloadToLogger()
				.LogResponseToLogger()
				.GetApiReturn<ScheduledPaymentReceipt>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível agendar o pagamento da conta, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}
		#endregion

		#region Cartão Pré-Pago
		public async Task<dynamic> GetCardsPageConfiguration(Guid campaignId, PrepaidCardType cardType) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("extraservices/prepaidcards/configuration")
				.Query("cardType", cardType.ToString())
				.AsyncGet()
				.Timeout(10_000)
				.GetApiReturn<dynamic>();
			if (apiReturn == null || apiReturn.HasNullReturn())
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível efetuar carregar as configurações, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> CalculateCardOrderFees(Guid campaignId, dynamic order) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("extraservices/prepaidcards/orders/fees")
				.Entity(order)
				.AsyncPost()
				.Timeout(30_000)
				.GetApiReturn<dynamic>();
			if (apiReturn == null || apiReturn.HasNullReturn())
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível efetuar o cálculo, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> CreateCardOrder(Guid campaignId, dynamic order) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("extraservices/prepaidcards/orders")
				.Entity(order)
				.AsyncPost()
				.LogPayloadToLogger()
				.LogResponseToLogger()
				.Timeout(30_000)
				.GetApiReturn<dynamic>();
			if (apiReturn == null || apiReturn.HasNullReturn())
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível solicitar a transferência, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> GetCardOrder(Guid campaignId, Guid orderId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("extraservices/prepaidcards/orders").Path(orderId)
				.AsyncGet()
				.Timeout(30_000)
				.GetApiReturn<dynamic>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o pedido, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<PrepaidCardParametrizations> GetCardParametrizations(Guid campaignId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId)
				.Path("extraservices/prepaidcards/parametrizations")
				.AsyncGet()
				.Timeout(30_000)
				.GetApiReturn<PrepaidCardParametrizations>();

			if (apiReturn == null) {
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as parametrizações do cartão.");
			}
			return apiReturn.GetReturnOrError();
		}

		#endregion

		#region Transferência Bancária

		public async Task<dynamic> GetBankTransferConfiguration(Guid campaignId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("extraservices/banktransfer/configurations")
				.AsyncGet()
				.Timeout(10_000)
				.GetApiReturn<dynamic>();
			if (apiReturn == null || apiReturn.HasNullReturn())
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível efetuar carregar as configurações, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> CalculateBankTransferOrderFees(Guid campaignId, dynamic order) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("extraservices/banktransfer/orders/fees")
				.Entity(order)
				.AsyncPost()
				.Timeout(30_000)
				.GetApiReturn<dynamic>();
			if (apiReturn == null || apiReturn.HasNullReturn())
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível efetuar o cálculo, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<string> CreateBankTransferOrder(Guid campaignId, dynamic order) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("extraservices/banktransfer/orders")
				.Entity(order)
				.AsyncPost()
				.Timeout(30_000)
				.GetApiReturn<string>();
			if (apiReturn == null || apiReturn.HasNullReturn())
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível solicitar a transferência, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> GetBankTransferOrderById(Guid campaignId, Guid orderId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("extraservices/banktransfer/orders").Path(orderId)
				.AsyncGet()
				.Timeout(30_000)
				.GetApiReturn<dynamic>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o pedido, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> GetRechargeOrderById(Guid campaignId, Guid orderId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId)
				.Path("extraservices/mobiles/recharge/orders")
				.Path(orderId)
				.AsyncGet()
				.LogPayloadToConsole()
				.GetApiReturn<dynamic>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o pedido, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> GetBillPaymentsOrderById(Guid campaignId, Guid orderId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId)
				.Path("extraservices/billpayments/orders")
				.Path(orderId)
				.AsyncGet()
				.LogPayloadToConsole()
				.GetApiReturn<dynamic>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o pedido, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<dynamic>> ConsultLinkVouchers(Guid campaignId, Guid orderId, Guid itemGrouperId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId)
				.Path("orders").Path(orderId)
				.Path("childrenorders").Path(itemGrouperId).Path("vouchers")
				.AsyncGet()
				.GetApiReturn<List<dynamic>>();

			if (apiReturn == null) {
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os links dos vales, por favor, tente novamente.");
			}
			return apiReturn.GetReturnOrError();
		}

		#endregion

	}
}