using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.WebCatalog.Models.ExtraServices.Cards.Partners;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.WebCatalog.Repositories
{
    public class PartnerPrepaidCardRepository
    {
        private readonly ICryptography cryptography;

        public PartnerPrepaidCardRepository(ICryptography cryptography)
        {
            this.cryptography = cryptography;
        }

        public async Task<PartnerPrepaidCard> FindPrepaidCard(Guid userId, Guid campaignId, string birthDate, string firstDigits, string lastDigits, string expirationDate)
        {
            var response = await HttpClient.Create(MotivaiApi.PrepaidCardsIntegrations)
                .Path("partners/prepaidcards/contaswap/users").Path(userId).Path("campaigns").Path(campaignId).Path("cards/search-one-by-query")
                .Query("birthDate", birthDate)
                .Query("firstDigits", firstDigits)
                .Query("lastDigits", lastDigits)
                .Query("expirationDate", expirationDate)
                .AsyncGet()
                .AcceptStatusCodes(401,404,400)
                .Timeout(30_000)
                .GetApiReturn<PartnerPrepaidCard>();

            var card = response.GetReturnOrError();
            if (card == null) return null;

            card.Id = cryptography.Encrypt(card.Id);
            card.PrepaidCard.Id = cryptography.Encrypt(card.PrepaidCard.Id);
            return card;
        }

        public async Task<List<MaskedPrepaidCard>> FindPrepaidCardsInUse(Guid userId, Guid campaignId)
        {
            var response = await HttpClient.Create(MotivaiApi.PrepaidCardsIntegrations)
                .Path("partners/prepaidcards/contaswap/users").Path(userId).Path("campaigns").Path(campaignId).Path("cards/in-use")
                .AsyncGet()
                .AcceptStatusCodes(401,404,400)
                .Timeout(30_000)
                .GetApiReturn<List<MaskedPrepaidCard>>();

            var cards = response.GetReturnOrError();
            if (cards == null) return null;

            cards.ForEach(r => {
				r.Id = cryptography.Encrypt(r.Id.ToString());
            });

            return cards;
        }

        public async Task<PartnerPrepaidCard> FindPrepaidCardById(Guid userId, Guid campaignId, string encryptedCardId)
        {
            var response = await HttpClient.Create(MotivaiApi.PrepaidCardsIntegrations)
                .Path("partners/prepaidcards/contaswap/users").Path(userId).Path("campaigns").Path(campaignId).Path("cards").Path(cryptography.Decrypt(encryptedCardId))
                .AsyncGet()
                .AcceptStatusCodes(401,404,400)
                .Timeout(30_000)
                .GetApiReturn<PartnerPrepaidCard>();

            var card = response.GetReturnOrError();
            if (card == null) return null;

            card.Id = cryptography.Encrypt(card.Id);
            card.PrepaidCard.Id = cryptography.Encrypt(card.PrepaidCard.Id);
            return card;
        }

        public async Task<dynamic> ActiveCard(Guid userId, Guid campaignId, string encryptedCardId)
		{
			var response = await HttpClient.Create(MotivaiApi.PrepaidCardsIntegrations)
				.Path("partners/prepaidcards/contaswap/users").Path(userId).Path("campaigns").Path(campaignId).Path("cards").Path(cryptography.Decrypt(encryptedCardId)).Path("active")
				.AsyncPut()
                .AcceptStatusCodes(401,404,400)
				.GetApiReturn<dynamic>();

			var activeResponse = response.GetReturnOrError();
			if (activeResponse == null) return null;
			return activeResponse;
		}

		public async Task<dynamic> BlockCard(Guid userId, Guid campaignId, string encryptedCardId)
		{
			var response = await HttpClient.Create(MotivaiApi.PrepaidCardsIntegrations)
				.Path("partners/prepaidcards/contaswap/users").Path(userId).Path("campaigns").Path(campaignId).Path("cards").Path(cryptography.Decrypt(encryptedCardId)).Path("block")
				.AsyncPut()
                .AcceptStatusCodes(401,404,400)
				.GetApiReturn<dynamic>();

			var blockResponse = response.GetReturnOrError();
			if (blockResponse == null) return null;
			return blockResponse;
		}

        public async Task<dynamic> UseCard(Guid userId, Guid campaignId, string encryptedCardId)
		{
			var response = await HttpClient.Create(MotivaiApi.PrepaidCardsIntegrations)
				.Path("partners/prepaidcards/contaswap/users").Path(userId).Path("campaigns").Path(campaignId).Path("cards").Path(cryptography.Decrypt(encryptedCardId)).Path("in-use")
				.AsyncPut()
                .AcceptStatusCodes(401,404,400)
				.GetApiReturn<dynamic>();

			var inUseResponse = response.GetReturnOrError();
			if (inUseResponse == null) return null;
			return inUseResponse;
		}

        public async Task<dynamic> ResetPin(Guid userId, Guid campaignId, string encryptedCardId, PinRequest request)
		{
            if (request == null || request.Pin == null) {
                throw MotivaiException.ofValidation("Obrigatório informar o PIN para alteração.");
            }

			var response = await HttpClient.Create(MotivaiApi.PrepaidCardsIntegrations)
				.Path("partners/prepaidcards/contaswap/users").Path(userId).Path("campaigns").Path(campaignId).Path("cards").Path(cryptography.Decrypt(encryptedCardId)).Path("pin")
                .Entity(request)
				.AsyncPut()
                .AcceptStatusCodes(401,404,400)
				.GetApiReturn<dynamic>();

			var resetPinResponse = response.GetReturnOrError();
			if (resetPinResponse == null) return null;
			return resetPinResponse;
		}

        public async Task<dynamic> GetPrepaidCardStatement(Guid userId, Guid campaignId, string encryptedCardId, string startPeriod, string endPeriod, int limit)
		{
            var response = await HttpClient.Create(MotivaiApi.PrepaidCardsIntegrations)
                .Path("partners/prepaidcards/contaswap/users").Path(userId).Path("campaigns").Path(campaignId).Path("cards").Path(cryptography.Decrypt(encryptedCardId)).Path("statement")
                .Query("startPeriod", startPeriod)
                .Query("endPeriod", endPeriod)
                .Query("limit", limit > 0 ? limit : 200)
				.AsyncGet()
				.AcceptStatusCodes(401,404,400)
				.GetApiReturn<dynamic>();

			var balanceAccountsResponse = response.GetReturnOrError();
			if (balanceAccountsResponse == null) return null;
			return balanceAccountsResponse;
		}

        public async Task<dynamic> GetBalance(Guid userId, Guid campaignId, string encryptedCardId)
		{
			var response = await HttpClient.Create(MotivaiApi.PrepaidCardsIntegrations)
                .Path("partners/prepaidcards/contaswap/users").Path(userId).Path("campaigns").Path(campaignId).Path("cards").Path(cryptography.Decrypt(encryptedCardId)).Path("balance")
				.AsyncGet()
				.AcceptCommonStatusCodes()
				.GetApiReturn<dynamic>();

			var balanceAccountsResponse = response.GetReturnOrError();
			if (balanceAccountsResponse == null) return null;
			return balanceAccountsResponse;
		}

       	public async Task<dynamic> RetrieveCardTracking(string encryptedCardId)
		{
            var response = await HttpClient.Create(MotivaiApi.PrepaidCardsIntegrations)
                .Path("partners/prepaidcards/contaswap/cards").Path(cryptography.Decrypt(encryptedCardId)).Path("tracking")
				.AsyncGet()
				.AcceptCommonStatusCodes()
				.GetApiReturn<dynamic>();

			var tracking = response.GetReturnOrError();
			if (tracking == null) return null;
			return tracking;
		}
    }
}