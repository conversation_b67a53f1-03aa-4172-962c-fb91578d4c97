using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.WebCatalog.Repositories
{
    public class ClientsIntegrationsRepository
    {
        public async Task<string> FetchCampaignIntegrationToken(Guid campaignId)
        {
            var request = await HttpClient.Create(MotivaiApi.ClientsIntegrations)
                .Path("clients/campaigns").Path(campaignId)
                .Path("integration/token")
                .AsyncGet()
                .GetApiReturn<string>();
            if (request == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível trazer o token da campanha.");
            return request.GetReturnOrError();
        }
    }
}
