using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Orders;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Entities.References.Users.FirstAccess;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Models.Addresses;
using Motivai.WebCatalog.Models.ExtraServices.Cards;
using Motivai.WebCatalog.Models.FirstAccess;
using Motivai.WebCatalog.Models.FirstAccess.CampaignAcceptanceResult.CampaignAcceptancesResult;
using Motivai.WebCatalog.Models.FirstAccess.Card;
using Motivai.WebCatalog.Models.FirstAccess.FirstAccessAcceptancesModel;
using Motivai.WebCatalog.Models.MyAccount;
using Motivai.WebCatalog.Models.Order;
using Newtonsoft.Json;

namespace Motivai.WebCatalog.Repositories
{
	public class UserParticipantRepository {
		public async Task<bool> UpdatePassword(Guid userId, Guid campaignId, PasswordUpdate passwordUpdate) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("password")
				.Entity(passwordUpdate)
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível alterar a senha, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<dynamic> UpdateAccountOperatorPassword(Guid accountOperatorId, Guid accountOperatorLoginId, PasswordUpdate passwordUpdate) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("users/operators").Path(accountOperatorId).Path("logins").Path(accountOperatorLoginId).Path("password")
				.Entity(passwordUpdate)
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível atualizar a senha, por favor, tente novamente.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<Motivai.SharedKernel.Domain.Entities.References.Users.ParticipantDataModel> GetPersonalData(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("data")
				.AsyncGet()
				.GetApiReturn<Motivai.SharedKernel.Domain.Entities.References.Users.ParticipantDataModel>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os dados do participante.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<ParticipantContact> GetPrincipalContact(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("users").Path(userId).Path("campaigns").Path(campaignId).Path("contact")
				.AsyncGet()
				.GetApiReturn<ParticipantContact>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o contato.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<FirstAccessResult> RegisterFirstAccess(Guid campaignId, Guid userId, FirstAccessDataModel dataModel) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("firstaccess")
				.Entity(dataModel)
				.AsyncPost()
				.GetApiReturn<FirstAccessResult>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível completar o cadastro.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> UpdatePersonalData(Guid campaignId, Guid userId, Motivai.SharedKernel.Domain.Entities.References.Users.ParticipantDataModel userData) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("data")
				.Entity(userData)
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível salvar os dados.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<Address>> GetShippingAddresses(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("addresses")
				.AsyncGet()
				.GetApiReturn<List<Address>>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os endereços.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<Address>> GetResumedShippingAddresses(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("addresses/resumed")
				.AsyncGet()
				.GetApiReturn<List<Address>>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os endereços.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<CampaignAcceptancesResult> RegisterPrivacyPolicyAcceptance(FirstAccessAcceptancesModel campaignAcceptancesResult, Guid userId, Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("partial/firstaccess")
				.Entity(campaignAcceptancesResult)
				.AsyncPost()
				.GetApiReturn<CampaignAcceptancesResult>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível enviar os dados de privacidade");
			return apiReturn.GetReturnOrError();
		}

		public async Task<Address> GetMainShippingAddress(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("addresses/main")
				.AsyncGet()
				.GetApiReturn<Address>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o endereço principal de entrega.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<Address> CreateAddress(Guid campaignId, Guid userId, AddressUpdate addressUpdate) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("addresses")
				.Entity(addressUpdate)
				.AsyncPost()
				.GetApiReturn<Address>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível cadastrar o endereço.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<Address> GetAddressById(Guid campaignId, Guid userId, Guid shippingAddressId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("addresses").Path(shippingAddressId)
				.AsyncGet()
				.GetApiReturn<Address>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os endereços.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> UpdateAddress(Guid campaignId, Guid userId,  AddressUpdate addressUpdate) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("addresses").Path(addressUpdate.Id)
				.Entity(addressUpdate)
				.AsyncPut()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível atualizar o endereço.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> DeleteAddress(Guid campaignId, Guid userId, Guid addressId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("addresses").Path(addressId)
				.AsyncDelete()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível excluir o endereço.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<string> GetAddressCepById(Guid campaignId, Guid userId, Guid addressId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("addresses").Path(addressId).Path("cep")
				.AsyncGet()
				.GetApiReturn<string>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o CEP do endereço.");
			return apiReturn.GetReturnOrError();
		}

		#region Saldos

		public async Task<decimal> GetAvailableBalance(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("balance")
				.AsyncGet()
				.GetApiReturn<decimal>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o saldo.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<decimal> GetAvailableBalanceByLowestRankings(Guid campaignId, Guid userId, List<Guid> commonRanking) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("balance/rankings")
				.Query("rankings", string.Join(",", commonRanking))
				.AsyncGet()
				.GetApiReturn<decimal>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o saldo.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<bool> SaveUserCard(Guid campaignId, Guid userId, UserCard card)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				 .Path("users").Path(userId)
				 .Path("campaigns").Path(campaignId)
				 .Path("cards")
				 .Entity(card)
				 .AsyncPost()
				 .GetApiReturn<bool>();

			return apiReturn.GetReturnOrError();
		}

		public async Task<decimal> GetReservedBalanceFor(Guid campaignId, Guid userId, Product product) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("users").Path(userId).Path("campaigns").Path(campaignId).Path("reservedbalance")
				.Query("partnerId", product.PartnerId)
				.Query("productId", product.ProductId)
				.Query("skuId", product.SkuId)
				.AsyncGet()
				.GetApiReturn<decimal>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o saldo.");
			return apiReturn.GetReturnOrError();
		}

		#endregion

		public async Task<BalanceResumeModel> GetSummary(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("summary")
				.AsyncGet()
				.GetApiReturn<BalanceResumeModel>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o resumo.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<TransactionSummaryModel>> GetLastAccumulations(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("lastaccumlations")
				.AsyncGet()
				.GetApiReturn<List<TransactionSummaryModel>>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os últimos acúmulos.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<TransactionSummaryModel>> GetLastRedeems(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("lastredeems")
				.AsyncGet()
				.GetApiReturn<List<TransactionSummaryModel>>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os últimos resgates.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<MechanicsSummary> GetExpirePoints(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("expiringpoints")
				.AsyncGet()
				.GetApiReturn<MechanicsSummary>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os pontos a expirar.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<Guid>> GetRankingChildren(Guid campaignId, Guid rankingId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("campaigns").Path(campaignId).Path("participantsrankings").Path(rankingId)
				.AsyncGet()
				.GetApiReturn<List<Guid>>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os rankings do participante.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<BlockedPointsSummary> GetBlockedPoints(Guid campaignId, Guid userId) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("blockedpoints")
				.AsyncGet()
				.GetApiReturn<BlockedPointsSummary>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os pontos bloqueados.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<ExtractModel> GetExtract(Guid campaignId, string userId, Dictionary<string, string> queryParameters, TransactionType? transactionType,
			TransactionOrigin? transactionOrigin, DateTime? startDate, DateTime? endDate) {
			var httpClient = HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("transactions");
			if (transactionType.HasValue)
				httpClient.Query("transactionType", transactionType.Value.ToString());
			if (transactionOrigin.HasValue)
				httpClient.Query("transactionOrigin", transactionOrigin.Value.ToString());
			if (startDate.HasValue)
				httpClient.QueryDateTime("startDate", startDate.Value.AtStartOfDay());
			if (endDate.HasValue)
				httpClient.QueryDateTime("endDate", endDate.Value.AtEndOfDay().AddHours(3));
			var apiReturn = await httpClient
				.AsyncGet()
				.GetApiReturn<ExtractModel>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o extrato.");
			return apiReturn.GetReturnOrError();
		}

		public async Task<List<OrderResumeModel>> GetOrders(Guid campaignId, Guid userId, DateTime? startDate, DateTime? endDate, OrderStatus? status) {
			var httpClient = HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path(campaignId).Path("orders");

			if (startDate.HasValue)
				httpClient.Query("startDate", startDate.Value.ToString("yyyy-MM-dd"));
			if (endDate.HasValue)
				httpClient.Query("endDate", endDate.Value.ToString("yyyy-MM-dd"));
			if (status != null && status.HasValue)
				httpClient.Query("status", status.Value.ToString());

			var apiReturn = await httpClient.AsyncGet().GetApiReturn<List<OrderResumeModel>>();
			if (apiReturn == null) return null;
			return apiReturn.GetReturnOrError();
		}

		public async Task RegisterRegulationAcceptance(Guid campaignId, Guid userId, string regulationId, string version) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("participants").Path(userId).Path("campaigns").Path(campaignId)
				.Path("regulations").Path(regulationId).Path("versions").Path(version)
				.AsyncPost()
				.GetApiReturn<bool>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o resumo.");
			if (!apiReturn.GetReturnOrError())
				throw MotivaiException.ofValidation("Não foi possível registrar o aceite do regulamento, por favor, tente novamente.");
		}

		public async Task<dynamic> UpdatePrivacyPolicyAcceptance(Guid campaignId, Guid userId, PrivacyPolicyChangeModel privacyPolicyChange) {
            var response = await HttpClient.Create(MotivaiApi.Users)
                .Path("users").Path(userId).Path("campaigns").Path(campaignId).Path("participant/privacypolicy/acceptance")
                .Entity(privacyPolicyChange)
                .AsyncPut()
                .GetApiReturn<dynamic>();
            return response.GetReturnOrError();
        }

		public async Task<List<PrepaidCard>> GetParticipantCardsByType(Guid campaignId, Guid userId, PrepaidCardType cardType) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
				.Path("catalogs").Path(campaignId).Path("extraservices/prepaidcards/participants").Path(userId).Path("active")
				.Query("cardType", cardType.ToString())
				.AsyncGet()
				.Timeout(20_000)
				.GetApiReturn<List<PrepaidCard>>();
			if (apiReturn == null)
				throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os cartões pré-pagos.");
			return apiReturn.GetReturnOrError();
		}

		#region Buscar informações da pessoa na Credify
		public async Task<dynamic> SearchPersonByDocument(string document) {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				.Path("users")
				.Path("document").Path(document)
				.Path("search")
				.AsyncGet()
				.GetApiReturn<dynamic>();

			return apiReturn.GetReturnOrError();
		}
		#endregion
	}
}
