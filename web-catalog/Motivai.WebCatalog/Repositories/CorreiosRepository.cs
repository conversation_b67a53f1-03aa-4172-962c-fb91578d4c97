using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Correios;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;

namespace Motivai.WebCatalog.Repositories {
    public class CorreiosRepository {
        public async Task<CorreiosAddress> QueryCep(string cep) {
            try {
                var apiReturn = await HttpClient.Create(MotivaiApi.Correios)
                    .Path("addresses").Path(cep)
                    .AsyncGet()
                    .GetApiReturn<CorreiosAddress>();
                if (apiReturn == null)
                    throw MotivaiException.of(ErrorType.ApiException, "Não foi possível pesquisar o endereço pelo CEP.");
                return apiReturn.GetReturnOrError();
            } catch (Exception ex) {
                await ExceptionLogger.LogException(ex, "Catálogo - Consulta Endereço", $"Erro ao consultar o endereço pelo CEP {cep}.");
                return null;
            }
        }
    }
}