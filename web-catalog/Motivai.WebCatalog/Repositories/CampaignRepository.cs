using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.Catalogs.Domain.Models.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations.Metadata;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Cache;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.WebCatalog.Configuration.Models;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Campaign;
using Motivai.WebCatalog.Models.Catalog;
using Motivai.WebCatalog.Services.Cache;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations.Security;
using Motivai.WebCatalog.Models.Pages;

namespace Motivai.WebCatalog.Repositories
{
    public class CampaignRepository {
        public const string CAMPAIGN_DNS_KEYPREFIX = "dns_";

        private const string CAMPAIGN_SETTINGS_KEYPREFIX = "sets_";
        private const string CAMPAIGN_PAGES_SETTINGS_KEYPREFIX = "pags_";
        private const string CAMPAIGN_FOOTER_SETTINGS_KEYPREFIX = "footer_";

        private readonly CacheSettings _cacheSettings;
        private readonly IMemoryCache _cache;
        private readonly CustomCache customCache;

        public CampaignRepository(IMemoryCache cache, CustomCache customCache, IOptions<CacheSettings> cacheSettings) {
            this._cache = cache;
            this.customCache = customCache;
            this._cacheSettings = cacheSettings.Value != null ? cacheSettings.Value : CacheSettings.Default();
        }

        public async Task<CampaignCustomization> GetCampaignCustomization(Guid campaignId) {
            return await customCache.GetOrCreate(SharedCacheKeysPrefix.WEB_CAMPAIGN_CATALOG_CUSTOMIZATION + campaignId, async() => {
                var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                    .Path("catalogs").Path(campaignId).Path("customization")
                    .AsyncGet()
                    .Timeout(30_000)
                    .GetApiReturn<CampaignCustomization>();
                if (apiReturn == null)
                    throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar a customização da campanha.");
                return apiReturn.GetReturnOrError();
            });
        }

        public async Task<CampaignIntegrationSettings> GetCampaignIntegrationSettings(Guid campaignId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("settings/integration")
                .AsyncGet()
                .GetApiReturn<CampaignIntegrationSettings>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações de integração da campanha.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<CampaignSettingsModel> GetCampaignParametrizations(Guid campaignId) {
            return await customCache.GetOrCreate(SharedCacheKeysPrefix.WEB_CAMPAIGN_SETTINGS + campaignId, async () => {
                var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                    .Path("campaigns").Path(campaignId).Path("settings/params")
                    .AsyncGet()
                    .GetApiReturn<CampaignSettingsModel>();
                if (apiReturn == null)
                    throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações da campanha.");
                return apiReturn.GetReturnOrError();
            });
        }

        public async Task<string> GetCampaignSiteUrl(Guid campaignId) {
            return await customCache.GetOrCreate(SharedCacheKeysPrefix.WEB_CAMPAIGN_SITE_URL + campaignId, async () => {
                var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                    .Path("campaigns").Path(campaignId).Path("site/url")
                    .AsyncGet()
                    .GetApiReturn<string>();
                if (apiReturn == null)
                    throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar a url do site campanha.");
                return apiReturn.GetReturnOrError();
            }, 3600);
        }

        public async Task<Guid> GetCampaignIdByDomain(string domain) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs).Path("catalogs/campaigns/id")
                .Query("domain", domain)
                .AsyncGet()
                .Timeout(30_000)
                .GetApiReturn<Guid>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível verificar a campanha a partir da URL.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<Guid> GetThemeByCampaign(Guid campaignId) {
            return await customCache.GetOrCreate(SharedCacheKeysPrefix.WEB_CAMPAIGN_CATALOG_THEME + campaignId, async() => {
                try {
                    var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                        .Path("catalogs").Path(campaignId).Path("theme")
                        .AsyncGet()
                        .Timeout(10_000)
                        .GetApiReturn<Guid>();
                    if (apiReturn == null)
                        throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o tema da campanha.");
                    return apiReturn.GetReturnOrError();
                } catch (Exception ex) {
                    await ExceptionLogger.LogException(ex, "Catálogo - Tema", $"Erro ao carregar tema da campanha {campaignId}");
                    return Guid.Empty;
                }
            });
        }

        public async Task<List<MenuModel>> GetCampaignMenu(Guid campaignId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("menu")
                .AsyncGet()
                .GetApiReturn<List<MenuModel>>();
            if (apiReturn == null) {
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o menu da campanha.");
            }
            return apiReturn.GetReturnOrError();
        }

        public async Task<CoinName> GetCoinName(Guid campaignId) {
            return await customCache.GetOrCreate(SharedCacheKeysPrefix.WEB_CAMPAIGN_COINNAME + campaignId, async() => {
                try {
                    var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                        .Path("catalogs").Path(campaignId).Path("coinname")
                        .Timeout(40_000)
                        .AsyncGet()
                        .GetApiReturn<CoinName>();
                    if (apiReturn == null)
                        throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar a moeda da campanha.");
                    return apiReturn.GetReturnOrError();
                } catch (Exception ex) {
                    await ExceptionLogger.LogException(ex, "Catálogo - Moeda", $"Erro ao carregar moeda da campanha {campaignId}");
                    return CoinName.Of("", "");
                }
            });
        }

        private async Task<CampaignSettingsModel> GetCampaignCurrentSettings(Guid campaignId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("settings")
                .AsyncGet()
                .GetApiReturn<CampaignSettingsModel>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações da campanha.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<CampaignSettingsModel> GetCampaignSettings(Guid campaignId) {
            return await customCache.GetOrCreate(SharedCacheKeysPrefix.WEB_CAMPAIGN_SETTINGS + campaignId, async() => {
                CampaignSettingsModel settings = null;
                try {
                    settings = await GetCampaignCurrentSettings(campaignId);
                    if (settings != null) {
                        _cache.Set(CAMPAIGN_SETTINGS_KEYPREFIX + campaignId, settings, TimeSpan.FromHours(6));
                    }
                } catch (Exception ex) {
                    await ExceptionLogger.LogException(ex, "Catalogo - Config", $"Erro carregar config da campanha {campaignId}");
                    // Se der erro carrega o tema mais antigo
                    _cache.TryGetValue(CAMPAIGN_SETTINGS_KEYPREFIX + campaignId, out settings);
                }
                if (settings == null)
                    throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações da campanha.");
                return settings;
            });
        }

        private async Task<CampaignCatalogSettings> GetCurrentPageSettings(Guid campaignId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("settings/pages")
                .AsyncGet()
                .GetApiReturn<CampaignCatalogSettings>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações da campanha.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<CampaignCatalogSettings> GetPagesSettings(Guid campaignId) {
            return await customCache.GetOrCreate(SharedCacheKeysPrefix.WEB_CAMPAIGN_CATALOG_PAGES_SETTINGS + campaignId, async() => {
                CampaignCatalogSettings settings = null;
                try {
                    settings = await GetCurrentPageSettings(campaignId);
                    _cache.Set(CAMPAIGN_PAGES_SETTINGS_KEYPREFIX + campaignId, settings, TimeSpan.FromHours(6));
                } catch (Exception ex) {
                    await ExceptionLogger.LogException(ex, "Catálogo - Page Settings", "Erro ao carregar pages settings");
                    // Se der erro carrega o tema mais antigo
                    _cache.TryGetValue(CAMPAIGN_PAGES_SETTINGS_KEYPREFIX + campaignId, out settings);
                }
                if (settings == null)
                    throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações das páginas.");
                return settings;
            });
        }

        public async Task<CampaignFooterSettings> GetFooterSettings(Guid campaignId) {
            return await customCache.GetOrCreate(SharedCacheKeysPrefix.WEB_CAMPAIGN_CATALOG_FOOTER_SETTINGS + campaignId, async() => {
                CampaignFooterSettings footerSettings = null;
                try {
                    footerSettings = await GetCurrentFooterSettings(campaignId);
                    _cache.Set(CAMPAIGN_FOOTER_SETTINGS_KEYPREFIX + campaignId, footerSettings, TimeSpan.FromHours(6));
                } catch (Exception ex) {
                    await ExceptionLogger.LogException(ex, "Catálogo - Footer Settings", "Erro ao carregar footer settings");
                    // Se der erro carrega o tema mais antigo
                    _cache.TryGetValue(CAMPAIGN_FOOTER_SETTINGS_KEYPREFIX + campaignId, out footerSettings);
                }
                if (footerSettings == null)
                    throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações do rodapé.");
                return footerSettings;
            });
        }

        private async Task<CampaignFooterSettings> GetCurrentFooterSettings(Guid campaignId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("settings/footer")
                .AsyncGet()
                .GetApiReturn<CampaignFooterSettings>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações do rodapé da campanha.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<ParticipantData> GetFirstAccessSettings(Guid campaignId) {
            return await _cache.GetOrCreateAsync(SharedCacheKeysPrefix.CAMPAIGN_FIRST_ACCESS_SETTINGS + campaignId, async entry => {
                var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                    .Path("campaigns").Path(campaignId).Path("settings/firstaccess")
                    .AsyncGet()
                    .GetApiReturn<ParticipantData>();
                if (apiReturn == null)
                    throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações da campanha.");
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1);
                return apiReturn.GetReturnOrError();
            });
        }

        public async Task<dynamic> GetFirstAccessPrivacyPolicy(Guid campaignId, Guid userId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("institutional/privacypolicy")
                .Query("uid", userId)
                .AsyncGet()
                .Timeout(30_000)
                .GetApiReturn<dynamic>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar a política de privacidade da campanha.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<List<dynamic>> GetCampaignUsersMetadataHeaders(Guid campaignId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("settings/metadata/fields")
                .Query("platformPage", PlatformPage.FIRST_ACCESS.ToString())
                .AsyncGet()
                .GetApiReturn<List<dynamic>>();
            return apiReturn.GetReturnOrError();
        }

        public async Task<dynamic> GetRegulation(Guid campaignId, Guid userId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("institutional/regulation")
                .Query(CatalogRepository.USER_ID_QUERY_NAME, userId)
                .AsyncGet()
                .Timeout(30_000)
                .GetApiReturn<dynamic>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o regulamento da campanha.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<dynamic> GetPrivacyPolicy(Guid campaignId, Guid userId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("institutional/privacypolicy")
                .Query("uid", userId)
                .AsyncGet()
                .Timeout(30_000)
                .GetApiReturn<dynamic>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar a política de privacidade da campanha.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<List<CampaignsCatalogPageCustomization>> GetCampaignCatalogPageCustomizations(Guid campaignId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("catalog/page/customization")
                .AsyncGet()
                .Timeout(30_000)
                .GetApiReturn<List<CampaignsCatalogPageCustomization>>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar a customização da página.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<dynamic> GetShippingPolicy(Guid campaignId, Guid userId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("institutional/shippingpolicy")
                .Query("uid", userId)
                .AsyncGet()
                .Timeout(30_000)
                .GetApiReturn<dynamic>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar a política de entrega da campanha.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<MediaBoxModel> GetCommunicationById(Guid campaignId, Guid communicationId, Guid participantId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("communications").Path(communicationId)
                .Query(CatalogRepository.PARTICIPANT_ID_QUERY_NAME, participantId)
                .AsyncGet()
                .GetApiReturn<MediaBoxModel>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o regulamento da campanha.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<List<FaqModel>> GetFaqs(Guid campaignId, Guid userId, string term = null) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("institutional/faqs")
                .Query("uid", userId)
                .Query("term", term)
                .AsyncGet()
                .GetApiReturn<List<FaqModel>>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o FAQ da campanha.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<List<SubjectModel>> GetSubjects(Guid campaignId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("contactus/subjects")
                .AsyncGet()
                .GetApiReturn<List<SubjectModel>>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os assuntos do contato.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<string> GetCampaignUrl(Guid campaignId) {
            var response = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId).Path("url")
                .AsyncGet()
                .GetApiReturn<string>();
            return response.GetReturnOrError();
        }

        public async Task<dynamic> GetCampaignPointsMetadataParametrization(Guid campaignId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId)
                .Path("points/metadata/parametrization")
                .Query("headerActive", "true")
                .AsyncGet()
                .GetApiReturn<dynamic>();

            return apiReturn.GetReturnOrError();
        }


		#region Buscando grupos do participante
		public async Task<List<Guid>> GetParticipantGroups(Guid campaignId, Guid userId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId)
                .Path("participantsgroups/ids")
                .Query("userId", userId)
                .AsyncGet()
                .GetApiReturn<List<Guid>>();

            return apiReturn.GetReturnOrError();
        }
		#endregion

        #region PrepaidCard

		public async Task<dynamic> GetCardParametrizations(Guid campaignId)
		{
			var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
				.Path("campaigns").Path(campaignId)
				.Path("cards/prepaid/parametrizations")
				.AsyncGet()
				.GetApiReturn<dynamic>();
			if (apiReturn.HasNullReturn())
				return null;
			return apiReturn.GetReturnOrError();
		}

		#endregion

		#region Segurança

		public async Task<dynamic> GetCampaignSecurityParameters(Guid campaignId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Campaigns)
                .Path("campaigns").Path(campaignId)
                .Path("settings/security")
                .AsyncGet()
                .GetApiReturn<CampaignSecuritySettings>();

            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar as configurações da campanha.");

            return apiReturn.GetReturnOrError();
        }

		#endregion
    }
}
