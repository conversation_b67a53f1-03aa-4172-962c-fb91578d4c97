using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Models.Product;
using Motivai.WebCatalog.Services.Cache;

namespace Motivai.WebCatalog.Repositories {
    public class ProductRepository {
        private readonly CustomCache cache;

        public ProductRepository(CustomCache cache) {
            this.cache = cache;
        }

        public async Task<ProductDetailsModel> GetProductByElasticId(Guid campaignId, Guid userId, Guid participantId, string elasticId,
                string skuCode = null, decimal? availableBalance = null) {
            // return await cache.GetOrCreate($"web-cmp-{campaignId}-sku-{elasticId}-{skuCode}", async () => {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("products").Path(elasticId)
                .Query(CatalogRepository.USER_ID_QUERY_NAME, userId)
                .Query(CatalogRepository.PARTICIPANT_ID_QUERY_NAME, participantId)
                .Query("skuCode", skuCode)
                .Query("all", "true")
                // .Query("searchSimiliarSkuAndEan", "true")
                .Query("balance", availableBalance)
                .AsyncGet()
                .Timeout(30_000)
                // .LogResponseToConsole()
                .GetApiReturn<ProductDetailsModel>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os detalhes do produto.");
            var product = apiReturn.GetReturnOrError();
            if (product == null) return null;
            product.SetMainImage();
            return product;
            // });
        }

        public async Task<ProductDetailsModel> GetProductForCart(Guid campaignId, Guid userId, Guid participantId, string elasticId,
                string skuCode, int quantity = 1, decimal? priceDefinedByParticipant = null) {
            // return await cache.GetOrCreate($"web-cmp-{campaignId}-sku-{elasticId}-{skuCode}-fr-crt", async () => {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("products").Path(elasticId).Path("skus").Path(skuCode)
                .Query(CatalogRepository.USER_ID_QUERY_NAME, userId)
                .Query(CatalogRepository.PARTICIPANT_ID_QUERY_NAME, participantId)
                .Query("quantity", quantity)
                .Query("priceDefinedByParticipant", priceDefinedByParticipant)
                .AsyncGet()
                .Timeout(30_000)
                .GetApiReturn<ProductDetailsModel>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os detalhes do produto.");
            return apiReturn.GetReturnOrError();
            // });
        }

        public async Task<List<Marketplace>> GetMarketplacesByEan(Guid campaignId, Guid userId, Guid participantId, string ean, decimal? availableBalance = null) {
            // return await cache.GetOrCreate($"web-cmp-{campaignId}-mkt-ean-{ean}", async () => {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("products").Path(ean).Path("marketplace")
                .Query(CatalogRepository.USER_ID_QUERY_NAME, userId)
                .Query(CatalogRepository.PARTICIPANT_ID_QUERY_NAME, participantId)
                .Query("balance", availableBalance)
                .AsyncGet()
                .Timeout(20_000)
                .GetApiReturn<List<Marketplace>>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar o marketplace.");
            var products = apiReturn.GetReturnOrError();
            if (products != null) {
                products.ForEach(prod => prod.EncodeUrl());
            }
            return products;
            // });
        }

        public async Task<List<ProductShowcaseModel>> GetSimilarProducts(Guid campaignId, Guid userId, Guid participantId,
                Guid categoryId, decimal? availableBalance = null) {
            // return await cache.GetOrCreate($"web-cmp-{campaignId}-siml-{categoryId}", async () => {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("products/similars").Path(categoryId)
                .Query(CatalogRepository.USER_ID_QUERY_NAME, userId)
                .Query(CatalogRepository.PARTICIPANT_ID_QUERY_NAME, participantId)
                .Query("balance", availableBalance)
                .AsyncGet()
                .Timeout(20_000)
                .GetApiReturn<List<ProductShowcaseModel>>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os produtos similares.");
            var products = apiReturn.GetReturnOrError();
            if (products != null) {
                products.ForEach(prod => prod.EncodeUrl());
            }
            return products;
            // });
        }

        public async Task<Attributes> GetSkusAttributes(Guid campaignId, Guid userId, Guid participantId, string elasticId, string model,
                string voltage, string color, string size) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("products").Path(elasticId).Path("attributes")
                .Query(CatalogRepository.USER_ID_QUERY_NAME, userId)
                .Query(CatalogRepository.PARTICIPANT_ID_QUERY_NAME, participantId)
                .Query("model", model)
                .Query("color", color)
                .Query("size", size)
                .Query("voltage", voltage)
                .AsyncGet()
                .Timeout(20_000)
                .GetApiReturn<Attributes>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível carregar os atributos do produto.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<AvailabilityModel> VerifyAvailability(Guid campaignId, Guid userId, Guid participantId, string elasticId, string skuCode,
            int quantity = 1, decimal? priceDefinedByParticipant = null) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("products").Path(elasticId)
                .Path("skus").Path(skuCode).Path("availability")
                .Query(CatalogRepository.USER_ID_QUERY_NAME, userId)
                .Query(CatalogRepository.PARTICIPANT_ID_QUERY_NAME, participantId)
                .Query("quantity", quantity > 0 ? quantity.ToString() : "1")
                .Query("priceDefinedByParticipant", priceDefinedByParticipant)
                .AsyncGet()
                .Timeout(35_000)
                .GetApiReturn<AvailabilityModel>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível consultar a disponibilidade do produto.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<bool> RegisterAvailableNotification(Guid campaignId, Guid userId, Guid participantId, Guid productId) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Catalogs)
                .Path("catalogs").Path(campaignId).Path("products").Path(productId).Path("notifications").Path(participantId)
                .Query(CatalogRepository.USER_ID_QUERY_NAME, userId)
                .Query(CatalogRepository.PARTICIPANT_ID_QUERY_NAME, participantId)
                .AsyncPost()
                .Timeout(30_000)
                .GetApiReturn<bool>();
            if (apiReturn == null)
                throw MotivaiException.of(ErrorType.ApiException, "Não foi possível registrar o aviso de disponibilidade do produto.");
            return apiReturn.GetReturnOrError();
        }
    }
}