using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.Users;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;

namespace Motivai.WebCatalog.Repositories
{
    public class AuthenticationMfaRepository
    {

        public async Task<ParticipantAuthenticationMfaSettings> FindParticipantAuthenticationMfaSettings(Guid campaignId, Guid userId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Users)
                 .Path("users").Path(userId)
                 .Path("campaigns").Path(campaignId)
                 .Path("mfa/data")
                 .AsyncGet()
                 .GetApiReturn<ParticipantAuthenticationMfaSettings>();

            return apiReturn.GetReturnOrError();
        }

        public async Task<ParticipantAuthenticationMfaSettings> FindParticipantAuthenticationMfaSettingsToValidate(Guid campaignId, Guid userId)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Users)
                 .Path("users").Path(userId)
                 .Path("campaigns").Path(campaignId)
                 .Path("mfa/validate/data")
                 .AsyncGet()
                 .GetApiReturn<ParticipantAuthenticationMfaSettings>();

            return apiReturn.GetReturnOrError();
        }


        public async Task<bool> SendAuthenticationMfaToken(Guid campaignId, Guid userId, ParticipantAuthenticationMfaSettings userAuthenticationMfa)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Users)
                 .Path("users").Path(userId)
                 .Path("campaigns").Path(campaignId)
                 .Path("mfa/send")
                 .Entity(userAuthenticationMfa)
                 .AsyncPost()
                 .GetApiReturn<bool>();

            return apiReturn.GetReturnOrError();
        }

        public async Task<bool> SendAuthenticationMfaTokenToValidate(Guid campaignId, Guid userId, ParticipantAuthenticationMfaSettings userAuthenticationMfa)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Users)
                 .Path("users").Path(userId)
                 .Path("campaigns").Path(campaignId)
                 .Path("mfa/validate/send")
                 .Entity(userAuthenticationMfa)
                 .AsyncPost()
                 .GetApiReturn<bool>();

            return apiReturn.GetReturnOrError();
        }

        public async Task<bool> ValidateAuthenticationMfaToken(Guid campaignId, Guid userId, dynamic userToken)
        {
            var apiReturn = await HttpClient.Create(MotivaiApi.Users)
                 .Path("users").Path(userId)
                 .Path("campaigns").Path(campaignId)
                 .Path("mfa/validate")
                 .Entity(userToken)
                 .AsyncPost()
                 .GetApiReturn<bool>();

            return apiReturn.GetReturnOrError();
        }

        public async Task<bool> UpdateAuthenticationMfaSettings(Guid campaignId, Guid userId, ParticipantAuthenticationMfaSettings userAuthenticationMfa)
        {
			var apiReturn = await HttpClient.Create(MotivaiApi.Users)
				 .Path("users").Path(userId)
				 .Path("campaigns").Path(campaignId)
				 .Path("mfa/data")
				 .Entity(userAuthenticationMfa)
				 .AsyncPut()
				 .GetApiReturn<bool>();

			return apiReturn.GetReturnOrError();
        }

    }
}