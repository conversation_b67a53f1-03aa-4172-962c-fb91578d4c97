using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Domain.Model.Transactions;
using Motivai.SharedKernel.Helpers;
using Newtonsoft.Json;

namespace Motivai.WebCatalog.Repositories {
    public class IntegrationsRepository {

        public async Task<TransactionsExtractModel> GetTransactions(Guid campaignId, string userId, Dictionary<string, string> queryParameters, TransactionType? type, TransactionOrigin? origin,
            DateTime? from, DateTime? to) {
            var request = HttpClient.Create(MotivaiApi.ClientsIntegrations)
                .Path("clients/campaigns").Path(campaignId).Path("users").Path(userId).Path("summary");

            request.Query(queryParameters);
            if (type.HasValue)
                request.Query("transactionType", type.ToString());
            if (origin.HasValue)
                request.Query("transactionOrigin", origin.ToString());
            if (from.HasValue)
                request.Query("startDate", from.Value.ToString());
            if (to.HasValue)
                request.Query("endDate", to.Value.ToString());
            var response = await request.AsyncGet().GetApiReturn<TransactionsExtractModel>();

            return response.GetReturnOrError();
        }
    }
}