using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.WebCatalog.Models.Security;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Newtonsoft.Json;

namespace Motivai.WebCatalog.Repositories
{
    public class AuthenticatorRepository
    {
        public AuthenticatorRepository() { }

        public async Task<bool> SendAuthorizationCode(Guid campaignId, Guid userId, SimpleSecurityCodeRequest securityCodeRequest) {
            if (securityCodeRequest == null || campaignId == null)
                throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");

            if (userId == null)
                throw MotivaiException.ofValidation("Identifica o usuário para prosseguir.");

            securityCodeRequest.CampaignId = campaignId;

            var response = await HttpClient.Create(MotivaiApi.Users)
                .Path("campaigns").Path(campaignId).Path("users").Path(userId)
                .Path("authentication/code")
                .Entity(securityCodeRequest)
                .AsyncPost()
                .GetApiReturn<bool>();
            return response.GetReturnOrError();
        }

        public async Task<bool> ValidateAuthorizationCode(Guid campaignId, Guid userId, SimpleSecurityCodeValidation securityCodeRequest) {
            if (securityCodeRequest == null || campaignId == null)
                throw MotivaiException.ofValidation("Preencha os campos corretamente para prosseguir.");

            if (userId == null)
                throw MotivaiException.ofValidation("Identifica o usuário para prosseguir.");

            var response = await HttpClient.Create(MotivaiApi.Users)
                .Path("campaigns").Path(campaignId).Path("users").Path(userId)
                .Path("authentication/code")
                .Entity(securityCodeRequest)
                .AsyncPut()
                .GetApiReturn<bool>();
            return response.GetReturnOrError();
        }
    }
}