using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Http;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Models.Integrations;
using Motivai.WebCatalog.Models.Login;
using Motivai.WebCatalog.Models.MyAccount;

namespace Motivai.WebCatalog.Repositories {
    public class LoginRepository {
        public async Task<UserParticipantModel> AuthenticateParticipant(Guid campaignId, LoginModel model) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Users)
                .Path("campaigns").Path(campaignId).Path("participants/authentication")
                .Entity(model)
                .Timeout(30_000)
                .AsyncPost()
                .GetApiReturn<UserParticipantModel>();
            if (apiReturn == null)
                throw MotivaiException.ofValidation("Não foi possível efetuar login, por favor, tente novamente.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<UserParticipantModel> AuthenticateParticipantByIntegration(Guid campaignId, IntegrationLogin login) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Users)
                .Path("campaigns").Path(campaignId).Path("participants/integration/authenticate")
                .Entity(login)
                .AsyncPost()
                .LogPayloadToLogger()
                .GetApiReturn<UserParticipantModel>();
            if (apiReturn == null)
                throw MotivaiException.ofValidation("Não foi possível efetuar login, por favor, tente novamente.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<string> GetAdminUsername(Guid userId) {
            var apiResponse = await HttpClient.Create(MotivaiApi.Users)
                .Path("administrators").Path(userId).Path("name")
                .AsyncGet()
                .GetApiReturn<string>();
            if (apiResponse == null) return "Não Encontrado";
            return apiResponse.GetReturnOrError();
        }

        public async Task<UserParticipantModel> AuthenticateParticipantForCallcenter(Guid campaignId, CallcenterLogin login) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Users)
                .Path("campaigns").Path(campaignId).Path("participants/integration/authenticatecallcenter")
                .Entity(login)
                .AsyncPost()
                .GetApiReturn<UserParticipantModel>();
            if (apiReturn == null)
                throw MotivaiException.ofValidation("Não foi possível efetuar login, por favor, tente novamente.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<UserParticipantModel> AuthenticateParticipantForCampaignSite(Guid campaignId, PlatformIntegrationLogin userLogin) {
            var response = await HttpClient.Create(MotivaiApi.Users)
                .Path("campaigns").Path(campaignId).Path("participants/integration/site/authenticate")
                .Entity(userLogin)
                .AsyncPost()
                .GetApiReturn<UserParticipantModel>();
            return response.GetReturnOrError();
        }

        public async Task<bool> ResetPassword(Guid campaignId, string login, string email) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Users)
                .Path("campaigns").Path(campaignId).Path("resetpassword")
                .Entity(new {
                    origin = "Catálogo",
                    login = login?.Trim(),
                    email = email?.Trim()
                })
                .AsyncPut()
                .GetApiReturn<bool>();
            if (apiReturn == null)
                throw MotivaiException.ofValidation("Não foi possível verificar o usuário, por favor, tente novamente.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<PasswordRecoveryContact> SearchParticipantToPasswordRecovery(Guid campaignId, string login, string email) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Users)
                .Path("campaigns").Path(campaignId).Path("participants/passwordrecovery/search")
                .Entity(new {
                    login = login?.Trim(),
                    email = email?.Trim()
                })
                .AsyncPost()
                .GetApiReturn<PasswordRecoveryContact>();
            if (apiReturn == null)
                throw MotivaiException.ofValidation("Não foi possível verificar o usuário, por favor, tente novamente.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<string> SendPasswordRecoveryToken(Guid campaignId, PasswordRecoverySession recoverySession) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Users)
                .Path("campaigns").Path(campaignId).Path("participants/passwordrecovery/token")
                .Entity(recoverySession)
                .AsyncPost()
                .GetApiReturn<string>();
            if (apiReturn == null)
                throw MotivaiException.ofValidation("Não foi possível enviar o token de segurança, por favor, tente novamente.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<bool> UpdatePassword(Guid campaignId, PasswordRecoverySession recoverySession) {
            var apiReturn = await HttpClient.Create(MotivaiApi.Users)
                .Path("campaigns").Path(campaignId).Path("participants/passwordrecovery/password")
                .Entity(recoverySession)
                .AsyncPut()
                .GetApiReturn<bool>();
            if (apiReturn == null)
                throw MotivaiException.ofValidation("Não foi possível atualizar a senha, por favor, tente novamente.");
            return apiReturn.GetReturnOrError();
        }

        public async Task<Guid> AuthenticateByPlatformSso(Guid campaignId, IntegrationLogin integrationLogin) {
             var apiReturn = await HttpClient.Create(MotivaiApi.Users)
                .Path("campaigns").Path(campaignId).Path("participants/sso/authentication")
                .Entity(integrationLogin)
                .AsyncPost()
                .GetApiReturn<Guid>();

            if (apiReturn == null)
                throw MotivaiException.ofValidation("Não foi possível gerar o token para autenticação, por favor, tente novamente");
            return apiReturn.GetReturnOrError();
        }

        public async Task<UserParticipantModel> FinalizeAuthenticatedUserUsingSsoToken(Guid campaignId, LoginSsoEndingRequest loginSso) {
             var apiReturn = await HttpClient.Create(MotivaiApi.Users)
                .Path("campaigns").Path(campaignId).Path("participants/sso")
                .Entity(loginSso)
                .AsyncPut()
                .GetApiReturn<UserParticipantModel>();

            if (apiReturn == null)
                throw MotivaiException.ofValidation("Não foi possível autenticar o usuário, por favor, tente novamente.");
            return apiReturn.GetReturnOrError();
        }
    }
}