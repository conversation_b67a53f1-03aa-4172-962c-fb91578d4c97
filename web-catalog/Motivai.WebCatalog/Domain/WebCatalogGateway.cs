using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.WebCatalog.Domain.Integrator;
using Motivai.WebCatalog.Models.MyAccount;
using Newtonsoft.Json;

namespace Motivai.WebCatalog.Domain {
    public class WebCatalogGateway {
        private readonly ApiResolver apiResolver;

        public WebCatalogGateway(ApiResolver apiResolver) {
            this.apiResolver = apiResolver;
        }
        public async Task<ExtractModel> GetTransactions(Guid userId, Guid campaignId, Dictionary<string, string> queryParameters, TransactionType? type, TransactionOrigin? origin,
            DateTime? from, DateTime? to) {
            var integrator = await apiResolver.ResolveIntegratorForCampaign(campaignId, settings => settings.IntegrationSettings.EnableSummaryAtWebCatalog);
            return await integrator.GetTransactions(userId, campaignId, queryParameters, type, origin, from, to);
        }
    }
}