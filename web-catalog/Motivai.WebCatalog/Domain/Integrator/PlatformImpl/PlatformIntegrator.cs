using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.WebCatalog.Models.MyAccount;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Domain.Integrator.PlatformImpl {
    public class PlatformIntegrator : IntegratorApi {

        private readonly UserParticipantRepository userParticipantRepository;

        public PlatformIntegrator(UserParticipantRepository userParticipantRepository) {
            this.userParticipantRepository = userParticipantRepository;
        }
        public async Task<ExtractModel> GetTransactions(Guid userId, Guid campaignId, Dictionary<string, string> queryParameters, TransactionType? type, TransactionOrigin? origin, DateTime? from, DateTime? to) {
            return await userParticipantRepository.GetExtract(campaignId, userId.ToString(), queryParameters, type, origin, from, to);

        }
    }
}