using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.WebCatalog.Models.MyAccount;
using Motivai.WebCatalog.Repositories;
using Newtonsoft.Json;

namespace Motivai.WebCatalog.Domain.Integrator.ClientsIntegratorImpl {
    public class ClientsIntegrator : IntegratorApi {

        private readonly IntegrationsRepository integrationsRepository;

        public ClientsIntegrator(IntegrationsRepository integrationsRepository) {
            this.integrationsRepository = integrationsRepository;
        }

        public async Task<ExtractModel> GetTransactions(Guid userId, Guid campaignId, Dictionary<string, string> queryParameters, TransactionType? type, TransactionOrigin? origin, DateTime? from, DateTime? to) {
            var transactions = await integrationsRepository.GetTransactions(campaignId, userId.ToString(), queryParameters, type, origin, from, to);
            if (transactions == null) return null;

            var extract = new ExtractModel {
                TotalAmount = transactions.TotalAmount.ToString()
            };
            if (transactions.Transactions != null) {
                extract.Transactions = transactions.Transactions
                    .Select(transaction => new ExtractRegister {
                        Description = transaction.Description,
                        Amount = transaction.TotalPoints.ToString(),
                        ProcessingDate = transaction.ProcessingDate,
                        // ExtraData = transaction.ExtraData
                    }).ToList();
            }
            return extract;
        }
    }
}