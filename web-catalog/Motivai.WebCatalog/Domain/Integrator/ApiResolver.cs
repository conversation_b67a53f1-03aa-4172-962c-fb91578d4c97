using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.WebCatalog.Domain.Integrator.ClientsIntegratorImpl;
using Motivai.WebCatalog.Domain.Integrator.PlatformImpl;
using Motivai.WebCatalog.Repositories;
using Newtonsoft.Json;

namespace Motivai.WebCatalog.Domain.Integrator {
    public class ApiResolver {
        private readonly CampaignRepository campaignRepository;
        private readonly IntegratorApi platformIntegrator;
        private readonly IntegratorApi clientsIntegrator;

        public ApiResolver(CampaignRepository campaignRepository, PlatformIntegrator platformIntegrator, ClientsIntegrator clientsIntegrator) {
            this.campaignRepository = campaignRepository;
            this.platformIntegrator = platformIntegrator;
            this.clientsIntegrator = clientsIntegrator;
        }

        private bool Verify(CampaignIntegrationSettings campaignSettings, Predicate<CampaignIntegrationSettings> paramVerifier) {
            return paramVerifier == null || paramVerifier.Invoke(campaignSettings);
        }

        public async Task<IntegratorApi> ResolveIntegratorForCampaign(Guid campaignId, Predicate<CampaignIntegrationSettings> paramVerifier = null) {
            var campaignSettings = await campaignRepository.GetCampaignIntegrationSettings(campaignId);
            if (campaignSettings != null && campaignSettings.HasClientIntegration() && Verify(campaignSettings, paramVerifier)) {
                return clientsIntegrator;
            }
            return platformIntegrator;
        }

    }
}