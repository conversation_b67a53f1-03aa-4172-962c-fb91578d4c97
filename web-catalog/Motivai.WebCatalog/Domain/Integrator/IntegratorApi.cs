using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.WebCatalog.Models.MyAccount;

namespace Motivai.WebCatalog.Domain.Integrator {
    public interface IntegratorApi {
        Task<ExtractModel> GetTransactions(Guid userId, Guid campaignId, Dictionary<string, string> queryParameters, TransactionType? type, TransactionOrigin? origin, DateTime? from, DateTime? to);
    }
}