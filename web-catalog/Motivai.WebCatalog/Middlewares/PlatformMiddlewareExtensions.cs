using Microsoft.AspNetCore.Builder;

namespace Motivai.WebCatalog.Middlewares {
    public static class PlatformMiddlewareExtensions {
        public static IApplicationBuilder UseLoginMiddleware(this IApplicationBuilder builder) {
            return builder.UseMiddleware<LoginMiddleware>();
        }
        public static IApplicationBuilder UseReCaptchaMiddleware(this IApplicationBuilder builder) {
            return  builder.UseMiddleware<RecaptchaMiddleware>();
        }

        public static IApplicationBuilder UseTemplateMiddleware(this IApplicationBuilder builder) {
            return builder.UseMiddleware<TemplateMiddleware>();
        }
    }
}
