using System;
using Microsoft.AspNetCore.Builder;

namespace Motivai.WebCatalog.Middlewares.Security {
    public static class SecurityMiddlewareExtension {
        public static IApplicationBuilder UseSecurityHeadersMiddleware(this IApplicationBuilder app, SecurityHeadersBuilder builder) {
            if (app == null) {
                throw new ArgumentNullException(nameof(app));
            }

            if (builder == null) {
                throw new ArgumentNullException(nameof(builder));
            }

            return app.UseMiddleware<SecurityHeadersMiddleware>(builder.Build());
        }
    }
}