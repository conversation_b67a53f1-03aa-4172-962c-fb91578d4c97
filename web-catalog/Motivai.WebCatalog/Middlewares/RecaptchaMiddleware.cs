using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Motivai.WebCatalog.Services.Recaptcha;
using Motivai.SharedKernel.Helpers.Exceptions;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Microsoft.Extensions.Primitives;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Domain.Services.Security;
using Motivai.SharedKernel.Helpers.Http;

namespace Motivai.WebCatalog.Middlewares
{
    public class RecaptchaMiddleware
    {
        private readonly RequestDelegate next;
        private readonly RecaptchaValidatorService recaptchaValidatorService;
        private readonly ConnectionAllowListValidator connectionAllowListValidator;

        public RecaptchaMiddleware(RequestDelegate next, RecaptchaValidatorService recaptchaValidatorService)
        {
            this.next = next;
            this.recaptchaValidatorService = recaptchaValidatorService;
            connectionAllowListValidator = ConnectionAllowListValidator.FromEnvironmentVariable("LOGIN_RPA_ALLOWED_IP");
        }

        public async Task Invoke(HttpContext context)
        {
            if (IsValidPath(context))
            {
                NextAction action;
                if (IsRpaLoginPath(context))
                {
                    if (connectionAllowListValidator.IsAllowed(context)) {
                        action = NextAction.CONTINUE;
                    } else {
                        LoggerFactory.GetLogger().Warn("IP {} - Tentando acessar Login RPA",
                            HttpContextHelper.ExtractRemoteIpAddress(context));

                        await SendNotAuthorized(context, "Acesso não autorizado");
                        action = NextAction.ABORT;
                    }
                }
                else
                {
                    action = await ValidateRecaptchaAuthentication(context);
                }
                if (action == NextAction.ABORT)
                {
                    return;
                }
            }

            await next(context);
        }

        private bool IsValidPath(HttpContext context)
        {
            return (IsLoginPath(context.Request.Path) || IsResetPasswordPath(context.Request.Path) || IsUserPinAuthentication(context.Request.Path))
                && IsPost(context.Request.Method);
        }

        private bool IsRpaLoginPath(HttpContext context)
        {
            return context.Request.Path == "/login/rpaLogar";
        }

        public bool IsLoginPath(string path)
        {
            return path.StartsWith("/login/logar") || path.StartsWith("/login/rpaLogar");
        }

        public bool IsResetPasswordPath(string path)
        {
            return path.EndsWith("/login/recuperacaosenha");
        }

        public bool IsUserPinAuthentication(string path)
        {
            return path.EndsWith("/Cartoes") || path.StartsWith("/partners/prepaidcards/") ;
        }

        private bool IsPost(string method)
        {
            return HttpMethods.IsPost(method);
        }

        private async Task<NextAction> ValidateRecaptchaAuthentication(HttpContext context)
        {
            string token = await ExtractRecaptchaToken(context);
            if (token == null)
            {
                return NextAction.ABORT;
            }

            try
            {
                await this.recaptchaValidatorService.ValidateCaptchaToken(context.Request.Host.Host, token);
            }
            catch (MotivaiException ex)
            {
                await SendNotAuthorized(context, ex.Message);
                return NextAction.ABORT;
            }
            catch (Exception ex)
            {
                await SendNotAuthorized(context, "Erro durante o processamento do reCaptcha: " + ex.Message);
                return NextAction.ABORT;
            }

            return NextAction.CONTINUE;
        }

        private async Task<string> ExtractRecaptchaToken(HttpContext context)
        {
            context.Request.EnableBuffering();
            var body = await new StreamReader(context.Request.Body).ReadToEndAsync();

            var response = JsonConvert.DeserializeObject<dynamic>(body);
            if (string.IsNullOrEmpty((string)response.recaptchaToken))
            {
                await SendNotAuthorized(context, "Token reCaptcha não está na requisição");
            }

            context.Request.Body.Seek(0, SeekOrigin.Begin);

            return (string)response.recaptchaToken;
        }

        private async Task SendNotAuthorized(HttpContext context, string message)
        {
            context.Response.StatusCode = 400;
            await context.Response.WriteAsync(message);
        }

        public enum NextAction
        {
            CONTINUE,
            ABORT
        }
    }
}