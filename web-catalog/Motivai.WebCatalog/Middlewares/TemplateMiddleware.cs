using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Api;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Catalog.Menu;
using Motivai.WebCatalog.Services.Catalog;
using Motivai.WebCatalog.Repositories;
using Newtonsoft.Json;

namespace Motivai.WebCatalog.Middlewares
{
    public class TemplateMiddleware {
        private readonly RequestDelegate _next;
        private readonly IHelperWeb _helperWeb;
        private readonly LayoutContentService layoutContentService;
        private readonly CatalogRepository catalogRepository;
        private readonly string urlCdnLayouts;

        public TemplateMiddleware(RequestDelegate next, IHelperWeb helperWeb, LayoutContentService layoutContentService, CatalogRepository _catalogRepository) {
            this._next = next;
            this._helperWeb = helperWeb;
            this.layoutContentService = layoutContentService;
            this.urlCdnLayouts = ConfigurationHelper.GetValue("ThemesUrl");
            this.catalogRepository = _catalogRepository;
        }

        private async Task<Guid> GetCampaignId() {
            try {
                return await _helperWeb.GetCampaignIdForCurrentDomain();
            } catch (Exception) {
                return Guid.Empty;
            }
        }

        private void SetDefaultVars(HttpContext context) {
            context.Items["coinPrefix"] = "";
            context.Items["coinSufix"] = "";
            context.Items["themeUrl"] = "~/themes";
            context.Items["showMyAccountLink"] = "1";
            context.Items["catalog-menu"] = new CatalogMenu();
            context.Items["scripts"] = null;
        }

        public async Task Invoke(HttpContext context) {
            if (NeedsThemeVars(context)) {
                Guid campaignId = await GetCampaignId();
                if (campaignId == Guid.Empty) {
                    SetDefaultVars(context);
                } else {
                    await SetupCampaignView(context, campaignId);
                    await SetupCampaignTheme(context, campaignId);
                    await SetupCampaignMenu(context, campaignId);
                    await SetupPagesSettings(context, campaignId);
                    await SetupCustomScripts(context, campaignId);
                }
            } else {
                context.Items["coinPrefix"] = "";
                context.Items["coinSufix"] = "";
            }
            try {
                await _next.Invoke(context);
            } catch (Exception ex) {
                await ExceptionLoggerMiddleware.HandleException(ex, "Catálogo - Template Middleware", "Erro durante processamento da requisição");
                context.Response.Redirect("/Erro");
            }
        }

        private static bool NeedsThemeVars(HttpContext context) {
            if (!context.Request.Path.HasValue)
                return true;
            if (LoginMiddleware.IsApi(context.Request.Path)) {
                return false;
            }
            var headerAccept = StringValues.Empty;
            context.Request.Headers.TryGetValue("Accept", out headerAccept);
            if (headerAccept.Count > 0) {
                // Console.WriteLine($"Accept {headerAccept}");
                return !headerAccept.ToString().Contains("application/json");
            }
            return true;
        }

        private async Task SetupCampaignView(HttpContext context, Guid campaignId) {
            try {
                var coinName = await layoutContentService.GetCoinNameByCampaign(campaignId);
                context.Items["coinPrefix"] = coinName.Prefix == null ? "" : coinName.Prefix;
                context.Items["coinSufix"] = coinName.Sufix == null ? "" : coinName.Sufix;
            } catch (Exception ex) {
                await ExceptionLogger.LogException(ex, "Catálogo - Template Middleware", "Erro durante carregamento moeda");
                context.Items["coinPrefix"] = "";
                context.Items["coinSufix"] = "";
            }
        }

        private async Task SetupCampaignMenu(HttpContext context, Guid campaignId) {
            try {
                context.Items["catalog-menu"] = await layoutContentService.GetMenuByCampaign(campaignId);
            } catch (Exception ex) {
                await ExceptionLogger.LogException(ex, "Catálogo - Template Middleware", "Erro durante carregamento menu");
                context.Items["catalog-menu"] = new CatalogMenu();
            }
        }

        private async Task SetupCampaignTheme(HttpContext context, Guid campaignId) {
            var themeFolder = "~/themes/";
            try {
                var folderId = await layoutContentService.GetCurrentThemeByCampaign(campaignId);
                if (folderId != Guid.Empty) {
                    themeFolder = String.Format("{0}/campaigns/{1}/themes/{2}", this.urlCdnLayouts, campaignId, folderId);
                }
            } catch (Exception ex) {
                await ExceptionLoggerMiddleware.HandleException(ex, "Catálogo - Template Middleware", $"Erro ao carregar o tema da campanha {campaignId}", true);
                themeFolder  = "~/themes";
            }
            context.Items["themeUrl"] = themeFolder;
        }

        private async Task SetupPagesSettings(HttpContext context, Guid campaignId) {
            try {
                var pagesSettings = await layoutContentService.GetPagesSettingsByCampaign(campaignId);
                context.Items["showMyAccountLink"] = pagesSettings.PagesSettings.EnableMyAccount ? "1" : "0";
            } catch (Exception ex) {
                await ExceptionLoggerMiddleware.HandleException(ex, "Catálogo - Template Middleware", "Erro ao carregar o configuração de páginas da campanha.", true);
                context.Items["showMyAccountLink"] = "1";
            }
        }

        private async Task SetupCustomScripts(HttpContext context, Guid campaignId)
        {
            try
            {
                context.Items["clearSaleAppId"] = ConfigurationHelper.GetValue("CLEARSALE_APP_ID");
                context.Items["scripts"] = await this.catalogRepository.GetCustomScripts(campaignId);
            }
            catch (Exception ex)
            {
                await ExceptionLoggerMiddleware.HandleException(ex, "Catálogo - Template Middleware", "Erro ao carregar os scripts da campanha", true);
            }
        }
    }
}
