﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using NLog.Web;

namespace Motivai.WebCatalog {
    public class Program {
        public static void Main(string[] args) {
            try {
                BuildWebHost(args).Build().Run();
            } catch (Exception) {
                throw;
            } finally {
                NLog.LogManager.Shutdown();
            }
        }

        private static IWebHostBuilder BuildWebHost(string[] args) {
            return new WebHostBuilder()
                .UseKestrel()
                .UseSetting("detailedErrors", "true")
                .UseKestrel(opt => {
                    opt.AddServerHeader = false;
                })
                .UseContentRoot(Directory.GetCurrentDirectory())
                .UseIISIntegration()
                .UseStartup<Startup>()
                .UseApplicationInsights()
                .UseNLog();
        }
    }
}
