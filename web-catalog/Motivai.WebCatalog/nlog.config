<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd" keepVariablesOnReload="true"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" autoReload="true" internalLogLevel="info" internalLogFile="logs/internal-nlog.txt">

  <!-- enable asp.net core layout renderers -->
  <extensions>
    <add assembly="NLog.Web.AspNetCore"/>
    <add assembly="R7Insight.NLog" />
  </extensions>

  <!-- the targets to write to -->
  <targets async="true">
    <target xsi:type="File" name="allfile" fileName="logs/nlog-all-${shortdate}.log" layout="${longdate}|${environment:ASPNETCORE_ENVIRONMENT}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}" />
    <target xsi:type="Console" name="console" layout="${longdate}|${environment:ASPNETCORE_ENVIRONMENT}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}" />
    <target type="Insight" name="insightops" region="us" debug="true" usessl="false" token="" layout="${longdate}|${environment:ASPNETCORE_ENVIRONMENT}|${uppercase:${level}}|${logger}|${message}" />
  </targets>

  <!-- rules to map from logger name to target -->
  <rules>
    <!--All logs, including from Microsoft-->
    <logger name="*" minlevel="Warn" writeTo="allfile" />
    <logger name="System.*" maxLevel="Warn" final="true" />
    <!--Skip non-critical Microsoft logs and so log only own logs-->
    <logger name="Microsoft.*" maxLevel="Warn" final="true" />
    <logger name="*" minLevel="Info" appendTo="console" />
    <logger name="*" minLevel="Info" appendTo="insightops"/>
  </rules>
</nlog>
