!function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(S,e){"use strict";var t=[],C=S.document,i=Object.getPrototypeOf,a=t.slice,g=t.concat,l=t.push,r=t.indexOf,n={},o=n.toString,v=n.hasOwnProperty,s=v.toString,c=s.call(Object),y={},m=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType},x=function(e){return null!=e&&e===e.window},u={type:!0,src:!0,nonce:!0,noModule:!0};function b(e,t,n){var i,r,o=(n=n||C).createElement("script");if(o.text=e,t)for(i in u)(r=t[i]||t.getAttribute&&t.getAttribute(i))&&o.setAttribute(i,r);n.head.appendChild(o).parentNode.removeChild(o)}function w(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?n[o.call(e)]||"object":typeof e}var d="3.4.1",A=function(e,t){return new A.fn.init(e,t)},f=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;function p(e){var t=!!e&&"length"in e&&e.length,n=w(e);return!m(e)&&!x(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}A.fn=A.prototype={jquery:d,constructor:A,length:0,toArray:function(){return a.call(this)},get:function(e){return null==e?a.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=A.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return A.each(this,e)},map:function(n){return this.pushStack(A.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(0<=n&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:l,sort:t.sort,splice:t.splice},A.extend=A.fn.extend=function(){var e,t,n,i,r,o,s=arguments[0]||{},a=1,l=arguments.length,c=!1;for("boolean"==typeof s&&(c=s,s=arguments[a]||{},a++),"object"==typeof s||m(s)||(s={}),a===l&&(s=this,a--);a<l;a++)if(null!=(e=arguments[a]))for(t in e)i=e[t],"__proto__"!==t&&s!==i&&(c&&i&&(A.isPlainObject(i)||(r=Array.isArray(i)))?(n=s[t],o=r&&!Array.isArray(n)?[]:r||A.isPlainObject(n)?n:{},r=!1,s[t]=A.extend(c,o,i)):void 0!==i&&(s[t]=i));return s},A.extend({expando:"jQuery"+(d+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==o.call(e)||(t=i(e))&&("function"!=typeof(n=v.call(t,"constructor")&&t.constructor)||s.call(n)!==c))},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t){b(e,{nonce:t&&t.nonce})},each:function(e,t){var n,i=0;if(p(e))for(n=e.length;i<n&&!1!==t.call(e[i],i,e[i]);i++);else for(i in e)if(!1===t.call(e[i],i,e[i]))break;return e},trim:function(e){return null==e?"":(e+"").replace(f,"")},makeArray:function(e,t){var n=t||[];return null!=e&&(p(Object(e))?A.merge(n,"string"==typeof e?[e]:e):l.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:r.call(t,e,n)},merge:function(e,t){for(var n=+t.length,i=0,r=e.length;i<n;i++)e[r++]=t[i];return e.length=r,e},grep:function(e,t,n){for(var i=[],r=0,o=e.length,s=!n;r<o;r++)!t(e[r],r)!==s&&i.push(e[r]);return i},map:function(e,t,n){var i,r,o=0,s=[];if(p(e))for(i=e.length;o<i;o++)null!=(r=t(e[o],o,n))&&s.push(r);else for(o in e)null!=(r=t(e[o],o,n))&&s.push(r);return g.apply([],s)},guid:1,support:y}),"function"==typeof Symbol&&(A.fn[Symbol.iterator]=t[Symbol.iterator]),A.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){n["[object "+t+"]"]=t.toLowerCase()});var h=function(n){var e,p,b,o,r,h,d,g,w,l,c,T,S,s,C,v,a,u,y,A="sizzle"+1*new Date,m=n.document,I=0,i=0,f=le(),x=le(),P=le(),k=le(),j=function(e,t){return e===t&&(c=!0),0},E={}.hasOwnProperty,t=[],N=t.pop,D=t.push,_=t.push,H=t.slice,L=function(e,t){for(var n=0,i=e.length;n<i;n++)if(e[n]===t)return n;return-1},O="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",q="[\\x20\\t\\r\\n\\f]",R="(?:\\\\.|[\\w-]|[^\0-\\xa0])+",z="\\["+q+"*("+R+")(?:"+q+"*([*^$|!~]?=)"+q+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+R+"))|)"+q+"*\\]",M=":("+R+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+z+")*)|.*)\\)|)",W=new RegExp(q+"+","g"),F=new RegExp("^"+q+"+|((?:^|[^\\\\])(?:\\\\.)*)"+q+"+$","g"),$=new RegExp("^"+q+"*,"+q+"*"),B=new RegExp("^"+q+"*([>+~]|"+q+")"+q+"*"),V=new RegExp(q+"|>"),Q=new RegExp(M),U=new RegExp("^"+R+"$"),X={ID:new RegExp("^#("+R+")"),CLASS:new RegExp("^\\.("+R+")"),TAG:new RegExp("^("+R+"|[*])"),ATTR:new RegExp("^"+z),PSEUDO:new RegExp("^"+M),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+q+"*(even|odd|(([+-]|)(\\d*)n|)"+q+"*(?:([+-]|)"+q+"*(\\d+)|))"+q+"*\\)|)","i"),bool:new RegExp("^(?:"+O+")$","i"),needsContext:new RegExp("^"+q+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+q+"*((?:-\\d)?\\d*)"+q+"*\\)|)(?=[^-]|$)","i")},G=/HTML$/i,J=/^(?:input|select|textarea|button)$/i,Z=/^h\d$/i,Y=/^[^{]+\{\s*\[native \w/,K=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ee=/[+~]/,te=new RegExp("\\\\([\\da-f]{1,6}"+q+"?|("+q+")|.)","ig"),ne=function(e,t,n){var i="0x"+t-65536;return i!=i||n?t:i<0?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320)},ie=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,re=function(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},oe=function(){T()},se=be(function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{_.apply(t=H.call(m.childNodes),m.childNodes),t[m.childNodes.length].nodeType}catch(e){_={apply:t.length?function(e,t){D.apply(e,H.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}function ae(e,t,n,i){var r,o,s,a,l,c,u,d=t&&t.ownerDocument,f=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==f&&9!==f&&11!==f)return n;if(!i&&((t?t.ownerDocument||t:m)!==S&&T(t),t=t||S,C)){if(11!==f&&(l=K.exec(e)))if(r=l[1]){if(9===f){if(!(s=t.getElementById(r)))return n;if(s.id===r)return n.push(s),n}else if(d&&(s=d.getElementById(r))&&y(t,s)&&s.id===r)return n.push(s),n}else{if(l[2])return _.apply(n,t.getElementsByTagName(e)),n;if((r=l[3])&&p.getElementsByClassName&&t.getElementsByClassName)return _.apply(n,t.getElementsByClassName(r)),n}if(p.qsa&&!k[e+" "]&&(!v||!v.test(e))&&(1!==f||"object"!==t.nodeName.toLowerCase())){if(u=e,d=t,1===f&&V.test(e)){for((a=t.getAttribute("id"))?a=a.replace(ie,re):t.setAttribute("id",a=A),o=(c=h(e)).length;o--;)c[o]="#"+a+" "+xe(c[o]);u=c.join(","),d=ee.test(e)&&ye(t.parentNode)||t}try{return _.apply(n,d.querySelectorAll(u)),n}catch(t){k(e,!0)}finally{a===A&&t.removeAttribute("id")}}}return g(e.replace(F,"$1"),t,n,i)}function le(){var i=[];return function e(t,n){return i.push(t+" ")>b.cacheLength&&delete e[i.shift()],e[t+" "]=n}}function ce(e){return e[A]=!0,e}function ue(e){var t=S.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function de(e,t){for(var n=e.split("|"),i=n.length;i--;)b.attrHandle[n[i]]=t}function fe(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function pe(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}function he(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}function ge(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&se(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function ve(s){return ce(function(o){return o=+o,ce(function(e,t){for(var n,i=s([],e.length,o),r=i.length;r--;)e[n=i[r]]&&(e[n]=!(t[n]=e[n]))})})}function ye(e){return e&&void 0!==e.getElementsByTagName&&e}for(e in p=ae.support={},r=ae.isXML=function(e){var t=e.namespaceURI,n=(e.ownerDocument||e).documentElement;return!G.test(t||n&&n.nodeName||"HTML")},T=ae.setDocument=function(e){var t,n,i=e?e.ownerDocument||e:m;return i!==S&&9===i.nodeType&&i.documentElement&&(s=(S=i).documentElement,C=!r(S),m!==S&&(n=S.defaultView)&&n.top!==n&&(n.addEventListener?n.addEventListener("unload",oe,!1):n.attachEvent&&n.attachEvent("onunload",oe)),p.attributes=ue(function(e){return e.className="i",!e.getAttribute("className")}),p.getElementsByTagName=ue(function(e){return e.appendChild(S.createComment("")),!e.getElementsByTagName("*").length}),p.getElementsByClassName=Y.test(S.getElementsByClassName),p.getById=ue(function(e){return s.appendChild(e).id=A,!S.getElementsByName||!S.getElementsByName(A).length}),p.getById?(b.filter.ID=function(e){var t=e.replace(te,ne);return function(e){return e.getAttribute("id")===t}},b.find.ID=function(e,t){if(void 0!==t.getElementById&&C){var n=t.getElementById(e);return n?[n]:[]}}):(b.filter.ID=function(e){var n=e.replace(te,ne);return function(e){var t=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return t&&t.value===n}},b.find.ID=function(e,t){if(void 0!==t.getElementById&&C){var n,i,r,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(r=t.getElementsByName(e),i=0;o=r[i++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),b.find.TAG=p.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):p.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,i=[],r=0,o=t.getElementsByTagName(e);if("*"!==e)return o;for(;n=o[r++];)1===n.nodeType&&i.push(n);return i},b.find.CLASS=p.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&C)return t.getElementsByClassName(e)},a=[],v=[],(p.qsa=Y.test(S.querySelectorAll))&&(ue(function(e){s.appendChild(e).innerHTML="<a id='"+A+"'></a><select id='"+A+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&v.push("[*^$]="+q+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||v.push("\\["+q+"*(?:value|"+O+")"),e.querySelectorAll("[id~="+A+"-]").length||v.push("~="),e.querySelectorAll(":checked").length||v.push(":checked"),e.querySelectorAll("a#"+A+"+*").length||v.push(".#.+[+~]")}),ue(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=S.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&v.push("name"+q+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&v.push(":enabled",":disabled"),s.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&v.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),v.push(",.*:")})),(p.matchesSelector=Y.test(u=s.matches||s.webkitMatchesSelector||s.mozMatchesSelector||s.oMatchesSelector||s.msMatchesSelector))&&ue(function(e){p.disconnectedMatch=u.call(e,"*"),u.call(e,"[s!='']:x"),a.push("!=",M)}),v=v.length&&new RegExp(v.join("|")),a=a.length&&new RegExp(a.join("|")),t=Y.test(s.compareDocumentPosition),y=t||Y.test(s.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,i=t&&t.parentNode;return e===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):e.compareDocumentPosition&&16&e.compareDocumentPosition(i)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},j=t?function(e,t){if(e===t)return c=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!p.sortDetached&&t.compareDocumentPosition(e)===n?e===S||e.ownerDocument===m&&y(m,e)?-1:t===S||t.ownerDocument===m&&y(m,t)?1:l?L(l,e)-L(l,t):0:4&n?-1:1)}:function(e,t){if(e===t)return c=!0,0;var n,i=0,r=e.parentNode,o=t.parentNode,s=[e],a=[t];if(!r||!o)return e===S?-1:t===S?1:r?-1:o?1:l?L(l,e)-L(l,t):0;if(r===o)return fe(e,t);for(n=e;n=n.parentNode;)s.unshift(n);for(n=t;n=n.parentNode;)a.unshift(n);for(;s[i]===a[i];)i++;return i?fe(s[i],a[i]):s[i]===m?-1:a[i]===m?1:0}),S},ae.matches=function(e,t){return ae(e,null,null,t)},ae.matchesSelector=function(e,t){if((e.ownerDocument||e)!==S&&T(e),p.matchesSelector&&C&&!k[t+" "]&&(!a||!a.test(t))&&(!v||!v.test(t)))try{var n=u.call(e,t);if(n||p.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){k(t,!0)}return 0<ae(t,S,null,[e]).length},ae.contains=function(e,t){return(e.ownerDocument||e)!==S&&T(e),y(e,t)},ae.attr=function(e,t){(e.ownerDocument||e)!==S&&T(e);var n=b.attrHandle[t.toLowerCase()],i=n&&E.call(b.attrHandle,t.toLowerCase())?n(e,t,!C):void 0;return void 0!==i?i:p.attributes||!C?e.getAttribute(t):(i=e.getAttributeNode(t))&&i.specified?i.value:null},ae.escape=function(e){return(e+"").replace(ie,re)},ae.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},ae.uniqueSort=function(e){var t,n=[],i=0,r=0;if(c=!p.detectDuplicates,l=!p.sortStable&&e.slice(0),e.sort(j),c){for(;t=e[r++];)t===e[r]&&(i=n.push(r));for(;i--;)e.splice(n[i],1)}return l=null,e},o=ae.getText=function(e){var t,n="",i=0,r=e.nodeType;if(r){if(1===r||9===r||11===r){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===r||4===r)return e.nodeValue}else for(;t=e[i++];)n+=o(t);return n},(b=ae.selectors={cacheLength:50,createPseudo:ce,match:X,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(te,ne),e[3]=(e[3]||e[4]||e[5]||"").replace(te,ne),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||ae.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&ae.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return X.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&Q.test(n)&&(t=h(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(te,ne).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=f[e+" "];return t||(t=new RegExp("(^|"+q+")"+e+"("+q+"|$)"))&&f(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(n,i,r){return function(e){var t=ae.attr(e,n);return null==t?"!="===i:!i||(t+="","="===i?t===r:"!="===i?t!==r:"^="===i?r&&0===t.indexOf(r):"*="===i?r&&-1<t.indexOf(r):"$="===i?r&&t.slice(-r.length)===r:"~="===i?-1<(" "+t.replace(W," ")+" ").indexOf(r):"|="===i&&(t===r||t.slice(0,r.length+1)===r+"-"))}},CHILD:function(h,e,t,g,v){var y="nth"!==h.slice(0,3),m="last"!==h.slice(-4),x="of-type"===e;return 1===g&&0===v?function(e){return!!e.parentNode}:function(e,t,n){var i,r,o,s,a,l,c=y!==m?"nextSibling":"previousSibling",u=e.parentNode,d=x&&e.nodeName.toLowerCase(),f=!n&&!x,p=!1;if(u){if(y){for(;c;){for(s=e;s=s[c];)if(x?s.nodeName.toLowerCase()===d:1===s.nodeType)return!1;l=c="only"===h&&!l&&"nextSibling"}return!0}if(l=[m?u.firstChild:u.lastChild],m&&f){for(p=(a=(i=(r=(o=(s=u)[A]||(s[A]={}))[s.uniqueID]||(o[s.uniqueID]={}))[h]||[])[0]===I&&i[1])&&i[2],s=a&&u.childNodes[a];s=++a&&s&&s[c]||(p=a=0)||l.pop();)if(1===s.nodeType&&++p&&s===e){r[h]=[I,a,p];break}}else if(f&&(p=a=(i=(r=(o=(s=e)[A]||(s[A]={}))[s.uniqueID]||(o[s.uniqueID]={}))[h]||[])[0]===I&&i[1]),!1===p)for(;(s=++a&&s&&s[c]||(p=a=0)||l.pop())&&((x?s.nodeName.toLowerCase()!==d:1!==s.nodeType)||!++p||(f&&((r=(o=s[A]||(s[A]={}))[s.uniqueID]||(o[s.uniqueID]={}))[h]=[I,p]),s!==e)););return(p-=v)===g||p%g==0&&0<=p/g}}},PSEUDO:function(e,o){var t,s=b.pseudos[e]||b.setFilters[e.toLowerCase()]||ae.error("unsupported pseudo: "+e);return s[A]?s(o):1<s.length?(t=[e,e,"",o],b.setFilters.hasOwnProperty(e.toLowerCase())?ce(function(e,t){for(var n,i=s(e,o),r=i.length;r--;)e[n=L(e,i[r])]=!(t[n]=i[r])}):function(e){return s(e,0,t)}):s}},pseudos:{not:ce(function(e){var i=[],r=[],a=d(e.replace(F,"$1"));return a[A]?ce(function(e,t,n,i){for(var r,o=a(e,null,i,[]),s=e.length;s--;)(r=o[s])&&(e[s]=!(t[s]=r))}):function(e,t,n){return i[0]=e,a(i,null,n,r),i[0]=null,!r.pop()}}),has:ce(function(t){return function(e){return 0<ae(t,e).length}}),contains:ce(function(t){return t=t.replace(te,ne),function(e){return-1<(e.textContent||o(e)).indexOf(t)}}),lang:ce(function(n){return U.test(n||"")||ae.error("unsupported lang: "+n),n=n.replace(te,ne).toLowerCase(),function(e){var t;do{if(t=C?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=n.location&&n.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===s},focus:function(e){return e===S.activeElement&&(!S.hasFocus||S.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:ge(!1),disabled:ge(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!b.pseudos.empty(e)},header:function(e){return Z.test(e.nodeName)},input:function(e){return J.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:ve(function(){return[0]}),last:ve(function(e,t){return[t-1]}),eq:ve(function(e,t,n){return[n<0?n+t:n]}),even:ve(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:ve(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:ve(function(e,t,n){for(var i=n<0?n+t:t<n?t:n;0<=--i;)e.push(i);return e}),gt:ve(function(e,t,n){for(var i=n<0?n+t:n;++i<t;)e.push(i);return e})}}).pseudos.nth=b.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})b.pseudos[e]=pe(e);for(e in{submit:!0,reset:!0})b.pseudos[e]=he(e);function me(){}function xe(e){for(var t=0,n=e.length,i="";t<n;t++)i+=e[t].value;return i}function be(a,e,t){var l=e.dir,c=e.next,u=c||l,d=t&&"parentNode"===u,f=i++;return e.first?function(e,t,n){for(;e=e[l];)if(1===e.nodeType||d)return a(e,t,n);return!1}:function(e,t,n){var i,r,o,s=[I,f];if(n){for(;e=e[l];)if((1===e.nodeType||d)&&a(e,t,n))return!0}else for(;e=e[l];)if(1===e.nodeType||d)if(r=(o=e[A]||(e[A]={}))[e.uniqueID]||(o[e.uniqueID]={}),c&&c===e.nodeName.toLowerCase())e=e[l]||e;else{if((i=r[u])&&i[0]===I&&i[1]===f)return s[2]=i[2];if((r[u]=s)[2]=a(e,t,n))return!0}return!1}}function we(r){return 1<r.length?function(e,t,n){for(var i=r.length;i--;)if(!r[i](e,t,n))return!1;return!0}:r[0]}function Te(e,t,n,i,r){for(var o,s=[],a=0,l=e.length,c=null!=t;a<l;a++)(o=e[a])&&(n&&!n(o,i,r)||(s.push(o),c&&t.push(a)));return s}function Se(p,h,g,v,y,e){return v&&!v[A]&&(v=Se(v)),y&&!y[A]&&(y=Se(y,e)),ce(function(e,t,n,i){var r,o,s,a=[],l=[],c=t.length,u=e||function(e,t,n){for(var i=0,r=t.length;i<r;i++)ae(e,t[i],n);return n}(h||"*",n.nodeType?[n]:n,[]),d=!p||!e&&h?u:Te(u,a,p,n,i),f=g?y||(e?p:c||v)?[]:t:d;if(g&&g(d,f,n,i),v)for(r=Te(f,l),v(r,[],n,i),o=r.length;o--;)(s=r[o])&&(f[l[o]]=!(d[l[o]]=s));if(e){if(y||p){if(y){for(r=[],o=f.length;o--;)(s=f[o])&&r.push(d[o]=s);y(null,f=[],r,i)}for(o=f.length;o--;)(s=f[o])&&-1<(r=y?L(e,s):a[o])&&(e[r]=!(t[r]=s))}}else f=Te(f===t?f.splice(c,f.length):f),y?y(null,t,f,i):_.apply(t,f)})}function Ce(e){for(var r,t,n,i=e.length,o=b.relative[e[0].type],s=o||b.relative[" "],a=o?1:0,l=be(function(e){return e===r},s,!0),c=be(function(e){return-1<L(r,e)},s,!0),u=[function(e,t,n){var i=!o&&(n||t!==w)||((r=t).nodeType?l(e,t,n):c(e,t,n));return r=null,i}];a<i;a++)if(t=b.relative[e[a].type])u=[be(we(u),t)];else{if((t=b.filter[e[a].type].apply(null,e[a].matches))[A]){for(n=++a;n<i&&!b.relative[e[n].type];n++);return Se(1<a&&we(u),1<a&&xe(e.slice(0,a-1).concat({value:" "===e[a-2].type?"*":""})).replace(F,"$1"),t,a<n&&Ce(e.slice(a,n)),n<i&&Ce(e=e.slice(n)),n<i&&xe(e))}u.push(t)}return we(u)}return me.prototype=b.filters=b.pseudos,b.setFilters=new me,h=ae.tokenize=function(e,t){var n,i,r,o,s,a,l,c=x[e+" "];if(c)return t?0:c.slice(0);for(s=e,a=[],l=b.preFilter;s;){for(o in n&&!(i=$.exec(s))||(i&&(s=s.slice(i[0].length)||s),a.push(r=[])),n=!1,(i=B.exec(s))&&(n=i.shift(),r.push({value:n,type:i[0].replace(F," ")}),s=s.slice(n.length)),b.filter)!(i=X[o].exec(s))||l[o]&&!(i=l[o](i))||(n=i.shift(),r.push({value:n,type:o,matches:i}),s=s.slice(n.length));if(!n)break}return t?s.length:s?ae.error(e):x(e,a).slice(0)},d=ae.compile=function(e,t){var n,v,y,m,x,i,r=[],o=[],s=P[e+" "];if(!s){for(t||(t=h(e)),n=t.length;n--;)(s=Ce(t[n]))[A]?r.push(s):o.push(s);(s=P(e,(v=o,m=0<(y=r).length,x=0<v.length,i=function(e,t,n,i,r){var o,s,a,l=0,c="0",u=e&&[],d=[],f=w,p=e||x&&b.find.TAG("*",r),h=I+=null==f?1:Math.random()||.1,g=p.length;for(r&&(w=t===S||t||r);c!==g&&null!=(o=p[c]);c++){if(x&&o){for(s=0,t||o.ownerDocument===S||(T(o),n=!C);a=v[s++];)if(a(o,t||S,n)){i.push(o);break}r&&(I=h)}m&&((o=!a&&o)&&l--,e&&u.push(o))}if(l+=c,m&&c!==l){for(s=0;a=y[s++];)a(u,d,t,n);if(e){if(0<l)for(;c--;)u[c]||d[c]||(d[c]=N.call(i));d=Te(d)}_.apply(i,d),r&&!e&&0<d.length&&1<l+y.length&&ae.uniqueSort(i)}return r&&(I=h,w=f),u},m?ce(i):i))).selector=e}return s},g=ae.select=function(e,t,n,i){var r,o,s,a,l,c="function"==typeof e&&e,u=!i&&h(e=c.selector||e);if(n=n||[],1===u.length){if(2<(o=u[0]=u[0].slice(0)).length&&"ID"===(s=o[0]).type&&9===t.nodeType&&C&&b.relative[o[1].type]){if(!(t=(b.find.ID(s.matches[0].replace(te,ne),t)||[])[0]))return n;c&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(r=X.needsContext.test(e)?0:o.length;r--&&(s=o[r],!b.relative[a=s.type]);)if((l=b.find[a])&&(i=l(s.matches[0].replace(te,ne),ee.test(o[0].type)&&ye(t.parentNode)||t))){if(o.splice(r,1),!(e=i.length&&xe(o)))return _.apply(n,i),n;break}}return(c||d(e,u))(i,t,!C,n,!t||ee.test(e)&&ye(t.parentNode)||t),n},p.sortStable=A.split("").sort(j).join("")===A,p.detectDuplicates=!!c,T(),p.sortDetached=ue(function(e){return 1&e.compareDocumentPosition(S.createElement("fieldset"))}),ue(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||de("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),p.attributes&&ue(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||de("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),ue(function(e){return null==e.getAttribute("disabled")})||de(O,function(e,t,n){var i;if(!n)return!0===e[t]?t.toLowerCase():(i=e.getAttributeNode(t))&&i.specified?i.value:null}),ae}(S);A.find=h,A.expr=h.selectors,A.expr[":"]=A.expr.pseudos,A.uniqueSort=A.unique=h.uniqueSort,A.text=h.getText,A.isXMLDoc=h.isXML,A.contains=h.contains,A.escapeSelector=h.escape;var T=function(e,t,n){for(var i=[],r=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(r&&A(e).is(n))break;i.push(e)}return i},I=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},P=A.expr.match.needsContext;function k(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var j=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function E(e,n,i){return m(n)?A.grep(e,function(e,t){return!!n.call(e,t,e)!==i}):n.nodeType?A.grep(e,function(e){return e===n!==i}):"string"!=typeof n?A.grep(e,function(e){return-1<r.call(n,e)!==i}):A.filter(n,e,i)}A.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?A.find.matchesSelector(i,e)?[i]:[]:A.find.matches(e,A.grep(t,function(e){return 1===e.nodeType}))},A.fn.extend({find:function(e){var t,n,i=this.length,r=this;if("string"!=typeof e)return this.pushStack(A(e).filter(function(){for(t=0;t<i;t++)if(A.contains(r[t],this))return!0}));for(n=this.pushStack([]),t=0;t<i;t++)A.find(e,r[t],n);return 1<i?A.uniqueSort(n):n},filter:function(e){return this.pushStack(E(this,e||[],!1))},not:function(e){return this.pushStack(E(this,e||[],!0))},is:function(e){return!!E(this,"string"==typeof e&&P.test(e)?A(e):e||[],!1).length}});var N,D=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(A.fn.init=function(e,t,n){var i,r;if(!e)return this;if(n=n||N,"string"!=typeof e)return e.nodeType?(this[0]=e,this.length=1,this):m(e)?void 0!==n.ready?n.ready(e):e(A):A.makeArray(e,this);if(!(i="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:D.exec(e))||!i[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(i[1]){if(t=t instanceof A?t[0]:t,A.merge(this,A.parseHTML(i[1],t&&t.nodeType?t.ownerDocument||t:C,!0)),j.test(i[1])&&A.isPlainObject(t))for(i in t)m(this[i])?this[i](t[i]):this.attr(i,t[i]);return this}return(r=C.getElementById(i[2]))&&(this[0]=r,this.length=1),this}).prototype=A.fn,N=A(C);var _=/^(?:parents|prev(?:Until|All))/,H={children:!0,contents:!0,next:!0,prev:!0};function L(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}A.fn.extend({has:function(e){var t=A(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(A.contains(this,t[e]))return!0})},closest:function(e,t){var n,i=0,r=this.length,o=[],s="string"!=typeof e&&A(e);if(!P.test(e))for(;i<r;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(s?-1<s.index(n):1===n.nodeType&&A.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?A.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?r.call(A(e),this[0]):r.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(A.uniqueSort(A.merge(this.get(),A(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),A.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return T(e,"parentNode")},parentsUntil:function(e,t,n){return T(e,"parentNode",n)},next:function(e){return L(e,"nextSibling")},prev:function(e){return L(e,"previousSibling")},nextAll:function(e){return T(e,"nextSibling")},prevAll:function(e){return T(e,"previousSibling")},nextUntil:function(e,t,n){return T(e,"nextSibling",n)},prevUntil:function(e,t,n){return T(e,"previousSibling",n)},siblings:function(e){return I((e.parentNode||{}).firstChild,e)},children:function(e){return I(e.firstChild)},contents:function(e){return void 0!==e.contentDocument?e.contentDocument:(k(e,"template")&&(e=e.content||e),A.merge([],e.childNodes))}},function(i,r){A.fn[i]=function(e,t){var n=A.map(this,r,e);return"Until"!==i.slice(-5)&&(t=e),t&&"string"==typeof t&&(n=A.filter(t,n)),1<this.length&&(H[i]||A.uniqueSort(n),_.test(i)&&n.reverse()),this.pushStack(n)}});var O=/[^\x20\t\r\n\f]+/g;function q(e){return e}function R(e){throw e}function z(e,t,n,i){var r;try{e&&m(r=e.promise)?r.call(e).done(t).fail(n):e&&m(r=e.then)?r.call(e,t,n):t.apply(void 0,[e].slice(i))}catch(e){n.apply(void 0,[e])}}A.Callbacks=function(i){var e,n;i="string"==typeof i?(e=i,n={},A.each(e.match(O)||[],function(e,t){n[t]=!0}),n):A.extend({},i);var r,t,o,s,a=[],l=[],c=-1,u=function(){for(s=s||i.once,o=r=!0;l.length;c=-1)for(t=l.shift();++c<a.length;)!1===a[c].apply(t[0],t[1])&&i.stopOnFalse&&(c=a.length,t=!1);i.memory||(t=!1),r=!1,s&&(a=t?[]:"")},d={add:function(){return a&&(t&&!r&&(c=a.length-1,l.push(t)),function n(e){A.each(e,function(e,t){m(t)?i.unique&&d.has(t)||a.push(t):t&&t.length&&"string"!==w(t)&&n(t)})}(arguments),t&&!r&&u()),this},remove:function(){return A.each(arguments,function(e,t){for(var n;-1<(n=A.inArray(t,a,n));)a.splice(n,1),n<=c&&c--}),this},has:function(e){return e?-1<A.inArray(e,a):0<a.length},empty:function(){return a&&(a=[]),this},disable:function(){return s=l=[],a=t="",this},disabled:function(){return!a},lock:function(){return s=l=[],t||r||(a=t=""),this},locked:function(){return!!s},fireWith:function(e,t){return s||(t=[e,(t=t||[]).slice?t.slice():t],l.push(t),r||u()),this},fire:function(){return d.fireWith(this,arguments),this},fired:function(){return!!o}};return d},A.extend({Deferred:function(e){var o=[["notify","progress",A.Callbacks("memory"),A.Callbacks("memory"),2],["resolve","done",A.Callbacks("once memory"),A.Callbacks("once memory"),0,"resolved"],["reject","fail",A.Callbacks("once memory"),A.Callbacks("once memory"),1,"rejected"]],r="pending",s={state:function(){return r},always:function(){return a.done(arguments).fail(arguments),this},catch:function(e){return s.then(null,e)},pipe:function(){var r=arguments;return A.Deferred(function(i){A.each(o,function(e,t){var n=m(r[t[4]])&&r[t[4]];a[t[1]](function(){var e=n&&n.apply(this,arguments);e&&m(e.promise)?e.promise().progress(i.notify).done(i.resolve).fail(i.reject):i[t[0]+"With"](this,n?[e]:arguments)})}),r=null}).promise()},then:function(t,n,i){var l=0;function c(r,o,s,a){return function(){var n=this,i=arguments,e=function(){var e,t;if(!(r<l)){if((e=s.apply(n,i))===o.promise())throw new TypeError("Thenable self-resolution");t=e&&("object"==typeof e||"function"==typeof e)&&e.then,m(t)?a?t.call(e,c(l,o,q,a),c(l,o,R,a)):(l++,t.call(e,c(l,o,q,a),c(l,o,R,a),c(l,o,q,o.notifyWith))):(s!==q&&(n=void 0,i=[e]),(a||o.resolveWith)(n,i))}},t=a?e:function(){try{e()}catch(e){A.Deferred.exceptionHook&&A.Deferred.exceptionHook(e,t.stackTrace),l<=r+1&&(s!==R&&(n=void 0,i=[e]),o.rejectWith(n,i))}};r?t():(A.Deferred.getStackHook&&(t.stackTrace=A.Deferred.getStackHook()),S.setTimeout(t))}}return A.Deferred(function(e){o[0][3].add(c(0,e,m(i)?i:q,e.notifyWith)),o[1][3].add(c(0,e,m(t)?t:q)),o[2][3].add(c(0,e,m(n)?n:R))}).promise()},promise:function(e){return null!=e?A.extend(e,s):s}},a={};return A.each(o,function(e,t){var n=t[2],i=t[5];s[t[1]]=n.add,i&&n.add(function(){r=i},o[3-e][2].disable,o[3-e][3].disable,o[0][2].lock,o[0][3].lock),n.add(t[3].fire),a[t[0]]=function(){return a[t[0]+"With"](this===a?void 0:this,arguments),this},a[t[0]+"With"]=n.fireWith}),s.promise(a),e&&e.call(a,a),a},when:function(e){var n=arguments.length,t=n,i=Array(t),r=a.call(arguments),o=A.Deferred(),s=function(t){return function(e){i[t]=this,r[t]=1<arguments.length?a.call(arguments):e,--n||o.resolveWith(i,r)}};if(n<=1&&(z(e,o.done(s(t)).resolve,o.reject,!n),"pending"===o.state()||m(r[t]&&r[t].then)))return o.then();for(;t--;)z(r[t],s(t),o.reject);return o.promise()}});var M=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;A.Deferred.exceptionHook=function(e,t){S.console&&S.console.warn&&e&&M.test(e.name)&&S.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},A.readyException=function(e){S.setTimeout(function(){throw e})};var W=A.Deferred();function F(){C.removeEventListener("DOMContentLoaded",F),S.removeEventListener("load",F),A.ready()}A.fn.ready=function(e){return W.then(e).catch(function(e){A.readyException(e)}),this},A.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--A.readyWait:A.isReady)||(A.isReady=!0)!==e&&0<--A.readyWait||W.resolveWith(C,[A])}}),A.ready.then=W.then,"complete"===C.readyState||"loading"!==C.readyState&&!C.documentElement.doScroll?S.setTimeout(A.ready):(C.addEventListener("DOMContentLoaded",F),S.addEventListener("load",F));var $=function(e,t,n,i,r,o,s){var a=0,l=e.length,c=null==n;if("object"===w(n))for(a in r=!0,n)$(e,t,a,n[a],!0,o,s);else if(void 0!==i&&(r=!0,m(i)||(s=!0),c&&(t=s?(t.call(e,i),null):(c=t,function(e,t,n){return c.call(A(e),n)})),t))for(;a<l;a++)t(e[a],n,s?i:i.call(e[a],a,t(e[a],n)));return r?e:c?t.call(e):l?t(e[0],n):o},B=/^-ms-/,V=/-([a-z])/g;function Q(e,t){return t.toUpperCase()}function U(e){return e.replace(B,"ms-").replace(V,Q)}var X=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function G(){this.expando=A.expando+G.uid++}G.uid=1,G.prototype={cache:function(e){var t=e[this.expando];return t||(t={},X(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var i,r=this.cache(e);if("string"==typeof t)r[U(t)]=n;else for(i in t)r[U(i)]=t[i];return r},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][U(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,i=e[this.expando];if(void 0!==i){if(void 0!==t){n=(t=Array.isArray(t)?t.map(U):(t=U(t))in i?[t]:t.match(O)||[]).length;for(;n--;)delete i[t[n]]}(void 0===t||A.isEmptyObject(i))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!A.isEmptyObject(t)}};var J=new G,Z=new G,Y=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,K=/[A-Z]/g;function ee(e,t,n){var i,r;if(void 0===n&&1===e.nodeType)if(i="data-"+t.replace(K,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(i))){try{n="true"===(r=n)||"false"!==r&&("null"===r?null:r===+r+""?+r:Y.test(r)?JSON.parse(r):r)}catch(e){}Z.set(e,t,n)}else n=void 0;return n}A.extend({hasData:function(e){return Z.hasData(e)||J.hasData(e)},data:function(e,t,n){return Z.access(e,t,n)},removeData:function(e,t){Z.remove(e,t)},_data:function(e,t,n){return J.access(e,t,n)},_removeData:function(e,t){J.remove(e,t)}}),A.fn.extend({data:function(n,e){var t,i,r,o=this[0],s=o&&o.attributes;if(void 0!==n)return"object"==typeof n?this.each(function(){Z.set(this,n)}):$(this,function(e){var t;if(o&&void 0===e)return void 0!==(t=Z.get(o,n))?t:void 0!==(t=ee(o,n))?t:void 0;this.each(function(){Z.set(this,n,e)})},null,e,1<arguments.length,null,!0);if(this.length&&(r=Z.get(o),1===o.nodeType&&!J.get(o,"hasDataAttrs"))){for(t=s.length;t--;)s[t]&&0===(i=s[t].name).indexOf("data-")&&(i=U(i.slice(5)),ee(o,i,r[i]));J.set(o,"hasDataAttrs",!0)}return r},removeData:function(e){return this.each(function(){Z.remove(this,e)})}}),A.extend({queue:function(e,t,n){var i;if(e)return t=(t||"fx")+"queue",i=J.get(e,t),n&&(!i||Array.isArray(n)?i=J.access(e,t,A.makeArray(n)):i.push(n)),i||[]},dequeue:function(e,t){t=t||"fx";var n=A.queue(e,t),i=n.length,r=n.shift(),o=A._queueHooks(e,t);"inprogress"===r&&(r=n.shift(),i--),r&&("fx"===t&&n.unshift("inprogress"),delete o.stop,r.call(e,function(){A.dequeue(e,t)},o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return J.get(e,n)||J.access(e,n,{empty:A.Callbacks("once memory").add(function(){J.remove(e,[t+"queue",n])})})}}),A.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?A.queue(this[0],t):void 0===n?this:this.each(function(){var e=A.queue(this,t,n);A._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&A.dequeue(this,t)})},dequeue:function(e){return this.each(function(){A.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,r=A.Deferred(),o=this,s=this.length,a=function(){--i||r.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(n=J.get(o[s],e+"queueHooks"))&&n.empty&&(i++,n.empty.add(a));return a(),r.promise(t)}});var te=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ne=new RegExp("^(?:([+-])=|)("+te+")([a-z%]*)$","i"),ie=["Top","Right","Bottom","Left"],re=C.documentElement,oe=function(e){return A.contains(e.ownerDocument,e)},se={composed:!0};re.getRootNode&&(oe=function(e){return A.contains(e.ownerDocument,e)||e.getRootNode(se)===e.ownerDocument});var ae=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&oe(e)&&"none"===A.css(e,"display")},le=function(e,t,n,i){var r,o,s={};for(o in t)s[o]=e.style[o],e.style[o]=t[o];for(o in r=n.apply(e,i||[]),t)e.style[o]=s[o];return r};function ce(e,t,n,i){var r,o,s=20,a=i?function(){return i.cur()}:function(){return A.css(e,t,"")},l=a(),c=n&&n[3]||(A.cssNumber[t]?"":"px"),u=e.nodeType&&(A.cssNumber[t]||"px"!==c&&+l)&&ne.exec(A.css(e,t));if(u&&u[3]!==c){for(l/=2,c=c||u[3],u=+l||1;s--;)A.style(e,t,u+c),(1-o)*(1-(o=a()/l||.5))<=0&&(s=0),u/=o;u*=2,A.style(e,t,u+c),n=n||[]}return n&&(u=+u||+l||0,r=n[1]?u+(n[1]+1)*n[2]:+n[2],i&&(i.unit=c,i.start=u,i.end=r)),r}var ue={};function de(e,t){for(var n,i,r,o,s,a,l,c=[],u=0,d=e.length;u<d;u++)(i=e[u]).style&&(n=i.style.display,t?("none"===n&&(c[u]=J.get(i,"display")||null,c[u]||(i.style.display="")),""===i.style.display&&ae(i)&&(c[u]=(l=s=o=void 0,s=(r=i).ownerDocument,a=r.nodeName,(l=ue[a])||(o=s.body.appendChild(s.createElement(a)),l=A.css(o,"display"),o.parentNode.removeChild(o),"none"===l&&(l="block"),ue[a]=l)))):"none"!==n&&(c[u]="none",J.set(i,"display",n)));for(u=0;u<d;u++)null!=c[u]&&(e[u].style.display=c[u]);return e}A.fn.extend({show:function(){return de(this,!0)},hide:function(){return de(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){ae(this)?A(this).show():A(this).hide()})}});var fe=/^(?:checkbox|radio)$/i,pe=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,he=/^$|^module$|\/(?:java|ecma)script/i,ge={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function ve(e,t){var n;return n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&k(e,t)?A.merge([e],n):n}function ye(e,t){for(var n=0,i=e.length;n<i;n++)J.set(e[n],"globalEval",!t||J.get(t[n],"globalEval"))}ge.optgroup=ge.option,ge.tbody=ge.tfoot=ge.colgroup=ge.caption=ge.thead,ge.th=ge.td;var me,xe,be=/<|&#?\w+;/;function we(e,t,n,i,r){for(var o,s,a,l,c,u,d=t.createDocumentFragment(),f=[],p=0,h=e.length;p<h;p++)if((o=e[p])||0===o)if("object"===w(o))A.merge(f,o.nodeType?[o]:o);else if(be.test(o)){for(s=s||d.appendChild(t.createElement("div")),a=(pe.exec(o)||["",""])[1].toLowerCase(),l=ge[a]||ge._default,s.innerHTML=l[1]+A.htmlPrefilter(o)+l[2],u=l[0];u--;)s=s.lastChild;A.merge(f,s.childNodes),(s=d.firstChild).textContent=""}else f.push(t.createTextNode(o));for(d.textContent="",p=0;o=f[p++];)if(i&&-1<A.inArray(o,i))r&&r.push(o);else if(c=oe(o),s=ve(d.appendChild(o),"script"),c&&ye(s),n)for(u=0;o=s[u++];)he.test(o.type||"")&&n.push(o);return d}me=C.createDocumentFragment().appendChild(C.createElement("div")),(xe=C.createElement("input")).setAttribute("type","radio"),xe.setAttribute("checked","checked"),xe.setAttribute("name","t"),me.appendChild(xe),y.checkClone=me.cloneNode(!0).cloneNode(!0).lastChild.checked,me.innerHTML="<textarea>x</textarea>",y.noCloneChecked=!!me.cloneNode(!0).lastChild.defaultValue;var Te=/^key/,Se=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Ce=/^([^.]*)(?:\.(.+)|)/;function Ae(){return!0}function Ie(){return!1}function Pe(e,t){return e===function(){try{return C.activeElement}catch(e){}}()==("focus"===t)}function ke(e,t,n,i,r,o){var s,a;if("object"==typeof t){for(a in"string"!=typeof n&&(i=i||n,n=void 0),t)ke(e,a,n,i,t[a],o);return e}if(null==i&&null==r?(r=n,i=n=void 0):null==r&&("string"==typeof n?(r=i,i=void 0):(r=i,i=n,n=void 0)),!1===r)r=Ie;else if(!r)return e;return 1===o&&(s=r,(r=function(e){return A().off(e),s.apply(this,arguments)}).guid=s.guid||(s.guid=A.guid++)),e.each(function(){A.event.add(this,t,r,i,n)})}function je(e,r,o){o?(J.set(e,r,!1),A.event.add(e,r,{namespace:!1,handler:function(e){var t,n,i=J.get(this,r);if(1&e.isTrigger&&this[r]){if(i.length)(A.event.special[r]||{}).delegateType&&e.stopPropagation();else if(i=a.call(arguments),J.set(this,r,i),t=o(this,r),this[r](),i!==(n=J.get(this,r))||t?J.set(this,r,!1):n={},i!==n)return e.stopImmediatePropagation(),e.preventDefault(),n.value}else i.length&&(J.set(this,r,{value:A.event.trigger(A.extend(i[0],A.Event.prototype),i.slice(1),this)}),e.stopImmediatePropagation())}})):void 0===J.get(e,r)&&A.event.add(e,r,Ae)}A.event={global:{},add:function(t,e,n,i,r){var o,s,a,l,c,u,d,f,p,h,g,v=J.get(t);if(v)for(n.handler&&(n=(o=n).handler,r=o.selector),r&&A.find.matchesSelector(re,r),n.guid||(n.guid=A.guid++),(l=v.events)||(l=v.events={}),(s=v.handle)||(s=v.handle=function(e){return void 0!==A&&A.event.triggered!==e.type?A.event.dispatch.apply(t,arguments):void 0}),c=(e=(e||"").match(O)||[""]).length;c--;)p=g=(a=Ce.exec(e[c])||[])[1],h=(a[2]||"").split(".").sort(),p&&(d=A.event.special[p]||{},p=(r?d.delegateType:d.bindType)||p,d=A.event.special[p]||{},u=A.extend({type:p,origType:g,data:i,handler:n,guid:n.guid,selector:r,needsContext:r&&A.expr.match.needsContext.test(r),namespace:h.join(".")},o),(f=l[p])||((f=l[p]=[]).delegateCount=0,d.setup&&!1!==d.setup.call(t,i,h,s)||t.addEventListener&&t.addEventListener(p,s)),d.add&&(d.add.call(t,u),u.handler.guid||(u.handler.guid=n.guid)),r?f.splice(f.delegateCount++,0,u):f.push(u),A.event.global[p]=!0)},remove:function(e,t,n,i,r){var o,s,a,l,c,u,d,f,p,h,g,v=J.hasData(e)&&J.get(e);if(v&&(l=v.events)){for(c=(t=(t||"").match(O)||[""]).length;c--;)if(p=g=(a=Ce.exec(t[c])||[])[1],h=(a[2]||"").split(".").sort(),p){for(d=A.event.special[p]||{},f=l[p=(i?d.delegateType:d.bindType)||p]||[],a=a[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=f.length;o--;)u=f[o],!r&&g!==u.origType||n&&n.guid!==u.guid||a&&!a.test(u.namespace)||i&&i!==u.selector&&("**"!==i||!u.selector)||(f.splice(o,1),u.selector&&f.delegateCount--,d.remove&&d.remove.call(e,u));s&&!f.length&&(d.teardown&&!1!==d.teardown.call(e,h,v.handle)||A.removeEvent(e,p,v.handle),delete l[p])}else for(p in l)A.event.remove(e,p+t[c],n,i,!0);A.isEmptyObject(l)&&J.remove(e,"handle events")}},dispatch:function(e){var t,n,i,r,o,s,a=A.event.fix(e),l=new Array(arguments.length),c=(J.get(this,"events")||{})[a.type]||[],u=A.event.special[a.type]||{};for(l[0]=a,t=1;t<arguments.length;t++)l[t]=arguments[t];if(a.delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,a)){for(s=A.event.handlers.call(this,a,c),t=0;(r=s[t++])&&!a.isPropagationStopped();)for(a.currentTarget=r.elem,n=0;(o=r.handlers[n++])&&!a.isImmediatePropagationStopped();)a.rnamespace&&!1!==o.namespace&&!a.rnamespace.test(o.namespace)||(a.handleObj=o,a.data=o.data,void 0!==(i=((A.event.special[o.origType]||{}).handle||o.handler).apply(r.elem,l))&&!1===(a.result=i)&&(a.preventDefault(),a.stopPropagation()));return u.postDispatch&&u.postDispatch.call(this,a),a.result}},handlers:function(e,t){var n,i,r,o,s,a=[],l=t.delegateCount,c=e.target;if(l&&c.nodeType&&!("click"===e.type&&1<=e.button))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==e.type||!0!==c.disabled)){for(o=[],s={},n=0;n<l;n++)void 0===s[r=(i=t[n]).selector+" "]&&(s[r]=i.needsContext?-1<A(r,this).index(c):A.find(r,this,null,[c]).length),s[r]&&o.push(i);o.length&&a.push({elem:c,handlers:o})}return c=this,l<t.length&&a.push({elem:c,handlers:t.slice(l)}),a},addProp:function(t,e){Object.defineProperty(A.Event.prototype,t,{enumerable:!0,configurable:!0,get:m(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[A.expando]?e:new A.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return fe.test(t.type)&&t.click&&k(t,"input")&&je(t,"click",Ae),!1},trigger:function(e){var t=this||e;return fe.test(t.type)&&t.click&&k(t,"input")&&je(t,"click"),!0},_default:function(e){var t=e.target;return fe.test(t.type)&&t.click&&k(t,"input")&&J.get(t,"click")||k(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},A.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},A.Event=function(e,t){if(!(this instanceof A.Event))return new A.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?Ae:Ie,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&A.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[A.expando]=!0},A.Event.prototype={constructor:A.Event,isDefaultPrevented:Ie,isPropagationStopped:Ie,isImmediatePropagationStopped:Ie,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Ae,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Ae,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Ae,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},A.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(e){var t=e.button;return null==e.which&&Te.test(e.type)?null!=e.charCode?e.charCode:e.keyCode:!e.which&&void 0!==t&&Se.test(e.type)?1&t?1:2&t?3:4&t?2:0:e.which}},A.event.addProp),A.each({focus:"focusin",blur:"focusout"},function(e,t){A.event.special[e]={setup:function(){return je(this,e,Pe),!1},trigger:function(){return je(this,e),!0},delegateType:t}}),A.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,r){A.event.special[e]={delegateType:r,bindType:r,handle:function(e){var t,n=e.relatedTarget,i=e.handleObj;return n&&(n===this||A.contains(this,n))||(e.type=i.origType,t=i.handler.apply(this,arguments),e.type=r),t}}}),A.fn.extend({on:function(e,t,n,i){return ke(this,e,t,n,i)},one:function(e,t,n,i){return ke(this,e,t,n,i,1)},off:function(e,t,n){var i,r;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,A(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"!=typeof e)return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Ie),this.each(function(){A.event.remove(this,e,n,t)});for(r in e)this.off(r,t,e[r]);return this}});var Ee=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,Ne=/<script|<style|<link/i,De=/checked\s*(?:[^=]|=\s*.checked.)/i,_e=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function He(e,t){return k(e,"table")&&k(11!==t.nodeType?t:t.firstChild,"tr")&&A(e).children("tbody")[0]||e}function Le(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Oe(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function qe(e,t){var n,i,r,o,s,a,l,c;if(1===t.nodeType){if(J.hasData(e)&&(o=J.access(e),s=J.set(t,o),c=o.events))for(r in delete s.handle,s.events={},c)for(n=0,i=c[r].length;n<i;n++)A.event.add(t,r,c[r][n]);Z.hasData(e)&&(a=Z.access(e),l=A.extend({},a),Z.set(t,l))}}function Re(n,i,r,o){i=g.apply([],i);var e,t,s,a,l,c,u=0,d=n.length,f=d-1,p=i[0],h=m(p);if(h||1<d&&"string"==typeof p&&!y.checkClone&&De.test(p))return n.each(function(e){var t=n.eq(e);h&&(i[0]=p.call(this,e,t.html())),Re(t,i,r,o)});if(d&&(t=(e=we(i,n[0].ownerDocument,!1,n,o)).firstChild,1===e.childNodes.length&&(e=t),t||o)){for(a=(s=A.map(ve(e,"script"),Le)).length;u<d;u++)l=e,u!==f&&(l=A.clone(l,!0,!0),a&&A.merge(s,ve(l,"script"))),r.call(n[u],l,u);if(a)for(c=s[s.length-1].ownerDocument,A.map(s,Oe),u=0;u<a;u++)l=s[u],he.test(l.type||"")&&!J.access(l,"globalEval")&&A.contains(c,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?A._evalUrl&&!l.noModule&&A._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")}):b(l.textContent.replace(_e,""),l,c))}return n}function ze(e,t,n){for(var i,r=t?A.filter(t,e):e,o=0;null!=(i=r[o]);o++)n||1!==i.nodeType||A.cleanData(ve(i)),i.parentNode&&(n&&oe(i)&&ye(ve(i,"script")),i.parentNode.removeChild(i));return e}A.extend({htmlPrefilter:function(e){return e.replace(Ee,"<$1></$2>")},clone:function(e,t,n){var i,r,o,s,a,l,c,u=e.cloneNode(!0),d=oe(e);if(!(y.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||A.isXMLDoc(e)))for(s=ve(u),i=0,r=(o=ve(e)).length;i<r;i++)a=o[i],"input"===(c=(l=s[i]).nodeName.toLowerCase())&&fe.test(a.type)?l.checked=a.checked:"input"!==c&&"textarea"!==c||(l.defaultValue=a.defaultValue);if(t)if(n)for(o=o||ve(e),s=s||ve(u),i=0,r=o.length;i<r;i++)qe(o[i],s[i]);else qe(e,u);return 0<(s=ve(u,"script")).length&&ye(s,!d&&ve(e,"script")),u},cleanData:function(e){for(var t,n,i,r=A.event.special,o=0;void 0!==(n=e[o]);o++)if(X(n)){if(t=n[J.expando]){if(t.events)for(i in t.events)r[i]?A.event.remove(n,i):A.removeEvent(n,i,t.handle);n[J.expando]=void 0}n[Z.expando]&&(n[Z.expando]=void 0)}}}),A.fn.extend({detach:function(e){return ze(this,e,!0)},remove:function(e){return ze(this,e)},text:function(e){return $(this,function(e){return void 0===e?A.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return Re(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||He(this,e).appendChild(e)})},prepend:function(){return Re(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=He(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return Re(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Re(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(A.cleanData(ve(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return A.clone(this,e,t)})},html:function(e){return $(this,function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Ne.test(e)&&!ge[(pe.exec(e)||["",""])[1].toLowerCase()]){e=A.htmlPrefilter(e);try{for(;n<i;n++)1===(t=this[n]||{}).nodeType&&(A.cleanData(ve(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return Re(this,arguments,function(e){var t=this.parentNode;A.inArray(this,n)<0&&(A.cleanData(ve(this)),t&&t.replaceChild(e,this))},n)}}),A.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,s){A.fn[e]=function(e){for(var t,n=[],i=A(e),r=i.length-1,o=0;o<=r;o++)t=o===r?this:this.clone(!0),A(i[o])[s](t),l.apply(n,t.get());return this.pushStack(n)}});var Me=new RegExp("^("+te+")(?!px)[a-z%]+$","i"),We=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=S),t.getComputedStyle(e)},Fe=new RegExp(ie.join("|"),"i");function $e(e,t,n){var i,r,o,s,a=e.style;return(n=n||We(e))&&(""!==(s=n.getPropertyValue(t)||n[t])||oe(e)||(s=A.style(e,t)),!y.pixelBoxStyles()&&Me.test(s)&&Fe.test(t)&&(i=a.width,r=a.minWidth,o=a.maxWidth,a.minWidth=a.maxWidth=a.width=s,s=n.width,a.width=i,a.minWidth=r,a.maxWidth=o)),void 0!==s?s+"":s}function Be(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function e(){if(l){a.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",l.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",re.appendChild(a).appendChild(l);var e=S.getComputedStyle(l);n="1%"!==e.top,s=12===t(e.marginLeft),l.style.right="60%",o=36===t(e.right),i=36===t(e.width),l.style.position="absolute",r=12===t(l.offsetWidth/3),re.removeChild(a),l=null}}function t(e){return Math.round(parseFloat(e))}var n,i,r,o,s,a=C.createElement("div"),l=C.createElement("div");l.style&&(l.style.backgroundClip="content-box",l.cloneNode(!0).style.backgroundClip="",y.clearCloneStyle="content-box"===l.style.backgroundClip,A.extend(y,{boxSizingReliable:function(){return e(),i},pixelBoxStyles:function(){return e(),o},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),s},scrollboxSize:function(){return e(),r}}))}();var Ve=["Webkit","Moz","ms"],Qe=C.createElement("div").style,Ue={};function Xe(e){return A.cssProps[e]||Ue[e]||(e in Qe?e:Ue[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=Ve.length;n--;)if((e=Ve[n]+t)in Qe)return e}(e)||e)}var Ge=/^(none|table(?!-c[ea]).+)/,Je=/^--/,Ze={position:"absolute",visibility:"hidden",display:"block"},Ye={letterSpacing:"0",fontWeight:"400"};function Ke(e,t,n){var i=ne.exec(t);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):t}function et(e,t,n,i,r,o){var s="width"===t?1:0,a=0,l=0;if(n===(i?"border":"content"))return 0;for(;s<4;s+=2)"margin"===n&&(l+=A.css(e,n+ie[s],!0,r)),i?("content"===n&&(l-=A.css(e,"padding"+ie[s],!0,r)),"margin"!==n&&(l-=A.css(e,"border"+ie[s]+"Width",!0,r))):(l+=A.css(e,"padding"+ie[s],!0,r),"padding"!==n?l+=A.css(e,"border"+ie[s]+"Width",!0,r):a+=A.css(e,"border"+ie[s]+"Width",!0,r));return!i&&0<=o&&(l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-l-a-.5))||0),l}function tt(e,t,n){var i=We(e),r=(!y.boxSizingReliable()||n)&&"border-box"===A.css(e,"boxSizing",!1,i),o=r,s=$e(e,t,i),a="offset"+t[0].toUpperCase()+t.slice(1);if(Me.test(s)){if(!n)return s;s="auto"}return(!y.boxSizingReliable()&&r||"auto"===s||!parseFloat(s)&&"inline"===A.css(e,"display",!1,i))&&e.getClientRects().length&&(r="border-box"===A.css(e,"boxSizing",!1,i),(o=a in e)&&(s=e[a])),(s=parseFloat(s)||0)+et(e,t,n||(r?"border":"content"),o,i,s)+"px"}function nt(e,t,n,i,r){return new nt.prototype.init(e,t,n,i,r)}A.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=$e(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var r,o,s,a=U(t),l=Je.test(t),c=e.style;if(l||(t=Xe(a)),s=A.cssHooks[t]||A.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(r=s.get(e,!1,i))?r:c[t];"string"==(o=typeof n)&&(r=ne.exec(n))&&r[1]&&(n=ce(e,t,r),o="number"),null!=n&&n==n&&("number"!==o||l||(n+=r&&r[3]||(A.cssNumber[a]?"":"px")),y.clearCloneStyle||""!==n||0!==t.indexOf("background")||(c[t]="inherit"),s&&"set"in s&&void 0===(n=s.set(e,n,i))||(l?c.setProperty(t,n):c[t]=n))}},css:function(e,t,n,i){var r,o,s,a=U(t);return Je.test(t)||(t=Xe(a)),(s=A.cssHooks[t]||A.cssHooks[a])&&"get"in s&&(r=s.get(e,!0,n)),void 0===r&&(r=$e(e,t,i)),"normal"===r&&t in Ye&&(r=Ye[t]),""===n||n?(o=parseFloat(r),!0===n||isFinite(o)?o||0:r):r}}),A.each(["height","width"],function(e,l){A.cssHooks[l]={get:function(e,t,n){if(t)return!Ge.test(A.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?tt(e,l,n):le(e,Ze,function(){return tt(e,l,n)})},set:function(e,t,n){var i,r=We(e),o=!y.scrollboxSize()&&"absolute"===r.position,s=(o||n)&&"border-box"===A.css(e,"boxSizing",!1,r),a=n?et(e,l,n,s,r):0;return s&&o&&(a-=Math.ceil(e["offset"+l[0].toUpperCase()+l.slice(1)]-parseFloat(r[l])-et(e,l,"border",!1,r)-.5)),a&&(i=ne.exec(t))&&"px"!==(i[3]||"px")&&(e.style[l]=t,t=A.css(e,l)),Ke(0,t,a)}}}),A.cssHooks.marginLeft=Be(y.reliableMarginLeft,function(e,t){if(t)return(parseFloat($e(e,"marginLeft"))||e.getBoundingClientRect().left-le(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),A.each({margin:"",padding:"",border:"Width"},function(r,o){A.cssHooks[r+o]={expand:function(e){for(var t=0,n={},i="string"==typeof e?e.split(" "):[e];t<4;t++)n[r+ie[t]+o]=i[t]||i[t-2]||i[0];return n}},"margin"!==r&&(A.cssHooks[r+o].set=Ke)}),A.fn.extend({css:function(e,t){return $(this,function(e,t,n){var i,r,o={},s=0;if(Array.isArray(t)){for(i=We(e),r=t.length;s<r;s++)o[t[s]]=A.css(e,t[s],!1,i);return o}return void 0!==n?A.style(e,t,n):A.css(e,t)},e,t,1<arguments.length)}}),((A.Tween=nt).prototype={constructor:nt,init:function(e,t,n,i,r,o){this.elem=e,this.prop=n,this.easing=r||A.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=o||(A.cssNumber[n]?"":"px")},cur:function(){var e=nt.propHooks[this.prop];return e&&e.get?e.get(this):nt.propHooks._default.get(this)},run:function(e){var t,n=nt.propHooks[this.prop];return this.options.duration?this.pos=t=A.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):nt.propHooks._default.set(this),this}}).init.prototype=nt.prototype,(nt.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=A.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){A.fx.step[e.prop]?A.fx.step[e.prop](e):1!==e.elem.nodeType||!A.cssHooks[e.prop]&&null==e.elem.style[Xe(e.prop)]?e.elem[e.prop]=e.now:A.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=nt.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},A.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},A.fx=nt.prototype.init,A.fx.step={};var it,rt,ot,st,at=/^(?:toggle|show|hide)$/,lt=/queueHooks$/;function ct(){rt&&(!1===C.hidden&&S.requestAnimationFrame?S.requestAnimationFrame(ct):S.setTimeout(ct,A.fx.interval),A.fx.tick())}function ut(){return S.setTimeout(function(){it=void 0}),it=Date.now()}function dt(e,t){var n,i=0,r={height:e};for(t=t?1:0;i<4;i+=2-t)r["margin"+(n=ie[i])]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}function ft(e,t,n){for(var i,r=(pt.tweeners[t]||[]).concat(pt.tweeners["*"]),o=0,s=r.length;o<s;o++)if(i=r[o].call(n,t,e))return i}function pt(o,e,t){var n,s,i=0,r=pt.prefilters.length,a=A.Deferred().always(function(){delete l.elem}),l=function(){if(s)return!1;for(var e=it||ut(),t=Math.max(0,c.startTime+c.duration-e),n=1-(t/c.duration||0),i=0,r=c.tweens.length;i<r;i++)c.tweens[i].run(n);return a.notifyWith(o,[c,n,t]),n<1&&r?t:(r||a.notifyWith(o,[c,1,0]),a.resolveWith(o,[c]),!1)},c=a.promise({elem:o,props:A.extend({},e),opts:A.extend(!0,{specialEasing:{},easing:A.easing._default},t),originalProperties:e,originalOptions:t,startTime:it||ut(),duration:t.duration,tweens:[],createTween:function(e,t){var n=A.Tween(o,c.opts,e,t,c.opts.specialEasing[e]||c.opts.easing);return c.tweens.push(n),n},stop:function(e){var t=0,n=e?c.tweens.length:0;if(s)return this;for(s=!0;t<n;t++)c.tweens[t].run(1);return e?(a.notifyWith(o,[c,1,0]),a.resolveWith(o,[c,e])):a.rejectWith(o,[c,e]),this}}),u=c.props;for(function(e,t){var n,i,r,o,s;for(n in e)if(r=t[i=U(n)],o=e[n],Array.isArray(o)&&(r=o[1],o=e[n]=o[0]),n!==i&&(e[i]=o,delete e[n]),(s=A.cssHooks[i])&&"expand"in s)for(n in o=s.expand(o),delete e[i],o)n in e||(e[n]=o[n],t[n]=r);else t[i]=r}(u,c.opts.specialEasing);i<r;i++)if(n=pt.prefilters[i].call(c,o,u,c.opts))return m(n.stop)&&(A._queueHooks(c.elem,c.opts.queue).stop=n.stop.bind(n)),n;return A.map(u,ft,c),m(c.opts.start)&&c.opts.start.call(o,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),A.fx.timer(A.extend(l,{elem:o,anim:c,queue:c.opts.queue})),c}A.Animation=A.extend(pt,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return ce(n.elem,e,ne.exec(t),n),n}]},tweener:function(e,t){for(var n,i=0,r=(e=m(e)?(t=e,["*"]):e.match(O)).length;i<r;i++)n=e[i],pt.tweeners[n]=pt.tweeners[n]||[],pt.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var i,r,o,s,a,l,c,u,d="width"in t||"height"in t,f=this,p={},h=e.style,g=e.nodeType&&ae(e),v=J.get(e,"fxshow");for(i in n.queue||(null==(s=A._queueHooks(e,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,f.always(function(){f.always(function(){s.unqueued--,A.queue(e,"fx").length||s.empty.fire()})})),t)if(r=t[i],at.test(r)){if(delete t[i],o=o||"toggle"===r,r===(g?"hide":"show")){if("show"!==r||!v||void 0===v[i])continue;g=!0}p[i]=v&&v[i]||A.style(e,i)}if((l=!A.isEmptyObject(t))||!A.isEmptyObject(p))for(i in d&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],null==(c=v&&v.display)&&(c=J.get(e,"display")),"none"===(u=A.css(e,"display"))&&(c?u=c:(de([e],!0),c=e.style.display||c,u=A.css(e,"display"),de([e]))),("inline"===u||"inline-block"===u&&null!=c)&&"none"===A.css(e,"float")&&(l||(f.done(function(){h.display=c}),null==c&&(u=h.display,c="none"===u?"":u)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",f.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),l=!1,p)l||(v?"hidden"in v&&(g=v.hidden):v=J.access(e,"fxshow",{display:c}),o&&(v.hidden=!g),g&&de([e],!0),f.done(function(){for(i in g||de([e]),J.remove(e,"fxshow"),p)A.style(e,i,p[i])})),l=ft(g?v[i]:0,i,f),i in v||(v[i]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(e,t){t?pt.prefilters.unshift(e):pt.prefilters.push(e)}}),A.speed=function(e,t,n){var i=e&&"object"==typeof e?A.extend({},e):{complete:n||!n&&t||m(e)&&e,duration:e,easing:n&&t||t&&!m(t)&&t};return A.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in A.fx.speeds?i.duration=A.fx.speeds[i.duration]:i.duration=A.fx.speeds._default),null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){m(i.old)&&i.old.call(this),i.queue&&A.dequeue(this,i.queue)},i},A.fn.extend({fadeTo:function(e,t,n,i){return this.filter(ae).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(t,e,n,i){var r=A.isEmptyObject(t),o=A.speed(e,n,i),s=function(){var e=pt(this,A.extend({},t),o);(r||J.get(this,"finish"))&&e.stop(!0)};return s.finish=s,r||!1===o.queue?this.each(s):this.queue(o.queue,s)},stop:function(r,e,o){var s=function(e){var t=e.stop;delete e.stop,t(o)};return"string"!=typeof r&&(o=e,e=r,r=void 0),e&&!1!==r&&this.queue(r||"fx",[]),this.each(function(){var e=!0,t=null!=r&&r+"queueHooks",n=A.timers,i=J.get(this);if(t)i[t]&&i[t].stop&&s(i[t]);else for(t in i)i[t]&&i[t].stop&&lt.test(t)&&s(i[t]);for(t=n.length;t--;)n[t].elem!==this||null!=r&&n[t].queue!==r||(n[t].anim.stop(o),e=!1,n.splice(t,1));!e&&o||A.dequeue(this,r)})},finish:function(s){return!1!==s&&(s=s||"fx"),this.each(function(){var e,t=J.get(this),n=t[s+"queue"],i=t[s+"queueHooks"],r=A.timers,o=n?n.length:0;for(t.finish=!0,A.queue(this,s,[]),i&&i.stop&&i.stop.call(this,!0),e=r.length;e--;)r[e].elem===this&&r[e].queue===s&&(r[e].anim.stop(!0),r.splice(e,1));for(e=0;e<o;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),A.each(["toggle","show","hide"],function(e,i){var r=A.fn[i];A.fn[i]=function(e,t,n){return null==e||"boolean"==typeof e?r.apply(this,arguments):this.animate(dt(i,!0),e,t,n)}}),A.each({slideDown:dt("show"),slideUp:dt("hide"),slideToggle:dt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,i){A.fn[e]=function(e,t,n){return this.animate(i,e,t,n)}}),A.timers=[],A.fx.tick=function(){var e,t=0,n=A.timers;for(it=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||A.fx.stop(),it=void 0},A.fx.timer=function(e){A.timers.push(e),A.fx.start()},A.fx.interval=13,A.fx.start=function(){rt||(rt=!0,ct())},A.fx.stop=function(){rt=null},A.fx.speeds={slow:600,fast:200,_default:400},A.fn.delay=function(i,e){return i=A.fx&&A.fx.speeds[i]||i,e=e||"fx",this.queue(e,function(e,t){var n=S.setTimeout(e,i);t.stop=function(){S.clearTimeout(n)}})},ot=C.createElement("input"),st=C.createElement("select").appendChild(C.createElement("option")),ot.type="checkbox",y.checkOn=""!==ot.value,y.optSelected=st.selected,(ot=C.createElement("input")).value="t",ot.type="radio",y.radioValue="t"===ot.value;var ht,gt=A.expr.attrHandle;A.fn.extend({attr:function(e,t){return $(this,A.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){A.removeAttr(this,e)})}}),A.extend({attr:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?A.prop(e,t,n):(1===o&&A.isXMLDoc(e)||(r=A.attrHooks[t.toLowerCase()]||(A.expr.match.bool.test(t)?ht:void 0)),void 0!==n?null===n?void A.removeAttr(e,t):r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:(e.setAttribute(t,n+""),n):r&&"get"in r&&null!==(i=r.get(e,t))?i:null==(i=A.find.attr(e,t))?void 0:i)},attrHooks:{type:{set:function(e,t){if(!y.radioValue&&"radio"===t&&k(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,i=0,r=t&&t.match(O);if(r&&1===e.nodeType)for(;n=r[i++];)e.removeAttribute(n)}}),ht={set:function(e,t,n){return!1===t?A.removeAttr(e,n):e.setAttribute(n,n),n}},A.each(A.expr.match.bool.source.match(/\w+/g),function(e,t){var s=gt[t]||A.find.attr;gt[t]=function(e,t,n){var i,r,o=t.toLowerCase();return n||(r=gt[o],gt[o]=i,i=null!=s(e,t,n)?o:null,gt[o]=r),i}});var vt=/^(?:input|select|textarea|button)$/i,yt=/^(?:a|area)$/i;function mt(e){return(e.match(O)||[]).join(" ")}function xt(e){return e.getAttribute&&e.getAttribute("class")||""}function bt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(O)||[]}A.fn.extend({prop:function(e,t){return $(this,A.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[A.propFix[e]||e]})}}),A.extend({prop:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&A.isXMLDoc(e)||(t=A.propFix[t]||t,r=A.propHooks[t]),void 0!==n?r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:e[t]=n:r&&"get"in r&&null!==(i=r.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=A.find.attr(e,"tabindex");return t?parseInt(t,10):vt.test(e.nodeName)||yt.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),y.optSelected||(A.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),A.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){A.propFix[this.toLowerCase()]=this}),A.fn.extend({addClass:function(t){var e,n,i,r,o,s,a,l=0;if(m(t))return this.each(function(e){A(this).addClass(t.call(this,e,xt(this)))});if((e=bt(t)).length)for(;n=this[l++];)if(r=xt(n),i=1===n.nodeType&&" "+mt(r)+" "){for(s=0;o=e[s++];)i.indexOf(" "+o+" ")<0&&(i+=o+" ");r!==(a=mt(i))&&n.setAttribute("class",a)}return this},removeClass:function(t){var e,n,i,r,o,s,a,l=0;if(m(t))return this.each(function(e){A(this).removeClass(t.call(this,e,xt(this)))});if(!arguments.length)return this.attr("class","");if((e=bt(t)).length)for(;n=this[l++];)if(r=xt(n),i=1===n.nodeType&&" "+mt(r)+" "){for(s=0;o=e[s++];)for(;-1<i.indexOf(" "+o+" ");)i=i.replace(" "+o+" "," ");r!==(a=mt(i))&&n.setAttribute("class",a)}return this},toggleClass:function(r,t){var o=typeof r,s="string"===o||Array.isArray(r);return"boolean"==typeof t&&s?t?this.addClass(r):this.removeClass(r):m(r)?this.each(function(e){A(this).toggleClass(r.call(this,e,xt(this),t),t)}):this.each(function(){var e,t,n,i;if(s)for(t=0,n=A(this),i=bt(r);e=i[t++];)n.hasClass(e)?n.removeClass(e):n.addClass(e);else void 0!==r&&"boolean"!==o||((e=xt(this))&&J.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",e||!1===r?"":J.get(this,"__className__")||""))})},hasClass:function(e){var t,n,i=0;for(t=" "+e+" ";n=this[i++];)if(1===n.nodeType&&-1<(" "+mt(xt(n))+" ").indexOf(t))return!0;return!1}});var wt=/\r/g;A.fn.extend({val:function(n){var i,e,r,t=this[0];return arguments.length?(r=m(n),this.each(function(e){var t;1===this.nodeType&&(null==(t=r?n.call(this,e,A(this).val()):n)?t="":"number"==typeof t?t+="":Array.isArray(t)&&(t=A.map(t,function(e){return null==e?"":e+""})),(i=A.valHooks[this.type]||A.valHooks[this.nodeName.toLowerCase()])&&"set"in i&&void 0!==i.set(this,t,"value")||(this.value=t))})):t?(i=A.valHooks[t.type]||A.valHooks[t.nodeName.toLowerCase()])&&"get"in i&&void 0!==(e=i.get(t,"value"))?e:"string"==typeof(e=t.value)?e.replace(wt,""):null==e?"":e:void 0}}),A.extend({valHooks:{option:{get:function(e){var t=A.find.attr(e,"value");return null!=t?t:mt(A.text(e))}},select:{get:function(e){var t,n,i,r=e.options,o=e.selectedIndex,s="select-one"===e.type,a=s?null:[],l=s?o+1:r.length;for(i=o<0?l:s?o:0;i<l;i++)if(((n=r[i]).selected||i===o)&&!n.disabled&&(!n.parentNode.disabled||!k(n.parentNode,"optgroup"))){if(t=A(n).val(),s)return t;a.push(t)}return a},set:function(e,t){for(var n,i,r=e.options,o=A.makeArray(t),s=r.length;s--;)((i=r[s]).selected=-1<A.inArray(A.valHooks.option.get(i),o))&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),A.each(["radio","checkbox"],function(){A.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<A.inArray(A(e).val(),t)}},y.checkOn||(A.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),y.focusin="onfocusin"in S;var Tt=/^(?:focusinfocus|focusoutblur)$/,St=function(e){e.stopPropagation()};A.extend(A.event,{trigger:function(e,t,n,i){var r,o,s,a,l,c,u,d,f=[n||C],p=v.call(e,"type")?e.type:e,h=v.call(e,"namespace")?e.namespace.split("."):[];if(o=d=s=n=n||C,3!==n.nodeType&&8!==n.nodeType&&!Tt.test(p+A.event.triggered)&&(-1<p.indexOf(".")&&(p=(h=p.split(".")).shift(),h.sort()),l=p.indexOf(":")<0&&"on"+p,(e=e[A.expando]?e:new A.Event(p,"object"==typeof e&&e)).isTrigger=i?2:3,e.namespace=h.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:A.makeArray(t,[e]),u=A.event.special[p]||{},i||!u.trigger||!1!==u.trigger.apply(n,t))){if(!i&&!u.noBubble&&!x(n)){for(a=u.delegateType||p,Tt.test(a+p)||(o=o.parentNode);o;o=o.parentNode)f.push(o),s=o;s===(n.ownerDocument||C)&&f.push(s.defaultView||s.parentWindow||S)}for(r=0;(o=f[r++])&&!e.isPropagationStopped();)d=o,e.type=1<r?a:u.bindType||p,(c=(J.get(o,"events")||{})[e.type]&&J.get(o,"handle"))&&c.apply(o,t),(c=l&&o[l])&&c.apply&&X(o)&&(e.result=c.apply(o,t),!1===e.result&&e.preventDefault());return e.type=p,i||e.isDefaultPrevented()||u._default&&!1!==u._default.apply(f.pop(),t)||!X(n)||l&&m(n[p])&&!x(n)&&((s=n[l])&&(n[l]=null),A.event.triggered=p,e.isPropagationStopped()&&d.addEventListener(p,St),n[p](),e.isPropagationStopped()&&d.removeEventListener(p,St),A.event.triggered=void 0,s&&(n[l]=s)),e.result}},simulate:function(e,t,n){var i=A.extend(new A.Event,n,{type:e,isSimulated:!0});A.event.trigger(i,null,t)}}),A.fn.extend({trigger:function(e,t){return this.each(function(){A.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return A.event.trigger(e,t,n,!0)}}),y.focusin||A.each({focus:"focusin",blur:"focusout"},function(n,i){var r=function(e){A.event.simulate(i,e.target,A.event.fix(e))};A.event.special[i]={setup:function(){var e=this.ownerDocument||this,t=J.access(e,i);t||e.addEventListener(n,r,!0),J.access(e,i,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this,t=J.access(e,i)-1;t?J.access(e,i,t):(e.removeEventListener(n,r,!0),J.remove(e,i))}}});var Ct=S.location,At=Date.now(),It=/\?/;A.parseXML=function(e){var t;if(!e||"string"!=typeof e)return null;try{t=(new S.DOMParser).parseFromString(e,"text/xml")}catch(e){t=void 0}return t&&!t.getElementsByTagName("parsererror").length||A.error("Invalid XML: "+e),t};var Pt=/\[\]$/,kt=/\r?\n/g,jt=/^(?:submit|button|image|reset|file)$/i,Et=/^(?:input|select|textarea|keygen)/i;function Nt(n,e,i,r){var t;if(Array.isArray(e))A.each(e,function(e,t){i||Pt.test(n)?r(n,t):Nt(n+"["+("object"==typeof t&&null!=t?e:"")+"]",t,i,r)});else if(i||"object"!==w(e))r(n,e);else for(t in e)Nt(n+"["+t+"]",e[t],i,r)}A.param=function(e,t){var n,i=[],r=function(e,t){var n=m(t)?t():t;i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!A.isPlainObject(e))A.each(e,function(){r(this.name,this.value)});else for(n in e)Nt(n,e[n],t,r);return i.join("&")},A.fn.extend({serialize:function(){return A.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=A.prop(this,"elements");return e?A.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!A(this).is(":disabled")&&Et.test(this.nodeName)&&!jt.test(e)&&(this.checked||!fe.test(e))}).map(function(e,t){var n=A(this).val();return null==n?null:Array.isArray(n)?A.map(n,function(e){return{name:t.name,value:e.replace(kt,"\r\n")}}):{name:t.name,value:n.replace(kt,"\r\n")}}).get()}});var Dt=/%20/g,_t=/#.*$/,Ht=/([?&])_=[^&]*/,Lt=/^(.*?):[ \t]*([^\r\n]*)$/gm,Ot=/^(?:GET|HEAD)$/,qt=/^\/\//,Rt={},zt={},Mt="*/".concat("*"),Wt=C.createElement("a");function Ft(o){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,i=0,r=e.toLowerCase().match(O)||[];if(m(t))for(;n=r[i++];)"+"===n[0]?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(t)):(o[n]=o[n]||[]).push(t)}}function $t(t,r,o,s){var a={},l=t===zt;function c(e){var i;return a[e]=!0,A.each(t[e]||[],function(e,t){var n=t(r,o,s);return"string"!=typeof n||l||a[n]?l?!(i=n):void 0:(r.dataTypes.unshift(n),c(n),!1)}),i}return c(r.dataTypes[0])||!a["*"]&&c("*")}function Bt(e,t){var n,i,r=A.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((r[n]?e:i||(i={}))[n]=t[n]);return i&&A.extend(!0,e,i),e}Wt.href=Ct.href,A.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ct.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Ct.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Mt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":A.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Bt(Bt(e,A.ajaxSettings),t):Bt(A.ajaxSettings,e)},ajaxPrefilter:Ft(Rt),ajaxTransport:Ft(zt),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var u,d,f,n,p,i,h,g,r,o,v=A.ajaxSetup({},t),y=v.context||v,m=v.context&&(y.nodeType||y.jquery)?A(y):A.event,x=A.Deferred(),b=A.Callbacks("once memory"),w=v.statusCode||{},s={},a={},l="canceled",T={readyState:0,getResponseHeader:function(e){var t;if(h){if(!n)for(n={};t=Lt.exec(f);)n[t[1].toLowerCase()+" "]=(n[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=n[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return h?f:null},setRequestHeader:function(e,t){return null==h&&(e=a[e.toLowerCase()]=a[e.toLowerCase()]||e,s[e]=t),this},overrideMimeType:function(e){return null==h&&(v.mimeType=e),this},statusCode:function(e){var t;if(e)if(h)T.always(e[T.status]);else for(t in e)w[t]=[w[t],e[t]];return this},abort:function(e){var t=e||l;return u&&u.abort(t),c(0,t),this}};if(x.promise(T),v.url=((e||v.url||Ct.href)+"").replace(qt,Ct.protocol+"//"),v.type=t.method||t.type||v.method||v.type,v.dataTypes=(v.dataType||"*").toLowerCase().match(O)||[""],null==v.crossDomain){i=C.createElement("a");try{i.href=v.url,i.href=i.href,v.crossDomain=Wt.protocol+"//"+Wt.host!=i.protocol+"//"+i.host}catch(e){v.crossDomain=!0}}if(v.data&&v.processData&&"string"!=typeof v.data&&(v.data=A.param(v.data,v.traditional)),$t(Rt,v,t,T),h)return T;for(r in(g=A.event&&v.global)&&0==A.active++&&A.event.trigger("ajaxStart"),v.type=v.type.toUpperCase(),v.hasContent=!Ot.test(v.type),d=v.url.replace(_t,""),v.hasContent?v.data&&v.processData&&0===(v.contentType||"").indexOf("application/x-www-form-urlencoded")&&(v.data=v.data.replace(Dt,"+")):(o=v.url.slice(d.length),v.data&&(v.processData||"string"==typeof v.data)&&(d+=(It.test(d)?"&":"?")+v.data,delete v.data),!1===v.cache&&(d=d.replace(Ht,"$1"),o=(It.test(d)?"&":"?")+"_="+At+++o),v.url=d+o),v.ifModified&&(A.lastModified[d]&&T.setRequestHeader("If-Modified-Since",A.lastModified[d]),A.etag[d]&&T.setRequestHeader("If-None-Match",A.etag[d])),(v.data&&v.hasContent&&!1!==v.contentType||t.contentType)&&T.setRequestHeader("Content-Type",v.contentType),T.setRequestHeader("Accept",v.dataTypes[0]&&v.accepts[v.dataTypes[0]]?v.accepts[v.dataTypes[0]]+("*"!==v.dataTypes[0]?", "+Mt+"; q=0.01":""):v.accepts["*"]),v.headers)T.setRequestHeader(r,v.headers[r]);if(v.beforeSend&&(!1===v.beforeSend.call(y,T,v)||h))return T.abort();if(l="abort",b.add(v.complete),T.done(v.success),T.fail(v.error),u=$t(zt,v,t,T)){if(T.readyState=1,g&&m.trigger("ajaxSend",[T,v]),h)return T;v.async&&0<v.timeout&&(p=S.setTimeout(function(){T.abort("timeout")},v.timeout));try{h=!1,u.send(s,c)}catch(e){if(h)throw e;c(-1,e)}}else c(-1,"No Transport");function c(e,t,n,i){var r,o,s,a,l,c=t;h||(h=!0,p&&S.clearTimeout(p),u=void 0,f=i||"",T.readyState=0<e?4:0,r=200<=e&&e<300||304===e,n&&(a=function(e,t,n){for(var i,r,o,s,a=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(r in a)if(a[r]&&a[r].test(i)){l.unshift(r);break}if(l[0]in n)o=l[0];else{for(r in n){if(!l[0]||e.converters[r+" "+l[0]]){o=r;break}s||(s=r)}o=o||s}if(o)return o!==l[0]&&l.unshift(o),n[o]}(v,T,n)),a=function(e,t,n,i){var r,o,s,a,l,c={},u=e.dataTypes.slice();if(u[1])for(s in e.converters)c[s.toLowerCase()]=e.converters[s];for(o=u.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=u.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(s=c[l+" "+o]||c["* "+o]))for(r in c)if((a=r.split(" "))[1]===o&&(s=c[l+" "+a[0]]||c["* "+a[0]])){!0===s?s=c[r]:!0!==c[r]&&(o=a[0],u.unshift(a[1]));break}if(!0!==s)if(s&&e.throws)t=s(t);else try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}(v,a,T,r),r?(v.ifModified&&((l=T.getResponseHeader("Last-Modified"))&&(A.lastModified[d]=l),(l=T.getResponseHeader("etag"))&&(A.etag[d]=l)),204===e||"HEAD"===v.type?c="nocontent":304===e?c="notmodified":(c=a.state,o=a.data,r=!(s=a.error))):(s=c,!e&&c||(c="error",e<0&&(e=0))),T.status=e,T.statusText=(t||c)+"",r?x.resolveWith(y,[o,c,T]):x.rejectWith(y,[T,c,s]),T.statusCode(w),w=void 0,g&&m.trigger(r?"ajaxSuccess":"ajaxError",[T,v,r?o:s]),b.fireWith(y,[T,c]),g&&(m.trigger("ajaxComplete",[T,v]),--A.active||A.event.trigger("ajaxStop")))}return T},getJSON:function(e,t,n){return A.get(e,t,n,"json")},getScript:function(e,t){return A.get(e,void 0,t,"script")}}),A.each(["get","post"],function(e,r){A[r]=function(e,t,n,i){return m(t)&&(i=i||n,n=t,t=void 0),A.ajax(A.extend({url:e,type:r,dataType:i,data:t,success:n},A.isPlainObject(e)&&e))}}),A._evalUrl=function(e,t){return A.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){A.globalEval(e,t)}})},A.fn.extend({wrapAll:function(e){var t;return this[0]&&(m(e)&&(e=e.call(this[0])),t=A(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return m(n)?this.each(function(e){A(this).wrapInner(n.call(this,e))}):this.each(function(){var e=A(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=m(t);return this.each(function(e){A(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not("body").each(function(){A(this).replaceWith(this.childNodes)}),this}}),A.expr.pseudos.hidden=function(e){return!A.expr.pseudos.visible(e)},A.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},A.ajaxSettings.xhr=function(){try{return new S.XMLHttpRequest}catch(e){}};var Vt={0:200,1223:204},Qt=A.ajaxSettings.xhr();y.cors=!!Qt&&"withCredentials"in Qt,y.ajax=Qt=!!Qt,A.ajaxTransport(function(r){var o,s;if(y.cors||Qt&&!r.crossDomain)return{send:function(e,t){var n,i=r.xhr();if(i.open(r.type,r.url,r.async,r.username,r.password),r.xhrFields)for(n in r.xhrFields)i[n]=r.xhrFields[n];for(n in r.mimeType&&i.overrideMimeType&&i.overrideMimeType(r.mimeType),r.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)i.setRequestHeader(n,e[n]);o=function(e){return function(){o&&(o=s=i.onload=i.onerror=i.onabort=i.ontimeout=i.onreadystatechange=null,"abort"===e?i.abort():"error"===e?"number"!=typeof i.status?t(0,"error"):t(i.status,i.statusText):t(Vt[i.status]||i.status,i.statusText,"text"!==(i.responseType||"text")||"string"!=typeof i.responseText?{binary:i.response}:{text:i.responseText},i.getAllResponseHeaders()))}},i.onload=o(),s=i.onerror=i.ontimeout=o("error"),void 0!==i.onabort?i.onabort=s:i.onreadystatechange=function(){4===i.readyState&&S.setTimeout(function(){o&&s()})},o=o("abort");try{i.send(r.hasContent&&r.data||null)}catch(e){if(o)throw e}},abort:function(){o&&o()}}}),A.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),A.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return A.globalEval(e),e}}}),A.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),A.ajaxTransport("script",function(n){var i,r;if(n.crossDomain||n.scriptAttrs)return{send:function(e,t){i=A("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",r=function(e){i.remove(),r=null,e&&t("error"===e.type?404:200,e.type)}),C.head.appendChild(i[0])},abort:function(){r&&r()}}});var Ut,Xt=[],Gt=/(=)\?(?=&|$)|\?\?/;A.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Xt.pop()||A.expando+"_"+At++;return this[e]=!0,e}}),A.ajaxPrefilter("json jsonp",function(e,t,n){var i,r,o,s=!1!==e.jsonp&&(Gt.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Gt.test(e.data)&&"data");if(s||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=m(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(Gt,"$1"+i):!1!==e.jsonp&&(e.url+=(It.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return o||A.error(i+" was not called"),o[0]},e.dataTypes[0]="json",r=S[i],S[i]=function(){o=arguments},n.always(function(){void 0===r?A(S).removeProp(i):S[i]=r,e[i]&&(e.jsonpCallback=t.jsonpCallback,Xt.push(i)),o&&m(r)&&r(o[0]),o=r=void 0}),"script"}),y.createHTMLDocument=((Ut=C.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Ut.childNodes.length),A.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(y.createHTMLDocument?((i=(t=C.implementation.createHTMLDocument("")).createElement("base")).href=C.location.href,t.head.appendChild(i)):t=C),o=!n&&[],(r=j.exec(e))?[t.createElement(r[1])]:(r=we([e],t,o),o&&o.length&&A(o).remove(),A.merge([],r.childNodes)));var i,r,o},A.fn.load=function(e,t,n){var i,r,o,s=this,a=e.indexOf(" ");return-1<a&&(i=mt(e.slice(a)),e=e.slice(0,a)),m(t)?(n=t,t=void 0):t&&"object"==typeof t&&(r="POST"),0<s.length&&A.ajax({url:e,type:r||"GET",dataType:"html",data:t}).done(function(e){o=arguments,s.html(i?A("<div>").append(A.parseHTML(e)).find(i):e)}).always(n&&function(e,t){s.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},A.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){A.fn[t]=function(e){return this.on(t,e)}}),A.expr.pseudos.animated=function(t){return A.grep(A.timers,function(e){return t===e.elem}).length},A.offset={setOffset:function(e,t,n){var i,r,o,s,a,l,c=A.css(e,"position"),u=A(e),d={};"static"===c&&(e.style.position="relative"),a=u.offset(),o=A.css(e,"top"),l=A.css(e,"left"),r=("absolute"===c||"fixed"===c)&&-1<(o+l).indexOf("auto")?(s=(i=u.position()).top,i.left):(s=parseFloat(o)||0,parseFloat(l)||0),m(t)&&(t=t.call(e,n,A.extend({},a))),null!=t.top&&(d.top=t.top-a.top+s),null!=t.left&&(d.left=t.left-a.left+r),"using"in t?t.using.call(e,d):u.css(d)}},A.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){A.offset.setOffset(this,t,e)});var e,n,i=this[0];return i?i.getClientRects().length?(e=i.getBoundingClientRect(),n=i.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,i=this[0],r={top:0,left:0};if("fixed"===A.css(i,"position"))t=i.getBoundingClientRect();else{for(t=this.offset(),n=i.ownerDocument,e=i.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===A.css(e,"position");)e=e.parentNode;e&&e!==i&&1===e.nodeType&&((r=A(e).offset()).top+=A.css(e,"borderTopWidth",!0),r.left+=A.css(e,"borderLeftWidth",!0))}return{top:t.top-r.top-A.css(i,"marginTop",!0),left:t.left-r.left-A.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===A.css(e,"position");)e=e.offsetParent;return e||re})}}),A.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,r){var o="pageYOffset"===r;A.fn[t]=function(e){return $(this,function(e,t,n){var i;if(x(e)?i=e:9===e.nodeType&&(i=e.defaultView),void 0===n)return i?i[r]:e[t];i?i.scrollTo(o?i.pageXOffset:n,o?n:i.pageYOffset):e[t]=n},t,e,arguments.length)}}),A.each(["top","left"],function(e,n){A.cssHooks[n]=Be(y.pixelPosition,function(e,t){if(t)return t=$e(e,n),Me.test(t)?A(e).position()[n]+"px":t})}),A.each({Height:"height",Width:"width"},function(s,a){A.each({padding:"inner"+s,content:a,"":"outer"+s},function(i,o){A.fn[o]=function(e,t){var n=arguments.length&&(i||"boolean"!=typeof e),r=i||(!0===e||!0===t?"margin":"border");return $(this,function(e,t,n){var i;return x(e)?0===o.indexOf("outer")?e["inner"+s]:e.document.documentElement["client"+s]:9===e.nodeType?(i=e.documentElement,Math.max(e.body["scroll"+s],i["scroll"+s],e.body["offset"+s],i["offset"+s],i["client"+s])):void 0===n?A.css(e,t,r):A.style(e,t,n,r)},a,n?e:void 0,n)}})}),A.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){A.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}}),A.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),A.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}}),A.proxy=function(e,t){var n,i,r;if("string"==typeof t&&(n=e[t],t=e,e=n),m(e))return i=a.call(arguments,2),(r=function(){return e.apply(t||this,i.concat(a.call(arguments)))}).guid=e.guid=e.guid||A.guid++,r},A.holdReady=function(e){e?A.readyWait++:A.ready(!0)},A.isArray=Array.isArray,A.parseJSON=JSON.parse,A.nodeName=k,A.isFunction=m,A.isWindow=x,A.camelCase=U,A.type=w,A.now=Date.now,A.isNumeric=function(e){var t=A.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},"function"==typeof define&&define.amd&&define("jquery",[],function(){return A});var Jt=S.jQuery,Zt=S.$;return A.noConflict=function(e){return S.$===A&&(S.$=Zt),e&&S.jQuery===A&&(S.jQuery=Jt),A},e||(S.jQuery=S.$=A),A}),void 0===jQuery.migrateMute&&(jQuery.migrateMute=!0),function(c,n,s){function u(e){var t=n.console;i[e]||(i[e]=!0,c.migrateWarnings.push(e),t&&t.warn&&!c.migrateMute&&(t.warn("JQMIGRATE: "+e),c.migrateTrace&&t.trace&&t.trace()))}function e(e,t,n,i){if(Object.defineProperty)try{return void Object.defineProperty(e,t,{configurable:!0,enumerable:!0,get:function(){return u(i),n},set:function(e){u(i),n=e}})}catch(e){}c._definePropertyBroken=!0,e[t]=n}c.migrateVersion="1.4.1";var i={};c.migrateWarnings=[],n.console&&n.console.log&&n.console.log("JQMIGRATE: Migrate is installed"+(c.migrateMute?"":" with logging active")+", version "+c.migrateVersion),c.migrateTrace===s&&(c.migrateTrace=!0),c.migrateReset=function(){i={},c.migrateWarnings.length=0},"BackCompat"===document.compatMode&&u("jQuery is not compatible with Quirks Mode");var a=c("<input/>",{size:1}).attr("size")&&c.attrFn,l=c.attr,r=c.attrHooks.value&&c.attrHooks.value.get||function(){return null},o=c.attrHooks.value&&c.attrHooks.value.set||function(){return s},d=/^(?:input|button)$/i,f=/^[238]$/,p=/^(?:autofocus|autoplay|async|checked|controls|defer|disabled|hidden|loop|multiple|open|readonly|required|scoped|selected)$/i,h=/^(?:checked|selected)$/i;e(c,"attrFn",a||{},"jQuery.attrFn is deprecated"),c.attr=function(e,t,n,i){var r=t.toLowerCase(),o=e&&e.nodeType;return i&&(l.length<4&&u("jQuery.fn.attr( props, pass ) is deprecated"),e&&!f.test(o)&&(a?t in a:c.isFunction(c.fn[t])))?c(e)[t](n):("type"===t&&n!==s&&d.test(e.nodeName)&&e.parentNode&&u("Can't change the 'type' of an input or button in IE 6/7/8"),!c.attrHooks[r]&&p.test(r)&&(c.attrHooks[r]={get:function(e,t){var n,i=c.prop(e,t);return!0===i||"boolean"!=typeof i&&(n=e.getAttributeNode(t))&&!1!==n.nodeValue?t.toLowerCase():s},set:function(e,t,n){var i;return!1===t?c.removeAttr(e,n):((i=c.propFix[n]||n)in e&&(e[i]=!0),e.setAttribute(n,n.toLowerCase())),n}},h.test(r)&&u("jQuery.fn.attr('"+r+"') might use property instead of attribute")),l.call(c,e,t,n))},c.attrHooks.value={get:function(e,t){var n=(e.nodeName||"").toLowerCase();return"button"===n?r.apply(this,arguments):("input"!==n&&"option"!==n&&u("jQuery.fn.attr('value') no longer gets properties"),t in e?e.value:null)},set:function(e,t){var n=(e.nodeName||"").toLowerCase();return"button"===n?o.apply(this,arguments):("input"!==n&&"option"!==n&&u("jQuery.fn.attr('value', val) no longer sets properties"),void(e.value=t))}};var t,g,v,y=c.fn.init,m=c.find,x=c.parseJSON,b=/^\s*</,w=/\[(\s*[-\w]+\s*)([~|^$*]?=)\s*([-\w#]*?#[-\w#]*)\s*\]/,T=/\[(\s*[-\w]+\s*)([~|^$*]?=)\s*([-\w#]*?#[-\w#]*)\s*\]/g,S=/^([^<]*)(<[\w\W]+>)([^>]*)$/;for(v in c.fn.init=function(e,t,n){var i,r;return e&&"string"==typeof e&&!c.isPlainObject(t)&&(i=S.exec(c.trim(e)))&&i[0]&&(b.test(e)||u("$(html) HTML strings must start with '<' character"),i[3]&&u("$(html) HTML text after last tag is ignored"),"#"===i[0].charAt(0)&&(u("HTML string cannot start with a '#' character"),c.error("JQMIGRATE: Invalid selector string (XSS)")),t&&t.context&&t.context.nodeType&&(t=t.context),c.parseHTML)?y.call(this,c.parseHTML(i[2],t&&t.ownerDocument||t||document,!0),t,n):(r=y.apply(this,arguments),e&&e.selector!==s?(r.selector=e.selector,r.context=e.context):(r.selector="string"==typeof e?e:"",e&&(r.context=e.nodeType?e:t||document)),r)},c.fn.init.prototype=c.fn,c.find=function(t){var n=Array.prototype.slice.call(arguments);if("string"==typeof t&&w.test(t))try{document.querySelector(t)}catch(e){t=t.replace(T,function(e,t,n,i){return"["+t+n+'"'+i+'"]'});try{document.querySelector(t),u("Attribute selector with '#' must be quoted: "+n[0]),n[0]=t}catch(e){u("Attribute selector with '#' was not fixed: "+n[0])}}return m.apply(this,n)},m)Object.prototype.hasOwnProperty.call(m,v)&&(c.find[v]=m[v]);c.parseJSON=function(e){return e?x.apply(this,arguments):(u("jQuery.parseJSON requires a valid JSON string"),null)},c.uaMatch=function(e){e=e.toLowerCase();var t=/(chrome)[ \/]([\w.]+)/.exec(e)||/(webkit)[ \/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||e.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(e)||[];return{browser:t[1]||"",version:t[2]||"0"}},c.browser||(g={},(t=c.uaMatch(navigator.userAgent)).browser&&(g[t.browser]=!0,g.version=t.version),g.chrome?g.webkit=!0:g.webkit&&(g.safari=!0),c.browser=g),e(c,"browser",c.browser,"jQuery.browser is deprecated"),c.boxModel=c.support.boxModel="CSS1Compat"===document.compatMode,e(c,"boxModel",c.boxModel,"jQuery.boxModel is deprecated"),e(c.support,"boxModel",c.support.boxModel,"jQuery.support.boxModel is deprecated"),c.sub=function(){function i(e,t){return new i.fn.init(e,t)}c.extend(!0,i,this),i.superclass=this,((i.fn=i.prototype=this()).constructor=i).sub=this.sub,i.fn.init=function(e,t){var n=c.fn.init.call(this,e,t,r);return n instanceof i?n:i(n)},i.fn.init.prototype=i.fn;var r=i(document);return u("jQuery.sub() is deprecated"),i};var C=!(c.fn.size=function(){return u("jQuery.fn.size() is deprecated; use the .length property"),this.length});c.swap&&c.each(["height","width","reliableMarginRight"],function(e,t){var n=c.cssHooks[t]&&c.cssHooks[t].get;n&&(c.cssHooks[t].get=function(){var e;return C=!0,e=n.apply(this,arguments),C=!1,e})}),c.swap=function(e,t,n,i){var r,o,s={};for(o in C||u("jQuery.swap() is undocumented and deprecated"),t)s[o]=e.style[o],e.style[o]=t[o];for(o in r=n.apply(e,i||[]),t)e.style[o]=s[o];return r},c.ajaxSetup({converters:{"text json":c.parseJSON}});var A=c.fn.data;c.fn.data=function(e){var t,n,i=this[0];return!i||"events"!==e||1!==arguments.length||(t=c.data(i,e),n=c._data(i,e),t!==s&&t!==n||n===s)?A.apply(this,arguments):(u("Use of jQuery.fn.data('events') is deprecated"),n)};var I=/\/(java|ecma)script/i;c.clean||(c.clean=function(e,t,n,i){t=(t=!(t=t||document).nodeType&&t[0]||t).ownerDocument||t,u("jQuery.clean() is deprecated");var r,o,s,a,l=[];if(c.merge(l,c.buildFragment(e,t).childNodes),n)for(s=function(e){return!e.type||I.test(e.type)?i?i.push(e.parentNode?e.parentNode.removeChild(e):e):n.appendChild(e):void 0},r=0;null!=(o=l[r]);r++)c.nodeName(o,"script")&&s(o)||(n.appendChild(o),void 0!==o.getElementsByTagName&&(a=c.grep(c.merge([],o.getElementsByTagName("script")),s),l.splice.apply(l,[r+1,0].concat(a)),r+=a.length));return l});var P=c.event.add,k=c.event.remove,j=c.event.trigger,E=c.fn.toggle,N=c.fn.live,D=c.fn.die,_=c.fn.load,H="ajaxStart|ajaxStop|ajaxSend|ajaxComplete|ajaxError|ajaxSuccess",L=new RegExp("\\b(?:"+H+")\\b"),O=/(?:^|\s)hover(\.\S+|)\b/,q=function(e){return"string"!=typeof e||c.event.special.hover?e:(O.test(e)&&u("'hover' pseudo-event is deprecated, use 'mouseenter mouseleave'"),e&&e.replace(O,"mouseenter$1 mouseleave$1"))};c.event.props&&"attrChange"!==c.event.props[0]&&c.event.props.unshift("attrChange","attrName","relatedNode","srcElement"),c.event.dispatch&&e(c.event,"handle",c.event.dispatch,"jQuery.event.handle is undocumented and deprecated"),c.event.add=function(e,t,n,i,r){e!==document&&L.test(t)&&u("AJAX events should be attached to document: "+t),P.call(this,e,q(t||""),n,i,r)},c.event.remove=function(e,t,n,i,r){k.call(this,e,q(t)||"",n,i,r)},c.each(["load","unload","error"],function(e,t){c.fn[t]=function(){var e=Array.prototype.slice.call(arguments,0);return"load"===t&&"string"==typeof e[0]?_.apply(this,e):(u("jQuery.fn."+t+"() is deprecated"),e.splice(0,0,t),arguments.length?this.bind.apply(this,e):(this.triggerHandler.apply(this,e),this))}}),c.fn.toggle=function(n,e){if(!c.isFunction(n)||!c.isFunction(e))return E.apply(this,arguments);u("jQuery.fn.toggle(handler, handler...) is deprecated");var i=arguments,t=n.guid||c.guid++,r=0,o=function(e){var t=(c._data(this,"lastToggle"+n.guid)||0)%r;return c._data(this,"lastToggle"+n.guid,t+1),e.preventDefault(),i[t].apply(this,arguments)||!1};for(o.guid=t;r<i.length;)i[r++].guid=t;return this.click(o)},c.fn.live=function(e,t,n){return u("jQuery.fn.live() is deprecated"),N?N.apply(this,arguments):(c(this.context).on(e,this.selector,t,n),this)},c.fn.die=function(e,t){return u("jQuery.fn.die() is deprecated"),D?D.apply(this,arguments):(c(this.context).off(e,this.selector||"**",t),this)},c.event.trigger=function(e,t,n,i){return n||L.test(e)||u("Global events are undocumented and deprecated"),j.call(this,e,t,n||document,i)},c.each(H.split("|"),function(e,t){c.event.special[t]={setup:function(){var e=this;return e!==document&&(c.event.add(document,t+"."+c.guid,function(){c.event.trigger(t,Array.prototype.slice.call(arguments,1),e,!0)}),c._data(this,t,c.guid++)),!1},teardown:function(){return this!==document&&c.event.remove(document,t+"."+c._data(this,t)),!1}}}),c.event.special.ready={setup:function(){this===document&&u("'ready' event is deprecated")}};var R=c.fn.andSelf||c.fn.addBack,z=c.fn.find;if(c.fn.andSelf=function(){return u("jQuery.fn.andSelf() replaced by jQuery.fn.addBack()"),R.apply(this,arguments)},c.fn.find=function(e){var t=z.apply(this,arguments);return t.context=this.context,t.selector=this.selector?this.selector+" "+e:e,t},c.Callbacks){var M=c.Deferred,W=[["resolve","done",c.Callbacks("once memory"),c.Callbacks("once memory"),"resolved"],["reject","fail",c.Callbacks("once memory"),c.Callbacks("once memory"),"rejected"],["notify","progress",c.Callbacks("memory"),c.Callbacks("memory")]];c.Deferred=function(e){var o=M(),s=o.promise();return o.pipe=s.pipe=function(){var r=arguments;return u("deferred.pipe() is deprecated"),c.Deferred(function(i){c.each(W,function(e,t){var n=c.isFunction(r[e])&&r[e];o[t[1]](function(){var e=n&&n.apply(this,arguments);e&&c.isFunction(e.promise)?e.promise().done(i.resolve).fail(i.reject).progress(i.notify):i[t[0]+"With"](this===s?i.promise():this,n?[e]:arguments)})}),r=null}).promise()},o.isResolved=function(){return u("deferred.isResolved is deprecated"),"resolved"===o.state()},o.isRejected=function(){return u("deferred.isRejected is deprecated"),"rejected"===o.state()},e&&e.call(o,o),o}}}(jQuery,window),function(s,f,p){function h(e,t){return typeof e===t}function o(e){var t=C.className,n=T._config.classPrefix||"";if(A&&(t=t.baseVal),T._config.enableJSClass){var i=new RegExp("(^|\\s)"+n+"no-js(\\s|$)");t=t.replace(i,"$1"+n+"js$2")}T._config.enableClasses&&(t+=" "+n+e.join(" "+n),A?C.className.baseVal=t:C.className=t)}function g(){return"function"!=typeof f.createElement?f.createElement(arguments[0]):A?f.createElementNS.call(f,"http://www.w3.org/2000/svg",arguments[0]):f.createElement.apply(f,arguments)}function v(e){return e.replace(/([a-z])-([a-z])/g,function(e,t,n){return t+n.toUpperCase()}).replace(/^-/,"")}function y(e,t){return!!~(""+e).indexOf(t)}function r(e,t,n,i){var r,o,s,a,l,c="modernizr",u=g("div"),d=((l=f.body)||((l=g(A?"svg":"body")).fake=!0),l);if(parseInt(n,10))for(;n--;)(s=g("div")).id=i?i[n]:c+(n+1),u.appendChild(s);return(r=g("style")).type="text/css",r.id="s"+c,(d.fake?d:u).appendChild(r),d.appendChild(u),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(f.createTextNode(e)),u.id=c,d.fake&&(d.style.background="",d.style.overflow="hidden",a=C.style.overflow,C.style.overflow="hidden",C.appendChild(d)),o=t(u,e),d.fake?(d.parentNode.removeChild(d),C.style.overflow=a,C.offsetHeight):u.parentNode.removeChild(u),!!o}function a(e,t){if("object"==typeof e)for(var n in e)F(e,n)&&a(n,e[n]);else{var i=(e=e.toLowerCase()).split("."),r=T[i[0]];if(2==i.length&&(r=r[i[1]]),void 0!==r)return T;t="function"==typeof t?t():t,1==i.length?T[i[0]]=t:(!T[i[0]]||T[i[0]]instanceof Boolean||(T[i[0]]=new Boolean(T[i[0]])),T[i[0]][i[1]]=t),o([(t&&0!=t?"":"no-")+i.join("-")]),T._trigger(e,t)}return T}function l(e,t){return function(){return e.apply(t,arguments)}}function c(e){return e.replace(/([A-Z])/g,function(e,t){return"-"+t.toLowerCase()}).replace(/^ms-/,"-ms-")}function m(e,t){var n=e.length;if("CSS"in s&&"supports"in s.CSS){for(;n--;)if(s.CSS.supports(c(e[n]),t))return!0;return!1}if("CSSSupportsRule"in s){for(var i=[];n--;)i.push("("+c(e[n])+":"+t+")");return r("@supports ("+(i=i.join(" or "))+") { #modernizr { position: absolute; } }",function(e){return"absolute"==function(e,t,n){var i;if("getComputedStyle"in s){i=getComputedStyle.call(s,e,t);var r=s.console;null!==i?n&&(i=i.getPropertyValue(n)):r&&r[r.error?"error":"log"].call(r,"getComputedStyle returning null, its possible modernizr test results are inaccurate")}else i=!t&&e.currentStyle&&e.currentStyle[n];return i}(e,null,"position")})}return p}function u(e,t,n,i){function r(){s&&(delete Q.style,delete Q.modElem)}if(i=!h(i,"undefined")&&i,!h(n,"undefined")){var o=m(e,n);if(!h(o,"undefined"))return o}for(var s,a,l,c,u,d=["modernizr","tspan","samp"];!Q.style&&d.length;)s=!0,Q.modElem=g(d.shift()),Q.style=Q.modElem.style;for(l=e.length,a=0;a<l;a++)if(c=e[a],u=Q.style[c],y(c,"-")&&(c=v(c)),Q.style[c]!==p){if(i||h(n,"undefined"))return r(),"pfx"!=t||c;try{Q.style[c]=n}catch(e){}if(Q.style[c]!=u)return r(),"pfx"!=t||c}return r(),!1}function i(e,t,n,i,r){var o=e.charAt(0).toUpperCase()+e.slice(1),s=(e+" "+q.join(o+" ")+o).split(" ");return h(t,"string")||h(t,"undefined")?u(s,t,i,r):function(e,t,n){var i;for(var r in e)if(e[r]in t)return!1===n?e[r]:h(i=t[e[r]],"function")?l(i,n||t):i;return!1}(s=(e+" "+I.join(o+" ")+o).split(" "),t,n)}function d(e,t){var n=e.deleteDatabase(t);n.onsuccess=function(){a("indexeddb.deletedatabase",!0)},n.onerror=function(){a("indexeddb.deletedatabase",!1)}}function x(e,t,n){return i(e,p,p,t,n)}var b=[],w=[],e={_version:"3.6.0",_config:{classPrefix:"",enableClasses:!0,enableJSClass:!0,usePrefixes:!0},_q:[],on:function(e,t){var n=this;setTimeout(function(){t(n[e])},0)},addTest:function(e,t,n){w.push({name:e,fn:t,options:n})},addAsyncTest:function(e){w.push({name:null,fn:e})}},T=function(){};T.prototype=e,(T=new T).addTest("applicationcache","applicationCache"in s),T.addTest("geolocation","geolocation"in navigator),T.addTest("history",function(){var e=navigator.userAgent;return(-1===e.indexOf("Android 2.")&&-1===e.indexOf("Android 4.0")||-1===e.indexOf("Mobile Safari")||-1!==e.indexOf("Chrome")||-1!==e.indexOf("Windows Phone")||"file:"===location.protocol)&&(s.history&&"pushState"in s.history)}),T.addTest("postmessage","postMessage"in s),T.addTest("svg",!!f.createElementNS&&!!f.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect);var t=!1;try{t="WebSocket"in s&&2===s.WebSocket.CLOSING}catch(e){}T.addTest("websockets",t),T.addTest("localstorage",function(){var e="modernizr";try{return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch(e){return!1}}),T.addTest("sessionstorage",function(){var e="modernizr";try{return sessionStorage.setItem(e,e),sessionStorage.removeItem(e),!0}catch(e){return!1}}),T.addTest("websqldatabase","openDatabase"in s),T.addTest("webworkers","Worker"in s);var S=e._config.usePrefixes?" -webkit- -moz- -o- -ms- ".split(" "):["",""];e._prefixes=S;var C=f.documentElement,A="svg"===C.nodeName.toLowerCase(),n="Moz O ms Webkit",I=e._config.usePrefixes?n.toLowerCase().split(" "):[];e._domPrefixes=I;var P,k=(P=!("onblur"in f.documentElement),function(e,t){var n;return!!e&&(t&&"string"!=typeof t||(t=g(t||"div")),!(n=(e="on"+e)in t)&&P&&(t.setAttribute||(t=g("div")),t.setAttribute(e,""),n="function"==typeof t[e],t[e]!==p&&(t[e]=p),t.removeAttribute(e)),n)});e.hasEvent=k,T.addTest("hashchange",function(){return!1!==k("hashchange",s)&&(f.documentMode===p||7<f.documentMode)}),T.addTest("pointerevents",function(){var e=!1,t=I.length;for(e=T.hasEvent("pointerdown");t--&&!e;)k(I[t]+"pointerdown")&&(e=!0);return e}),T.addTest("audio",function(){var e=g("audio"),t=!1;try{(t=!!e.canPlayType)&&((t=new Boolean(t)).ogg=e.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),t.mp3=e.canPlayType('audio/mpeg; codecs="mp3"').replace(/^no$/,""),t.opus=e.canPlayType('audio/ogg; codecs="opus"')||e.canPlayType('audio/webm; codecs="opus"').replace(/^no$/,""),t.wav=e.canPlayType('audio/wav; codecs="1"').replace(/^no$/,""),t.m4a=(e.canPlayType("audio/x-m4a;")||e.canPlayType("audio/aac;")).replace(/^no$/,""))}catch(e){}return t}),T.addTest("canvas",function(){var e=g("canvas");return!(!e.getContext||!e.getContext("2d"))}),T.addTest("canvastext",function(){return!1!==T.canvas&&"function"==typeof g("canvas").getContext("2d").fillText}),T.addTest("video",function(){var e=g("video"),t=!1;try{(t=!!e.canPlayType)&&((t=new Boolean(t)).ogg=e.canPlayType('video/ogg; codecs="theora"').replace(/^no$/,""),t.h264=e.canPlayType('video/mp4; codecs="avc1.42E01E"').replace(/^no$/,""),t.webm=e.canPlayType('video/webm; codecs="vp8, vorbis"').replace(/^no$/,""),t.vp9=e.canPlayType('video/webm; codecs="vp9"').replace(/^no$/,""),t.hls=e.canPlayType('application/x-mpegURL; codecs="avc1.42E01E"').replace(/^no$/,""))}catch(e){}return t}),T.addTest("webgl",function(){var e=g("canvas"),t="probablySupportsContext"in e?"probablySupportsContext":"supportsContext";return t in e?e[t]("webgl")||e[t]("experimental-webgl"):"WebGLRenderingContext"in s}),T.addTest("cssgradients",function(){for(var e,t="background-image:",n="",i=0,r=S.length-1;i<r;i++)e=0===i?"to ":"",n+=t+S[i]+"linear-gradient("+e+"left top, #9f9, white);";T._config.usePrefixes&&(n+=t+"-webkit-gradient(linear,left top,right bottom,from(#9f9),to(white));");var o=g("a").style;return o.cssText=n,-1<(""+o.backgroundImage).indexOf("gradient")}),T.addTest("multiplebgs",function(){var e=g("a").style;return e.cssText="background:url(https://),url(https://),red url(https://)",/(url\s*\(.*?){3}/.test(e.background)}),T.addTest("opacity",function(){var e=g("a").style;return e.cssText=S.join("opacity:.55;"),/^0.55$/.test(e.opacity)}),T.addTest("rgba",function(){var e=g("a").style;return e.cssText="background-color:rgba(150,255,150,.5)",-1<(""+e.backgroundColor).indexOf("rgba")}),T.addTest("inlinesvg",function(){var e=g("div");return e.innerHTML="<svg/>","http://www.w3.org/2000/svg"==("undefined"!=typeof SVGRect&&e.firstChild&&e.firstChild.namespaceURI)});var j=g("input"),E="autocomplete autofocus list placeholder max min multiple pattern required step".split(" "),N={};T.input=function(e){for(var t=0,n=e.length;t<n;t++)N[e[t]]=!!(e[t]in j);return N.list&&(N.list=!(!g("datalist")||!s.HTMLDataListElement)),N}(E);var D="search tel url email datetime date month week time datetime-local number range color".split(" "),_={};T.inputtypes=function(e){for(var t,n,i,r=e.length,o=0;o<r;o++)j.setAttribute("type",t=e[o]),(i="text"!==j.type&&"style"in j)&&(j.value="1)",j.style.cssText="position:absolute;visibility:hidden;",/^range$/.test(t)&&j.style.WebkitAppearance!==p?(C.appendChild(j),i=(n=f.defaultView).getComputedStyle&&"textfield"!==n.getComputedStyle(j,null).WebkitAppearance&&0!==j.offsetHeight,C.removeChild(j)):/^(search|tel)$/.test(t)||(i=/^(url|email)$/.test(t)?j.checkValidity&&!1===j.checkValidity():"1)"!=j.value)),_[e[o]]=!!i;return _}(D),T.addTest("hsla",function(){var e=g("a").style;return e.cssText="background-color:hsla(120,40%,100%,.5)",y(e.backgroundColor,"rgba")||y(e.backgroundColor,"hsla")});var H="CSS"in s&&"supports"in s.CSS,L="supportsCSS"in s;T.addTest("supports",H||L);var O={}.toString;T.addTest("svgclippaths",function(){return!!f.createElementNS&&/SVGClipPath/.test(O.call(f.createElementNS("http://www.w3.org/2000/svg","clipPath")))}),T.addTest("smil",function(){return!!f.createElementNS&&/SVGAnimate/.test(O.call(f.createElementNS("http://www.w3.org/2000/svg","animate")))});var q=e._config.usePrefixes?n.split(" "):[];e._cssomPrefixes=q;var R=function(e){var t,n=S.length,i=s.CSSRule;if(void 0===i)return p;if(!e)return!1;if((t=(e=e.replace(/^@/,"")).replace(/-/g,"_").toUpperCase()+"_RULE")in i)return"@"+e;for(var r=0;r<n;r++){var o=S[r];if(o.toUpperCase()+"_"+t in i)return"@-"+o.toLowerCase()+"-"+e}return!1};e.atRule=R;var z,M,W,F,$,B=e.testStyles=r;T.addTest("touchevents",function(){var t;if("ontouchstart"in s||s.DocumentTouch&&f instanceof DocumentTouch)t=!0;else{var e=["@media (",S.join("touch-enabled),("),"heartz",")","{#modernizr{top:9px;position:absolute}}"].join("");B(e,function(e){t=9===e.offsetTop})}return t}),(z=navigator.userAgent,M=z.match(/w(eb)?osbrowser/gi),W=z.match(/windows phone/gi)&&z.match(/iemobile\/([0-9])+/gi)&&9<=parseFloat(RegExp.$1),M||W)?T.addTest("fontface",!1):B('@font-face {font-family:"font";src:url("https://")}',function(e,t){var n=f.getElementById("smodernizr"),i=n.sheet||n.styleSheet,r=i?i.cssRules&&i.cssRules[0]?i.cssRules[0].cssText:i.cssText||"":"",o=/src/i.test(r)&&0===r.indexOf(t.split(" ")[0]);T.addTest("fontface",o)}),B('#modernizr{font:0/0 a}#modernizr:after{content:":)";visibility:hidden;font:7px/1 a}',function(e){T.addTest("generatedcontent",6<=e.offsetHeight)}),F=h($={}.hasOwnProperty,"undefined")||h($.call,"undefined")?function(e,t){return t in e&&h(e.constructor.prototype[t],"undefined")}:function(e,t){return $.call(e,t)},e._l={},e.on=function(e,t){this._l[e]||(this._l[e]=[]),this._l[e].push(t),T.hasOwnProperty(e)&&setTimeout(function(){T._trigger(e,T[e])},0)},e._trigger=function(e,t){if(this._l[e]){var n=this._l[e];setTimeout(function(){var e;for(e=0;e<n.length;e++)(0,n[e])(t)},0),delete this._l[e]}},T._q.push(function(){e.addTest=a});var V={elem:g("modernizr")};T._q.push(function(){delete V.elem});var Q={style:V.elem.style};T._q.unshift(function(){delete Q.style});var U=e.testProp=function(e,t,n){return u([e],p,t,n)};T.addTest("textshadow",U("textShadow","1px 1px")),e.testAllProps=i;var X=e.prefixed=function(e,t,n){return 0===e.indexOf("@")?R(e):(-1!=e.indexOf("-")&&(e=v(e)),t?i(e,t,n):i(e,"pfx"))};T.addAsyncTest(function(){var e;try{e=X("indexedDB",s)}catch(e){}if(e){var t="modernizr-"+Math.random(),n=e.open(t);n.onerror=function(){n.error&&"InvalidStateError"===n.error.name?a("indexeddb",!1):(a("indexeddb",!0),d(e,t))},n.onsuccess=function(){a("indexeddb",!0),d(e,t)}}else a("indexeddb",!1)}),e.testAllProps=x,T.addTest("cssanimations",x("animationName","a",!0)),T.addTest("backgroundsize",x("backgroundSize","100%",!0)),T.addTest("borderimage",x("borderImage","url() 1",!0)),T.addTest("borderradius",x("borderRadius","0px",!0)),T.addTest("boxshadow",x("boxShadow","1px 1px",!0)),function(){T.addTest("csscolumns",function(){var e=!1,t=x("columnCount");try{(e=!!t)&&(e=new Boolean(e))}catch(e){}return e});for(var e,t,n=["Width","Span","Fill","Gap","Rule","RuleColor","RuleStyle","RuleWidth","BreakBefore","BreakAfter","BreakInside"],i=0;i<n.length;i++)e=n[i].toLowerCase(),t=x("column"+n[i]),("breakbefore"===e||"breakafter"===e||"breakinside"==e)&&(t=t||x(n[i])),T.addTest("csscolumns."+e,t)}(),T.addTest("flexbox",x("flexBasis","1px",!0)),T.addTest("flexboxlegacy",x("boxDirection","reverse",!0)),T.addTest("cssreflections",x("boxReflect","above",!0)),T.addTest("csstransforms",function(){return-1===navigator.userAgent.indexOf("Android 2.")&&x("transform","scale(1)",!0)}),T.addTest("csstransforms3d",function(){return!!x("perspective","1px",!0)}),T.addTest("csstransitions",x("transition","all",!0)),function(){var e,t,n,i,r,o;for(var s in w)if(w.hasOwnProperty(s)){if(e=[],(t=w[s]).name&&(e.push(t.name.toLowerCase()),t.options&&t.options.aliases&&t.options.aliases.length))for(n=0;n<t.options.aliases.length;n++)e.push(t.options.aliases[n].toLowerCase());for(i=h(t.fn,"function")?t.fn():t.fn,r=0;r<e.length;r++)1===(o=e[r].split(".")).length?T[o[0]]=i:(!T[o[0]]||T[o[0]]instanceof Boolean||(T[o[0]]=new Boolean(T[o[0]])),T[o[0]][o[1]]=i),b.push((i?"":"no-")+o.join("-"))}}(),o(b),delete e.addTest,delete e.addAsyncTest;for(var G=0;G<T._q.length;G++)T._q[G]();s.Modernizr=T}(window,document),function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t:e.conditionizr=t()}(this,function(){"use strict";function i(i,e,r){function t(e){var t,n=r?i:o+i+("style"===e?".css":".js");switch(e){case"script":(t=document.createElement("script")).src=n;break;case"style":(t=document.createElement("link")).href=n,t.rel="stylesheet";break;case"class":document.documentElement.className+=" "+i}t&&(document.head||document.getElementsByTagName("head")[0]).appendChild(t)}for(var n=e.length;n--;)t(e[n])}var o,r={config:function(e){for(var t in o=e.assets||"",e.tests)r[t]&&i(t,e.tests[t])},add:function(e,t){r[e]="function"==typeof t?t():t},on:function(e,t){(r[e]||/^!/.test(e)&&!r[e.slice(1)])&&t()}};return r.load=r.polyfill=function(e,t){for(var n=t.length;n--;)r[t[n]]&&i(e,[/\.js$/.test(e)?"script":"style"],!0)},r}),function(c){"use strict";function l(e){return(e||"").toLowerCase()}c.fn.cycle=function(a){var e;return 0!==this.length||c.isReady?this.each(function(){var e,n,t,i,r=c(this),o=c.fn.cycle.log;if(!r.data("cycle.opts")){for(var s in(!1===r.data("cycle-log")||a&&!1===a.log||n&&!1===n.log)&&(o=c.noop),o("--c2 init--"),e=r.data())e.hasOwnProperty(s)&&/^cycle[A-Z]+/.test(s)&&(i=e[s],o((t=s.match(/^cycle(.*)/)[1].replace(/^[A-Z]/,l))+":",i,"("+typeof i+")"),e[t]=i);(n=c.extend({},c.fn.cycle.defaults,e,a||{})).timeoutId=0,n.paused=n.paused||!1,n.container=r,n._maxZ=n.maxZ,n.API=c.extend({_container:r},c.fn.cycle.API),n.API.log=o,n.API.trigger=function(e,t){return n.container.trigger(e,t),n.API},r.data("cycle.opts",n),r.data("cycle.API",n.API),n.API.trigger("cycle-bootstrap",[n,n.API]),n.API.addInitialSlides(),n.API.preInitSlideshow(),n.slides.length&&n.API.initSlideshow()}}):(e={s:this.selector,c:this.context},c.fn.cycle.log("requeuing slideshow (dom not ready)"),c(function(){c(e.s,e.c).cycle(a)}),this)},c.fn.cycle.API={opts:function(){return this._container.data("cycle.opts")},addInitialSlides:function(){var e=this.opts(),t=e.slides;e.slideCount=0,e.slides=c(),t=t.jquery?t:e.container.find(t),e.random&&t.sort(function(){return Math.random()-.5}),e.API.add(t)},preInitSlideshow:function(){var e=this.opts();e.API.trigger("cycle-pre-initialize",[e]);var t=c.fn.cycle.transitions[e.fx];t&&c.isFunction(t.preInit)&&t.preInit(e),e._preInitialized=!0},postInitSlideshow:function(){var e=this.opts();e.API.trigger("cycle-post-initialize",[e]);var t=c.fn.cycle.transitions[e.fx];t&&c.isFunction(t.postInit)&&t.postInit(e)},initSlideshow:function(){var e,t=this.opts(),n=t.container;t.API.calcFirstSlide(),"static"==t.container.css("position")&&t.container.css("position","relative"),c(t.slides[t.currSlide]).css({opacity:1,display:"block",visibility:"visible"}),t.API.stackSlides(t.slides[t.currSlide],t.slides[t.nextSlide],!t.reverse),t.pauseOnHover&&(!0!==t.pauseOnHover&&(n=c(t.pauseOnHover)),n.hover(function(){t.API.pause(!0)},function(){t.API.resume(!0)})),t.timeout&&(e=t.API.getSlideOpts(t.currSlide),t.API.queueTransition(e,e.timeout+t.delay)),t._initialized=!0,t.API.updateView(!0),t.API.trigger("cycle-initialized",[t]),t.API.postInitSlideshow()},pause:function(e){var t=this.opts(),n=t.API.getSlideOpts(),i=t.hoverPaused||t.paused;e?t.hoverPaused=!0:t.paused=!0,i||(t.container.addClass("cycle-paused"),t.API.trigger("cycle-paused",[t]).log("cycle-paused"),n.timeout&&(clearTimeout(t.timeoutId),t.timeoutId=0,t._remainingTimeout-=c.now()-t._lastQueue,(t._remainingTimeout<0||isNaN(t._remainingTimeout))&&(t._remainingTimeout=void 0)))},resume:function(e){var t=this.opts(),n=!t.hoverPaused&&!t.paused;e?t.hoverPaused=!1:t.paused=!1,n||(t.container.removeClass("cycle-paused"),0===t.slides.filter(":animated").length&&t.API.queueTransition(t.API.getSlideOpts(),t._remainingTimeout),t.API.trigger("cycle-resumed",[t,t._remainingTimeout]).log("cycle-resumed"))},add:function(e,n){var t,i=this.opts(),r=i.slideCount;"string"==c.type(e)&&(e=c.trim(e)),c(e).each(function(){var e,t=c(this);n?i.container.prepend(t):i.container.append(t),i.slideCount++,e=i.API.buildSlideOpts(t),i.slides=n?c(t).add(i.slides):i.slides.add(t),i.API.initSlide(e,t,--i._maxZ),t.data("cycle.opts",e),i.API.trigger("cycle-slide-added",[i,e,t])}),i.API.updateView(!0),i._preInitialized&&r<2&&1<=i.slideCount&&(i._initialized?i.timeout&&(t=i.slides.length,i.nextSlide=i.reverse?t-1:1,i.timeoutId||i.API.queueTransition(i)):i.API.initSlideshow())},calcFirstSlide:function(){var e,t=this.opts();((e=parseInt(t.startingSlide||0,10))>=t.slides.length||e<0)&&(e=0),t.currSlide=e,t.reverse?(t.nextSlide=e-1,t.nextSlide<0&&(t.nextSlide=t.slides.length-1)):(t.nextSlide=e+1,t.nextSlide==t.slides.length&&(t.nextSlide=0))},calcNextSlide:function(){var e,t=this.opts();t.reverse?(e=t.nextSlide-1<0,t.nextSlide=e?t.slideCount-1:t.nextSlide-1,t.currSlide=e?0:t.nextSlide+1):(e=t.nextSlide+1==t.slides.length,t.nextSlide=e?0:t.nextSlide+1,t.currSlide=e?t.slides.length-1:t.nextSlide-1)},calcTx:function(e,t){var n,i=e;return i._tempFx?n=c.fn.cycle.transitions[i._tempFx]:t&&i.manualFx&&(n=c.fn.cycle.transitions[i.manualFx]),n||(n=c.fn.cycle.transitions[i.fx]),i._tempFx=null,this.opts()._tempFx=null,n||(n=c.fn.cycle.transitions.fade,i.API.log('Transition "'+i.fx+'" not found.  Using fade.')),n},prepareTx:function(e,t){var n,i,r,o,s,a=this.opts();return a.slideCount<2?void(a.timeoutId=0):(!e||a.busy&&!a.manualTrump||(a.API.stopTransition(),a.busy=!1,clearTimeout(a.timeoutId),a.timeoutId=0),void(a.busy||(0!==a.timeoutId||e)&&(i=a.slides[a.currSlide],r=a.slides[a.nextSlide],o=a.API.getSlideOpts(a.nextSlide),s=a.API.calcTx(o,e),a._tx=s,e&&void 0!==o.manualSpeed&&(o.speed=o.manualSpeed),a.nextSlide!=a.currSlide&&(e||!a.paused&&!a.hoverPaused&&a.timeout)?(a.API.trigger("cycle-before",[o,i,r,t]),s.before&&s.before(o,i,r,t),n=function(){a.busy=!1,a.container.data("cycle.opts")&&(s.after&&s.after(o,i,r,t),a.API.trigger("cycle-after",[o,i,r,t]),a.API.queueTransition(o),a.API.updateView(!0))},a.busy=!0,s.transition?s.transition(o,i,r,t,n):a.API.doTransition(o,i,r,t,n),a.API.calcNextSlide(),a.API.updateView()):a.API.queueTransition(o))))},doTransition:function(e,t,n,i,r){var o=e,s=c(t),a=c(n),l=function(){a.animate(o.animIn||{opacity:1},o.speed,o.easeIn||o.easing,r)};a.css(o.cssBefore||{}),s.animate(o.animOut||{},o.speed,o.easeOut||o.easing,function(){s.css(o.cssAfter||{}),o.sync||l()}),o.sync&&l()},queueTransition:function(e,t){var n=this.opts(),i=void 0!==t?t:e.timeout;return 0===n.nextSlide&&0==--n.loop?(n.API.log("terminating; loop=0"),n.timeout=0,i?setTimeout(function(){n.API.trigger("cycle-finished",[n])},i):n.API.trigger("cycle-finished",[n]),void(n.nextSlide=n.currSlide)):void 0!==n.continueAuto&&(!1===n.continueAuto||c.isFunction(n.continueAuto)&&!1===n.continueAuto())?(n.API.log("terminating automatic transitions"),n.timeout=0,void(n.timeoutId&&clearTimeout(n.timeoutId))):void(i&&(n._lastQueue=c.now(),void 0===t&&(n._remainingTimeout=e.timeout),n.paused||n.hoverPaused||(n.timeoutId=setTimeout(function(){n.API.prepareTx(!1,!n.reverse)},i))))},stopTransition:function(){var e=this.opts();e.slides.filter(":animated").length&&(e.slides.stop(!1,!0),e.API.trigger("cycle-transition-stopped",[e])),e._tx&&e._tx.stopTransition&&e._tx.stopTransition(e)},advanceSlide:function(e){var t=this.opts();return clearTimeout(t.timeoutId),t.timeoutId=0,t.nextSlide=t.currSlide+e,t.nextSlide<0?t.nextSlide=t.slides.length-1:t.nextSlide>=t.slides.length&&(t.nextSlide=0),t.API.prepareTx(!0,0<=e),!1},buildSlideOpts:function(e){var t,n,i=this.opts(),r=e.data()||{};for(var o in r)r.hasOwnProperty(o)&&/^cycle[A-Z]+/.test(o)&&(t=r[o],n=o.match(/^cycle(.*)/)[1].replace(/^[A-Z]/,l),i.API.log("["+(i.slideCount-1)+"]",n+":",t,"("+typeof t+")"),r[n]=t);(r=c.extend({},c.fn.cycle.defaults,i,r)).slideNum=i.slideCount;try{delete r.API,delete r.slideCount,delete r.currSlide,delete r.nextSlide,delete r.slides}catch(e){}return r},getSlideOpts:function(e){var t=this.opts();void 0===e&&(e=t.currSlide);var n=t.slides[e],i=c(n).data("cycle.opts");return c.extend({},t,i)},initSlide:function(e,t,n){var i=this.opts();t.css(e.slideCss||{}),0<n&&t.css("zIndex",n),isNaN(e.speed)&&(e.speed=c.fx.speeds[e.speed]||c.fx.speeds._default),e.sync||(e.speed=e.speed/2),t.addClass(i.slideClass)},updateView:function(e,t){var n=this.opts();if(n._initialized){var i=n.API.getSlideOpts(),r=n.slides[n.currSlide];!e&&!0!==t&&(n.API.trigger("cycle-update-view-before",[n,i,r]),n.updateView<0)||(n.slideActiveClass&&n.slides.removeClass(n.slideActiveClass).eq(n.currSlide).addClass(n.slideActiveClass),e&&n.hideNonActive&&n.slides.filter(":not(."+n.slideActiveClass+")").css("visibility","hidden"),0===n.updateView&&setTimeout(function(){n.API.trigger("cycle-update-view",[n,i,r,e])},i.speed/(n.sync?2:1)),0!==n.updateView&&n.API.trigger("cycle-update-view",[n,i,r,e]),e&&n.API.trigger("cycle-update-view-after",[n,i,r]))}},getComponent:function(e){var t=this.opts(),n=t[e];return"string"==typeof n?/^\s*[\>|\+|~]/.test(n)?t.container.find(n):c(n):n.jquery?n:c(n)},stackSlides:function(e,t,n){var i=this.opts();e||(e=i.slides[i.currSlide],t=i.slides[i.nextSlide],n=!i.reverse),c(e).css("zIndex",i.maxZ);var r,o=i.maxZ-2,s=i.slideCount;if(n){for(r=i.currSlide+1;r<s;r++)c(i.slides[r]).css("zIndex",o--);for(r=0;r<i.currSlide;r++)c(i.slides[r]).css("zIndex",o--)}else{for(r=i.currSlide-1;0<=r;r--)c(i.slides[r]).css("zIndex",o--);for(r=s-1;r>i.currSlide;r--)c(i.slides[r]).css("zIndex",o--)}c(t).css("zIndex",i.maxZ-1)},getSlideIndex:function(e){return this.opts().slides.index(e)}},c.fn.cycle.log=function(){window.console&&console.log&&console.log("[cycle2] "+Array.prototype.join.call(arguments," "))},c.fn.cycle.version=function(){return"Cycle2: 2.1.6"},c.fn.cycle.transitions={custom:{},none:{before:function(e,t,n,i){e.API.stackSlides(n,t,i),e.cssBefore={opacity:1,visibility:"visible",display:"block"}}},fade:{before:function(e,t,n,i){var r=e.API.getSlideOpts(e.nextSlide).slideCss||{};e.API.stackSlides(t,n,i),e.cssBefore=c.extend(r,{opacity:0,visibility:"visible",display:"block"}),e.animIn={opacity:1},e.animOut={opacity:0}}},fadeout:{before:function(e,t,n,i){var r=e.API.getSlideOpts(e.nextSlide).slideCss||{};e.API.stackSlides(t,n,i),e.cssBefore=c.extend(r,{opacity:1,visibility:"visible",display:"block"}),e.animOut={opacity:0}}},scrollHorz:{before:function(e,t,n,i){e.API.stackSlides(t,n,i);var r=e.container.css("overflow","hidden").width();e.cssBefore={left:i?r:-r,top:0,opacity:1,visibility:"visible",display:"block"},e.cssAfter={zIndex:e._maxZ-2,left:0},e.animIn={left:0},e.animOut={left:i?-r:r}}}},c.fn.cycle.defaults={allowWrap:!0,autoSelector:".cycle-slideshow[data-cycle-auto-init!=false]",delay:0,easing:null,fx:"fade",hideNonActive:!0,loop:0,manualFx:void 0,manualSpeed:void 0,manualTrump:!0,maxZ:100,pauseOnHover:!1,reverse:!1,slideActiveClass:"cycle-slide-active",slideClass:"cycle-slide",slideCss:{position:"absolute",top:0,left:0},slides:"> img",speed:500,startingSlide:0,sync:!0,timeout:4e3,updateView:0},c(document).ready(function(){c(c.fn.cycle.defaults.autoSelector).cycle()})}(jQuery),function(l){"use strict";function a(e,t){var n,i,r,o,s,a=t.autoHeight;if("container"==a)i=l(t.slides[t.currSlide]).outerHeight(),t.container.height(i);else if(t._autoHeightRatio)t.container.height(t.container.width()/t._autoHeightRatio);else if("calc"===a||"number"==l.type(a)&&0<=a){if((r="calc"===a?(o=0,s=-1,t.slides.each(function(e){var t=l(this).height();s<t&&(s=t,o=e)}),o):a>=t.slides.length?0:a)==t._sentinelIndex)return;t._sentinelIndex=r,t._sentinel&&t._sentinel.remove(),(n=l(t.slides[r].cloneNode(!0))).removeAttr("id name rel").find("[id],[name],[rel]").removeAttr("id name rel"),n.css({position:"static",visibility:"hidden",display:"block"}).prependTo(t.container).addClass("cycle-sentinel cycle-slide").removeClass("cycle-slide-active"),n.find("*").css("visibility","hidden"),t._sentinel=n}}function c(e,t,n,i){var r=l(i).outerHeight();t.container.animate({height:r},t.autoHeightSpeed,t.autoHeightEasing)}function u(e,t){t._autoHeightOnResize&&(l(window).off("resize orientationchange",t._autoHeightOnResize),t._autoHeightOnResize=null),t.container.off("cycle-slide-added cycle-slide-removed",a),t.container.off("cycle-destroyed",u),t.container.off("cycle-before",c),t._sentinel&&(t._sentinel.remove(),t._sentinel=null)}l.extend(l.fn.cycle.defaults,{autoHeight:0,autoHeightSpeed:250,autoHeightEasing:null}),l(document).on("cycle-initialized",function(e,t){function n(){a(0,t)}var i,r=t.autoHeight,o=l.type(r),s=null;("string"===o||"number"===o)&&(t.container.on("cycle-slide-added cycle-slide-removed",a),t.container.on("cycle-destroyed",u),"container"==r?t.container.on("cycle-before",c):"string"===o&&/\d+\:\d+/.test(r)&&(i=(i=r.match(/(\d+)\:(\d+)/))[1]/i[2],t._autoHeightRatio=i),"number"!==o&&(t._autoHeightOnResize=function(){clearTimeout(s),s=setTimeout(n,50)},l(window).on("resize orientationchange",t._autoHeightOnResize)),setTimeout(n,30))})}(jQuery),function(o){"use strict";o.extend(o.fn.cycle.defaults,{caption:"> .cycle-caption",captionTemplate:"{{slideNum}} / {{slideCount}}",overlay:"> .cycle-overlay",overlayTemplate:"<div>{{title}}</div><div>{{desc}}</div>",captionModule:"caption"}),o(document).on("cycle-update-view",function(e,n,i,r){"caption"===n.captionModule&&o.each(["caption","overlay"],function(){var e=i[this+"Template"],t=n.API.getComponent(this);t.length&&e?(t.html(n.API.tmpl(e,i,n,r)),t.show()):t.hide()})}),o(document).on("cycle-destroyed",function(e,t){o.each(["caption","overlay"],function(){var e=t[this+"Template"];t[this]&&e&&t.API.getComponent("caption").empty()})})}(jQuery),function(a){"use strict";var s=a.fn.cycle;a.fn.cycle=function(t){var n,i,r,o=a.makeArray(arguments);return"number"==a.type(t)?this.cycle("goto",t):"string"==a.type(t)?this.each(function(){var e;return n=t,void 0===(r=a(this).data("cycle.opts"))?void s.log('slideshow must be initialized before sending commands; "'+n+'" ignored'):(n="goto"==n?"jump":n,i=r.API[n],a.isFunction(i)?((e=a.makeArray(o)).shift(),i.apply(r.API,e)):void s.log("unknown command: ",n))}):s.apply(this,arguments)},a.extend(a.fn.cycle,s),a.extend(s.API,{next:function(){var e=this.opts();if(!e.busy||e.manualTrump){var t=e.reverse?-1:1;!1===e.allowWrap&&e.currSlide+t>=e.slideCount||(e.API.advanceSlide(t),e.API.trigger("cycle-next",[e]).log("cycle-next"))}},prev:function(){var e=this.opts();if(!e.busy||e.manualTrump){var t=e.reverse?1:-1;!1===e.allowWrap&&e.currSlide+t<0||(e.API.advanceSlide(t),e.API.trigger("cycle-prev",[e]).log("cycle-prev"))}},destroy:function(){this.stop();var t=this.opts(),n=a.isFunction(a._data)?a._data:a.noop;clearTimeout(t.timeoutId),t.timeoutId=0,t.API.stop(),t.API.trigger("cycle-destroyed",[t]).log("cycle-destroyed"),t.container.removeData(),n(t.container[0],"parsedAttrs",!1),t.retainStylesOnDestroy||(t.container.removeAttr("style"),t.slides.removeAttr("style"),t.slides.removeClass(t.slideActiveClass)),t.slides.each(function(){var e=a(this);e.removeData(),e.removeClass(t.slideClass),n(this,"parsedAttrs",!1)})},jump:function(e,t){var n,i=this.opts();if(!i.busy||i.manualTrump){var r=parseInt(e,10);if(isNaN(r)||r<0||r>=i.slides.length)return void i.API.log("goto: invalid slide index: "+r);if(r==i.currSlide)return void i.API.log("goto: skipping, already on slide",r);i.nextSlide=r,clearTimeout(i.timeoutId),i.timeoutId=0,i.API.log("goto: ",r," (zero-index)"),n=i.currSlide<i.nextSlide,i._tempFx=t,i.API.prepareTx(!0,n)}},stop:function(){var e=this.opts(),t=e.container;clearTimeout(e.timeoutId),e.timeoutId=0,e.API.stopTransition(),e.pauseOnHover&&(!0!==e.pauseOnHover&&(t=a(e.pauseOnHover)),t.off("mouseenter mouseleave")),e.API.trigger("cycle-stopped",[e]).log("cycle-stopped")},reinit:function(){var e=this.opts();e.API.destroy(),e.container.cycle()},remove:function(e){for(var t,n,i=this.opts(),r=[],o=1,s=0;s<i.slides.length;s++)t=i.slides[s],s==e?n=t:(r.push(t),a(t).data("cycle.opts").slideNum=o,o++);n&&(i.slides=a(r),i.slideCount--,a(n).remove(),e==i.currSlide?i.API.advanceSlide(1):e<i.currSlide?i.currSlide--:i.currSlide++,i.API.trigger("cycle-slide-removed",[i,e,n]).log("cycle-slide-removed"),i.API.updateView())}}),a(document).on("click.cycle","[data-cycle-cmd]",function(e){e.preventDefault();var t=a(this),n=t.data("cycle-cmd"),i=t.data("cycle-context")||".cycle-slideshow";a(i).cycle(n,t.data("cycle-arg"))})}(jQuery),function(o){"use strict";function n(n,i){var r;return n._hashFence?void(n._hashFence=!1):(r=window.location.hash.substring(1),void n.slides.each(function(e){if(o(this).data("cycle-hash")==r){if(!0===i)n.startingSlide=e;else{var t=n.currSlide<e;n.nextSlide=e,n.API.prepareTx(!0,t)}return!1}}))}o(document).on("cycle-pre-initialize",function(e,t){n(t,!0),t._onHashChange=function(){n(t,!1)},o(window).on("hashchange",t._onHashChange)}),o(document).on("cycle-update-view",function(e,t,n){n.hash&&"#"+n.hash!=window.location.hash&&(t._hashFence=!0,window.location.hash=n.hash)}),o(document).on("cycle-destroyed",function(e,t){t._onHashChange&&o(window).off("hashchange",t._onHashChange)})}(jQuery),function(d){"use strict";d.extend(d.fn.cycle.defaults,{loader:!1}),d(document).on("cycle-bootstrap",function(e,c){var u;c.loader&&(u=c.API.add,c.API.add=function(e,o){function s(e,t){return e.data("index")-t.data("index")}var a=[];if("string"==d.type(e))e=d.trim(e);else if("array"===d.type(e))for(var t=0;t<e.length;t++)e[t]=d(e[t])[0];var l=(e=d(e)).length;l&&(e.css("visibility","hidden").appendTo("body").each(function(e){function t(){var e,t;0==--n&&(--l,e=i,"wait"==c.loader?(a.push(e),0===l&&(a.sort(s),u.apply(c.API,[a,o]),c.container.removeClass("cycle-loading"))):(t=d(c.slides[c.currSlide]),u.apply(c.API,[e,o]),t.show(),c.container.removeClass("cycle-loading")))}var n=0,i=d(this),r=i.is("img")?i:i.find("img");return i.data("index",e),(r=r.filter(":not(.cycle-loader-ignore)").filter(':not([src=""])')).length?(n=r.length,void r.each(function(){this.complete?t():d(this).load(function(){t()}).on("error",function(){0==--n&&(c.API.log("slide skipped; img not loaded:",this.src),0==--l&&"wait"==c.loader&&u.apply(c.API,[a,o]))})})):(--l,void a.push(i))}),l&&c.container.addClass("cycle-loading"))})})}(jQuery),function(s){"use strict";function i(n,i,r){var o;n.API.getComponent("pager").each(function(){var t=s(this);if(i.pagerTemplate){var e=n.API.tmpl(i.pagerTemplate,i,n,r[0]);o=s(e).appendTo(t)}else o=t.children().eq(n.slideCount-1);o.on(n.pagerEvent,function(e){n.pagerEventBubble||e.preventDefault(),n.API.page(t,e.currentTarget)})})}function r(e,t){var n=this.opts();if(!n.busy||n.manualTrump){var i=e.children().index(t),r=n.currSlide<i;n.currSlide!=i&&(n.nextSlide=i,n._tempFx=n.pagerFx,n.API.prepareTx(!0,r),n.API.trigger("cycle-pager-activated",[n,e,t]))}}s.extend(s.fn.cycle.defaults,{pager:"> .cycle-pager",pagerActiveClass:"cycle-pager-active",pagerEvent:"click.cycle",pagerEventBubble:void 0,pagerTemplate:"<span>&bull;</span>"}),s(document).on("cycle-bootstrap",function(e,t,n){n.buildPagerLink=i}),s(document).on("cycle-slide-added",function(e,t,n,i){t.pager&&(t.API.buildPagerLink(t,n,i),t.API.page=r)}),s(document).on("cycle-slide-removed",function(e,t,n){t.pager&&t.API.getComponent("pager").each(function(){var e=s(this);s(e.children()[n]).remove()})}),s(document).on("cycle-update-view",function(e,t){t.pager&&t.API.getComponent("pager").each(function(){s(this).children().removeClass(t.pagerActiveClass).eq(t.currSlide).addClass(t.pagerActiveClass)})}),s(document).on("cycle-destroyed",function(e,t){var n=t.API.getComponent("pager");n&&(n.children().off(t.pagerEvent),t.pagerTemplate&&n.empty())})}(jQuery),function(e){"use strict";e.extend(e.fn.cycle.defaults,{next:"> .cycle-next",nextEvent:"click.cycle",disabledClass:"disabled",prev:"> .cycle-prev",prevEvent:"click.cycle",swipe:!1}),e(document).on("cycle-initialized",function(e,t){if(t.API.getComponent("next").on(t.nextEvent,function(e){e.preventDefault(),t.API.next()}),t.API.getComponent("prev").on(t.prevEvent,function(e){e.preventDefault(),t.API.prev()}),t.swipe){var n=t.swipeVert?"swipeUp.cycle":"swipeLeft.cycle swipeleft.cycle",i=t.swipeVert?"swipeDown.cycle":"swipeRight.cycle swiperight.cycle";t.container.on(n,function(){t._tempFx=t.swipeFx,t.API.next()}),t.container.on(i,function(){t._tempFx=t.swipeFx,t.API.prev()})}}),e(document).on("cycle-update-view",function(e,t){if(!t.allowWrap){var n=t.disabledClass,i=t.API.getComponent("next"),r=t.API.getComponent("prev"),o=t._prevBoundry||0,s=void 0!==t._nextBoundry?t._nextBoundry:t.slideCount-1;t.currSlide==s?i.addClass(n).prop("disabled",!0):i.removeClass(n).prop("disabled",!1),t.currSlide===o?r.addClass(n).prop("disabled",!0):r.removeClass(n).prop("disabled",!1)}}),e(document).on("cycle-destroyed",function(e,t){t.API.getComponent("prev").off(t.nextEvent),t.API.getComponent("next").off(t.prevEvent),t.container.off("swipeleft.cycle swiperight.cycle swipeLeft.cycle swipeRight.cycle swipeUp.cycle swipeDown.cycle")})}(jQuery),function(c){"use strict";c.extend(c.fn.cycle.defaults,{progressive:!1}),c(document).on("cycle-pre-initialize",function(e,r){if(r.progressive){var o,t,n=r.API,i=n.next,s=n.prev,a=n.prepareTx,l=c.type(r.progressive);if("array"==l)o=r.progressive;else if(c.isFunction(r.progressive))o=r.progressive(r);else if("string"==l){if(t=c(r.progressive),!(o=c.trim(t.html())))return;if(/^(\[)/.test(o))try{o=c.parseJSON(o)}catch(e){return void n.log("error parsing progressive slides",e)}else(o=o.split(new RegExp(t.data("cycle-split")||"\n")))[o.length-1]||o.pop()}a&&(n.prepareTx=function(e,t){var n,i;return e||0===o.length?void a.apply(r.API,[e,t]):void(t&&r.currSlide==r.slideCount-1?(i=o[0],o=o.slice(1),r.container.one("cycle-slide-added",function(e,t){setTimeout(function(){t.API.advanceSlide(1)},50)}),r.API.add(i)):t||0!==r.currSlide?a.apply(r.API,[e,t]):(n=o.length-1,i=o[n],o=o.slice(0,n),r.container.one("cycle-slide-added",function(e,t){setTimeout(function(){t.currSlide=1,t.API.advanceSlide(-1)},50)}),r.API.add(i,!0)))}),i&&(n.next=function(){var e=this.opts();if(o.length&&e.currSlide==e.slideCount-1){var t=o[0];o=o.slice(1),e.container.one("cycle-slide-added",function(e,t){i.apply(t.API),t.container.removeClass("cycle-loading")}),e.container.addClass("cycle-loading"),e.API.add(t)}else i.apply(e.API)}),s&&(n.prev=function(){var e=this.opts();if(o.length&&0===e.currSlide){var t=o.length-1,n=o[t];o=o.slice(0,t),e.container.one("cycle-slide-added",function(e,t){t.currSlide=1,t.API.advanceSlide(-1),t.container.removeClass("cycle-loading")}),e.container.addClass("cycle-loading"),e.API.add(n,!0)}else s.apply(e.API)})}})}(jQuery),function(l){"use strict";l.extend(l.fn.cycle.defaults,{tmplRegex:"{{((.)?.*?)}}"}),l.extend(l.fn.cycle.API,{tmpl:function(e,t){var n=new RegExp(t.tmplRegex||l.fn.cycle.defaults.tmplRegex,"g"),a=l.makeArray(arguments);return a.shift(),e.replace(n,function(e,t){var n,i,r,o,s=t.split(".");for(n=0;n<a.length;n++)if(r=a[n]){if(1<s.length)for(o=r,i=0;i<s.length;i++)o=(r=o)[s[i]]||t;else o=r[t];if(l.isFunction(o))return o.apply(r,a);if(null!=o&&o!=t)return o}return t})}})}(jQuery),function(f){"use strict";f(document).on("cycle-bootstrap",function(e,t,n){"carousel"===t.fx&&(n.getSlideIndex=function(e){var t=this.opts()._carouselWrap.children();return t.index(e)%t.length},n.next=function(){var e=t.reverse?-1:1;!1===t.allowWrap&&t.currSlide+e>t.slideCount-t.carouselVisible||(t.API.advanceSlide(e),t.API.trigger("cycle-next",[t]).log("cycle-next"))})}),f.fn.cycle.transitions.carousel={preInit:function(e){e.hideNonActive=!1,e.container.on("cycle-destroyed",f.proxy(this.onDestroy,e.API)),e.API.stopTransition=this.stopTransition;for(var t=0;t<e.startingSlide;t++)e.container.append(e.slides[0])},postInit:function(e){var t,n,i,r,o=e.carouselVertical;e.carouselVisible&&e.carouselVisible>e.slideCount&&(e.carouselVisible=e.slideCount-1);var s=e.carouselVisible||e.slides.length,a={display:o?"block":"inline-block",position:"static"};if(e.container.css({position:"relative",overflow:"hidden"}),e.slides.css(a),e._currSlide=e.currSlide,r=f('<div class="cycle-carousel-wrap"></div>').prependTo(e.container).css({margin:0,padding:0,top:0,left:0,position:"absolute"}).append(e.slides),e._carouselWrap=r,o||r.css("white-space","nowrap"),!1!==e.allowWrap){for(n=0;n<(void 0===e.carouselVisible?2:1);n++){for(t=0;t<e.slideCount;t++)r.append(e.slides[t].cloneNode(!0));for(t=e.slideCount;t--;)r.prepend(e.slides[t].cloneNode(!0))}r.find(".cycle-slide-active").removeClass("cycle-slide-active"),e.slides.eq(e.startingSlide).addClass("cycle-slide-active")}e.pager&&!1===e.allowWrap&&(i=e.slideCount-s,f(e.pager).children().filter(":gt("+i+")").hide()),e._nextBoundry=e.slideCount-e.carouselVisible,this.prepareDimensions(e)},prepareDimensions:function(e){var t,n,i,r,o=e.carouselVertical,s=e.carouselVisible||e.slides.length;if(e.carouselFluid&&e.carouselVisible?e._carouselResizeThrottle||this.fluidSlides(e):e.carouselVisible&&e.carouselSlideDimension?(t=s*e.carouselSlideDimension,e.container[o?"height":"width"](t)):e.carouselVisible&&(t=s*f(e.slides[0])[o?"outerHeight":"outerWidth"](!0),e.container[o?"height":"width"](t)),n=e.carouselOffset||0,!1!==e.allowWrap)if(e.carouselSlideDimension)n-=(e.slideCount+e.currSlide)*e.carouselSlideDimension;else for(i=e._carouselWrap.children(),r=0;r<e.slideCount+e.currSlide;r++)n-=f(i[r])[o?"outerHeight":"outerWidth"](!0);e._carouselWrap.css(o?"top":"left",n)},fluidSlides:function(t){function e(){clearTimeout(i),i=setTimeout(n,20)}function n(){t._carouselWrap.stop(!1,!0);var e=t.container.width()/t.carouselVisible;e=Math.ceil(e-o),t._carouselWrap.children().width(e),t._sentinel&&t._sentinel.width(e),s(t)}var i,r=t.slides.eq(0),o=r.outerWidth()-r.width(),s=this.prepareDimensions;f(window).on("resize",e),t._carouselResizeThrottle=e,n()},transition:function(e,t,n,i,r){var o,s={},a=e.nextSlide-e.currSlide,l=e.carouselVertical,c=e.speed;if(!1===e.allowWrap){i=0<a;var u=e._currSlide,d=e.slideCount-e.carouselVisible;0<a&&e.nextSlide>d&&u==d?a=0:0<a&&e.nextSlide>d?a=e.nextSlide-u-(e.nextSlide-d):a<0&&e.currSlide>d&&e.nextSlide>d?a=0:a<0&&e.currSlide>d?a+=e.currSlide-d:u=e.currSlide,o=this.getScroll(e,l,u,a),e.API.opts()._currSlide=e.nextSlide>d?d:e.nextSlide}else i&&0===e.nextSlide?(o=this.getDim(e,e.currSlide,l),r=this.genCallback(e,i,l,r)):i||e.nextSlide!=e.slideCount-1?o=this.getScroll(e,l,e.currSlide,a):(o=this.getDim(e,e.currSlide,l),r=this.genCallback(e,i,l,r));s[l?"top":"left"]=i?"-="+o:"+="+o,e.throttleSpeed&&(c=o/f(e.slides[0])[l?"height":"width"]()*e.speed),e._carouselWrap.animate(s,c,e.easing,r)},getDim:function(e,t,n){return f(e.slides[t])[n?"outerHeight":"outerWidth"](!0)},getScroll:function(e,t,n,i){var r,o=0;if(0<i)for(r=n;r<n+i;r++)o+=this.getDim(e,r,t);else for(r=n;n+i<r;r--)o+=this.getDim(e,r,t);return o},genCallback:function(t,e,n,i){return function(){var e=0-f(t.slides[t.nextSlide]).position()[n?"top":"left"]+(t.carouselOffset||0);t._carouselWrap.css(t.carouselVertical?"top":"left",e),i()}},stopTransition:function(){var e=this.opts();e.slides.stop(!1,!0),e._carouselWrap.stop(!1,!0)},onDestroy:function(){var e=this.opts();e._carouselResizeThrottle&&f(window).off("resize",e._carouselResizeThrottle),e.slides.prependTo(e.container),e._carouselWrap.remove()}}}(jQuery),function(c,u,e,d){var s=/\d+h/i,a=/\d+w/i,l=/([\d\.]+)x\b/i;function t(e){if(e.data("srcset-sizes")){var r=c(u).height(),o=c(u).width(),s=u.devicePixelRatio||1,t=e.data("srcset-sizes");filteredData=c.grep(t,function(e,t){return e.w>=o&&e.h>=r&&e.dpi>=s});var n={data:d,diff:1/0};if(1===filteredData.length&&(n.data=filteredData[0]),0===filteredData.length)t.map(function(e,t){var n=o-e.w==-1/0?0:o*s-e.w,i=r-e.h==-1/0?0:r*s-e.h;return e.diff=n+i,e}).sort(function(e,t){return e.diff<t.diff}).forEach(function(e,t){n.data&&n.data.diff<=0||(n.data=e)});else for(var i=0;i<filteredData.length;i++){var a=(filteredData[i].w-o==1/0?0:filteredData[i].w*s-o)+(filteredData[i].h-r==1/0?0:filteredData[i].h*s-r);a<n.diff&&(n.diff=a,n.data=filteredData[i])}if((t=n.data)&&t.url){var l=(e.data("srcset-base")||"")+t.url+(e.data("srcset-ext")||"");e.attr("src")!==l&&(e.trigger("beforeSrcReplace"),e.attr("src",l),e.trigger("srcReplaced"))}else console.log("No src found for %o",e)}}c.srcset=c.extend({autoInit:!0,updateOnResize:!0},c.srcset||{}),c.fn.srcset=function(e){options=c.extend({},c.srcset,e||{}),c(this).each(function(){!function(e){for(var t,n=e.data("srcset").split(","),i=[],r=0;r<n.length;r++){var o={url:0<(t=n[r].replace(/^\s*|\s*$/gi,"")).split(" ").length?t.split(" ")[0]:d,w:a.test(t)?parseInt(a.exec(t)[0]):1/0,h:s.test(t)?parseInt(s.exec(t)[0]):1/0,dpi:parseFloat(l.test(t)?l.exec(t)[1]:1)};o.url?i.push(o):console.log("Couldn't parse URL; got %o",o)}0===i.length?console.log("Couldn't parse srcset data for %o",e):e.data("srcset-sizes",i)}(c(this)),t(c(this))}),options.updateOnResize&&(c(u).on("resize",function(){c(this).each(function(){t(c(this))})}.bind(this)),c(u))},c(function(){c.srcset.autoInit&&c("img[data-srcset]").srcset()})}(window.jQuery,window,document),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t():"function"==typeof define&&define.amd?define(t):t()}(0,function(){"use strict";var e=window.jQuery;if(!e)throw new Error("resizeend require jQuery");e.event.special.resizeend={setup:function(){var t=e(this);t.on("resize.resizeend",function(r){function o(){var e=Date.now()-d;e<s&&0<=e?l=setTimeout(o,s-e):(l=null,a||(f=r.apply(u,c),c=u=null))}var s=1<arguments.length&&void 0!==arguments[1]?arguments[1]:100,a=arguments[2],l=void 0,c=void 0,u=void 0,d=void 0,f=void 0,e=function(){u=this;for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];c=t,d=Date.now();var i=a&&!l;return l||(l=setTimeout(o,s)),i&&(f=r.apply(u,c),c=u=null),f};return e.clear=function(){l&&(clearTimeout(l),l=null)},e.flush=function(){l&&(f=r.apply(u,c),c=u=null,clearTimeout(l),l=null)},e}.call(null,function(e){e.type="resizeend",t.trigger("resizeend",e)},250))},teardown:function(){e(this).off("resize.resizeend")}}}),function(e,t){"use strict";var n;n=void 0,e.fn.adjustWindow=function(){n.height()},e.fn.resizeWindow=function(){return e(window).resizeend(function(){n=e(window),e.fn.adjustWindow()})},e(document).ready(function(){n=e(window),e.fn.adjustWindow(),conditionizr.add("chrome",!!window.chrome&&/google/i.test(navigator.vendor)),conditionizr.add("firefox","InstallTrigger"in window),conditionizr.add("ie8",!!Function("/*@cc_on return (@_jscript_version > 5.7 && !/^(9|10)/.test(@_jscript_version)); @*/")()),conditionizr.add("ie9",!!Function("/*@cc_on return (/^9/.test(@_jscript_version) && /MSIE 9.0(?!.*IEMobile)/i.test(navigator.userAgent)); @*/")()),conditionizr.add("ie10",!!Function("/*@cc_on return (/^10/.test(@_jscript_version) && /MSIE 10.0(?!.*IEMobile)/i.test(navigator.userAgent)); @*/")()),conditionizr.add("ie11",/(?:\sTrident\/7\.0;.*\srv:11\.0)/i.test(navigator.userAgent)),conditionizr.add("ios",/iP(ad|hone|od)/i.test(navigator.userAgent)),conditionizr.add("safari",/Constructor/.test(window.HTMLElement)),conditionizr.add("windows",/win/i.test(navigator.platform)),conditionizr.config({tests:{chrome:["class"],firefox:["class"],ie8:["class"],ie9:["class"],ie10:["class"],ie11:["class"],ios:["class"],opera:["class"],safari:["class"],windows:["class"]}}),conditionizr.polyfill("assets/js/min/html5.min.js",["ie8","ie9"]),conditionizr.polyfill("assets/js/min/css3-mediaqueries.js",["ie8","ie9"]),conditionizr.polyfill("assets/js/min/selectivizr-min.js",["ie8","ie9"]),Modernizr.touchevents&&setTimeout(function(){window.scrollTo(0,1)},1e3)}),e(window).resize(function(){}),e(window).scroll(function(){})}(jQuery);