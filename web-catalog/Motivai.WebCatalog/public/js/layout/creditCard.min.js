(function(){var n,t,r,e=[].indexOf||function(n){for(var t=0,r=this.length;t<r;t++)if(t in this&&this[t]===n)return t;return-1};r=function(){function n(){this.trie={}}return n.prototype.push=function(n){var t,r,e,a,i,l,u;for(n=n.toString(),i=this.trie,u=[],r=e=0,a=(l=n.split("")).length;e<a;r=++e)null==i[t=l[r]]&&(r===n.length-1?i[t]=null:i[t]={}),u.push(i=i[t]);return u},n.prototype.find=function(n){var t,r,e,a,i,l;for(n=n.toString(),i=this.trie,r=e=0,a=(l=n.split("")).length;e<a;r=++e){if(t=l[r],!i.hasOwnProperty(t))return!1;if(null===i[t])return!0;i=i[t]}},n}(),t=function(){function n(n){if(this.trie=n,this.trie.constructor!==r)throw Error("Range constructor requires a Trie parameter")}return n.rangeWithString=function(t){var e,a,i,l,u,c,o,h,f;if("string"!=typeof t)throw Error("rangeWithString requires a string parameter");for(t=(t=t.replace(/ /g,"")).split(","),f=new r,e=0,i=t.length;e<i;e++)if(c=t[e],u=c.match(/^(\d+)-(\d+)$/))for(l=a=o=u[1],h=u[2];o<=h?a<=h:a>=h;l=o<=h?++a:--a)f.push(l);else{if(!c.match(/^\d+$/))throw Error("Invalid range '"+u+"'");f.push(c)}return new n(f)},n.prototype.match=function(n){return this.trie.find(n)},n}(),(n=jQuery).fn.validateCreditCard=function(r,a){var i,l,u,c,o,h,f,s,g,d,v,p,m;for(c=[{name:"amex",range:"34,37",valid_length:[15]},{name:"diners_club_carte_blanche",range:"300-305",valid_length:[14]},{name:"diners_club_international",range:"36",valid_length:[14]},{name:"jcb",range:"3528-3589",valid_length:[16]},{name:"laser",range:"6304, 6706, 6709, 6771",valid_length:[16,17,18,19]},{name:"visa",range:"4",valid_length:[13,14,15,16,17,18,19]},{name:"mastercard",range:"51-55,2221-2720",valid_length:[16]},{name:"discover",range:"6011, 622126-622925, 644-649, 65",valid_length:[16]},{name:"dankort",range:"5019",valid_length:[16]},{name:"uatp",range:"1",valid_length:[15]},{name:"elo",range:"636368,438935,504175,451416,509048,509067,509049,509069,509050,509074,509068,509040,509045,509051,509046,509066,509047,509042,509052,509043,509064,509040,36297,5067,4576,4011",valid_length:[15]},{name:"hipercard",range:"38,60",valid_length:[13,16,19]},{name:"aura",range:"50",valid_length:[16]}],i=!1,r&&("object"==typeof r?(a=r,i=!1,r=null):"function"==typeof r&&(i=!0)),null==a&&(a={}),null==a.accept&&(a.accept=function(){var n,t,r;for(r=[],n=0,t=c.length;n<t;n++)l=c[n],r.push(l.name);return r}()),s=0,g=(v=a.accept).length;s<g;s++)if(u=v[s],e.call(function(){var n,t,r;for(r=[],n=0,t=c.length;n<t;n++)l=c[n],r.push(l.name);return r}(),u)<0)throw Error("Credit card type '"+u+"' is not supported");return o=function(n){var r,i,o;for(o=function(){var n,t,r,i;for(i=[],n=0,t=c.length;n<t;n++)r=(l=c[n]).name,e.call(a.accept,r)>=0&&i.push(l);return i}(),r=0,i=o.length;r<i;r++)if(u=o[r],t.rangeWithString(u.range).match(n))return u;return null},f=function(n){var t,r,e,a,i,l;for(l=0,a=r=0,e=(i=n.split("").reverse()).length;r<e;a=++r)t=+(t=i[a]),l+=a%2?(t*=2)<10?t:t-9:t;return l%10==0},h=function(n,t){var r;return r=n.length,e.call(t.valid_length,r)>=0},m=function(n){var t,r;return u=o(n),r=!1,t=!1,null!=u&&(r=f(n),t=h(n,u)),{card_type:u,valid:r&&t,luhn_valid:r,length_valid:t}},p=function(t){return function(){var r;return r=d(n(t).val()),m(r)}}(this),d=function(n){return n.replace(/[ -]/g,"")},i?(this.on("input.jccv",function(t){return function(){return n(t).off("keyup.jccv"),r.call(t,p())}}(this)),this.on("keyup.jccv",function(n){return function(){return r.call(n,p())}}(this)),r.call(this,p()),this):p()}}).call(this),function(n,t){"use strict";n.fn.initCreditCardBrand=function(t){n(t).length&&n(t).validateCreditCard(function(t){return null===t.card_type?n(this).next().attr("class","cardBrand"):(n(this).next().attr("class","cardBrand"),n(this).next().addClass("icons-"+t.card_type.name))})}}(jQuery);