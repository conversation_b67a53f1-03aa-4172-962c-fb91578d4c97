!function(e,t){"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(e,t){function n(e){var t=e.length,n=ie.type(e);return"function"!==n&&!ie.isWindow(e)&&(!(1!==e.nodeType||!t)||("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e))}function i(e,t,n){if(ie.isFunction(t))return ie.grep(e,function(e,i){return!!t.call(e,i,e)!==n});if(t.nodeType)return ie.grep(e,function(e){return e===t!==n});if("string"==typeof t){if(de.test(t))return ie.filter(t,e,n);t=ie.filter(t,e)}return ie.grep(e,function(e){return ie.inArray(e,t)>=0!==n})}function r(e,t){do{e=e[t]}while(e&&1!==e.nodeType);return e}function o(e){var t=ye[e]={};return ie.each(e.match(me)||[],function(e,n){t[n]=!0}),t}function a(){pe.addEventListener?(pe.removeEventListener("DOMContentLoaded",s,!1),e.removeEventListener("load",s,!1)):(pe.detachEvent("onreadystatechange",s),e.detachEvent("onload",s))}function s(){(pe.addEventListener||"load"===event.type||"complete"===pe.readyState)&&(a(),ie.ready())}function l(e,t,n){if(void 0===n&&1===e.nodeType){var i="data-"+t.replace(Se,"-$1").toLowerCase();if("string"==typeof(n=e.getAttribute(i))){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:Te.test(n)?ie.parseJSON(n):n)}catch(e){}ie.data(e,t,n)}else n=void 0}return n}function c(e){var t;for(t in e)if(("data"!==t||!ie.isEmptyObject(e[t]))&&"toJSON"!==t)return!1;return!0}function u(e,t,n,i){if(ie.acceptData(e)){var r,o,a=ie.expando,s=e.nodeType,l=s?ie.cache:e,c=s?e[a]:e[a]&&a;if(c&&l[c]&&(i||l[c].data)||void 0!==n||"string"!=typeof t)return c||(c=s?e[a]=X.pop()||ie.guid++:a),l[c]||(l[c]=s?{}:{toJSON:ie.noop}),("object"==typeof t||"function"==typeof t)&&(i?l[c]=ie.extend(l[c],t):l[c].data=ie.extend(l[c].data,t)),o=l[c],i||(o.data||(o.data={}),o=o.data),void 0!==n&&(o[ie.camelCase(t)]=n),"string"==typeof t?null==(r=o[t])&&(r=o[ie.camelCase(t)]):r=o,r}}function d(e,t,n){if(ie.acceptData(e)){var i,r,o=e.nodeType,a=o?ie.cache:e,s=o?e[ie.expando]:ie.expando;if(a[s]){if(t&&(i=n?a[s]:a[s].data)){ie.isArray(t)?t=t.concat(ie.map(t,ie.camelCase)):t in i?t=[t]:(t=ie.camelCase(t),t=t in i?[t]:t.split(" ")),r=t.length;for(;r--;)delete i[t[r]];if(n?!c(i):!ie.isEmptyObject(i))return}(n||(delete a[s].data,c(a[s])))&&(o?ie.cleanData([e],!0):te.deleteExpando||a!=a.window?delete a[s]:a[s]=null)}}}function f(){return!0}function p(){return!1}function h(){try{return pe.activeElement}catch(e){}}function g(e){var t=He.split("|"),n=e.createDocumentFragment();if(n.createElement)for(;t.length;)n.createElement(t.pop());return n}function v(e,t){var n,i,r=0,o=typeof e.getElementsByTagName!==we?e.getElementsByTagName(t||"*"):typeof e.querySelectorAll!==we?e.querySelectorAll(t||"*"):void 0;if(!o)for(o=[],n=e.childNodes||e;null!=(i=n[r]);r++)!t||ie.nodeName(i,t)?o.push(i):ie.merge(o,v(i,t));return void 0===t||t&&ie.nodeName(e,t)?ie.merge([e],o):o}function m(e){Ee.test(e.type)&&(e.defaultChecked=e.checked)}function y(e,t){return ie.nodeName(e,"table")&&ie.nodeName(11!==t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function x(e){return e.type=(null!==ie.find.attr(e,"type"))+"/"+e.type,e}function b(e){var t=Ve.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function w(e,t){for(var n,i=0;null!=(n=e[i]);i++)ie._data(n,"globalEval",!t||ie._data(t[i],"globalEval"))}function T(e,t){if(1===t.nodeType&&ie.hasData(e)){var n,i,r,o=ie._data(e),a=ie._data(t,o),s=o.events;if(s){delete a.handle,a.events={};for(n in s)for(i=0,r=s[n].length;r>i;i++)ie.event.add(t,n,s[n][i])}a.data&&(a.data=ie.extend({},a.data))}}function S(e,t){var n,i,r;if(1===t.nodeType){if(n=t.nodeName.toLowerCase(),!te.noCloneEvent&&t[ie.expando]){r=ie._data(t);for(i in r.events)ie.removeEvent(t,i,r.handle);t.removeAttribute(ie.expando)}"script"===n&&t.text!==e.text?(x(t).text=e.text,b(t)):"object"===n?(t.parentNode&&(t.outerHTML=e.outerHTML),te.html5Clone&&e.innerHTML&&!ie.trim(t.innerHTML)&&(t.innerHTML=e.innerHTML)):"input"===n&&Ee.test(e.type)?(t.defaultChecked=t.checked=e.checked,t.value!==e.value&&(t.value=e.value)):"option"===n?t.defaultSelected=t.selected=e.defaultSelected:("input"===n||"textarea"===n)&&(t.defaultValue=e.defaultValue)}}function C(t,n){var i,r=ie(n.createElement(t)).appendTo(n.body),o=e.getDefaultComputedStyle&&(i=e.getDefaultComputedStyle(r[0]))?i.display:ie.css(r[0],"display");return r.detach(),o}function A(e){var t=pe,n=Ge[e];return n||("none"!==(n=C(e,t))&&n||(Je=(Je||ie("<iframe frameborder='0' width='0' height='0'/>")).appendTo(t.documentElement),(t=(Je[0].contentWindow||Je[0].contentDocument).document).write(),t.close(),n=C(e,t),Je.detach()),Ge[e]=n),n}function k(e,t){return{get:function(){var n=e();if(null!=n)return n?void delete this.get:(this.get=t).apply(this,arguments)}}}function N(e,t){if(t in e)return t;for(var n=t.charAt(0).toUpperCase()+t.slice(1),i=t,r=ct.length;r--;)if((t=ct[r]+n)in e)return t;return i}function E(e,t){for(var n,i,r,o=[],a=0,s=e.length;s>a;a++)(i=e[a]).style&&(o[a]=ie._data(i,"olddisplay"),n=i.style.display,t?(o[a]||"none"!==n||(i.style.display=""),""===i.style.display&&ke(i)&&(o[a]=ie._data(i,"olddisplay",A(i.nodeName)))):(r=ke(i),(n&&"none"!==n||!r)&&ie._data(i,"olddisplay",r?n:ie.css(i,"display"))));for(a=0;s>a;a++)(i=e[a]).style&&(t&&"none"!==i.style.display&&""!==i.style.display||(i.style.display=t?o[a]||"":"none"));return e}function I(e,t,n){var i=ot.exec(t);return i?Math.max(0,i[1]-(n||0))+(i[2]||"px"):t}function P(e,t,n,i,r){for(var o=n===(i?"border":"content")?4:"width"===t?1:0,a=0;4>o;o+=2)"margin"===n&&(a+=ie.css(e,n+Ae[o],!0,r)),i?("content"===n&&(a-=ie.css(e,"padding"+Ae[o],!0,r)),"margin"!==n&&(a-=ie.css(e,"border"+Ae[o]+"Width",!0,r))):(a+=ie.css(e,"padding"+Ae[o],!0,r),"padding"!==n&&(a+=ie.css(e,"border"+Ae[o]+"Width",!0,r)));return a}function j(e,t,n){var i=!0,r="width"===t?e.offsetWidth:e.offsetHeight,o=Ze(e),a=te.boxSizing&&"border-box"===ie.css(e,"boxSizing",!1,o);if(0>=r||null==r){if((0>(r=Ye(e,t,o))||null==r)&&(r=e.style[t]),et.test(r))return r;i=a&&(te.boxSizingReliable()||r===e.style[t]),r=parseFloat(r)||0}return r+P(e,t,n||(a?"border":"content"),i,o)+"px"}function _(e,t,n,i,r){return new _.prototype.init(e,t,n,i,r)}function D(){return setTimeout(function(){ut=void 0}),ut=ie.now()}function H(e,t){var n,i={height:e},r=0;for(t=t?1:0;4>r;r+=2-t)n=Ae[r],i["margin"+n]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function L(e,t,n){for(var i,r=(vt[t]||[]).concat(vt["*"]),o=0,a=r.length;a>o;o++)if(i=r[o].call(n,t,e))return i}function z(e,t){var n,i,r,o,a;for(n in e)if(i=ie.camelCase(n),r=t[i],o=e[n],ie.isArray(o)&&(r=o[1],o=e[n]=o[0]),n!==i&&(e[i]=o,delete e[n]),(a=ie.cssHooks[i])&&"expand"in a){o=a.expand(o),delete e[i];for(n in o)n in e||(e[n]=o[n],t[n]=r)}else t[i]=r}function O(e,t,n){var i,r,o=0,a=gt.length,s=ie.Deferred().always(function(){delete l.elem}),l=function(){if(r)return!1;for(var t=ut||D(),n=Math.max(0,c.startTime+c.duration-t),i=1-(n/c.duration||0),o=0,a=c.tweens.length;a>o;o++)c.tweens[o].run(i);return s.notifyWith(e,[c,i,n]),1>i&&a?n:(s.resolveWith(e,[c]),!1)},c=s.promise({elem:e,props:ie.extend({},t),opts:ie.extend(!0,{specialEasing:{}},n),originalProperties:t,originalOptions:n,startTime:ut||D(),duration:n.duration,tweens:[],createTween:function(t,n){var i=ie.Tween(e,c.opts,t,n,c.opts.specialEasing[t]||c.opts.easing);return c.tweens.push(i),i},stop:function(t){var n=0,i=t?c.tweens.length:0;if(r)return this;for(r=!0;i>n;n++)c.tweens[n].run(1);return t?s.resolveWith(e,[c,t]):s.rejectWith(e,[c,t]),this}}),u=c.props;for(z(u,c.opts.specialEasing);a>o;o++)if(i=gt[o].call(c,e,u,c.opts))return i;return ie.map(u,L,c),ie.isFunction(c.opts.start)&&c.opts.start.call(e,c),ie.fx.timer(ie.extend(l,{elem:e,anim:c,queue:c.opts.queue})),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always)}function F(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var i,r=0,o=t.toLowerCase().match(me)||[];if(ie.isFunction(n))for(;i=o[r++];)"+"===i.charAt(0)?(i=i.slice(1)||"*",(e[i]=e[i]||[]).unshift(n)):(e[i]=e[i]||[]).push(n)}}function M(e,t,n,i){function r(s){var l;return o[s]=!0,ie.each(e[s]||[],function(e,s){var c=s(t,n,i);return"string"!=typeof c||a||o[c]?a?!(l=c):void 0:(t.dataTypes.unshift(c),r(c),!1)}),l}var o={},a=e===qt;return r(t.dataTypes[0])||!o["*"]&&r("*")}function q(e,t){var n,i,r=ie.ajaxSettings.flatOptions||{};for(i in t)void 0!==t[i]&&((r[i]?e:n||(n={}))[i]=t[i]);return n&&ie.extend(!0,e,n),e}function R(e,t,n){for(var i,r,o,a,s=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(a in s)if(s[a]&&s[a].test(r)){l.unshift(a);break}if(l[0]in n)o=l[0];else{for(a in n){if(!l[0]||e.converters[a+" "+l[0]]){o=a;break}i||(i=a)}o=o||i}return o?(o!==l[0]&&l.unshift(o),n[o]):void 0}function $(e,t,n,i){var r,o,a,s,l,c={},u=e.dataTypes.slice();if(u[1])for(a in e.converters)c[a.toLowerCase()]=e.converters[a];for(o=u.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=u.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(a=c[l+" "+o]||c["* "+o]))for(r in c)if((s=r.split(" "))[1]===o&&(a=c[l+" "+s[0]]||c["* "+s[0]])){!0===a?a=c[r]:!0!==c[r]&&(o=s[0],u.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}function W(e,t,n,i){var r;if(ie.isArray(t))ie.each(t,function(t,r){n||Wt.test(e)?i(e,r):W(e+"["+("object"==typeof r?t:"")+"]",r,n,i)});else if(n||"object"!==ie.type(t))i(e,t);else for(r in t)W(e+"["+r+"]",t[r],n,i)}function B(){try{return new e.XMLHttpRequest}catch(e){}}function V(){try{return new e.ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}function Q(e){return ie.isWindow(e)?e:9===e.nodeType&&(e.defaultView||e.parentWindow)}var X=[],U=X.slice,J=X.concat,G=X.push,Z=X.indexOf,Y={},K=Y.toString,ee=Y.hasOwnProperty,te={},ne="1.11.1",ie=function(e,t){return new ie.fn.init(e,t)},re=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,oe=/^-ms-/,ae=/-([\da-z])/gi,se=function(e,t){return t.toUpperCase()};ie.fn=ie.prototype={jquery:ne,constructor:ie,selector:"",length:0,toArray:function(){return U.call(this)},get:function(e){return null!=e?0>e?this[e+this.length]:this[e]:U.call(this)},pushStack:function(e){var t=ie.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e,t){return ie.each(this,e,t)},map:function(e){return this.pushStack(ie.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(U.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(0>e?t:0);return this.pushStack(n>=0&&t>n?[this[n]]:[])},end:function(){return this.prevObject||this.constructor(null)},push:G,sort:X.sort,splice:X.splice},ie.extend=ie.fn.extend=function(){var e,t,n,i,r,o,a=arguments[0]||{},s=1,l=arguments.length,c=!1;for("boolean"==typeof a&&(c=a,a=arguments[s]||{},s++),"object"==typeof a||ie.isFunction(a)||(a={}),s===l&&(a=this,s--);l>s;s++)if(null!=(r=arguments[s]))for(i in r)e=a[i],n=r[i],a!==n&&(c&&n&&(ie.isPlainObject(n)||(t=ie.isArray(n)))?(t?(t=!1,o=e&&ie.isArray(e)?e:[]):o=e&&ie.isPlainObject(e)?e:{},a[i]=ie.extend(c,o,n)):void 0!==n&&(a[i]=n));return a},ie.extend({expando:"jQuery"+(ne+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===ie.type(e)},isArray:Array.isArray||function(e){return"array"===ie.type(e)},isWindow:function(e){return null!=e&&e==e.window},isNumeric:function(e){return!ie.isArray(e)&&e-parseFloat(e)>=0},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},isPlainObject:function(e){var t;if(!e||"object"!==ie.type(e)||e.nodeType||ie.isWindow(e))return!1;try{if(e.constructor&&!ee.call(e,"constructor")&&!ee.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(e){return!1}if(te.ownLast)for(t in e)return ee.call(e,t);for(t in e);return void 0===t||ee.call(e,t)},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?Y[K.call(e)]||"object":typeof e},globalEval:function(t){t&&ie.trim(t)&&(e.execScript||function(t){e.eval.call(e,t)})(t)},camelCase:function(e){return e.replace(oe,"ms-").replace(ae,se)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t,i){var r=0,o=e.length,a=n(e);if(i){if(a)for(;o>r&&!1!==t.apply(e[r],i);r++);else for(r in e)if(!1===t.apply(e[r],i))break}else if(a)for(;o>r&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},trim:function(e){return null==e?"":(e+"").replace(re,"")},makeArray:function(e,t){var i=t||[];return null!=e&&(n(Object(e))?ie.merge(i,"string"==typeof e?[e]:e):G.call(i,e)),i},inArray:function(e,t,n){var i;if(t){if(Z)return Z.call(t,e,n);for(i=t.length,n=n?0>n?Math.max(0,i+n):n:0;i>n;n++)if(n in t&&t[n]===e)return n}return-1},merge:function(e,t){for(var n=+t.length,i=0,r=e.length;n>i;)e[r++]=t[i++];if(n!==n)for(;void 0!==t[i];)e[r++]=t[i++];return e.length=r,e},grep:function(e,t,n){for(var i=[],r=0,o=e.length,a=!n;o>r;r++)!t(e[r],r)!==a&&i.push(e[r]);return i},map:function(e,t,i){var r,o=0,a=e.length,s=[];if(n(e))for(;a>o;o++)null!=(r=t(e[o],o,i))&&s.push(r);else for(o in e)null!=(r=t(e[o],o,i))&&s.push(r);return J.apply([],s)},guid:1,proxy:function(e,t){var n,i,r;return"string"==typeof t&&(r=e[t],t=e,e=r),ie.isFunction(e)?(n=U.call(arguments,2),i=function(){return e.apply(t||this,n.concat(U.call(arguments)))},i.guid=e.guid=e.guid||ie.guid++,i):void 0},now:function(){return+new Date},support:te}),ie.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(e,t){Y["[object "+t+"]"]=t.toLowerCase()});var le=function(e){function t(e,t,n,i){var r,o,a,s,c,d,f,p,h,g;if((t?t.ownerDocument||t:F)!==P&&I(t),t=t||P,n=n||[],!e||"string"!=typeof e)return n;if(1!==(s=t.nodeType)&&9!==s)return[];if(_&&!i){if(r=ve.exec(e))if(a=r[1]){if(9===s){if(!(o=t.getElementById(a))||!o.parentNode)return n;if(o.id===a)return n.push(o),n}else if(t.ownerDocument&&(o=t.ownerDocument.getElementById(a))&&z(t,o)&&o.id===a)return n.push(o),n}else{if(r[2])return Z.apply(n,t.getElementsByTagName(e)),n;if((a=r[3])&&x.getElementsByClassName&&t.getElementsByClassName)return Z.apply(n,t.getElementsByClassName(a)),n}if(x.qsa&&(!D||!D.test(e))){if(p=f=O,h=t,g=9===s&&e,1===s&&"object"!==t.nodeName.toLowerCase()){for(d=S(e),(f=t.getAttribute("id"))?p=f.replace(ye,"\\$&"):t.setAttribute("id",p),p="[id='"+p+"'] ",c=d.length;c--;)d[c]=p+u(d[c]);h=me.test(e)&&l(t.parentNode)||t,g=d.join(",")}if(g)try{return Z.apply(n,h.querySelectorAll(g)),n}catch(e){}finally{f||t.removeAttribute("id")}}}return A(e.replace(ae,"$1"),t,n,i)}function n(){function e(n,i){return t.push(n+" ")>b.cacheLength&&delete e[t.shift()],e[n+" "]=i}var t=[];return e}function i(e){return e[O]=!0,e}function r(e){var t=P.createElement("div");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function o(e,t){for(var n=e.split("|"),i=e.length;i--;)b.attrHandle[n[i]]=t}function a(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||Q)-(~e.sourceIndex||Q);if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function s(e){return i(function(t){return t=+t,i(function(n,i){for(var r,o=e([],n.length,t),a=o.length;a--;)n[r=o[a]]&&(n[r]=!(i[r]=n[r]))})})}function l(e){return e&&typeof e.getElementsByTagName!==V&&e}function c(){}function u(e){for(var t=0,n=e.length,i="";n>t;t++)i+=e[t].value;return i}function d(e,t,n){var i=t.dir,r=n&&"parentNode"===i,o=q++;return t.first?function(t,n,o){for(;t=t[i];)if(1===t.nodeType||r)return e(t,n,o)}:function(t,n,a){var s,l,c=[M,o];if(a){for(;t=t[i];)if((1===t.nodeType||r)&&e(t,n,a))return!0}else for(;t=t[i];)if(1===t.nodeType||r){if(l=t[O]||(t[O]={}),(s=l[i])&&s[0]===M&&s[1]===o)return c[2]=s[2];if(l[i]=c,c[2]=e(t,n,a))return!0}}}function f(e){return e.length>1?function(t,n,i){for(var r=e.length;r--;)if(!e[r](t,n,i))return!1;return!0}:e[0]}function p(e,n,i){for(var r=0,o=n.length;o>r;r++)t(e,n[r],i);return i}function h(e,t,n,i,r){for(var o,a=[],s=0,l=e.length,c=null!=t;l>s;s++)(o=e[s])&&(!n||n(o,i,r))&&(a.push(o),c&&t.push(s));return a}function g(e,t,n,r,o,a){return r&&!r[O]&&(r=g(r)),o&&!o[O]&&(o=g(o,a)),i(function(i,a,s,l){var c,u,d,f=[],g=[],v=a.length,m=i||p(t||"*",s.nodeType?[s]:s,[]),y=!e||!i&&t?m:h(m,f,e,s,l),x=n?o||(i?e:v||r)?[]:a:y;if(n&&n(y,x,s,l),r)for(c=h(x,g),r(c,[],s,l),u=c.length;u--;)(d=c[u])&&(x[g[u]]=!(y[g[u]]=d));if(i){if(o||e){if(o){for(c=[],u=x.length;u--;)(d=x[u])&&c.push(y[u]=d);o(null,x=[],c,l)}for(u=x.length;u--;)(d=x[u])&&(c=o?K.call(i,d):f[u])>-1&&(i[c]=!(a[c]=d))}}else x=h(x===a?x.splice(v,x.length):x),o?o(null,a,x,l):Z.apply(a,x)})}function v(e){for(var t,n,i,r=e.length,o=b.relative[e[0].type],a=o||b.relative[" "],s=o?1:0,l=d(function(e){return e===t},a,!0),c=d(function(e){return K.call(t,e)>-1},a,!0),p=[function(e,n,i){return!o&&(i||n!==k)||((t=n).nodeType?l(e,n,i):c(e,n,i))}];r>s;s++)if(n=b.relative[e[s].type])p=[d(f(p),n)];else{if((n=b.filter[e[s].type].apply(null,e[s].matches))[O]){for(i=++s;r>i&&!b.relative[e[i].type];i++);return g(s>1&&f(p),s>1&&u(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(ae,"$1"),n,i>s&&v(e.slice(s,i)),r>i&&v(e=e.slice(i)),r>i&&u(e))}p.push(n)}return f(p)}function m(e,n){var r=n.length>0,o=e.length>0,a=function(i,a,s,l,c){var u,d,f,p=0,g="0",v=i&&[],m=[],y=k,x=i||o&&b.find.TAG("*",c),w=M+=null==y?1:Math.random()||.1,T=x.length;for(c&&(k=a!==P&&a);g!==T&&null!=(u=x[g]);g++){if(o&&u){for(d=0;f=e[d++];)if(f(u,a,s)){l.push(u);break}c&&(M=w)}r&&((u=!f&&u)&&p--,i&&v.push(u))}if(p+=g,r&&g!==p){for(d=0;f=n[d++];)f(v,m,a,s);if(i){if(p>0)for(;g--;)v[g]||m[g]||(m[g]=J.call(l));m=h(m)}Z.apply(l,m),c&&!i&&m.length>0&&p+n.length>1&&t.uniqueSort(l)}return c&&(M=w,k=y),v};return r?i(a):a}var y,x,b,w,T,S,C,A,k,N,E,I,P,j,_,D,H,L,z,O="sizzle"+-new Date,F=e.document,M=0,q=0,R=n(),$=n(),W=n(),B=function(e,t){return e===t&&(E=!0),0},V="undefined",Q=1<<31,X={}.hasOwnProperty,U=[],J=U.pop,G=U.push,Z=U.push,Y=U.slice,K=U.indexOf||function(e){for(var t=0,n=this.length;n>t;t++)if(this[t]===e)return t;return-1},ee="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",te="[\\x20\\t\\r\\n\\f]",ne="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",ie=ne.replace("w","w#"),re="\\["+te+"*("+ne+")(?:"+te+"*([*^$|!~]?=)"+te+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+ie+"))|)"+te+"*\\]",oe=":("+ne+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+re+")*)|.*)\\)|)",ae=new RegExp("^"+te+"+|((?:^|[^\\\\])(?:\\\\.)*)"+te+"+$","g"),se=new RegExp("^"+te+"*,"+te+"*"),le=new RegExp("^"+te+"*([>+~]|"+te+")"+te+"*"),ce=new RegExp("="+te+"*([^\\]'\"]*?)"+te+"*\\]","g"),ue=new RegExp(oe),de=new RegExp("^"+ie+"$"),fe={ID:new RegExp("^#("+ne+")"),CLASS:new RegExp("^\\.("+ne+")"),TAG:new RegExp("^("+ne.replace("w","w*")+")"),ATTR:new RegExp("^"+re),PSEUDO:new RegExp("^"+oe),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+te+"*(even|odd|(([+-]|)(\\d*)n|)"+te+"*(?:([+-]|)"+te+"*(\\d+)|))"+te+"*\\)|)","i"),bool:new RegExp("^(?:"+ee+")$","i"),needsContext:new RegExp("^"+te+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+te+"*((?:-\\d)?\\d*)"+te+"*\\)|)(?=[^-]|$)","i")},pe=/^(?:input|select|textarea|button)$/i,he=/^h\d$/i,ge=/^[^{]+\{\s*\[native \w/,ve=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,me=/[+~]/,ye=/'|\\/g,xe=new RegExp("\\\\([\\da-f]{1,6}"+te+"?|("+te+")|.)","ig"),be=function(e,t,n){var i="0x"+t-65536;return i!==i||n?t:0>i?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320)};try{Z.apply(U=Y.call(F.childNodes),F.childNodes),U[F.childNodes.length].nodeType}catch(e){Z={apply:U.length?function(e,t){G.apply(e,Y.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}x=t.support={},T=t.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},I=t.setDocument=function(e){var t,n=e?e.ownerDocument||e:F,i=n.defaultView;return n!==P&&9===n.nodeType&&n.documentElement?(P=n,j=n.documentElement,_=!T(n),i&&i!==i.top&&(i.addEventListener?i.addEventListener("unload",function(){I()},!1):i.attachEvent&&i.attachEvent("onunload",function(){I()})),x.attributes=r(function(e){return e.className="i",!e.getAttribute("className")}),x.getElementsByTagName=r(function(e){return e.appendChild(n.createComment("")),!e.getElementsByTagName("*").length}),x.getElementsByClassName=ge.test(n.getElementsByClassName)&&r(function(e){return e.innerHTML="<div class='a'></div><div class='a i'></div>",e.firstChild.className="i",2===e.getElementsByClassName("i").length}),x.getById=r(function(e){return j.appendChild(e).id=O,!n.getElementsByName||!n.getElementsByName(O).length}),x.getById?(b.find.ID=function(e,t){if(typeof t.getElementById!==V&&_){var n=t.getElementById(e);return n&&n.parentNode?[n]:[]}},b.filter.ID=function(e){var t=e.replace(xe,be);return function(e){return e.getAttribute("id")===t}}):(delete b.find.ID,b.filter.ID=function(e){var t=e.replace(xe,be);return function(e){var n=typeof e.getAttributeNode!==V&&e.getAttributeNode("id");return n&&n.value===t}}),b.find.TAG=x.getElementsByTagName?function(e,t){return typeof t.getElementsByTagName!==V?t.getElementsByTagName(e):void 0}:function(e,t){var n,i=[],r=0,o=t.getElementsByTagName(e);if("*"===e){for(;n=o[r++];)1===n.nodeType&&i.push(n);return i}return o},b.find.CLASS=x.getElementsByClassName&&function(e,t){return typeof t.getElementsByClassName!==V&&_?t.getElementsByClassName(e):void 0},H=[],D=[],(x.qsa=ge.test(n.querySelectorAll))&&(r(function(e){e.innerHTML="<select msallowclip=''><option selected=''></option></select>",e.querySelectorAll("[msallowclip^='']").length&&D.push("[*^$]="+te+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||D.push("\\["+te+"*(?:value|"+ee+")"),e.querySelectorAll(":checked").length||D.push(":checked")}),r(function(e){var t=n.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&D.push("name"+te+"*[*^$|!~]?="),e.querySelectorAll(":enabled").length||D.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),D.push(",.*:")})),(x.matchesSelector=ge.test(L=j.matches||j.webkitMatchesSelector||j.mozMatchesSelector||j.oMatchesSelector||j.msMatchesSelector))&&r(function(e){x.disconnectedMatch=L.call(e,"div"),L.call(e,"[s!='']:x"),H.push("!=",oe)}),D=D.length&&new RegExp(D.join("|")),H=H.length&&new RegExp(H.join("|")),t=ge.test(j.compareDocumentPosition),z=t||ge.test(j.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,i=t&&t.parentNode;return e===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):e.compareDocumentPosition&&16&e.compareDocumentPosition(i)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},B=t?function(e,t){if(e===t)return E=!0,0;var i=!e.compareDocumentPosition-!t.compareDocumentPosition;return i||(1&(i=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!x.sortDetached&&t.compareDocumentPosition(e)===i?e===n||e.ownerDocument===F&&z(F,e)?-1:t===n||t.ownerDocument===F&&z(F,t)?1:N?K.call(N,e)-K.call(N,t):0:4&i?-1:1)}:function(e,t){if(e===t)return E=!0,0;var i,r=0,o=e.parentNode,s=t.parentNode,l=[e],c=[t];if(!o||!s)return e===n?-1:t===n?1:o?-1:s?1:N?K.call(N,e)-K.call(N,t):0;if(o===s)return a(e,t);for(i=e;i=i.parentNode;)l.unshift(i);for(i=t;i=i.parentNode;)c.unshift(i);for(;l[r]===c[r];)r++;return r?a(l[r],c[r]):l[r]===F?-1:c[r]===F?1:0},n):P},t.matches=function(e,n){return t(e,null,null,n)},t.matchesSelector=function(e,n){if((e.ownerDocument||e)!==P&&I(e),n=n.replace(ce,"='$1']"),!(!x.matchesSelector||!_||H&&H.test(n)||D&&D.test(n)))try{var i=L.call(e,n);if(i||x.disconnectedMatch||e.document&&11!==e.document.nodeType)return i}catch(e){}return t(n,P,null,[e]).length>0},t.contains=function(e,t){return(e.ownerDocument||e)!==P&&I(e),z(e,t)},t.attr=function(e,t){(e.ownerDocument||e)!==P&&I(e);var n=b.attrHandle[t.toLowerCase()],i=n&&X.call(b.attrHandle,t.toLowerCase())?n(e,t,!_):void 0;return void 0!==i?i:x.attributes||!_?e.getAttribute(t):(i=e.getAttributeNode(t))&&i.specified?i.value:null},t.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},t.uniqueSort=function(e){var t,n=[],i=0,r=0;if(E=!x.detectDuplicates,N=!x.sortStable&&e.slice(0),e.sort(B),E){for(;t=e[r++];)t===e[r]&&(i=n.push(r));for(;i--;)e.splice(n[i],1)}return N=null,e},w=t.getText=function(e){var t,n="",i=0,r=e.nodeType;if(r){if(1===r||9===r||11===r){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=w(e)}else if(3===r||4===r)return e.nodeValue}else for(;t=e[i++];)n+=w(t);return n},(b=t.selectors={cacheLength:50,createPseudo:i,match:fe,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(xe,be),e[3]=(e[3]||e[4]||e[5]||"").replace(xe,be),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||t.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&t.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return fe.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&ue.test(n)&&(t=S(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(xe,be).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=R[e+" "];return t||(t=new RegExp("(^|"+te+")"+e+"("+te+"|$)"))&&R(e,function(e){return t.test("string"==typeof e.className&&e.className||typeof e.getAttribute!==V&&e.getAttribute("class")||"")})},ATTR:function(e,n,i){return function(r){var o=t.attr(r,e);return null==o?"!="===n:!n||(o+="","="===n?o===i:"!="===n?o!==i:"^="===n?i&&0===o.indexOf(i):"*="===n?i&&o.indexOf(i)>-1:"$="===n?i&&o.slice(-i.length)===i:"~="===n?(" "+o+" ").indexOf(i)>-1:"|="===n&&(o===i||o.slice(0,i.length+1)===i+"-"))}},CHILD:function(e,t,n,i,r){var o="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===i&&0===r?function(e){return!!e.parentNode}:function(t,n,l){var c,u,d,f,p,h,g=o!==a?"nextSibling":"previousSibling",v=t.parentNode,m=s&&t.nodeName.toLowerCase(),y=!l&&!s;if(v){if(o){for(;g;){for(d=t;d=d[g];)if(s?d.nodeName.toLowerCase()===m:1===d.nodeType)return!1;h=g="only"===e&&!h&&"nextSibling"}return!0}if(h=[a?v.firstChild:v.lastChild],a&&y){for(p=(c=(u=v[O]||(v[O]={}))[e]||[])[0]===M&&c[1],f=c[0]===M&&c[2],d=p&&v.childNodes[p];d=++p&&d&&d[g]||(f=p=0)||h.pop();)if(1===d.nodeType&&++f&&d===t){u[e]=[M,p,f];break}}else if(y&&(c=(t[O]||(t[O]={}))[e])&&c[0]===M)f=c[1];else for(;(d=++p&&d&&d[g]||(f=p=0)||h.pop())&&((s?d.nodeName.toLowerCase()!==m:1!==d.nodeType)||!++f||(y&&((d[O]||(d[O]={}))[e]=[M,f]),d!==t)););return(f-=r)===i||f%i==0&&f/i>=0}}},PSEUDO:function(e,n){var r,o=b.pseudos[e]||b.setFilters[e.toLowerCase()]||t.error("unsupported pseudo: "+e);return o[O]?o(n):o.length>1?(r=[e,e,"",n],b.setFilters.hasOwnProperty(e.toLowerCase())?i(function(e,t){for(var i,r=o(e,n),a=r.length;a--;)i=K.call(e,r[a]),e[i]=!(t[i]=r[a])}):function(e){return o(e,0,r)}):o}},pseudos:{not:i(function(e){var t=[],n=[],r=C(e.replace(ae,"$1"));return r[O]?i(function(e,t,n,i){for(var o,a=r(e,null,i,[]),s=e.length;s--;)(o=a[s])&&(e[s]=!(t[s]=o))}):function(e,i,o){return t[0]=e,r(t,null,o,n),!n.pop()}}),has:i(function(e){return function(n){return t(e,n).length>0}}),contains:i(function(e){return function(t){return(t.textContent||t.innerText||w(t)).indexOf(e)>-1}}),lang:i(function(e){return de.test(e||"")||t.error("unsupported lang: "+e),e=e.replace(xe,be).toLowerCase(),function(t){var n;do{if(n=_?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===j},focus:function(e){return e===P.activeElement&&(!P.hasFocus||P.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!b.pseudos.empty(e)},header:function(e){return he.test(e.nodeName)},input:function(e){return pe.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:s(function(){return[0]}),last:s(function(e,t){return[t-1]}),eq:s(function(e,t,n){return[0>n?n+t:n]}),even:s(function(e,t){for(var n=0;t>n;n+=2)e.push(n);return e}),odd:s(function(e,t){for(var n=1;t>n;n+=2)e.push(n);return e}),lt:s(function(e,t,n){for(var i=0>n?n+t:n;--i>=0;)e.push(i);return e}),gt:s(function(e,t,n){for(var i=0>n?n+t:n;++i<t;)e.push(i);return e})}}).pseudos.nth=b.pseudos.eq;for(y in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})b.pseudos[y]=function(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}(y);for(y in{submit:!0,reset:!0})b.pseudos[y]=function(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}(y);return c.prototype=b.filters=b.pseudos,b.setFilters=new c,S=t.tokenize=function(e,n){var i,r,o,a,s,l,c,u=$[e+" "];if(u)return n?0:u.slice(0);for(s=e,l=[],c=b.preFilter;s;){(!i||(r=se.exec(s)))&&(r&&(s=s.slice(r[0].length)||s),l.push(o=[])),i=!1,(r=le.exec(s))&&(i=r.shift(),o.push({value:i,type:r[0].replace(ae," ")}),s=s.slice(i.length));for(a in b.filter)!(r=fe[a].exec(s))||c[a]&&!(r=c[a](r))||(i=r.shift(),o.push({value:i,type:a,matches:r}),s=s.slice(i.length));if(!i)break}return n?s.length:s?t.error(e):$(e,l).slice(0)},C=t.compile=function(e,t){var n,i=[],r=[],o=W[e+" "];if(!o){for(t||(t=S(e)),n=t.length;n--;)(o=v(t[n]))[O]?i.push(o):r.push(o);(o=W(e,m(r,i))).selector=e}return o},A=t.select=function(e,t,n,i){var r,o,a,s,c,d="function"==typeof e&&e,f=!i&&S(e=d.selector||e);if(n=n||[],1===f.length){if((o=f[0]=f[0].slice(0)).length>2&&"ID"===(a=o[0]).type&&x.getById&&9===t.nodeType&&_&&b.relative[o[1].type]){if(!(t=(b.find.ID(a.matches[0].replace(xe,be),t)||[])[0]))return n;d&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(r=fe.needsContext.test(e)?0:o.length;r--&&(a=o[r],!b.relative[s=a.type]);)if((c=b.find[s])&&(i=c(a.matches[0].replace(xe,be),me.test(o[0].type)&&l(t.parentNode)||t))){if(o.splice(r,1),!(e=i.length&&u(o)))return Z.apply(n,i),n;break}}return(d||C(e,f))(i,t,!_,n,me.test(e)&&l(t.parentNode)||t),n},x.sortStable=O.split("").sort(B).join("")===O,x.detectDuplicates=!!E,I(),x.sortDetached=r(function(e){return 1&e.compareDocumentPosition(P.createElement("div"))}),r(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||o("type|href|height|width",function(e,t,n){return n?void 0:e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),x.attributes&&r(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||o("value",function(e,t,n){return n||"input"!==e.nodeName.toLowerCase()?void 0:e.defaultValue}),r(function(e){return null==e.getAttribute("disabled")})||o(ee,function(e,t,n){var i;return n?void 0:!0===e[t]?t.toLowerCase():(i=e.getAttributeNode(t))&&i.specified?i.value:null}),t}(e);ie.find=le,ie.expr=le.selectors,ie.expr[":"]=ie.expr.pseudos,ie.unique=le.uniqueSort,ie.text=le.getText,ie.isXMLDoc=le.isXML,ie.contains=le.contains;var ce=ie.expr.match.needsContext,ue=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,de=/^.[^:#\[\.,]*$/;ie.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?ie.find.matchesSelector(i,e)?[i]:[]:ie.find.matches(e,ie.grep(t,function(e){return 1===e.nodeType}))},ie.fn.extend({find:function(e){var t,n=[],i=this,r=i.length;if("string"!=typeof e)return this.pushStack(ie(e).filter(function(){for(t=0;r>t;t++)if(ie.contains(i[t],this))return!0}));for(t=0;r>t;t++)ie.find(e,i[t],n);return n=this.pushStack(r>1?ie.unique(n):n),n.selector=this.selector?this.selector+" "+e:e,n},filter:function(e){return this.pushStack(i(this,e||[],!1))},not:function(e){return this.pushStack(i(this,e||[],!0))},is:function(e){return!!i(this,"string"==typeof e&&ce.test(e)?ie(e):e||[],!1).length}});var fe,pe=e.document,he=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/;(ie.fn.init=function(e,t){var n,i;if(!e)return this;if("string"==typeof e){if(!(n="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:he.exec(e))||!n[1]&&t)return!t||t.jquery?(t||fe).find(e):this.constructor(t).find(e);if(n[1]){if(t=t instanceof ie?t[0]:t,ie.merge(this,ie.parseHTML(n[1],t&&t.nodeType?t.ownerDocument||t:pe,!0)),ue.test(n[1])&&ie.isPlainObject(t))for(n in t)ie.isFunction(this[n])?this[n](t[n]):this.attr(n,t[n]);return this}if((i=pe.getElementById(n[2]))&&i.parentNode){if(i.id!==n[2])return fe.find(e);this.length=1,this[0]=i}return this.context=pe,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):ie.isFunction(e)?void 0!==fe.ready?fe.ready(e):e(ie):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),ie.makeArray(e,this))}).prototype=ie.fn,fe=ie(pe);var ge=/^(?:parents|prev(?:Until|All))/,ve={children:!0,contents:!0,next:!0,prev:!0};ie.extend({dir:function(e,t,n){for(var i=[],r=e[t];r&&9!==r.nodeType&&(void 0===n||1!==r.nodeType||!ie(r).is(n));)1===r.nodeType&&i.push(r),r=r[t];return i},sibling:function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}}),ie.fn.extend({has:function(e){var t,n=ie(e,this),i=n.length;return this.filter(function(){for(t=0;i>t;t++)if(ie.contains(this,n[t]))return!0})},closest:function(e,t){for(var n,i=0,r=this.length,o=[],a=ce.test(e)||"string"!=typeof e?ie(e,t||this.context):0;r>i;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?a.index(n)>-1:1===n.nodeType&&ie.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?ie.unique(o):o)},index:function(e){return e?"string"==typeof e?ie.inArray(this[0],ie(e)):ie.inArray(e.jquery?e[0]:e,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(ie.unique(ie.merge(this.get(),ie(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),ie.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return ie.dir(e,"parentNode")},parentsUntil:function(e,t,n){return ie.dir(e,"parentNode",n)},next:function(e){return r(e,"nextSibling")},prev:function(e){return r(e,"previousSibling")},nextAll:function(e){return ie.dir(e,"nextSibling")},prevAll:function(e){return ie.dir(e,"previousSibling")},nextUntil:function(e,t,n){return ie.dir(e,"nextSibling",n)},prevUntil:function(e,t,n){return ie.dir(e,"previousSibling",n)},siblings:function(e){return ie.sibling((e.parentNode||{}).firstChild,e)},children:function(e){return ie.sibling(e.firstChild)},contents:function(e){return ie.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:ie.merge([],e.childNodes)}},function(e,t){ie.fn[e]=function(n,i){var r=ie.map(this,t,n);return"Until"!==e.slice(-5)&&(i=n),i&&"string"==typeof i&&(r=ie.filter(i,r)),this.length>1&&(ve[e]||(r=ie.unique(r)),ge.test(e)&&(r=r.reverse())),this.pushStack(r)}});var me=/\S+/g,ye={};ie.Callbacks=function(e){var t,n,i,r,a,s,l=[],c=!(e="string"==typeof e?ye[e]||o(e):ie.extend({},e)).once&&[],u=function(o){for(n=e.memory&&o,i=!0,a=s||0,s=0,r=l.length,t=!0;l&&r>a;a++)if(!1===l[a].apply(o[0],o[1])&&e.stopOnFalse){n=!1;break}t=!1,l&&(c?c.length&&u(c.shift()):n?l=[]:d.disable())},d={add:function(){if(l){var i=l.length;!function t(n){ie.each(n,function(n,i){var r=ie.type(i);"function"===r?e.unique&&d.has(i)||l.push(i):i&&i.length&&"string"!==r&&t(i)})}(arguments),t?r=l.length:n&&(s=i,u(n))}return this},remove:function(){return l&&ie.each(arguments,function(e,n){for(var i;(i=ie.inArray(n,l,i))>-1;)l.splice(i,1),t&&(r>=i&&r--,a>=i&&a--)}),this},has:function(e){return e?ie.inArray(e,l)>-1:!(!l||!l.length)},empty:function(){return l=[],r=0,this},disable:function(){return l=c=n=void 0,this},disabled:function(){return!l},lock:function(){return c=void 0,n||d.disable(),this},locked:function(){return!c},fireWith:function(e,n){return!l||i&&!c||(n=n||[],n=[e,n.slice?n.slice():n],t?c.push(n):u(n)),this},fire:function(){return d.fireWith(this,arguments),this},fired:function(){return!!i}};return d},ie.extend({Deferred:function(e){var t=[["resolve","done",ie.Callbacks("once memory"),"resolved"],["reject","fail",ie.Callbacks("once memory"),"rejected"],["notify","progress",ie.Callbacks("memory")]],n="pending",i={state:function(){return n},always:function(){return r.done(arguments).fail(arguments),this},then:function(){var e=arguments;return ie.Deferred(function(n){ie.each(t,function(t,o){var a=ie.isFunction(e[t])&&e[t];r[o[1]](function(){var e=a&&a.apply(this,arguments);e&&ie.isFunction(e.promise)?e.promise().done(n.resolve).fail(n.reject).progress(n.notify):n[o[0]+"With"](this===i?n.promise():this,a?[e]:arguments)})}),e=null}).promise()},promise:function(e){return null!=e?ie.extend(e,i):i}},r={};return i.pipe=i.then,ie.each(t,function(e,o){var a=o[2],s=o[3];i[o[1]]=a.add,s&&a.add(function(){n=s},t[1^e][2].disable,t[2][2].lock),r[o[0]]=function(){return r[o[0]+"With"](this===r?i:this,arguments),this},r[o[0]+"With"]=a.fireWith}),i.promise(r),e&&e.call(r,r),r},when:function(e){var t,n,i,r=0,o=U.call(arguments),a=o.length,s=1!==a||e&&ie.isFunction(e.promise)?a:0,l=1===s?e:ie.Deferred(),c=function(e,n,i){return function(r){n[e]=this,i[e]=arguments.length>1?U.call(arguments):r,i===t?l.notifyWith(n,i):--s||l.resolveWith(n,i)}};if(a>1)for(t=new Array(a),n=new Array(a),i=new Array(a);a>r;r++)o[r]&&ie.isFunction(o[r].promise)?o[r].promise().done(c(r,i,o)).fail(l.reject).progress(c(r,n,t)):--s;return s||l.resolveWith(i,o),l.promise()}});var xe;ie.fn.ready=function(e){return ie.ready.promise().done(e),this},ie.extend({isReady:!1,readyWait:1,holdReady:function(e){e?ie.readyWait++:ie.ready(!0)},ready:function(e){if(!0===e?!--ie.readyWait:!ie.isReady){if(!pe.body)return setTimeout(ie.ready);ie.isReady=!0,!0!==e&&--ie.readyWait>0||(xe.resolveWith(pe,[ie]),ie.fn.triggerHandler&&(ie(pe).triggerHandler("ready"),ie(pe).off("ready")))}}}),ie.ready.promise=function(t){if(!xe)if(xe=ie.Deferred(),"complete"===pe.readyState)setTimeout(ie.ready);else if(pe.addEventListener)pe.addEventListener("DOMContentLoaded",s,!1),e.addEventListener("load",s,!1);else{pe.attachEvent("onreadystatechange",s),e.attachEvent("onload",s);var n=!1;try{n=null==e.frameElement&&pe.documentElement}catch(e){}n&&n.doScroll&&function e(){if(!ie.isReady){try{n.doScroll("left")}catch(t){return setTimeout(e,50)}a(),ie.ready()}}()}return xe.promise(t)};var be,we="undefined";for(be in ie(te))break;te.ownLast="0"!==be,te.inlineBlockNeedsLayout=!1,ie(function(){var e,t,n,i;(n=pe.getElementsByTagName("body")[0])&&n.style&&(t=pe.createElement("div"),i=pe.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(t),typeof t.style.zoom!==we&&(t.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",te.inlineBlockNeedsLayout=e=3===t.offsetWidth,e&&(n.style.zoom=1)),n.removeChild(i))}),function(){var e=pe.createElement("div");if(null==te.deleteExpando){te.deleteExpando=!0;try{delete e.test}catch(e){te.deleteExpando=!1}}e=null}(),ie.acceptData=function(e){var t=ie.noData[(e.nodeName+" ").toLowerCase()],n=+e.nodeType||1;return(1===n||9===n)&&(!t||!0!==t&&e.getAttribute("classid")===t)};var Te=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Se=/([A-Z])/g;ie.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"},hasData:function(e){return!!(e=e.nodeType?ie.cache[e[ie.expando]]:e[ie.expando])&&!c(e)},data:function(e,t,n){return u(e,t,n)},removeData:function(e,t){return d(e,t)},_data:function(e,t,n){return u(e,t,n,!0)},_removeData:function(e,t){return d(e,t,!0)}}),ie.fn.extend({data:function(e,t){var n,i,r,o=this[0],a=o&&o.attributes;if(void 0===e){if(this.length&&(r=ie.data(o),1===o.nodeType&&!ie._data(o,"parsedAttrs"))){for(n=a.length;n--;)a[n]&&0===(i=a[n].name).indexOf("data-")&&(i=ie.camelCase(i.slice(5)),l(o,i,r[i]));ie._data(o,"parsedAttrs",!0)}return r}return"object"==typeof e?this.each(function(){ie.data(this,e)}):arguments.length>1?this.each(function(){ie.data(this,e,t)}):o?l(o,e,ie.data(o,e)):void 0},removeData:function(e){return this.each(function(){ie.removeData(this,e)})}}),ie.extend({queue:function(e,t,n){var i;return e?(t=(t||"fx")+"queue",i=ie._data(e,t),n&&(!i||ie.isArray(n)?i=ie._data(e,t,ie.makeArray(n)):i.push(n)),i||[]):void 0},dequeue:function(e,t){t=t||"fx";var n=ie.queue(e,t),i=n.length,r=n.shift(),o=ie._queueHooks(e,t);"inprogress"===r&&(r=n.shift(),i--),r&&("fx"===t&&n.unshift("inprogress"),delete o.stop,r.call(e,function(){ie.dequeue(e,t)},o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return ie._data(e,n)||ie._data(e,n,{empty:ie.Callbacks("once memory").add(function(){ie._removeData(e,t+"queue"),ie._removeData(e,n)})})}}),ie.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?ie.queue(this[0],e):void 0===t?this:this.each(function(){var n=ie.queue(this,e,t);ie._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&ie.dequeue(this,e)})},dequeue:function(e){return this.each(function(){ie.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,r=ie.Deferred(),o=this,a=this.length,s=function(){--i||r.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)(n=ie._data(o[a],e+"queueHooks"))&&n.empty&&(i++,n.empty.add(s));return s(),r.promise(t)}});var Ce=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Ae=["Top","Right","Bottom","Left"],ke=function(e,t){return e=t||e,"none"===ie.css(e,"display")||!ie.contains(e.ownerDocument,e)},Ne=ie.access=function(e,t,n,i,r,o,a){var s=0,l=e.length,c=null==n;if("object"===ie.type(n)){r=!0;for(s in n)ie.access(e,t,s,n[s],!0,o,a)}else if(void 0!==i&&(r=!0,ie.isFunction(i)||(a=!0),c&&(a?(t.call(e,i),t=null):(c=t,t=function(e,t,n){return c.call(ie(e),n)})),t))for(;l>s;s++)t(e[s],n,a?i:i.call(e[s],s,t(e[s],n)));return r?e:c?t.call(e):l?t(e[0],n):o},Ee=/^(?:checkbox|radio)$/i;!function(){var e=pe.createElement("input"),t=pe.createElement("div"),n=pe.createDocumentFragment();if(t.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",te.leadingWhitespace=3===t.firstChild.nodeType,te.tbody=!t.getElementsByTagName("tbody").length,te.htmlSerialize=!!t.getElementsByTagName("link").length,te.html5Clone="<:nav></:nav>"!==pe.createElement("nav").cloneNode(!0).outerHTML,e.type="checkbox",e.checked=!0,n.appendChild(e),te.appendChecked=e.checked,t.innerHTML="<textarea>x</textarea>",te.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue,n.appendChild(t),t.innerHTML="<input type='radio' checked='checked' name='t'/>",te.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,te.noCloneEvent=!0,t.attachEvent&&(t.attachEvent("onclick",function(){te.noCloneEvent=!1}),t.cloneNode(!0).click()),null==te.deleteExpando){te.deleteExpando=!0;try{delete t.test}catch(e){te.deleteExpando=!1}}}(),function(){var t,n,i=pe.createElement("div");for(t in{submit:!0,change:!0,focusin:!0})n="on"+t,(te[t+"Bubbles"]=n in e)||(i.setAttribute(n,"t"),te[t+"Bubbles"]=!1===i.attributes[n].expando);i=null}();var Ie=/^(?:input|select|textarea)$/i,Pe=/^key/,je=/^(?:mouse|pointer|contextmenu)|click/,_e=/^(?:focusinfocus|focusoutblur)$/,De=/^([^.]*)(?:\.(.+)|)$/;ie.event={global:{},add:function(e,t,n,i,r){var o,a,s,l,c,u,d,f,p,h,g,v=ie._data(e);if(v){for(n.handler&&(l=n,n=l.handler,r=l.selector),n.guid||(n.guid=ie.guid++),(a=v.events)||(a=v.events={}),(u=v.handle)||(u=v.handle=function(e){return typeof ie===we||e&&ie.event.triggered===e.type?void 0:ie.event.dispatch.apply(u.elem,arguments)},u.elem=e),s=(t=(t||"").match(me)||[""]).length;s--;)o=De.exec(t[s])||[],p=g=o[1],h=(o[2]||"").split(".").sort(),p&&(c=ie.event.special[p]||{},p=(r?c.delegateType:c.bindType)||p,c=ie.event.special[p]||{},d=ie.extend({type:p,origType:g,data:i,handler:n,guid:n.guid,selector:r,needsContext:r&&ie.expr.match.needsContext.test(r),namespace:h.join(".")},l),(f=a[p])||(f=a[p]=[],f.delegateCount=0,c.setup&&!1!==c.setup.call(e,i,h,u)||(e.addEventListener?e.addEventListener(p,u,!1):e.attachEvent&&e.attachEvent("on"+p,u))),c.add&&(c.add.call(e,d),d.handler.guid||(d.handler.guid=n.guid)),r?f.splice(f.delegateCount++,0,d):f.push(d),ie.event.global[p]=!0);e=null}},remove:function(e,t,n,i,r){var o,a,s,l,c,u,d,f,p,h,g,v=ie.hasData(e)&&ie._data(e);if(v&&(u=v.events)){for(c=(t=(t||"").match(me)||[""]).length;c--;)if(s=De.exec(t[c])||[],p=g=s[1],h=(s[2]||"").split(".").sort(),p){for(d=ie.event.special[p]||{},f=u[p=(i?d.delegateType:d.bindType)||p]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),l=o=f.length;o--;)a=f[o],!r&&g!==a.origType||n&&n.guid!==a.guid||s&&!s.test(a.namespace)||i&&i!==a.selector&&("**"!==i||!a.selector)||(f.splice(o,1),a.selector&&f.delegateCount--,d.remove&&d.remove.call(e,a));l&&!f.length&&(d.teardown&&!1!==d.teardown.call(e,h,v.handle)||ie.removeEvent(e,p,v.handle),delete u[p])}else for(p in u)ie.event.remove(e,p+t[c],n,i,!0);ie.isEmptyObject(u)&&(delete v.handle,ie._removeData(e,"events"))}},trigger:function(t,n,i,r){var o,a,s,l,c,u,d,f=[i||pe],p=ee.call(t,"type")?t.type:t,h=ee.call(t,"namespace")?t.namespace.split("."):[];if(s=u=i=i||pe,3!==i.nodeType&&8!==i.nodeType&&!_e.test(p+ie.event.triggered)&&(p.indexOf(".")>=0&&(h=p.split("."),p=h.shift(),h.sort()),a=p.indexOf(":")<0&&"on"+p,t=t[ie.expando]?t:new ie.Event(p,"object"==typeof t&&t),t.isTrigger=r?2:3,t.namespace=h.join("."),t.namespace_re=t.namespace?new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=i),n=null==n?[t]:ie.makeArray(n,[t]),c=ie.event.special[p]||{},r||!c.trigger||!1!==c.trigger.apply(i,n))){if(!r&&!c.noBubble&&!ie.isWindow(i)){for(l=c.delegateType||p,_e.test(l+p)||(s=s.parentNode);s;s=s.parentNode)f.push(s),u=s;u===(i.ownerDocument||pe)&&f.push(u.defaultView||u.parentWindow||e)}for(d=0;(s=f[d++])&&!t.isPropagationStopped();)t.type=d>1?l:c.bindType||p,(o=(ie._data(s,"events")||{})[t.type]&&ie._data(s,"handle"))&&o.apply(s,n),(o=a&&s[a])&&o.apply&&ie.acceptData(s)&&(t.result=o.apply(s,n),!1===t.result&&t.preventDefault());if(t.type=p,!r&&!t.isDefaultPrevented()&&(!c._default||!1===c._default.apply(f.pop(),n))&&ie.acceptData(i)&&a&&i[p]&&!ie.isWindow(i)){(u=i[a])&&(i[a]=null),ie.event.triggered=p;try{i[p]()}catch(e){}ie.event.triggered=void 0,u&&(i[a]=u)}return t.result}},dispatch:function(e){e=ie.event.fix(e);var t,n,i,r,o,a=[],s=U.call(arguments),l=(ie._data(this,"events")||{})[e.type]||[],c=ie.event.special[e.type]||{};if(s[0]=e,e.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,e)){for(a=ie.event.handlers.call(this,e,l),t=0;(r=a[t++])&&!e.isPropagationStopped();)for(e.currentTarget=r.elem,o=0;(i=r.handlers[o++])&&!e.isImmediatePropagationStopped();)(!e.namespace_re||e.namespace_re.test(i.namespace))&&(e.handleObj=i,e.data=i.data,void 0!==(n=((ie.event.special[i.origType]||{}).handle||i.handler).apply(r.elem,s))&&!1===(e.result=n)&&(e.preventDefault(),e.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,i,r,o,a=[],s=t.delegateCount,l=e.target;if(s&&l.nodeType&&(!e.button||"click"!==e.type))for(;l!=this;l=l.parentNode||this)if(1===l.nodeType&&(!0!==l.disabled||"click"!==e.type)){for(r=[],o=0;s>o;o++)i=t[o],n=i.selector+" ",void 0===r[n]&&(r[n]=i.needsContext?ie(n,this).index(l)>=0:ie.find(n,this,null,[l]).length),r[n]&&r.push(i);r.length&&a.push({elem:l,handlers:r})}return s<t.length&&a.push({elem:this,handlers:t.slice(s)}),a},fix:function(e){if(e[ie.expando])return e;var t,n,i,r=e.type,o=e,a=this.fixHooks[r];for(a||(this.fixHooks[r]=a=je.test(r)?this.mouseHooks:Pe.test(r)?this.keyHooks:{}),i=a.props?this.props.concat(a.props):this.props,e=new ie.Event(o),t=i.length;t--;)n=i[t],e[n]=o[n];return e.target||(e.target=o.srcElement||pe),3===e.target.nodeType&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,a.filter?a.filter(e,o):e},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,i,r,o=t.button,a=t.fromElement;return null==e.pageX&&null!=t.clientX&&(i=e.target.ownerDocument||pe,r=i.documentElement,n=i.body,e.pageX=t.clientX+(r&&r.scrollLeft||n&&n.scrollLeft||0)-(r&&r.clientLeft||n&&n.clientLeft||0),e.pageY=t.clientY+(r&&r.scrollTop||n&&n.scrollTop||0)-(r&&r.clientTop||n&&n.clientTop||0)),!e.relatedTarget&&a&&(e.relatedTarget=a===e.target?t.toElement:a),e.which||void 0===o||(e.which=1&o?1:2&o?3:4&o?2:0),e}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==h()&&this.focus)try{return this.focus(),!1}catch(e){}},delegateType:"focusin"},blur:{trigger:function(){return this===h()&&this.blur?(this.blur(),!1):void 0},delegateType:"focusout"},click:{trigger:function(){return ie.nodeName(this,"input")&&"checkbox"===this.type&&this.click?(this.click(),!1):void 0},_default:function(e){return ie.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n,i){var r=ie.extend(new ie.Event,n,{type:e,isSimulated:!0,originalEvent:{}});i?ie.event.trigger(r,null,t):ie.event.dispatch.call(t,r),r.isDefaultPrevented()&&n.preventDefault()}},ie.removeEvent=pe.removeEventListener?function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)}:function(e,t,n){var i="on"+t;e.detachEvent&&(typeof e[i]===we&&(e[i]=null),e.detachEvent(i,n))},ie.Event=function(e,t){return this instanceof ie.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?f:p):this.type=e,t&&ie.extend(this,t),this.timeStamp=e&&e.timeStamp||ie.now(),void(this[ie.expando]=!0)):new ie.Event(e,t)},ie.Event.prototype={isDefaultPrevented:p,isPropagationStopped:p,isImmediatePropagationStopped:p,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=f,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=f,e&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0)},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=f,e&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),this.stopPropagation()}},ie.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){ie.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,i=this,r=e.relatedTarget,o=e.handleObj;return(!r||r!==i&&!ie.contains(i,r))&&(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}}),te.submitBubbles||(ie.event.special.submit={setup:function(){return!ie.nodeName(this,"form")&&void ie.event.add(this,"click._submit keypress._submit",function(e){var t=e.target,n=ie.nodeName(t,"input")||ie.nodeName(t,"button")?t.form:void 0;n&&!ie._data(n,"submitBubbles")&&(ie.event.add(n,"submit._submit",function(e){e._submit_bubble=!0}),ie._data(n,"submitBubbles",!0))})},postDispatch:function(e){e._submit_bubble&&(delete e._submit_bubble,this.parentNode&&!e.isTrigger&&ie.event.simulate("submit",this.parentNode,e,!0))},teardown:function(){return!ie.nodeName(this,"form")&&void ie.event.remove(this,"._submit")}}),te.changeBubbles||(ie.event.special.change={setup:function(){return Ie.test(this.nodeName)?(("checkbox"===this.type||"radio"===this.type)&&(ie.event.add(this,"propertychange._change",function(e){"checked"===e.originalEvent.propertyName&&(this._just_changed=!0)}),ie.event.add(this,"click._change",function(e){this._just_changed&&!e.isTrigger&&(this._just_changed=!1),ie.event.simulate("change",this,e,!0)})),!1):void ie.event.add(this,"beforeactivate._change",function(e){var t=e.target;Ie.test(t.nodeName)&&!ie._data(t,"changeBubbles")&&(ie.event.add(t,"change._change",function(e){!this.parentNode||e.isSimulated||e.isTrigger||ie.event.simulate("change",this.parentNode,e,!0)}),ie._data(t,"changeBubbles",!0))})},handle:function(e){var t=e.target;return this!==t||e.isSimulated||e.isTrigger||"radio"!==t.type&&"checkbox"!==t.type?e.handleObj.handler.apply(this,arguments):void 0},teardown:function(){return ie.event.remove(this,"._change"),!Ie.test(this.nodeName)}}),te.focusinBubbles||ie.each({focus:"focusin",blur:"focusout"},function(e,t){var n=function(e){ie.event.simulate(t,e.target,ie.event.fix(e),!0)};ie.event.special[t]={setup:function(){var i=this.ownerDocument||this,r=ie._data(i,t);r||i.addEventListener(e,n,!0),ie._data(i,t,(r||0)+1)},teardown:function(){var i=this.ownerDocument||this,r=ie._data(i,t)-1;r?ie._data(i,t,r):(i.removeEventListener(e,n,!0),ie._removeData(i,t))}}}),ie.fn.extend({on:function(e,t,n,i,r){var o,a;if("object"==typeof e){"string"!=typeof t&&(n=n||t,t=void 0);for(o in e)this.on(o,t,n,e[o],r);return this}if(null==n&&null==i?(i=t,n=t=void 0):null==i&&("string"==typeof t?(i=n,n=void 0):(i=n,n=t,t=void 0)),!1===i)i=p;else if(!i)return this;return 1===r&&(a=i,i=function(e){return ie().off(e),a.apply(this,arguments)},i.guid=a.guid||(a.guid=ie.guid++)),this.each(function(){ie.event.add(this,e,i,n,t)})},one:function(e,t,n,i){return this.on(e,t,n,i,1)},off:function(e,t,n){var i,r;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,ie(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof e){for(r in e)this.off(r,t,e[r]);return this}return(!1===t||"function"==typeof t)&&(n=t,t=void 0),!1===n&&(n=p),this.each(function(){ie.event.remove(this,e,n,t)})},trigger:function(e,t){return this.each(function(){ie.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];return n?ie.event.trigger(e,t,n,!0):void 0}});var He="abbr|article|aside|audio|bdi|canvas|data|datalist|details|figcaption|figure|footer|header|hgroup|mark|meter|nav|output|progress|section|summary|time|video",Le=/ jQuery\d+="(?:null|\d+)"/g,ze=new RegExp("<(?:"+He+")[\\s/>]","i"),Oe=/^\s+/,Fe=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,Me=/<([\w:]+)/,qe=/<tbody/i,Re=/<|&#?\w+;/,$e=/<(?:script|style|link)/i,We=/checked\s*(?:[^=]|=\s*.checked.)/i,Be=/^$|\/(?:java|ecma)script/i,Ve=/^true\/(.*)/,Qe=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,Xe={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:te.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]},Ue=g(pe).appendChild(pe.createElement("div"));Xe.optgroup=Xe.option,Xe.tbody=Xe.tfoot=Xe.colgroup=Xe.caption=Xe.thead,Xe.th=Xe.td,ie.extend({clone:function(e,t,n){var i,r,o,a,s,l=ie.contains(e.ownerDocument,e);if(te.html5Clone||ie.isXMLDoc(e)||!ze.test("<"+e.nodeName+">")?o=e.cloneNode(!0):(Ue.innerHTML=e.outerHTML,Ue.removeChild(o=Ue.firstChild)),!(te.noCloneEvent&&te.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||ie.isXMLDoc(e)))for(i=v(o),s=v(e),a=0;null!=(r=s[a]);++a)i[a]&&S(r,i[a]);if(t)if(n)for(s=s||v(e),i=i||v(o),a=0;null!=(r=s[a]);a++)T(r,i[a]);else T(e,o);return(i=v(o,"script")).length>0&&w(i,!l&&v(e,"script")),i=s=r=null,o},buildFragment:function(e,t,n,i){for(var r,o,a,s,l,c,u,d=e.length,f=g(t),p=[],h=0;d>h;h++)if((o=e[h])||0===o)if("object"===ie.type(o))ie.merge(p,o.nodeType?[o]:o);else if(Re.test(o)){for(s=s||f.appendChild(t.createElement("div")),l=(Me.exec(o)||["",""])[1].toLowerCase(),u=Xe[l]||Xe._default,s.innerHTML=u[1]+o.replace(Fe,"<$1></$2>")+u[2],r=u[0];r--;)s=s.lastChild;if(!te.leadingWhitespace&&Oe.test(o)&&p.push(t.createTextNode(Oe.exec(o)[0])),!te.tbody)for(r=(o="table"!==l||qe.test(o)?"<table>"!==u[1]||qe.test(o)?0:s:s.firstChild)&&o.childNodes.length;r--;)ie.nodeName(c=o.childNodes[r],"tbody")&&!c.childNodes.length&&o.removeChild(c);for(ie.merge(p,s.childNodes),s.textContent="";s.firstChild;)s.removeChild(s.firstChild);s=f.lastChild}else p.push(t.createTextNode(o));for(s&&f.removeChild(s),te.appendChecked||ie.grep(v(p,"input"),m),h=0;o=p[h++];)if((!i||-1===ie.inArray(o,i))&&(a=ie.contains(o.ownerDocument,o),s=v(f.appendChild(o),"script"),a&&w(s),n))for(r=0;o=s[r++];)Be.test(o.type||"")&&n.push(o);return s=null,f},cleanData:function(e,t){for(var n,i,r,o,a=0,s=ie.expando,l=ie.cache,c=te.deleteExpando,u=ie.event.special;null!=(n=e[a]);a++)if((t||ie.acceptData(n))&&(r=n[s],o=r&&l[r])){if(o.events)for(i in o.events)u[i]?ie.event.remove(n,i):ie.removeEvent(n,i,o.handle);l[r]&&(delete l[r],c?delete n[s]:typeof n.removeAttribute!==we?n.removeAttribute(s):n[s]=null,X.push(r))}}}),ie.fn.extend({text:function(e){return Ne(this,function(e){return void 0===e?ie.text(this):this.empty().append((this[0]&&this[0].ownerDocument||pe).createTextNode(e))},null,e,arguments.length)},append:function(){return this.domManip(arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||y(this,e).appendChild(e)})},prepend:function(){return this.domManip(arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=y(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},remove:function(e,t){for(var n,i=e?ie.filter(e,this):this,r=0;null!=(n=i[r]);r++)t||1!==n.nodeType||ie.cleanData(v(n)),n.parentNode&&(t&&ie.contains(n.ownerDocument,n)&&w(v(n,"script")),n.parentNode.removeChild(n));return this},empty:function(){for(var e,t=0;null!=(e=this[t]);t++){for(1===e.nodeType&&ie.cleanData(v(e,!1));e.firstChild;)e.removeChild(e.firstChild);e.options&&ie.nodeName(e,"select")&&(e.options.length=0)}return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return ie.clone(this,e,t)})},html:function(e){return Ne(this,function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e)return 1===t.nodeType?t.innerHTML.replace(Le,""):void 0;if(!("string"!=typeof e||$e.test(e)||!te.htmlSerialize&&ze.test(e)||!te.leadingWhitespace&&Oe.test(e)||Xe[(Me.exec(e)||["",""])[1].toLowerCase()])){e=e.replace(Fe,"<$1></$2>");try{for(;i>n;n++)1===(t=this[n]||{}).nodeType&&(ie.cleanData(v(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=arguments[0];return this.domManip(arguments,function(t){e=this.parentNode,ie.cleanData(v(this)),e&&e.replaceChild(t,this)}),e&&(e.length||e.nodeType)?this:this.remove()},detach:function(e){return this.remove(e,!0)},domManip:function(e,t){e=J.apply([],e);var n,i,r,o,a,s,l=0,c=this.length,u=this,d=c-1,f=e[0],p=ie.isFunction(f);if(p||c>1&&"string"==typeof f&&!te.checkClone&&We.test(f))return this.each(function(n){var i=u.eq(n);p&&(e[0]=f.call(this,n,i.html())),i.domManip(e,t)});if(c&&(s=ie.buildFragment(e,this[0].ownerDocument,!1,this),n=s.firstChild,1===s.childNodes.length&&(s=n),n)){for(r=(o=ie.map(v(s,"script"),x)).length;c>l;l++)i=s,l!==d&&(i=ie.clone(i,!0,!0),r&&ie.merge(o,v(i,"script"))),t.call(this[l],i,l);if(r)for(a=o[o.length-1].ownerDocument,ie.map(o,b),l=0;r>l;l++)i=o[l],Be.test(i.type||"")&&!ie._data(i,"globalEval")&&ie.contains(a,i)&&(i.src?ie._evalUrl&&ie._evalUrl(i.src):ie.globalEval((i.text||i.textContent||i.innerHTML||"").replace(Qe,"")));s=n=null}return this}}),ie.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){ie.fn[e]=function(e){for(var n,i=0,r=[],o=ie(e),a=o.length-1;a>=i;i++)n=i===a?this:this.clone(!0),ie(o[i])[t](n),G.apply(r,n.get());return this.pushStack(r)}});var Je,Ge={};!function(){var e;te.shrinkWrapBlocks=function(){if(null!=e)return e;e=!1;var t,n,i;return(n=pe.getElementsByTagName("body")[0])&&n.style?(t=pe.createElement("div"),i=pe.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(t),typeof t.style.zoom!==we&&(t.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1",t.appendChild(pe.createElement("div")).style.width="5px",e=3!==t.offsetWidth),n.removeChild(i),e):void 0}}();var Ze,Ye,Ke=/^margin/,et=new RegExp("^("+Ce+")(?!px)[a-z%]+$","i"),tt=/^(top|right|bottom|left)$/;e.getComputedStyle?(Ze=function(e){return e.ownerDocument.defaultView.getComputedStyle(e,null)},Ye=function(e,t,n){var i,r,o,a,s=e.style;return n=n||Ze(e),a=n?n.getPropertyValue(t)||n[t]:void 0,n&&(""!==a||ie.contains(e.ownerDocument,e)||(a=ie.style(e,t)),et.test(a)&&Ke.test(t)&&(i=s.width,r=s.minWidth,o=s.maxWidth,s.minWidth=s.maxWidth=s.width=a,a=n.width,s.width=i,s.minWidth=r,s.maxWidth=o)),void 0===a?a:a+""}):pe.documentElement.currentStyle&&(Ze=function(e){return e.currentStyle},Ye=function(e,t,n){var i,r,o,a,s=e.style;return n=n||Ze(e),null==(a=n?n[t]:void 0)&&s&&s[t]&&(a=s[t]),et.test(a)&&!tt.test(t)&&(i=s.left,r=e.runtimeStyle,(o=r&&r.left)&&(r.left=e.currentStyle.left),s.left="fontSize"===t?"1em":a,a=s.pixelLeft+"px",s.left=i,o&&(r.left=o)),void 0===a?a:a+""||"auto"}),function(){function t(){var t,n,i,r;(n=pe.getElementsByTagName("body")[0])&&n.style&&(t=pe.createElement("div"),i=pe.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(t),t.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;display:block;margin-top:1%;top:1%;border:1px;padding:1px;width:4px;position:absolute",o=a=!1,l=!0,e.getComputedStyle&&(o="1%"!==(e.getComputedStyle(t,null)||{}).top,a="4px"===(e.getComputedStyle(t,null)||{width:"4px"}).width,r=t.appendChild(pe.createElement("div")),r.style.cssText=t.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",r.style.marginRight=r.style.width="0",t.style.width="1px",l=!parseFloat((e.getComputedStyle(r,null)||{}).marginRight)),t.innerHTML="<table><tr><td></td><td>t</td></tr></table>",r=t.getElementsByTagName("td"),r[0].style.cssText="margin:0;border:0;padding:0;display:none",(s=0===r[0].offsetHeight)&&(r[0].style.display="",r[1].style.display="none",s=0===r[0].offsetHeight),n.removeChild(i))}var n,i,r,o,a,s,l;(n=pe.createElement("div")).innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",(i=(r=n.getElementsByTagName("a")[0])&&r.style)&&(i.cssText="float:left;opacity:.5",te.opacity="0.5"===i.opacity,te.cssFloat=!!i.cssFloat,n.style.backgroundClip="content-box",n.cloneNode(!0).style.backgroundClip="",te.clearCloneStyle="content-box"===n.style.backgroundClip,te.boxSizing=""===i.boxSizing||""===i.MozBoxSizing||""===i.WebkitBoxSizing,ie.extend(te,{reliableHiddenOffsets:function(){return null==s&&t(),s},boxSizingReliable:function(){return null==a&&t(),a},pixelPosition:function(){return null==o&&t(),o},reliableMarginRight:function(){return null==l&&t(),l}}))}(),ie.swap=function(e,t,n,i){var r,o,a={};for(o in t)a[o]=e.style[o],e.style[o]=t[o];r=n.apply(e,i||[]);for(o in t)e.style[o]=a[o];return r};var nt=/alpha\([^)]*\)/i,it=/opacity\s*=\s*([^)]*)/,rt=/^(none|table(?!-c[ea]).+)/,ot=new RegExp("^("+Ce+")(.*)$","i"),at=new RegExp("^([+-])=("+Ce+")","i"),st={position:"absolute",visibility:"hidden",display:"block"},lt={letterSpacing:"0",fontWeight:"400"},ct=["Webkit","O","Moz","ms"];ie.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Ye(e,"opacity");return""===n?"1":n}}}},cssNumber:{columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:te.cssFloat?"cssFloat":"styleFloat"},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var r,o,a,s=ie.camelCase(t),l=e.style;if(t=ie.cssProps[s]||(ie.cssProps[s]=N(l,s)),a=ie.cssHooks[t]||ie.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(r=a.get(e,!1,i))?r:l[t];if("string"===(o=typeof n)&&(r=at.exec(n))&&(n=(r[1]+1)*r[2]+parseFloat(ie.css(e,t)),o="number"),null!=n&&n===n&&("number"!==o||ie.cssNumber[s]||(n+="px"),te.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),!(a&&"set"in a&&void 0===(n=a.set(e,n,i)))))try{l[t]=n}catch(e){}}},css:function(e,t,n,i){var r,o,a,s=ie.camelCase(t);return t=ie.cssProps[s]||(ie.cssProps[s]=N(e.style,s)),(a=ie.cssHooks[t]||ie.cssHooks[s])&&"get"in a&&(o=a.get(e,!0,n)),void 0===o&&(o=Ye(e,t,i)),"normal"===o&&t in lt&&(o=lt[t]),""===n||n?(r=parseFloat(o),!0===n||ie.isNumeric(r)?r||0:o):o}}),ie.each(["height","width"],function(e,t){ie.cssHooks[t]={get:function(e,n,i){return n?rt.test(ie.css(e,"display"))&&0===e.offsetWidth?ie.swap(e,st,function(){return j(e,t,i)}):j(e,t,i):void 0},set:function(e,n,i){var r=i&&Ze(e);return I(0,n,i?P(e,t,i,te.boxSizing&&"border-box"===ie.css(e,"boxSizing",!1,r),r):0)}}}),te.opacity||(ie.cssHooks.opacity={get:function(e,t){return it.test((t&&e.currentStyle?e.currentStyle.filter:e.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":t?"1":""},set:function(e,t){var n=e.style,i=e.currentStyle,r=ie.isNumeric(t)?"alpha(opacity="+100*t+")":"",o=i&&i.filter||n.filter||"";n.zoom=1,(t>=1||""===t)&&""===ie.trim(o.replace(nt,""))&&n.removeAttribute&&(n.removeAttribute("filter"),""===t||i&&!i.filter)||(n.filter=nt.test(o)?o.replace(nt,r):o+" "+r)}}),ie.cssHooks.marginRight=k(te.reliableMarginRight,function(e,t){return t?ie.swap(e,{display:"inline-block"},Ye,[e,"marginRight"]):void 0}),ie.each({margin:"",padding:"",border:"Width"},function(e,t){ie.cssHooks[e+t]={expand:function(n){for(var i=0,r={},o="string"==typeof n?n.split(" "):[n];4>i;i++)r[e+Ae[i]+t]=o[i]||o[i-2]||o[0];return r}},Ke.test(e)||(ie.cssHooks[e+t].set=I)}),ie.fn.extend({css:function(e,t){return Ne(this,function(e,t,n){var i,r,o={},a=0;if(ie.isArray(t)){for(i=Ze(e),r=t.length;r>a;a++)o[t[a]]=ie.css(e,t[a],!1,i);return o}return void 0!==n?ie.style(e,t,n):ie.css(e,t)},e,t,arguments.length>1)},show:function(){return E(this,!0)},hide:function(){return E(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){ke(this)?ie(this).show():ie(this).hide()})}}),ie.Tween=_,_.prototype={constructor:_,init:function(e,t,n,i,r,o){this.elem=e,this.prop=n,this.easing=r||"swing",this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=o||(ie.cssNumber[n]?"":"px")},cur:function(){var e=_.propHooks[this.prop];return e&&e.get?e.get(this):_.propHooks._default.get(this)},run:function(e){var t,n=_.propHooks[this.prop];return this.pos=t=this.options.duration?ie.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):_.propHooks._default.set(this),this}},_.prototype.init.prototype=_.prototype,_.propHooks={_default:{get:function(e){var t;return null==e.elem[e.prop]||e.elem.style&&null!=e.elem.style[e.prop]?(t=ie.css(e.elem,e.prop,""))&&"auto"!==t?t:0:e.elem[e.prop]},set:function(e){ie.fx.step[e.prop]?ie.fx.step[e.prop](e):e.elem.style&&(null!=e.elem.style[ie.cssProps[e.prop]]||ie.cssHooks[e.prop])?ie.style(e.elem,e.prop,e.now+e.unit):e.elem[e.prop]=e.now}}},_.propHooks.scrollTop=_.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},ie.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2}},ie.fx=_.prototype.init,ie.fx.step={};var ut,dt,ft=/^(?:toggle|show|hide)$/,pt=new RegExp("^(?:([+-])=|)("+Ce+")([a-z%]*)$","i"),ht=/queueHooks$/,gt=[function(e,t,n){var i,r,o,a,s,l,c,u=this,d={},f=e.style,p=e.nodeType&&ke(e),h=ie._data(e,"fxshow");n.queue||(null==(s=ie._queueHooks(e,"fx")).unqueued&&(s.unqueued=0,l=s.empty.fire,s.empty.fire=function(){s.unqueued||l()}),s.unqueued++,u.always(function(){u.always(function(){s.unqueued--,ie.queue(e,"fx").length||s.empty.fire()})})),1===e.nodeType&&("height"in t||"width"in t)&&(n.overflow=[f.overflow,f.overflowX,f.overflowY],"inline"===("none"===(c=ie.css(e,"display"))?ie._data(e,"olddisplay")||A(e.nodeName):c)&&"none"===ie.css(e,"float")&&(te.inlineBlockNeedsLayout&&"inline"!==A(e.nodeName)?f.zoom=1:f.display="inline-block")),n.overflow&&(f.overflow="hidden",te.shrinkWrapBlocks()||u.always(function(){f.overflow=n.overflow[0],f.overflowX=n.overflow[1],f.overflowY=n.overflow[2]}));for(i in t)if(r=t[i],ft.exec(r)){if(delete t[i],o=o||"toggle"===r,r===(p?"hide":"show")){if("show"!==r||!h||void 0===h[i])continue;p=!0}d[i]=h&&h[i]||ie.style(e,i)}else c=void 0;if(ie.isEmptyObject(d))"inline"===("none"===c?A(e.nodeName):c)&&(f.display=c);else{h?"hidden"in h&&(p=h.hidden):h=ie._data(e,"fxshow",{}),o&&(h.hidden=!p),p?ie(e).show():u.done(function(){ie(e).hide()}),u.done(function(){var t;ie._removeData(e,"fxshow");for(t in d)ie.style(e,t,d[t])});for(i in d)a=L(p?h[i]:0,i,u),i in h||(h[i]=a.start,p&&(a.end=a.start,a.start="width"===i||"height"===i?1:0))}}],vt={"*":[function(e,t){var n=this.createTween(e,t),i=n.cur(),r=pt.exec(t),o=r&&r[3]||(ie.cssNumber[e]?"":"px"),a=(ie.cssNumber[e]||"px"!==o&&+i)&&pt.exec(ie.css(n.elem,e)),s=1,l=20;if(a&&a[3]!==o){o=o||a[3],r=r||[],a=+i||1;do{s=s||".5",a/=s,ie.style(n.elem,e,a+o)}while(s!==(s=n.cur()/i)&&1!==s&&--l)}return r&&(a=n.start=+a||+i||0,n.unit=o,n.end=r[1]?a+(r[1]+1)*r[2]:+r[2]),n}]};ie.Animation=ie.extend(O,{tweener:function(e,t){ie.isFunction(e)?(t=e,e=["*"]):e=e.split(" ");for(var n,i=0,r=e.length;r>i;i++)n=e[i],vt[n]=vt[n]||[],vt[n].unshift(t)},prefilter:function(e,t){t?gt.unshift(e):gt.push(e)}}),ie.speed=function(e,t,n){var i=e&&"object"==typeof e?ie.extend({},e):{complete:n||!n&&t||ie.isFunction(e)&&e,duration:e,easing:n&&t||t&&!ie.isFunction(t)&&t};return i.duration=ie.fx.off?0:"number"==typeof i.duration?i.duration:i.duration in ie.fx.speeds?ie.fx.speeds[i.duration]:ie.fx.speeds._default,(null==i.queue||!0===i.queue)&&(i.queue="fx"),i.old=i.complete,i.complete=function(){ie.isFunction(i.old)&&i.old.call(this),i.queue&&ie.dequeue(this,i.queue)},i},ie.fn.extend({fadeTo:function(e,t,n,i){return this.filter(ke).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(e,t,n,i){var r=ie.isEmptyObject(e),o=ie.speed(t,n,i),a=function(){var t=O(this,ie.extend({},e),o);(r||ie._data(this,"finish"))&&t.stop(!0)};return a.finish=a,r||!1===o.queue?this.each(a):this.queue(o.queue,a)},stop:function(e,t,n){var i=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&!1!==e&&this.queue(e||"fx",[]),this.each(function(){var t=!0,r=null!=e&&e+"queueHooks",o=ie.timers,a=ie._data(this);if(r)a[r]&&a[r].stop&&i(a[r]);else for(r in a)a[r]&&a[r].stop&&ht.test(r)&&i(a[r]);for(r=o.length;r--;)o[r].elem!==this||null!=e&&o[r].queue!==e||(o[r].anim.stop(n),t=!1,o.splice(r,1));(t||!n)&&ie.dequeue(this,e)})},finish:function(e){return!1!==e&&(e=e||"fx"),this.each(function(){var t,n=ie._data(this),i=n[e+"queue"],r=n[e+"queueHooks"],o=ie.timers,a=i?i.length:0;for(n.finish=!0,ie.queue(this,e,[]),r&&r.stop&&r.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;a>t;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete n.finish})}}),ie.each(["toggle","show","hide"],function(e,t){var n=ie.fn[t];ie.fn[t]=function(e,i,r){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(H(t,!0),e,i,r)}}),ie.each({slideDown:H("show"),slideUp:H("hide"),slideToggle:H("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){ie.fn[e]=function(e,n,i){return this.animate(t,e,n,i)}}),ie.timers=[],ie.fx.tick=function(){var e,t=ie.timers,n=0;for(ut=ie.now();n<t.length;n++)(e=t[n])()||t[n]!==e||t.splice(n--,1);t.length||ie.fx.stop(),ut=void 0},ie.fx.timer=function(e){ie.timers.push(e),e()?ie.fx.start():ie.timers.pop()},ie.fx.interval=13,ie.fx.start=function(){dt||(dt=setInterval(ie.fx.tick,ie.fx.interval))},ie.fx.stop=function(){clearInterval(dt),dt=null},ie.fx.speeds={slow:600,fast:200,_default:400},ie.fn.delay=function(e,t){return e=ie.fx?ie.fx.speeds[e]||e:e,t=t||"fx",this.queue(t,function(t,n){var i=setTimeout(t,e);n.stop=function(){clearTimeout(i)}})},function(){var e,t,n,i,r;(t=pe.createElement("div")).setAttribute("className","t"),t.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",i=t.getElementsByTagName("a")[0],r=(n=pe.createElement("select")).appendChild(pe.createElement("option")),e=t.getElementsByTagName("input")[0],i.style.cssText="top:1px",te.getSetAttribute="t"!==t.className,te.style=/top/.test(i.getAttribute("style")),te.hrefNormalized="/a"===i.getAttribute("href"),te.checkOn=!!e.value,te.optSelected=r.selected,te.enctype=!!pe.createElement("form").enctype,n.disabled=!0,te.optDisabled=!r.disabled,(e=pe.createElement("input")).setAttribute("value",""),te.input=""===e.getAttribute("value"),e.value="t",e.setAttribute("type","radio"),te.radioValue="t"===e.value}();var mt=/\r/g;ie.fn.extend({val:function(e){var t,n,i,r=this[0];return arguments.length?(i=ie.isFunction(e),this.each(function(n){var r;1===this.nodeType&&(null==(r=i?e.call(this,n,ie(this).val()):e)?r="":"number"==typeof r?r+="":ie.isArray(r)&&(r=ie.map(r,function(e){return null==e?"":e+""})),(t=ie.valHooks[this.type]||ie.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,r,"value")||(this.value=r))})):r?(t=ie.valHooks[r.type]||ie.valHooks[r.nodeName.toLowerCase()])&&"get"in t&&void 0!==(n=t.get(r,"value"))?n:"string"==typeof(n=r.value)?n.replace(mt,""):null==n?"":n:void 0}}),ie.extend({valHooks:{option:{get:function(e){var t=ie.find.attr(e,"value");return null!=t?t:ie.trim(ie.text(e))}},select:{get:function(e){for(var t,n,i=e.options,r=e.selectedIndex,o="select-one"===e.type||0>r,a=o?null:[],s=o?r+1:i.length,l=0>r?s:o?r:0;s>l;l++)if(!(!(n=i[l]).selected&&l!==r||(te.optDisabled?n.disabled:null!==n.getAttribute("disabled"))||n.parentNode.disabled&&ie.nodeName(n.parentNode,"optgroup"))){if(t=ie(n).val(),o)return t;a.push(t)}return a},set:function(e,t){for(var n,i,r=e.options,o=ie.makeArray(t),a=r.length;a--;)if(i=r[a],ie.inArray(ie.valHooks.option.get(i),o)>=0)try{i.selected=n=!0}catch(e){i.scrollHeight}else i.selected=!1;return n||(e.selectedIndex=-1),r}}}}),ie.each(["radio","checkbox"],function(){ie.valHooks[this]={set:function(e,t){return ie.isArray(t)?e.checked=ie.inArray(ie(e).val(),t)>=0:void 0}},te.checkOn||(ie.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var yt,xt,bt=ie.expr.attrHandle,wt=/^(?:checked|selected)$/i,Tt=te.getSetAttribute,St=te.input;ie.fn.extend({attr:function(e,t){return Ne(this,ie.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){ie.removeAttr(this,e)})}}),ie.extend({attr:function(e,t,n){var i,r,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return typeof e.getAttribute===we?ie.prop(e,t,n):(1===o&&ie.isXMLDoc(e)||(t=t.toLowerCase(),i=ie.attrHooks[t]||(ie.expr.match.bool.test(t)?xt:yt)),void 0===n?i&&"get"in i&&null!==(r=i.get(e,t))?r:null==(r=ie.find.attr(e,t))?void 0:r:null!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):void ie.removeAttr(e,t))},removeAttr:function(e,t){var n,i,r=0,o=t&&t.match(me);if(o&&1===e.nodeType)for(;n=o[r++];)i=ie.propFix[n]||n,ie.expr.match.bool.test(n)?St&&Tt||!wt.test(n)?e[i]=!1:e[ie.camelCase("default-"+n)]=e[i]=!1:ie.attr(e,n,""),e.removeAttribute(Tt?n:i)},attrHooks:{type:{set:function(e,t){if(!te.radioValue&&"radio"===t&&ie.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}}}),xt={set:function(e,t,n){return!1===t?ie.removeAttr(e,n):St&&Tt||!wt.test(n)?e.setAttribute(!Tt&&ie.propFix[n]||n,n):e[ie.camelCase("default-"+n)]=e[n]=!0,n}},ie.each(ie.expr.match.bool.source.match(/\w+/g),function(e,t){var n=bt[t]||ie.find.attr;bt[t]=St&&Tt||!wt.test(t)?function(e,t,i){var r,o;return i||(o=bt[t],bt[t]=r,r=null!=n(e,t,i)?t.toLowerCase():null,bt[t]=o),r}:function(e,t,n){return n?void 0:e[ie.camelCase("default-"+t)]?t.toLowerCase():null}}),St&&Tt||(ie.attrHooks.value={set:function(e,t,n){return ie.nodeName(e,"input")?void(e.defaultValue=t):yt&&yt.set(e,t,n)}}),Tt||(yt={set:function(e,t,n){var i=e.getAttributeNode(n);return i||e.setAttributeNode(i=e.ownerDocument.createAttribute(n)),i.value=t+="","value"===n||t===e.getAttribute(n)?t:void 0}},bt.id=bt.name=bt.coords=function(e,t,n){var i;return n?void 0:(i=e.getAttributeNode(t))&&""!==i.value?i.value:null},ie.valHooks.button={get:function(e,t){var n=e.getAttributeNode(t);return n&&n.specified?n.value:void 0},set:yt.set},ie.attrHooks.contenteditable={set:function(e,t,n){yt.set(e,""!==t&&t,n)}},ie.each(["width","height"],function(e,t){ie.attrHooks[t]={set:function(e,n){return""===n?(e.setAttribute(t,"auto"),n):void 0}}})),te.style||(ie.attrHooks.style={get:function(e){return e.style.cssText||void 0},set:function(e,t){return e.style.cssText=t+""}});var Ct=/^(?:input|select|textarea|button|object)$/i,At=/^(?:a|area)$/i;ie.fn.extend({prop:function(e,t){return Ne(this,ie.prop,e,t,arguments.length>1)},removeProp:function(e){return e=ie.propFix[e]||e,this.each(function(){try{this[e]=void 0,delete this[e]}catch(e){}})}}),ie.extend({propFix:{for:"htmlFor",class:"className"},prop:function(e,t,n){var i,r,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return(1!==o||!ie.isXMLDoc(e))&&(t=ie.propFix[t]||t,r=ie.propHooks[t]),void 0!==n?r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:e[t]=n:r&&"get"in r&&null!==(i=r.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=ie.find.attr(e,"tabindex");return t?parseInt(t,10):Ct.test(e.nodeName)||At.test(e.nodeName)&&e.href?0:-1}}}}),te.hrefNormalized||ie.each(["href","src"],function(e,t){ie.propHooks[t]={get:function(e){return e.getAttribute(t,4)}}}),te.optSelected||(ie.propHooks.selected={get:function(e){var t=e.parentNode;return t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex),null}}),ie.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){ie.propFix[this.toLowerCase()]=this}),te.enctype||(ie.propFix.enctype="encoding");var kt=/[\t\r\n\f]/g;ie.fn.extend({addClass:function(e){var t,n,i,r,o,a,s=0,l=this.length,c="string"==typeof e&&e;if(ie.isFunction(e))return this.each(function(t){ie(this).addClass(e.call(this,t,this.className))});if(c)for(t=(e||"").match(me)||[];l>s;s++)if(n=this[s],i=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(kt," "):" ")){for(o=0;r=t[o++];)i.indexOf(" "+r+" ")<0&&(i+=r+" ");a=ie.trim(i),n.className!==a&&(n.className=a)}return this},removeClass:function(e){var t,n,i,r,o,a,s=0,l=this.length,c=0===arguments.length||"string"==typeof e&&e;if(ie.isFunction(e))return this.each(function(t){ie(this).removeClass(e.call(this,t,this.className))});if(c)for(t=(e||"").match(me)||[];l>s;s++)if(n=this[s],i=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(kt," "):"")){for(o=0;r=t[o++];)for(;i.indexOf(" "+r+" ")>=0;)i=i.replace(" "+r+" "," ");a=e?ie.trim(i):"",n.className!==a&&(n.className=a)}return this},toggleClass:function(e,t){var n=typeof e;return"boolean"==typeof t&&"string"===n?t?this.addClass(e):this.removeClass(e):this.each(ie.isFunction(e)?function(n){ie(this).toggleClass(e.call(this,n,this.className,t),t)}:function(){if("string"===n)for(var t,i=0,r=ie(this),o=e.match(me)||[];t=o[i++];)r.hasClass(t)?r.removeClass(t):r.addClass(t);else(n===we||"boolean"===n)&&(this.className&&ie._data(this,"__className__",this.className),this.className=this.className||!1===e?"":ie._data(this,"__className__")||"")})},hasClass:function(e){for(var t=" "+e+" ",n=0,i=this.length;i>n;n++)if(1===this[n].nodeType&&(" "+this[n].className+" ").replace(kt," ").indexOf(t)>=0)return!0;return!1}}),ie.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,t){ie.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}),ie.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)},bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}});var Nt=ie.now(),Et=/\?/,It=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;ie.parseJSON=function(t){if(e.JSON&&e.JSON.parse)return e.JSON.parse(t+"");var n,i=null,r=ie.trim(t+"");return r&&!ie.trim(r.replace(It,function(e,t,r,o){return n&&t&&(i=0),0===i?e:(n=r||t,i+=!o-!r,"")}))?Function("return "+r)():ie.error("Invalid JSON: "+t)},ie.parseXML=function(t){var n,i;if(!t||"string"!=typeof t)return null;try{e.DOMParser?(i=new DOMParser,n=i.parseFromString(t,"text/xml")):(n=new ActiveXObject("Microsoft.XMLDOM"),n.async="false",n.loadXML(t))}catch(e){n=void 0}return n&&n.documentElement&&!n.getElementsByTagName("parsererror").length||ie.error("Invalid XML: "+t),n};var Pt,jt,_t=/#.*$/,Dt=/([?&])_=[^&]*/,Ht=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,Lt=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,zt=/^(?:GET|HEAD)$/,Ot=/^\/\//,Ft=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Mt={},qt={},Rt="*/".concat("*");try{jt=location.href}catch(e){(jt=pe.createElement("a")).href="",jt=jt.href}Pt=Ft.exec(jt.toLowerCase())||[],ie.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:jt,type:"GET",isLocal:Lt.test(Pt[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Rt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":ie.parseJSON,"text xml":ie.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?q(q(e,ie.ajaxSettings),t):q(ie.ajaxSettings,e)},ajaxPrefilter:F(Mt),ajaxTransport:F(qt),ajax:function(e,t){function n(e,t,n,i){var r,u,m,y,b,T=t;2!==x&&(x=2,s&&clearTimeout(s),c=void 0,a=i||"",w.readyState=e>0?4:0,r=e>=200&&300>e||304===e,n&&(y=R(d,w,n)),y=$(d,y,w,r),r?(d.ifModified&&((b=w.getResponseHeader("Last-Modified"))&&(ie.lastModified[o]=b),(b=w.getResponseHeader("etag"))&&(ie.etag[o]=b)),204===e||"HEAD"===d.type?T="nocontent":304===e?T="notmodified":(T=y.state,u=y.data,m=y.error,r=!m)):(m=T,(e||!T)&&(T="error",0>e&&(e=0))),w.status=e,w.statusText=(t||T)+"",r?h.resolveWith(f,[u,T,w]):h.rejectWith(f,[w,T,m]),w.statusCode(v),v=void 0,l&&p.trigger(r?"ajaxSuccess":"ajaxError",[w,d,r?u:m]),g.fireWith(f,[w,T]),l&&(p.trigger("ajaxComplete",[w,d]),--ie.active||ie.event.trigger("ajaxStop")))}"object"==typeof e&&(t=e,e=void 0),t=t||{};var i,r,o,a,s,l,c,u,d=ie.ajaxSetup({},t),f=d.context||d,p=d.context&&(f.nodeType||f.jquery)?ie(f):ie.event,h=ie.Deferred(),g=ie.Callbacks("once memory"),v=d.statusCode||{},m={},y={},x=0,b="canceled",w={readyState:0,getResponseHeader:function(e){var t;if(2===x){if(!u)for(u={};t=Ht.exec(a);)u[t[1].toLowerCase()]=t[2];t=u[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===x?a:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return x||(e=y[n]=y[n]||e,m[e]=t),this},overrideMimeType:function(e){return x||(d.mimeType=e),this},statusCode:function(e){var t;if(e)if(2>x)for(t in e)v[t]=[v[t],e[t]];else w.always(e[w.status]);return this},abort:function(e){var t=e||b;return c&&c.abort(t),n(0,t),this}};if(h.promise(w).complete=g.add,w.success=w.done,w.error=w.fail,d.url=((e||d.url||jt)+"").replace(_t,"").replace(Ot,Pt[1]+"//"),d.type=t.method||t.type||d.method||d.type,d.dataTypes=ie.trim(d.dataType||"*").toLowerCase().match(me)||[""],null==d.crossDomain&&(i=Ft.exec(d.url.toLowerCase()),d.crossDomain=!(!i||i[1]===Pt[1]&&i[2]===Pt[2]&&(i[3]||("http:"===i[1]?"80":"443"))===(Pt[3]||("http:"===Pt[1]?"80":"443")))),d.data&&d.processData&&"string"!=typeof d.data&&(d.data=ie.param(d.data,d.traditional)),M(Mt,d,t,w),2===x)return w;(l=d.global)&&0==ie.active++&&ie.event.trigger("ajaxStart"),d.type=d.type.toUpperCase(),d.hasContent=!zt.test(d.type),o=d.url,d.hasContent||(d.data&&(o=d.url+=(Et.test(o)?"&":"?")+d.data,delete d.data),!1===d.cache&&(d.url=Dt.test(o)?o.replace(Dt,"$1_="+Nt++):o+(Et.test(o)?"&":"?")+"_="+Nt++)),d.ifModified&&(ie.lastModified[o]&&w.setRequestHeader("If-Modified-Since",ie.lastModified[o]),ie.etag[o]&&w.setRequestHeader("If-None-Match",ie.etag[o])),(d.data&&d.hasContent&&!1!==d.contentType||t.contentType)&&w.setRequestHeader("Content-Type",d.contentType),w.setRequestHeader("Accept",d.dataTypes[0]&&d.accepts[d.dataTypes[0]]?d.accepts[d.dataTypes[0]]+("*"!==d.dataTypes[0]?", "+Rt+"; q=0.01":""):d.accepts["*"]);for(r in d.headers)w.setRequestHeader(r,d.headers[r]);if(d.beforeSend&&(!1===d.beforeSend.call(f,w,d)||2===x))return w.abort();b="abort";for(r in{success:1,error:1,complete:1})w[r](d[r]);if(c=M(qt,d,t,w)){w.readyState=1,l&&p.trigger("ajaxSend",[w,d]),d.async&&d.timeout>0&&(s=setTimeout(function(){w.abort("timeout")},d.timeout));try{x=1,c.send(m,n)}catch(e){if(!(2>x))throw e;n(-1,e)}}else n(-1,"No Transport");return w},getJSON:function(e,t,n){return ie.get(e,t,n,"json")},getScript:function(e,t){return ie.get(e,void 0,t,"script")}}),ie.each(["get","post"],function(e,t){ie[t]=function(e,n,i,r){return ie.isFunction(n)&&(r=r||i,i=n,n=void 0),ie.ajax({url:e,type:t,dataType:r,data:n,success:i})}}),ie.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){ie.fn[t]=function(e){return this.on(t,e)}}),ie._evalUrl=function(e){return ie.ajax({url:e,type:"GET",dataType:"script",async:!1,global:!1,throws:!0})},ie.fn.extend({wrapAll:function(e){if(ie.isFunction(e))return this.each(function(t){ie(this).wrapAll(e.call(this,t))});if(this[0]){var t=ie(e,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstChild&&1===e.firstChild.nodeType;)e=e.firstChild;return e}).append(this)}return this},wrapInner:function(e){return this.each(ie.isFunction(e)?function(t){ie(this).wrapInner(e.call(this,t))}:function(){var t=ie(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=ie.isFunction(e);return this.each(function(n){ie(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(){return this.parent().each(function(){ie.nodeName(this,"body")||ie(this).replaceWith(this.childNodes)}).end()}}),ie.expr.filters.hidden=function(e){return e.offsetWidth<=0&&e.offsetHeight<=0||!te.reliableHiddenOffsets()&&"none"===(e.style&&e.style.display||ie.css(e,"display"))},ie.expr.filters.visible=function(e){return!ie.expr.filters.hidden(e)};var $t=/%20/g,Wt=/\[\]$/,Bt=/\r?\n/g,Vt=/^(?:submit|button|image|reset|file)$/i,Qt=/^(?:input|select|textarea|keygen)/i;ie.param=function(e,t){var n,i=[],r=function(e,t){t=ie.isFunction(t)?t():null==t?"":t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(void 0===t&&(t=ie.ajaxSettings&&ie.ajaxSettings.traditional),ie.isArray(e)||e.jquery&&!ie.isPlainObject(e))ie.each(e,function(){r(this.name,this.value)});else for(n in e)W(n,e[n],t,r);return i.join("&").replace($t,"+")},ie.fn.extend({serialize:function(){return ie.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=ie.prop(this,"elements");return e?ie.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!ie(this).is(":disabled")&&Qt.test(this.nodeName)&&!Vt.test(e)&&(this.checked||!Ee.test(e))}).map(function(e,t){var n=ie(this).val();return null==n?null:ie.isArray(n)?ie.map(n,function(e){return{name:t.name,value:e.replace(Bt,"\r\n")}}):{name:t.name,value:n.replace(Bt,"\r\n")}}).get()}}),ie.ajaxSettings.xhr=void 0!==e.ActiveXObject?function(){return!this.isLocal&&/^(get|post|head|put|delete|options)$/i.test(this.type)&&B()||V()}:B;var Xt=0,Ut={},Jt=ie.ajaxSettings.xhr();e.ActiveXObject&&ie(e).on("unload",function(){for(var e in Ut)Ut[e](void 0,!0)}),te.cors=!!Jt&&"withCredentials"in Jt,(Jt=te.ajax=!!Jt)&&ie.ajaxTransport(function(e){if(!e.crossDomain||te.cors){var t;return{send:function(n,i){var r,o=e.xhr(),a=++Xt;if(o.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(r in e.xhrFields)o[r]=e.xhrFields[r];e.mimeType&&o.overrideMimeType&&o.overrideMimeType(e.mimeType),e.crossDomain||n["X-Requested-With"]||(n["X-Requested-With"]="XMLHttpRequest");for(r in n)void 0!==n[r]&&o.setRequestHeader(r,n[r]+"");o.send(e.hasContent&&e.data||null),t=function(n,r){var s,l,c;if(t&&(r||4===o.readyState))if(delete Ut[a],t=void 0,o.onreadystatechange=ie.noop,r)4!==o.readyState&&o.abort();else{c={},s=o.status,"string"==typeof o.responseText&&(c.text=o.responseText);try{l=o.statusText}catch(e){l=""}s||!e.isLocal||e.crossDomain?1223===s&&(s=204):s=c.text?200:404}c&&i(s,l,c,o.getAllResponseHeaders())},e.async?4===o.readyState?setTimeout(t):o.onreadystatechange=Ut[a]=t:t()},abort:function(){t&&t(void 0,!0)}}}}),ie.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(e){return ie.globalEval(e),e}}}),ie.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)}),ie.ajaxTransport("script",function(e){if(e.crossDomain){var t,n=pe.head||ie("head")[0]||pe.documentElement;return{send:function(i,r){(t=pe.createElement("script")).async=!0,e.scriptCharset&&(t.charset=e.scriptCharset),t.src=e.url,t.onload=t.onreadystatechange=function(e,n){(n||!t.readyState||/loaded|complete/.test(t.readyState))&&(t.onload=t.onreadystatechange=null,t.parentNode&&t.parentNode.removeChild(t),t=null,n||r(200,"success"))},n.insertBefore(t,n.firstChild)},abort:function(){t&&t.onload(void 0,!0)}}}});var Gt=[],Zt=/(=)\?(?=&|$)|\?\?/;ie.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Gt.pop()||ie.expando+"_"+Nt++;return this[e]=!0,e}}),ie.ajaxPrefilter("json jsonp",function(t,n,i){var r,o,a,s=!1!==t.jsonp&&(Zt.test(t.url)?"url":"string"==typeof t.data&&!(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&Zt.test(t.data)&&"data");return s||"jsonp"===t.dataTypes[0]?(r=t.jsonpCallback=ie.isFunction(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,s?t[s]=t[s].replace(Zt,"$1"+r):!1!==t.jsonp&&(t.url+=(Et.test(t.url)?"&":"?")+t.jsonp+"="+r),t.converters["script json"]=function(){return a||ie.error(r+" was not called"),a[0]},t.dataTypes[0]="json",o=e[r],e[r]=function(){a=arguments},i.always(function(){e[r]=o,t[r]&&(t.jsonpCallback=n.jsonpCallback,Gt.push(r)),a&&ie.isFunction(o)&&o(a[0]),a=o=void 0}),"script"):void 0}),ie.parseHTML=function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||pe;var i=ue.exec(e),r=!n&&[];return i?[t.createElement(i[1])]:(i=ie.buildFragment([e],t,r),r&&r.length&&ie(r).remove(),ie.merge([],i.childNodes))};var Yt=ie.fn.load;ie.fn.load=function(e,t,n){if("string"!=typeof e&&Yt)return Yt.apply(this,arguments);var i,r,o,a=this,s=e.indexOf(" ");return s>=0&&(i=ie.trim(e.slice(s,e.length)),e=e.slice(0,s)),ie.isFunction(t)?(n=t,t=void 0):t&&"object"==typeof t&&(o="POST"),a.length>0&&ie.ajax({url:e,type:o,dataType:"html",data:t}).done(function(e){r=arguments,a.html(i?ie("<div>").append(ie.parseHTML(e)).find(i):e)}).complete(n&&function(e,t){a.each(n,r||[e.responseText,t,e])}),this},ie.expr.filters.animated=function(e){return ie.grep(ie.timers,function(t){return e===t.elem}).length};var Kt=e.document.documentElement;ie.offset={setOffset:function(e,t,n){var i,r,o,a,s,l,c=ie.css(e,"position"),u=ie(e),d={};"static"===c&&(e.style.position="relative"),s=u.offset(),o=ie.css(e,"top"),l=ie.css(e,"left"),("absolute"===c||"fixed"===c)&&ie.inArray("auto",[o,l])>-1?(i=u.position(),a=i.top,r=i.left):(a=parseFloat(o)||0,r=parseFloat(l)||0),ie.isFunction(t)&&(t=t.call(e,n,s)),null!=t.top&&(d.top=t.top-s.top+a),null!=t.left&&(d.left=t.left-s.left+r),"using"in t?t.using.call(e,d):u.css(d)}},ie.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){ie.offset.setOffset(this,e,t)});var t,n,i={top:0,left:0},r=this[0],o=r&&r.ownerDocument;return o?(t=o.documentElement,ie.contains(t,r)?(typeof r.getBoundingClientRect!==we&&(i=r.getBoundingClientRect()),n=Q(o),{top:i.top+(n.pageYOffset||t.scrollTop)-(t.clientTop||0),left:i.left+(n.pageXOffset||t.scrollLeft)-(t.clientLeft||0)}):i):void 0},position:function(){if(this[0]){var e,t,n={top:0,left:0},i=this[0];return"fixed"===ie.css(i,"position")?t=i.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),ie.nodeName(e[0],"html")||(n=e.offset()),n.top+=ie.css(e[0],"borderTopWidth",!0),n.left+=ie.css(e[0],"borderLeftWidth",!0)),{top:t.top-n.top-ie.css(i,"marginTop",!0),left:t.left-n.left-ie.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent||Kt;e&&!ie.nodeName(e,"html")&&"static"===ie.css(e,"position");)e=e.offsetParent;return e||Kt})}}),ie.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n=/Y/.test(t);ie.fn[e]=function(i){return Ne(this,function(e,i,r){var o=Q(e);return void 0===r?o?t in o?o[t]:o.document.documentElement[i]:e[i]:void(o?o.scrollTo(n?ie(o).scrollLeft():r,n?r:ie(o).scrollTop()):e[i]=r)},e,i,arguments.length,null)}}),ie.each(["top","left"],function(e,t){ie.cssHooks[t]=k(te.pixelPosition,function(e,n){return n?(n=Ye(e,t),et.test(n)?ie(e).position()[t]+"px":n):void 0})}),ie.each({Height:"height",Width:"width"},function(e,t){ie.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,i){ie.fn[i]=function(i,r){var o=arguments.length&&(n||"boolean"!=typeof i),a=n||(!0===i||!0===r?"margin":"border");return Ne(this,function(t,n,i){var r;return ie.isWindow(t)?t.document.documentElement["client"+e]:9===t.nodeType?(r=t.documentElement,Math.max(t.body["scroll"+e],r["scroll"+e],t.body["offset"+e],r["offset"+e],r["client"+e])):void 0===i?ie.css(t,n,a):ie.style(t,n,i,a)},t,o?i:void 0,o,null)}})}),ie.fn.size=function(){return this.length},ie.fn.andSelf=ie.fn.addBack,"function"==typeof define&&define.amd&&define("jquery",[],function(){return ie});var en=e.jQuery,tn=e.$;return ie.noConflict=function(t){return e.$===ie&&(e.$=tn),t&&e.jQuery===ie&&(e.jQuery=en),ie},typeof t===we&&(e.jQuery=e.$=ie),ie}),void 0===jQuery.migrateMute&&(jQuery.migrateMute=!0),function(e,t,n){function i(n){var i=t.console;o[n]||(o[n]=!0,e.migrateWarnings.push(n),i&&i.warn&&!e.migrateMute&&(i.warn("JQMIGRATE: "+n),e.migrateTrace&&i.trace&&i.trace()))}function r(t,r,o,a){if(Object.defineProperty)try{return Object.defineProperty(t,r,{configurable:!0,enumerable:!0,get:function(){return i(a),o},set:function(e){i(a),o=e}}),n}catch(e){}e._definePropertyBroken=!0,t[r]=o}var o={};e.migrateWarnings=[],!e.migrateMute&&t.console&&t.console.log&&t.console.log("JQMIGRATE: Logging is active"),e.migrateTrace===n&&(e.migrateTrace=!0),e.migrateReset=function(){o={},e.migrateWarnings.length=0},"BackCompat"===document.compatMode&&i("jQuery is not compatible with Quirks Mode");var a=e("<input/>",{size:1}).attr("size")&&e.attrFn,s=e.attr,l=e.attrHooks.value&&e.attrHooks.value.get||function(){return null},c=e.attrHooks.value&&e.attrHooks.value.set||function(){return n},u=/^(?:input|button)$/i,d=/^[238]$/,f=/^(?:autofocus|autoplay|async|checked|controls|defer|disabled|hidden|loop|multiple|open|readonly|required|scoped|selected)$/i,p=/^(?:checked|selected)$/i;r(e,"attrFn",a||{},"jQuery.attrFn is deprecated"),e.attr=function(t,r,o,l){var c=r.toLowerCase(),h=t&&t.nodeType;return l&&(4>s.length&&i("jQuery.fn.attr( props, pass ) is deprecated"),t&&!d.test(h)&&(a?r in a:e.isFunction(e.fn[r])))?e(t)[r](o):("type"===r&&o!==n&&u.test(t.nodeName)&&t.parentNode&&i("Can't change the 'type' of an input or button in IE 6/7/8"),!e.attrHooks[c]&&f.test(c)&&(e.attrHooks[c]={get:function(t,i){var r,o=e.prop(t,i);return!0===o||"boolean"!=typeof o&&(r=t.getAttributeNode(i))&&!1!==r.nodeValue?i.toLowerCase():n},set:function(t,n,i){var r;return!1===n?e.removeAttr(t,i):((r=e.propFix[i]||i)in t&&(t[r]=!0),t.setAttribute(i,i.toLowerCase())),i}},p.test(c)&&i("jQuery.fn.attr('"+c+"') may use property instead of attribute")),s.call(e,t,r,o))},e.attrHooks.value={get:function(e,t){var n=(e.nodeName||"").toLowerCase();return"button"===n?l.apply(this,arguments):("input"!==n&&"option"!==n&&i("jQuery.fn.attr('value') no longer gets properties"),t in e?e.value:null)},set:function(e,t){var r=(e.nodeName||"").toLowerCase();return"button"===r?c.apply(this,arguments):("input"!==r&&"option"!==r&&i("jQuery.fn.attr('value', val) no longer sets properties"),e.value=t,n)}};var h,g,v=e.fn.init,m=e.parseJSON,y=/^([^<]*)(<[\w\W]+>)([^>]*)$/;e.fn.init=function(t,n,r){var o;return t&&"string"==typeof t&&!e.isPlainObject(n)&&(o=y.exec(e.trim(t)))&&o[0]&&("<"!==t.charAt(0)&&i("$(html) HTML strings must start with '<' character"),o[3]&&i("$(html) HTML text after last tag is ignored"),"#"===o[0].charAt(0)&&(i("HTML string cannot start with a '#' character"),e.error("JQMIGRATE: Invalid selector string (XSS)")),n&&n.context&&(n=n.context),e.parseHTML)?v.call(this,e.parseHTML(o[2],n,!0),n,r):v.apply(this,arguments)},e.fn.init.prototype=e.fn,e.parseJSON=function(e){return e||null===e?m.apply(this,arguments):(i("jQuery.parseJSON requires a valid JSON string"),null)},e.uaMatch=function(e){e=e.toLowerCase();var t=/(chrome)[ \/]([\w.]+)/.exec(e)||/(webkit)[ \/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||0>e.indexOf("compatible")&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(e)||[];return{browser:t[1]||"",version:t[2]||"0"}},e.browser||(h=e.uaMatch(navigator.userAgent),g={},h.browser&&(g[h.browser]=!0,g.version=h.version),g.chrome?g.webkit=!0:g.webkit&&(g.safari=!0),e.browser=g),r(e,"browser",e.browser,"jQuery.browser is deprecated"),e.sub=function(){function t(e,n){return new t.fn.init(e,n)}e.extend(!0,t,this),t.superclass=this,t.fn=t.prototype=this(),t.fn.constructor=t,t.sub=this.sub,t.fn.init=function(i,r){return r&&r instanceof e&&!(r instanceof t)&&(r=t(r)),e.fn.init.call(this,i,r,n)},t.fn.init.prototype=t.fn;var n=t(document);return i("jQuery.sub() is deprecated"),t},e.ajaxSetup({converters:{"text json":e.parseJSON}});var x=e.fn.data;e.fn.data=function(t){var r,o,a=this[0];return!a||"events"!==t||1!==arguments.length||(r=e.data(a,t),o=e._data(a,t),r!==n&&r!==o||o===n)?x.apply(this,arguments):(i("Use of jQuery.fn.data('events') is deprecated"),o)};var b=/\/(java|ecma)script/i,w=e.fn.andSelf||e.fn.addBack;e.fn.andSelf=function(){return i("jQuery.fn.andSelf() replaced by jQuery.fn.addBack()"),w.apply(this,arguments)},e.clean||(e.clean=function(t,r,o,a){r=(r=!(r=r||document).nodeType&&r[0]||r).ownerDocument||r,i("jQuery.clean() is deprecated");var s,l,c,u,d=[];if(e.merge(d,e.buildFragment(t,r).childNodes),o)for(c=function(e){return!e.type||b.test(e.type)?a?a.push(e.parentNode?e.parentNode.removeChild(e):e):o.appendChild(e):n},s=0;null!=(l=d[s]);s++)e.nodeName(l,"script")&&c(l)||(o.appendChild(l),l.getElementsByTagName!==n&&(u=e.grep(e.merge([],l.getElementsByTagName("script")),c),d.splice.apply(d,[s+1,0].concat(u)),s+=u.length));return d});var T=e.event.add,S=e.event.remove,C=e.event.trigger,A=e.fn.toggle,k=e.fn.live,N=e.fn.die,E="ajaxStart|ajaxStop|ajaxSend|ajaxComplete|ajaxError|ajaxSuccess",I=RegExp("\\b(?:"+E+")\\b"),P=/(?:^|\s)hover(\.\S+|)\b/,j=function(t){return"string"!=typeof t||e.event.special.hover?t:(P.test(t)&&i("'hover' pseudo-event is deprecated, use 'mouseenter mouseleave'"),t&&t.replace(P,"mouseenter$1 mouseleave$1"))};e.event.props&&"attrChange"!==e.event.props[0]&&e.event.props.unshift("attrChange","attrName","relatedNode","srcElement"),e.event.dispatch&&r(e.event,"handle",e.event.dispatch,"jQuery.event.handle is undocumented and deprecated"),e.event.add=function(e,t,n,r,o){e!==document&&I.test(t)&&i("AJAX events should be attached to document: "+t),T.call(this,e,j(t||""),n,r,o)},e.event.remove=function(e,t,n,i,r){S.call(this,e,j(t)||"",n,i,r)},e.fn.error=function(){var e=Array.prototype.slice.call(arguments,0);return i("jQuery.fn.error() is deprecated"),e.splice(0,0,"error"),arguments.length?this.bind.apply(this,e):(this.triggerHandler.apply(this,e),this)},e.fn.toggle=function(t,n){if(!e.isFunction(t)||!e.isFunction(n))return A.apply(this,arguments);i("jQuery.fn.toggle(handler, handler...) is deprecated");var r=arguments,o=t.guid||e.guid++,a=0,s=function(n){var i=(e._data(this,"lastToggle"+t.guid)||0)%a;return e._data(this,"lastToggle"+t.guid,i+1),n.preventDefault(),r[i].apply(this,arguments)||!1};for(s.guid=o;r.length>a;)r[a++].guid=o;return this.click(s)},e.fn.live=function(t,n,r){return i("jQuery.fn.live() is deprecated"),k?k.apply(this,arguments):(e(this.context).on(t,this.selector,n,r),this)},e.fn.die=function(t,n){return i("jQuery.fn.die() is deprecated"),N?N.apply(this,arguments):(e(this.context).off(t,this.selector||"**",n),this)},e.event.trigger=function(e,t,n,r){return n||I.test(e)||i("Global events are undocumented and deprecated"),C.call(this,e,t,n||document,r)},e.each(E.split("|"),function(t,n){e.event.special[n]={setup:function(){var t=this;return t!==document&&(e.event.add(document,n+"."+e.guid,function(){e.event.trigger(n,null,t,!0)}),e._data(this,n,e.guid++)),!1},teardown:function(){return this!==document&&e.event.remove(document,n+"."+e._data(this,n)),!1}}})}(jQuery,window),function(e,t,n){function i(e,t){return typeof e===t}function r(){return"function"!=typeof t.createElement?t.createElement(arguments[0]):S?t.createElementNS.call(t,"http://www.w3.org/2000/svg",arguments[0]):t.createElement.apply(t,arguments)}function o(e){return e.replace(/([a-z])-([a-z])/g,function(e,t,n){return t+n.toUpperCase()}).replace(/^-/,"")}function a(e,t){return!!~(""+e).indexOf(t)}function s(){var e=t.body;return e||(e=r(S?"svg":"body"),e.fake=!0),e}function l(e,n,i,o){var a,l,c,u,d="modernizr",f=r("div"),p=s();if(parseInt(i,10))for(;i--;)c=r("div"),c.id=o?o[i]:d+(i+1),f.appendChild(c);return a=r("style"),a.type="text/css",a.id="s"+d,(p.fake?p:f).appendChild(a),p.appendChild(f),a.styleSheet?a.styleSheet.cssText=e:a.appendChild(t.createTextNode(e)),f.id=d,p.fake&&(p.style.background="",p.style.overflow="hidden",u=T.style.overflow,T.style.overflow="hidden",T.appendChild(p)),l=n(f,e),p.fake?(p.parentNode.removeChild(p),T.style.overflow=u,T.offsetHeight):f.parentNode.removeChild(f),!!l}function c(e,t){return function(){return e.apply(t,arguments)}}function u(e,t,n){var r;for(var o in e)if(e[o]in t)return!1===n?e[o]:(r=t[e[o]],i(r,"function")?c(r,n||t):r);return!1}function d(e){return e.replace(/([A-Z])/g,function(e,t){return"-"+t.toLowerCase()}).replace(/^ms-/,"-ms-")}function f(t,i){var r=t.length;if("CSS"in e&&"supports"in e.CSS){for(;r--;)if(e.CSS.supports(d(t[r]),i))return!0;return!1}if("CSSSupportsRule"in e){for(var o=[];r--;)o.push("("+d(t[r])+":"+i+")");return o=o.join(" or "),l("@supports ("+o+") { #modernizr { position: absolute; } }",function(e){return"absolute"==getComputedStyle(e,null).position})}return n}function p(e,t,s,l){function c(){d&&(delete M.style,delete M.modElem)}if(l=!i(l,"undefined")&&l,!i(s,"undefined")){var u=f(e,s);if(!i(u,"undefined"))return u}for(var d,p,h,g,v,m=["modernizr","tspan","samp"];!M.style&&m.length;)d=!0,M.modElem=r(m.shift()),M.style=M.modElem.style;for(h=e.length,p=0;h>p;p++)if(g=e[p],v=M.style[g],a(g,"-")&&(g=o(g)),M.style[g]!==n){if(l||i(s,"undefined"))return c(),"pfx"!=t||g;try{M.style[g]=s}catch(e){}if(M.style[g]!=v)return c(),"pfx"!=t||g}return c(),!1}function h(e,t,n,r,o){var a=e.charAt(0).toUpperCase()+e.slice(1),s=(e+" "+L.join(a+" ")+a).split(" ");return i(t,"string")||i(t,"undefined")?p(s,t,r,o):(s=(e+" "+A.join(a+" ")+a).split(" "),u(s,t,n))}function g(e,t,i){return h(e,n,n,t,i)}var v=[],m=[],y={_version:"3.3.1",_config:{classPrefix:"",enableClasses:!0,enableJSClass:!0,usePrefixes:!0},_q:[],on:function(e,t){var n=this;setTimeout(function(){t(n[e])},0)},addTest:function(e,t,n){m.push({name:e,fn:t,options:n})},addAsyncTest:function(e){m.push({name:null,fn:e})}},x=function(){};x.prototype=y,(x=new x).addTest("applicationcache","applicationCache"in e),x.addTest("geolocation","geolocation"in navigator),x.addTest("history",function(){var t=navigator.userAgent;return(-1===t.indexOf("Android 2.")&&-1===t.indexOf("Android 4.0")||-1===t.indexOf("Mobile Safari")||-1!==t.indexOf("Chrome")||-1!==t.indexOf("Windows Phone"))&&(e.history&&"pushState"in e.history)}),x.addTest("postmessage","postMessage"in e),x.addTest("svg",!!t.createElementNS&&!!t.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect);var b=!1;try{b="WebSocket"in e&&2===e.WebSocket.CLOSING}catch(e){}x.addTest("websockets",b),x.addTest("localstorage",function(){var e="modernizr";try{return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch(e){return!1}}),x.addTest("sessionstorage",function(){var e="modernizr";try{return sessionStorage.setItem(e,e),sessionStorage.removeItem(e),!0}catch(e){return!1}}),x.addTest("websqldatabase","openDatabase"in e),x.addTest("webworkers","Worker"in e);var w=y._config.usePrefixes?" -webkit- -moz- -o- -ms- ".split(" "):["",""];y._prefixes=w;var T=t.documentElement,S="svg"===T.nodeName.toLowerCase(),C="Moz O ms Webkit",A=y._config.usePrefixes?C.toLowerCase().split(" "):[];y._domPrefixes=A;var k=function(){var e=!("onblur"in t.documentElement);return function(t,i){var o;return!!t&&(i&&"string"!=typeof i||(i=r(i||"div")),t="on"+t,!(o=t in i)&&e&&(i.setAttribute||(i=r("div")),i.setAttribute(t,""),o="function"==typeof i[t],i[t]!==n&&(i[t]=n),i.removeAttribute(t)),o)}}();y.hasEvent=k,x.addTest("hashchange",function(){return!1!==k("hashchange",e)&&(t.documentMode===n||t.documentMode>7)}),x.addTest("pointerevents",function(){var e=!1,t=A.length;for(e=x.hasEvent("pointerdown");t--&&!e;)k(A[t]+"pointerdown")&&(e=!0);return e}),x.addTest("audio",function(){var e=r("audio"),t=!1;try{(t=!!e.canPlayType)&&(t=new Boolean(t),t.ogg=e.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),t.mp3=e.canPlayType('audio/mpeg; codecs="mp3"').replace(/^no$/,""),t.opus=e.canPlayType('audio/ogg; codecs="opus"')||e.canPlayType('audio/webm; codecs="opus"').replace(/^no$/,""),t.wav=e.canPlayType('audio/wav; codecs="1"').replace(/^no$/,""),t.m4a=(e.canPlayType("audio/x-m4a;")||e.canPlayType("audio/aac;")).replace(/^no$/,""))}catch(e){}return t}),x.addTest("canvas",function(){var e=r("canvas");return!(!e.getContext||!e.getContext("2d"))}),x.addTest("canvastext",function(){return!1!==x.canvas&&"function"==typeof r("canvas").getContext("2d").fillText}),x.addTest("video",function(){var e=r("video"),t=!1;try{(t=!!e.canPlayType)&&(t=new Boolean(t),t.ogg=e.canPlayType('video/ogg; codecs="theora"').replace(/^no$/,""),t.h264=e.canPlayType('video/mp4; codecs="avc1.42E01E"').replace(/^no$/,""),t.webm=e.canPlayType('video/webm; codecs="vp8, vorbis"').replace(/^no$/,""),t.vp9=e.canPlayType('video/webm; codecs="vp9"').replace(/^no$/,""),t.hls=e.canPlayType('application/x-mpegURL; codecs="avc1.42E01E"').replace(/^no$/,""))}catch(e){}return t}),x.addTest("webgl",function(){var t=r("canvas"),n="probablySupportsContext"in t?"probablySupportsContext":"supportsContext";return n in t?t[n]("webgl")||t[n]("experimental-webgl"):"WebGLRenderingContext"in e}),x.addTest("cssgradients",function(){for(var e,t="background-image:",n="",i=0,o=w.length-1;o>i;i++)e=0===i?"to ":"",n+=t+w[i]+"linear-gradient("+e+"left top, #9f9, white);";x._config.usePrefixes&&(n+=t+"-webkit-gradient(linear,left top,right bottom,from(#9f9),to(white));");var a=r("a").style;return a.cssText=n,(""+a.backgroundImage).indexOf("gradient")>-1}),x.addTest("multiplebgs",function(){var e=r("a").style;return e.cssText="background:url(https://),url(https://),red url(https://)",/(url\s*\(.*?){3}/.test(e.background)}),x.addTest("opacity",function(){var e=r("a").style;return e.cssText=w.join("opacity:.55;"),/^0.55$/.test(e.opacity)}),x.addTest("rgba",function(){var e=r("a").style;return e.cssText="background-color:rgba(150,255,150,.5)",(""+e.backgroundColor).indexOf("rgba")>-1}),x.addTest("inlinesvg",function(){var e=r("div");return e.innerHTML="<svg/>","http://www.w3.org/2000/svg"==("undefined"!=typeof SVGRect&&e.firstChild&&e.firstChild.namespaceURI)});var N=r("input"),E="autocomplete autofocus list placeholder max min multiple pattern required step".split(" "),I={};x.input=function(t){for(var n=0,i=t.length;i>n;n++)I[t[n]]=!!(t[n]in N);return I.list&&(I.list=!(!r("datalist")||!e.HTMLDataListElement)),I}(E);var P="search tel url email datetime date month week time datetime-local number range color".split(" "),j={};x.inputtypes=function(e){for(var i,r,o,a=e.length,s=0;a>s;s++)N.setAttribute("type",i=e[s]),(o="text"!==N.type&&"style"in N)&&(N.value="1)",N.style.cssText="position:absolute;visibility:hidden;",/^range$/.test(i)&&N.style.WebkitAppearance!==n?(T.appendChild(N),r=t.defaultView,o=r.getComputedStyle&&"textfield"!==r.getComputedStyle(N,null).WebkitAppearance&&0!==N.offsetHeight,T.removeChild(N)):/^(search|tel)$/.test(i)||(o=/^(url|email)$/.test(i)?N.checkValidity&&!1===N.checkValidity():"1)"!=N.value)),j[e[s]]=!!o;return j}(P),x.addTest("hsla",function(){var e=r("a").style;return e.cssText="background-color:hsla(120,40%,100%,.5)",a(e.backgroundColor,"rgba")||a(e.backgroundColor,"hsla")});var _="CSS"in e&&"supports"in e.CSS,D="supportsCSS"in e;x.addTest("supports",_||D);var H={}.toString;x.addTest("svgclippaths",function(){return!!t.createElementNS&&/SVGClipPath/.test(H.call(t.createElementNS("http://www.w3.org/2000/svg","clipPath")))}),x.addTest("smil",function(){return!!t.createElementNS&&/SVGAnimate/.test(H.call(t.createElementNS("http://www.w3.org/2000/svg","animate")))});var L=y._config.usePrefixes?C.split(" "):[];y._cssomPrefixes=L;var z=function(t){var i,r=w.length,o=e.CSSRule;if(void 0===o)return n;if(!t)return!1;if(t=t.replace(/^@/,""),(i=t.replace(/-/g,"_").toUpperCase()+"_RULE")in o)return"@"+t;for(var a=0;r>a;a++){var s=w[a];if(s.toUpperCase()+"_"+i in o)return"@-"+s.toLowerCase()+"-"+t}return!1};y.atRule=z;var O=y.testStyles=l;x.addTest("touchevents",function(){var n;if("ontouchstart"in e||e.DocumentTouch&&t instanceof DocumentTouch)n=!0;else{var i=["@media (",w.join("touch-enabled),("),"heartz",")","{#modernizr{top:9px;position:absolute}}"].join("");O(i,function(e){n=9===e.offsetTop})}return n}),function(){var e=navigator.userAgent,t=e.match(/applewebkit\/([0-9]+)/gi)&&parseFloat(RegExp.$1),n=e.match(/w(eb)?osbrowser/gi),i=e.match(/windows phone/gi)&&e.match(/iemobile\/([0-9])+/gi)&&parseFloat(RegExp.$1)>=9,r=533>t&&e.match(/android/gi);return n||r||i}()?x.addTest("fontface",!1):O('@font-face {font-family:"font";src:url("https://")}',function(e,n){var i=t.getElementById("smodernizr"),r=i.sheet||i.styleSheet,o=r?r.cssRules&&r.cssRules[0]?r.cssRules[0].cssText:r.cssText||"":"",a=/src/i.test(o)&&0===o.indexOf(n.split(" ")[0]);x.addTest("fontface",a)}),O('#modernizr{font:0/0 a}#modernizr:after{content:":)";visibility:hidden;font:7px/1 a}',function(e){x.addTest("generatedcontent",e.offsetHeight>=7)});var F={elem:r("modernizr")};x._q.push(function(){delete F.elem});var M={style:F.elem.style};x._q.unshift(function(){delete M.style});var q=y.testProp=function(e,t,i){return p([e],n,t,i)};x.addTest("textshadow",q("textShadow","1px 1px")),y.testAllProps=h;var R,$=y.prefixed=function(e,t,n){return 0===e.indexOf("@")?z(e):(-1!=e.indexOf("-")&&(e=o(e)),t?h(e,t,n):h(e,"pfx"))};try{R=$("indexedDB",e)}catch(e){}x.addTest("indexeddb",!!R),R&&x.addTest("indexeddb.deletedatabase","deleteDatabase"in R),y.testAllProps=g,x.addTest("cssanimations",g("animationName","a",!0)),x.addTest("backgroundsize",g("backgroundSize","100%",!0)),x.addTest("borderimage",g("borderImage","url() 1",!0)),x.addTest("borderradius",g("borderRadius","0px",!0)),x.addTest("boxshadow",g("boxShadow","1px 1px",!0)),function(){x.addTest("csscolumns",function(){var e=!1,t=g("columnCount");try{(e=!!t)&&(e=new Boolean(e))}catch(e){}return e});for(var e,t,n=["Width","Span","Fill","Gap","Rule","RuleColor","RuleStyle","RuleWidth","BreakBefore","BreakAfter","BreakInside"],i=0;i<n.length;i++)e=n[i].toLowerCase(),t=g("column"+n[i]),("breakbefore"===e||"breakafter"===e||"breakinside"==e)&&(t=t||g(n[i])),x.addTest("csscolumns."+e,t)}(),x.addTest("flexbox",g("flexBasis","1px",!0)),x.addTest("flexboxlegacy",g("boxDirection","reverse",!0)),x.addTest("cssreflections",g("boxReflect","above",!0)),x.addTest("csstransforms",function(){return-1===navigator.userAgent.indexOf("Android 2.")&&g("transform","scale(1)",!0)}),x.addTest("csstransforms3d",function(){var e=!!g("perspective","1px",!0),t=x._config.usePrefixes;if(e&&(!t||"webkitPerspective"in T.style)){var n;x.supports?n="@supports (perspective: 1px)":(n="@media (transform-3d)",t&&(n+=",(-webkit-transform-3d)")),O("#modernizr{width:0;height:0}"+(n+="{#modernizr{width:7px;height:18px;margin:0;padding:0;border:0}}"),function(t){e=7===t.offsetWidth&&18===t.offsetHeight})}return e}),x.addTest("csstransitions",g("transition","all",!0)),function(){var e,t,n,r,o,a,s;for(var l in m)if(m.hasOwnProperty(l)){if(e=[],(t=m[l]).name&&(e.push(t.name.toLowerCase()),t.options&&t.options.aliases&&t.options.aliases.length))for(n=0;n<t.options.aliases.length;n++)e.push(t.options.aliases[n].toLowerCase());for(r=i(t.fn,"function")?t.fn():t.fn,o=0;o<e.length;o++)a=e[o],1===(s=a.split(".")).length?x[s[0]]=r:(!x[s[0]]||x[s[0]]instanceof Boolean||(x[s[0]]=new Boolean(x[s[0]])),x[s[0]][s[1]]=r),v.push((r?"":"no-")+s.join("-"))}}(),function(e){var t=T.className,n=x._config.classPrefix||"";if(S&&(t=t.baseVal),x._config.enableJSClass){var i=new RegExp("(^|\\s)"+n+"no-js(\\s|$)");t=t.replace(i,"$1"+n+"js$2")}x._config.enableClasses&&(t+=" "+n+e.join(" "+n),S?T.className.baseVal=t:T.className=t)}(v),delete y.addTest,delete y.addAsyncTest;for(var W=0;W<x._q.length;W++)x._q[W]();e.Modernizr=x}(window,document),function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t:e.conditionizr=t()}(this,function(){"use strict";function e(e,n,i){for(var r=n.length;r--;)!function(n){var r,o=i?e:t+e+("style"===n?".css":".js");switch(n){case"script":(r=document.createElement("script")).src=o;break;case"style":(r=document.createElement("link")).href=o,r.rel="stylesheet";break;case"class":document.documentElement.className+=" "+e}!!r&&(document.head||document.getElementsByTagName("head")[0]).appendChild(r)}(n[r])}var t,n={};return n.config=function(i){t=i.assets||"";for(var r in i.tests)n[r]&&e(r,i.tests[r])},n.add=function(e,t){n[e]="function"==typeof t?t():t},n.on=function(e,t){(n[e]||/\!/.test(e)&&!n[e.slice(1)])&&t()},n.load=n.polyfill=function(t,i){for(var r=i.length;r--;)n[i[r]]&&e(t,[/\.js$/.test(t)?"script":"style"],!0)},n}),function(e){"use strict";function t(e){return(e||"").toLowerCase()}e.fn.cycle=function(n){var i;return 0!==this.length||e.isReady?this.each(function(){var i,r,o,a,s=e(this),l=e.fn.cycle.log;if(!s.data("cycle.opts")){(!1===s.data("cycle-log")||n&&!1===n.log||r&&!1===r.log)&&(l=e.noop),l("--c2 init--"),i=s.data();for(var c in i)i.hasOwnProperty(c)&&/^cycle[A-Z]+/.test(c)&&(a=i[c],o=c.match(/^cycle(.*)/)[1].replace(/^[A-Z]/,t),l(o+":",a,"("+typeof a+")"),i[o]=a);(r=e.extend({},e.fn.cycle.defaults,i,n||{})).timeoutId=0,r.paused=r.paused||!1,r.container=s,r._maxZ=r.maxZ,r.API=e.extend({_container:s},e.fn.cycle.API),r.API.log=l,r.API.trigger=function(e,t){return r.container.trigger(e,t),r.API},s.data("cycle.opts",r),s.data("cycle.API",r.API),r.API.trigger("cycle-bootstrap",[r,r.API]),r.API.addInitialSlides(),r.API.preInitSlideshow(),r.slides.length&&r.API.initSlideshow()}}):(i={s:this.selector,c:this.context},e.fn.cycle.log("requeuing slideshow (dom not ready)"),e(function(){e(i.s,i.c).cycle(n)}),this)},e.fn.cycle.API={opts:function(){return this._container.data("cycle.opts")},addInitialSlides:function(){var t=this.opts(),n=t.slides;t.slideCount=0,t.slides=e(),n=n.jquery?n:t.container.find(n),t.random&&n.sort(function(){return Math.random()-.5}),t.API.add(n)},preInitSlideshow:function(){var t=this.opts();t.API.trigger("cycle-pre-initialize",[t]);var n=e.fn.cycle.transitions[t.fx];n&&e.isFunction(n.preInit)&&n.preInit(t),t._preInitialized=!0},postInitSlideshow:function(){var t=this.opts();t.API.trigger("cycle-post-initialize",[t]);var n=e.fn.cycle.transitions[t.fx];n&&e.isFunction(n.postInit)&&n.postInit(t)},initSlideshow:function(){var t,n=this.opts(),i=n.container;n.API.calcFirstSlide(),"static"==n.container.css("position")&&n.container.css("position","relative"),e(n.slides[n.currSlide]).css({opacity:1,display:"block",visibility:"visible"}),n.API.stackSlides(n.slides[n.currSlide],n.slides[n.nextSlide],!n.reverse),n.pauseOnHover&&(!0!==n.pauseOnHover&&(i=e(n.pauseOnHover)),i.hover(function(){n.API.pause(!0)},function(){n.API.resume(!0)})),n.timeout&&(t=n.API.getSlideOpts(n.currSlide),n.API.queueTransition(t,t.timeout+n.delay)),n._initialized=!0,n.API.updateView(!0),n.API.trigger("cycle-initialized",[n]),n.API.postInitSlideshow()},pause:function(t){var n=this.opts(),i=n.API.getSlideOpts(),r=n.hoverPaused||n.paused;t?n.hoverPaused=!0:n.paused=!0,r||(n.container.addClass("cycle-paused"),n.API.trigger("cycle-paused",[n]).log("cycle-paused"),i.timeout&&(clearTimeout(n.timeoutId),n.timeoutId=0,n._remainingTimeout-=e.now()-n._lastQueue,(n._remainingTimeout<0||isNaN(n._remainingTimeout))&&(n._remainingTimeout=void 0)))},resume:function(e){var t=this.opts(),n=!t.hoverPaused&&!t.paused;e?t.hoverPaused=!1:t.paused=!1,n||(t.container.removeClass("cycle-paused"),0===t.slides.filter(":animated").length&&t.API.queueTransition(t.API.getSlideOpts(),t._remainingTimeout),t.API.trigger("cycle-resumed",[t,t._remainingTimeout]).log("cycle-resumed"))},add:function(t,n){var i,r=this.opts(),o=r.slideCount;"string"==e.type(t)&&(t=e.trim(t)),e(t).each(function(){var t,i=e(this);n?r.container.prepend(i):r.container.append(i),r.slideCount++,t=r.API.buildSlideOpts(i),r.slides=n?e(i).add(r.slides):r.slides.add(i),r.API.initSlide(t,i,--r._maxZ),i.data("cycle.opts",t),r.API.trigger("cycle-slide-added",[r,t,i])}),r.API.updateView(!0),r._preInitialized&&2>o&&r.slideCount>=1&&(r._initialized?r.timeout&&(i=r.slides.length,r.nextSlide=r.reverse?i-1:1,r.timeoutId||r.API.queueTransition(r)):r.API.initSlideshow())},calcFirstSlide:function(){var e,t=this.opts();((e=parseInt(t.startingSlide||0,10))>=t.slides.length||0>e)&&(e=0),t.currSlide=e,t.reverse?(t.nextSlide=e-1,t.nextSlide<0&&(t.nextSlide=t.slides.length-1)):(t.nextSlide=e+1,t.nextSlide==t.slides.length&&(t.nextSlide=0))},calcNextSlide:function(){var e,t=this.opts();t.reverse?(e=t.nextSlide-1<0,t.nextSlide=e?t.slideCount-1:t.nextSlide-1,t.currSlide=e?0:t.nextSlide+1):(e=t.nextSlide+1==t.slides.length,t.nextSlide=e?0:t.nextSlide+1,t.currSlide=e?t.slides.length-1:t.nextSlide-1)},calcTx:function(t,n){var i,r=t;return r._tempFx?i=e.fn.cycle.transitions[r._tempFx]:n&&r.manualFx&&(i=e.fn.cycle.transitions[r.manualFx]),i||(i=e.fn.cycle.transitions[r.fx]),r._tempFx=null,this.opts()._tempFx=null,i||(i=e.fn.cycle.transitions.fade,r.API.log('Transition "'+r.fx+'" not found.  Using fade.')),i},prepareTx:function(e,t){var n,i,r,o,a,s=this.opts();return s.slideCount<2?void(s.timeoutId=0):(!e||s.busy&&!s.manualTrump||(s.API.stopTransition(),s.busy=!1,clearTimeout(s.timeoutId),s.timeoutId=0),void(s.busy||(0!==s.timeoutId||e)&&(i=s.slides[s.currSlide],r=s.slides[s.nextSlide],o=s.API.getSlideOpts(s.nextSlide),a=s.API.calcTx(o,e),s._tx=a,e&&void 0!==o.manualSpeed&&(o.speed=o.manualSpeed),s.nextSlide!=s.currSlide&&(e||!s.paused&&!s.hoverPaused&&s.timeout)?(s.API.trigger("cycle-before",[o,i,r,t]),a.before&&a.before(o,i,r,t),n=function(){s.busy=!1,s.container.data("cycle.opts")&&(a.after&&a.after(o,i,r,t),s.API.trigger("cycle-after",[o,i,r,t]),s.API.queueTransition(o),s.API.updateView(!0))},s.busy=!0,a.transition?a.transition(o,i,r,t,n):s.API.doTransition(o,i,r,t,n),s.API.calcNextSlide(),s.API.updateView()):s.API.queueTransition(o))))},doTransition:function(t,n,i,r,o){var a=t,s=e(n),l=e(i),c=function(){l.animate(a.animIn||{opacity:1},a.speed,a.easeIn||a.easing,o)};l.css(a.cssBefore||{}),s.animate(a.animOut||{},a.speed,a.easeOut||a.easing,function(){s.css(a.cssAfter||{}),a.sync||c()}),a.sync&&c()},queueTransition:function(t,n){var i=this.opts(),r=void 0!==n?n:t.timeout;return 0===i.nextSlide&&0==--i.loop?(i.API.log("terminating; loop=0"),i.timeout=0,r?setTimeout(function(){i.API.trigger("cycle-finished",[i])},r):i.API.trigger("cycle-finished",[i]),void(i.nextSlide=i.currSlide)):void 0!==i.continueAuto&&(!1===i.continueAuto||e.isFunction(i.continueAuto)&&!1===i.continueAuto())?(i.API.log("terminating automatic transitions"),i.timeout=0,void(i.timeoutId&&clearTimeout(i.timeoutId))):void(r&&(i._lastQueue=e.now(),void 0===n&&(i._remainingTimeout=t.timeout),i.paused||i.hoverPaused||(i.timeoutId=setTimeout(function(){i.API.prepareTx(!1,!i.reverse)},r))))},stopTransition:function(){var e=this.opts();e.slides.filter(":animated").length&&(e.slides.stop(!1,!0),e.API.trigger("cycle-transition-stopped",[e])),e._tx&&e._tx.stopTransition&&e._tx.stopTransition(e)},advanceSlide:function(e){var t=this.opts();return clearTimeout(t.timeoutId),t.timeoutId=0,t.nextSlide=t.currSlide+e,t.nextSlide<0?t.nextSlide=t.slides.length-1:t.nextSlide>=t.slides.length&&(t.nextSlide=0),t.API.prepareTx(!0,e>=0),!1},buildSlideOpts:function(n){var i,r,o=this.opts(),a=n.data()||{};for(var s in a)a.hasOwnProperty(s)&&/^cycle[A-Z]+/.test(s)&&(i=a[s],r=s.match(/^cycle(.*)/)[1].replace(/^[A-Z]/,t),o.API.log("["+(o.slideCount-1)+"]",r+":",i,"("+typeof i+")"),a[r]=i);(a=e.extend({},e.fn.cycle.defaults,o,a)).slideNum=o.slideCount;try{delete a.API,delete a.slideCount,delete a.currSlide,delete a.nextSlide,delete a.slides}catch(e){}return a},getSlideOpts:function(t){var n=this.opts();void 0===t&&(t=n.currSlide);var i=n.slides[t],r=e(i).data("cycle.opts");return e.extend({},n,r)},initSlide:function(t,n,i){var r=this.opts();n.css(t.slideCss||{}),i>0&&n.css("zIndex",i),isNaN(t.speed)&&(t.speed=e.fx.speeds[t.speed]||e.fx.speeds._default),t.sync||(t.speed=t.speed/2),n.addClass(r.slideClass)},updateView:function(e,t){var n=this.opts();if(n._initialized){var i=n.API.getSlideOpts(),r=n.slides[n.currSlide];!e&&!0!==t&&(n.API.trigger("cycle-update-view-before",[n,i,r]),n.updateView<0)||(n.slideActiveClass&&n.slides.removeClass(n.slideActiveClass).eq(n.currSlide).addClass(n.slideActiveClass),e&&n.hideNonActive&&n.slides.filter(":not(."+n.slideActiveClass+")").css("visibility","hidden"),0===n.updateView&&setTimeout(function(){n.API.trigger("cycle-update-view",[n,i,r,e])},i.speed/(n.sync?2:1)),0!==n.updateView&&n.API.trigger("cycle-update-view",[n,i,r,e]),e&&n.API.trigger("cycle-update-view-after",[n,i,r]))}},getComponent:function(t){var n=this.opts(),i=n[t];return"string"==typeof i?/^\s*[\>|\+|~]/.test(i)?n.container.find(i):e(i):i.jquery?i:e(i)},stackSlides:function(t,n,i){var r=this.opts();t||(t=r.slides[r.currSlide],n=r.slides[r.nextSlide],i=!r.reverse),e(t).css("zIndex",r.maxZ);var o,a=r.maxZ-2,s=r.slideCount;if(i){for(o=r.currSlide+1;s>o;o++)e(r.slides[o]).css("zIndex",a--);for(o=0;o<r.currSlide;o++)e(r.slides[o]).css("zIndex",a--)}else{for(o=r.currSlide-1;o>=0;o--)e(r.slides[o]).css("zIndex",a--);for(o=s-1;o>r.currSlide;o--)e(r.slides[o]).css("zIndex",a--)}e(n).css("zIndex",r.maxZ-1)},getSlideIndex:function(e){return this.opts().slides.index(e)}},e.fn.cycle.log=function(){window.console&&console.log&&console.log("[cycle2] "+Array.prototype.join.call(arguments," "))},e.fn.cycle.version=function(){return"Cycle2: 2.1.6"},e.fn.cycle.transitions={custom:{},none:{before:function(e,t,n,i){e.API.stackSlides(n,t,i),e.cssBefore={opacity:1,visibility:"visible",display:"block"}}},fade:{before:function(t,n,i,r){var o=t.API.getSlideOpts(t.nextSlide).slideCss||{};t.API.stackSlides(n,i,r),t.cssBefore=e.extend(o,{opacity:0,visibility:"visible",display:"block"}),t.animIn={opacity:1},t.animOut={opacity:0}}},fadeout:{before:function(t,n,i,r){var o=t.API.getSlideOpts(t.nextSlide).slideCss||{};t.API.stackSlides(n,i,r),t.cssBefore=e.extend(o,{opacity:1,visibility:"visible",display:"block"}),t.animOut={opacity:0}}},scrollHorz:{before:function(e,t,n,i){e.API.stackSlides(t,n,i);var r=e.container.css("overflow","hidden").width();e.cssBefore={left:i?r:-r,top:0,opacity:1,visibility:"visible",display:"block"},e.cssAfter={zIndex:e._maxZ-2,left:0},e.animIn={left:0},e.animOut={left:i?-r:r}}}},e.fn.cycle.defaults={allowWrap:!0,autoSelector:".cycle-slideshow[data-cycle-auto-init!=false]",delay:0,easing:null,fx:"fade",hideNonActive:!0,loop:0,manualFx:void 0,manualSpeed:void 0,manualTrump:!0,maxZ:100,pauseOnHover:!1,reverse:!1,slideActiveClass:"cycle-slide-active",slideClass:"cycle-slide",slideCss:{position:"absolute",top:0,left:0},slides:"> img",speed:500,startingSlide:0,sync:!0,timeout:4e3,updateView:0},e(document).ready(function(){e(e.fn.cycle.defaults.autoSelector).cycle()})}(jQuery),function(e){"use strict";function t(t,i){var r,o,a,s=i.autoHeight;if("container"==s)o=e(i.slides[i.currSlide]).outerHeight(),i.container.height(o);else if(i._autoHeightRatio)i.container.height(i.container.width()/i._autoHeightRatio);else if("calc"===s||"number"==e.type(s)&&s>=0){if((a="calc"===s?n(t,i):s>=i.slides.length?0:s)==i._sentinelIndex)return;i._sentinelIndex=a,i._sentinel&&i._sentinel.remove(),(r=e(i.slides[a].cloneNode(!0))).removeAttr("id name rel").find("[id],[name],[rel]").removeAttr("id name rel"),r.css({position:"static",visibility:"hidden",display:"block"}).prependTo(i.container).addClass("cycle-sentinel cycle-slide").removeClass("cycle-slide-active"),r.find("*").css("visibility","hidden"),i._sentinel=r}}function n(t,n){var i=0,r=-1;return n.slides.each(function(t){var n=e(this).height();n>r&&(r=n,i=t)}),i}function i(t,n,i,r){var o=e(r).outerHeight();n.container.animate({height:o},n.autoHeightSpeed,n.autoHeightEasing)}function r(n,o){o._autoHeightOnResize&&(e(window).off("resize orientationchange",o._autoHeightOnResize),o._autoHeightOnResize=null),o.container.off("cycle-slide-added cycle-slide-removed",t),o.container.off("cycle-destroyed",r),o.container.off("cycle-before",i),o._sentinel&&(o._sentinel.remove(),o._sentinel=null)}e.extend(e.fn.cycle.defaults,{autoHeight:0,autoHeightSpeed:250,autoHeightEasing:null}),e(document).on("cycle-initialized",function(n,o){function a(){t(n,o)}var s,l=o.autoHeight,c=e.type(l),u=null;("string"===c||"number"===c)&&(o.container.on("cycle-slide-added cycle-slide-removed",t),o.container.on("cycle-destroyed",r),"container"==l?o.container.on("cycle-before",i):"string"===c&&/\d+\:\d+/.test(l)&&(s=l.match(/(\d+)\:(\d+)/),s=s[1]/s[2],o._autoHeightRatio=s),"number"!==c&&(o._autoHeightOnResize=function(){clearTimeout(u),u=setTimeout(a,50)},e(window).on("resize orientationchange",o._autoHeightOnResize)),setTimeout(a,30))})}(jQuery),function(e){"use strict";e.extend(e.fn.cycle.defaults,{caption:"> .cycle-caption",captionTemplate:"{{slideNum}} / {{slideCount}}",overlay:"> .cycle-overlay",overlayTemplate:"<div>{{title}}</div><div>{{desc}}</div>",captionModule:"caption"}),e(document).on("cycle-update-view",function(t,n,i,r){"caption"===n.captionModule&&e.each(["caption","overlay"],function(){var e=this,t=i[e+"Template"],o=n.API.getComponent(e);o.length&&t?(o.html(n.API.tmpl(t,i,n,r)),o.show()):o.hide()})}),e(document).on("cycle-destroyed",function(t,n){var i;e.each(["caption","overlay"],function(){var e=this,t=n[e+"Template"];n[e]&&t&&(i=n.API.getComponent("caption")).empty()})})}(jQuery),function(e){"use strict";var t=e.fn.cycle;e.fn.cycle=function(n){var i,r,o,a=e.makeArray(arguments);return"number"==e.type(n)?this.cycle("goto",n):"string"==e.type(n)?this.each(function(){var s;return i=n,void 0===(o=e(this).data("cycle.opts"))?void t.log('slideshow must be initialized before sending commands; "'+i+'" ignored'):(i="goto"==i?"jump":i,r=o.API[i],e.isFunction(r)?((s=e.makeArray(a)).shift(),r.apply(o.API,s)):void t.log("unknown command: ",i))}):t.apply(this,arguments)},e.extend(e.fn.cycle,t),e.extend(t.API,{next:function(){var e=this.opts();if(!e.busy||e.manualTrump){var t=e.reverse?-1:1;!1===e.allowWrap&&e.currSlide+t>=e.slideCount||(e.API.advanceSlide(t),e.API.trigger("cycle-next",[e]).log("cycle-next"))}},prev:function(){var e=this.opts();if(!e.busy||e.manualTrump){var t=e.reverse?1:-1;!1===e.allowWrap&&e.currSlide+t<0||(e.API.advanceSlide(t),e.API.trigger("cycle-prev",[e]).log("cycle-prev"))}},destroy:function(){this.stop();var t=this.opts(),n=e.isFunction(e._data)?e._data:e.noop;clearTimeout(t.timeoutId),t.timeoutId=0,t.API.stop(),t.API.trigger("cycle-destroyed",[t]).log("cycle-destroyed"),t.container.removeData(),n(t.container[0],"parsedAttrs",!1),t.retainStylesOnDestroy||(t.container.removeAttr("style"),t.slides.removeAttr("style"),t.slides.removeClass(t.slideActiveClass)),t.slides.each(function(){var i=e(this);i.removeData(),i.removeClass(t.slideClass),n(this,"parsedAttrs",!1)})},jump:function(e,t){var n,i=this.opts();if(!i.busy||i.manualTrump){var r=parseInt(e,10);if(isNaN(r)||0>r||r>=i.slides.length)return void i.API.log("goto: invalid slide index: "+r);if(r==i.currSlide)return void i.API.log("goto: skipping, already on slide",r);i.nextSlide=r,clearTimeout(i.timeoutId),i.timeoutId=0,i.API.log("goto: ",r," (zero-index)"),n=i.currSlide<i.nextSlide,i._tempFx=t,i.API.prepareTx(!0,n)}},stop:function(){var t=this.opts(),n=t.container;clearTimeout(t.timeoutId),t.timeoutId=0,t.API.stopTransition(),t.pauseOnHover&&(!0!==t.pauseOnHover&&(n=e(t.pauseOnHover)),n.off("mouseenter mouseleave")),t.API.trigger("cycle-stopped",[t]).log("cycle-stopped")},reinit:function(){var e=this.opts();e.API.destroy(),e.container.cycle()},remove:function(t){for(var n,i,r=this.opts(),o=[],a=1,s=0;s<r.slides.length;s++)n=r.slides[s],s==t?i=n:(o.push(n),e(n).data("cycle.opts").slideNum=a,a++);i&&(r.slides=e(o),r.slideCount--,e(i).remove(),t==r.currSlide?r.API.advanceSlide(1):t<r.currSlide?r.currSlide--:r.currSlide++,r.API.trigger("cycle-slide-removed",[r,t,i]).log("cycle-slide-removed"),r.API.updateView())}}),e(document).on("click.cycle","[data-cycle-cmd]",function(t){t.preventDefault();var n=e(this),i=n.data("cycle-cmd"),r=n.data("cycle-context")||".cycle-slideshow";e(r).cycle(i,n.data("cycle-arg"))})}(jQuery),function(e){"use strict";function t(t,n){var i;return t._hashFence?void(t._hashFence=!1):(i=window.location.hash.substring(1),void t.slides.each(function(r){if(e(this).data("cycle-hash")==i){if(!0===n)t.startingSlide=r;else{var o=t.currSlide<r;t.nextSlide=r,t.API.prepareTx(!0,o)}return!1}}))}e(document).on("cycle-pre-initialize",function(n,i){t(i,!0),i._onHashChange=function(){t(i,!1)},e(window).on("hashchange",i._onHashChange)}),e(document).on("cycle-update-view",function(e,t,n){n.hash&&"#"+n.hash!=window.location.hash&&(t._hashFence=!0,window.location.hash=n.hash)}),e(document).on("cycle-destroyed",function(t,n){n._onHashChange&&e(window).off("hashchange",n._onHashChange)})}(jQuery),function(e){"use strict";e.extend(e.fn.cycle.defaults,{loader:!1}),e(document).on("cycle-bootstrap",function(t,n){var i;n.loader&&(i=n.API.add,n.API.add=function(t,r){function o(t){var o;"wait"==n.loader?(s.push(t),0===c&&(s.sort(a),i.apply(n.API,[s,r]),n.container.removeClass("cycle-loading"))):(o=e(n.slides[n.currSlide]),i.apply(n.API,[t,r]),o.show(),n.container.removeClass("cycle-loading"))}function a(e,t){return e.data("index")-t.data("index")}var s=[];if("string"==e.type(t))t=e.trim(t);else if("array"===e.type(t))for(var l=0;l<t.length;l++)t[l]=e(t[l])[0];var c=(t=e(t)).length;c&&(t.css("visibility","hidden").appendTo("body").each(function(t){function a(){0==--l&&(--c,o(u))}var l=0,u=e(this),d=u.is("img")?u:u.find("img");return u.data("index",t),(d=d.filter(":not(.cycle-loader-ignore)").filter(':not([src=""])')).length?(l=d.length,void d.each(function(){this.complete?a():e(this).load(function(){a()}).on("error",function(){0==--l&&(n.API.log("slide skipped; img not loaded:",this.src),0==--c&&"wait"==n.loader&&i.apply(n.API,[s,r]))})})):(--c,void s.push(u))}),c&&n.container.addClass("cycle-loading"))})})}(jQuery),function(e){"use strict";function t(t,n,i){var r;t.API.getComponent("pager").each(function(){var o=e(this);if(n.pagerTemplate){var a=t.API.tmpl(n.pagerTemplate,n,t,i[0]);r=e(a).appendTo(o)}else r=o.children().eq(t.slideCount-1);r.on(t.pagerEvent,function(e){t.pagerEventBubble||e.preventDefault(),t.API.page(o,e.currentTarget)})})}function n(e,t){var n=this.opts();if(!n.busy||n.manualTrump){var i=e.children().index(t),r=n.currSlide<i;n.currSlide!=i&&(n.nextSlide=i,n._tempFx=n.pagerFx,n.API.prepareTx(!0,r),n.API.trigger("cycle-pager-activated",[n,e,t]))}}e.extend(e.fn.cycle.defaults,{pager:"> .cycle-pager",pagerActiveClass:"cycle-pager-active",pagerEvent:"click.cycle",pagerEventBubble:void 0,pagerTemplate:"<span>&bull;</span>"}),e(document).on("cycle-bootstrap",function(e,n,i){i.buildPagerLink=t}),e(document).on("cycle-slide-added",function(e,t,i,r){t.pager&&(t.API.buildPagerLink(t,i,r),t.API.page=n)}),e(document).on("cycle-slide-removed",function(t,n,i){n.pager&&n.API.getComponent("pager").each(function(){var t=e(this);e(t.children()[i]).remove()})}),e(document).on("cycle-update-view",function(t,n){n.pager&&n.API.getComponent("pager").each(function(){e(this).children().removeClass(n.pagerActiveClass).eq(n.currSlide).addClass(n.pagerActiveClass)})}),e(document).on("cycle-destroyed",function(e,t){var n=t.API.getComponent("pager");n&&(n.children().off(t.pagerEvent),t.pagerTemplate&&n.empty())})}(jQuery),function(e){"use strict";e.extend(e.fn.cycle.defaults,{next:"> .cycle-next",nextEvent:"click.cycle",disabledClass:"disabled",prev:"> .cycle-prev",prevEvent:"click.cycle",swipe:!1}),e(document).on("cycle-initialized",function(e,t){if(t.API.getComponent("next").on(t.nextEvent,function(e){e.preventDefault(),t.API.next()}),t.API.getComponent("prev").on(t.prevEvent,function(e){e.preventDefault(),t.API.prev()}),t.swipe){var n=t.swipeVert?"swipeUp.cycle":"swipeLeft.cycle swipeleft.cycle",i=t.swipeVert?"swipeDown.cycle":"swipeRight.cycle swiperight.cycle";t.container.on(n,function(){t._tempFx=t.swipeFx,t.API.next()}),t.container.on(i,function(){t._tempFx=t.swipeFx,t.API.prev()})}}),e(document).on("cycle-update-view",function(e,t){if(!t.allowWrap){var n=t.disabledClass,i=t.API.getComponent("next"),r=t.API.getComponent("prev"),o=t._prevBoundry||0,a=void 0!==t._nextBoundry?t._nextBoundry:t.slideCount-1;t.currSlide==a?i.addClass(n).prop("disabled",!0):i.removeClass(n).prop("disabled",!1),t.currSlide===o?r.addClass(n).prop("disabled",!0):r.removeClass(n).prop("disabled",!1)}}),e(document).on("cycle-destroyed",function(e,t){t.API.getComponent("prev").off(t.nextEvent),t.API.getComponent("next").off(t.prevEvent),t.container.off("swipeleft.cycle swiperight.cycle swipeLeft.cycle swipeRight.cycle swipeUp.cycle swipeDown.cycle")})}(jQuery),function(e){"use strict";e.extend(e.fn.cycle.defaults,{progressive:!1}),e(document).on("cycle-pre-initialize",function(t,n){if(n.progressive){var i,r,o=n.API,a=o.next,s=o.prev,l=o.prepareTx,c=e.type(n.progressive);if("array"==c)i=n.progressive;else if(e.isFunction(n.progressive))i=n.progressive(n);else if("string"==c){if(r=e(n.progressive),!(i=e.trim(r.html())))return;if(/^(\[)/.test(i))try{i=e.parseJSON(i)}catch(e){return void o.log("error parsing progressive slides",e)}else(i=i.split(new RegExp(r.data("cycle-split")||"\n")))[i.length-1]||i.pop()}l&&(o.prepareTx=function(e,t){var r,o;return e||0===i.length?void l.apply(n.API,[e,t]):void(t&&n.currSlide==n.slideCount-1?(o=i[0],i=i.slice(1),n.container.one("cycle-slide-added",function(e,t){setTimeout(function(){t.API.advanceSlide(1)},50)}),n.API.add(o)):t||0!==n.currSlide?l.apply(n.API,[e,t]):(r=i.length-1,o=i[r],i=i.slice(0,r),n.container.one("cycle-slide-added",function(e,t){setTimeout(function(){t.currSlide=1,t.API.advanceSlide(-1)},50)}),n.API.add(o,!0)))}),a&&(o.next=function(){var e=this.opts();if(i.length&&e.currSlide==e.slideCount-1){var t=i[0];i=i.slice(1),e.container.one("cycle-slide-added",function(e,t){a.apply(t.API),t.container.removeClass("cycle-loading")}),e.container.addClass("cycle-loading"),e.API.add(t)}else a.apply(e.API)}),s&&(o.prev=function(){var e=this.opts();if(i.length&&0===e.currSlide){var t=i.length-1,n=i[t];i=i.slice(0,t),e.container.one("cycle-slide-added",function(e,t){t.currSlide=1,t.API.advanceSlide(-1),t.container.removeClass("cycle-loading")}),e.container.addClass("cycle-loading"),e.API.add(n,!0)}else s.apply(e.API)})}})}(jQuery),function(e){"use strict";e.extend(e.fn.cycle.defaults,{tmplRegex:"{{((.)?.*?)}}"}),e.extend(e.fn.cycle.API,{tmpl:function(t,n){var i=new RegExp(n.tmplRegex||e.fn.cycle.defaults.tmplRegex,"g"),r=e.makeArray(arguments);return r.shift(),t.replace(i,function(t,n){var i,o,a,s,l=n.split(".");for(i=0;i<r.length;i++)if(a=r[i]){if(l.length>1)for(s=a,o=0;o<l.length;o++)a=s,s=s[l[o]]||n;else s=a[n];if(e.isFunction(s))return s.apply(a,r);if(void 0!==s&&null!==s&&s!=n)return s}return n})}})}(jQuery),function(e){"use strict";e(document).on("cycle-bootstrap",function(e,t,n){"carousel"===t.fx&&(n.getSlideIndex=function(e){var t=this.opts()._carouselWrap.children();return t.index(e)%t.length},n.next=function(){var e=t.reverse?-1:1;!1===t.allowWrap&&t.currSlide+e>t.slideCount-t.carouselVisible||(t.API.advanceSlide(e),t.API.trigger("cycle-next",[t]).log("cycle-next"))})}),e.fn.cycle.transitions.carousel={preInit:function(t){t.hideNonActive=!1,t.container.on("cycle-destroyed",e.proxy(this.onDestroy,t.API)),t.API.stopTransition=this.stopTransition;for(var n=0;n<t.startingSlide;n++)t.container.append(t.slides[0])},postInit:function(t){var n,i,r,o,a=t.carouselVertical;t.carouselVisible&&t.carouselVisible>t.slideCount&&(t.carouselVisible=t.slideCount-1);var s=t.carouselVisible||t.slides.length,l={display:a?"block":"inline-block",position:"static"};if(t.container.css({position:"relative",overflow:"hidden"}),t.slides.css(l),t._currSlide=t.currSlide,o=e('<div class="cycle-carousel-wrap"></div>').prependTo(t.container).css({margin:0,padding:0,top:0,left:0,position:"absolute"}).append(t.slides),t._carouselWrap=o,a||o.css("white-space","nowrap"),!1!==t.allowWrap){for(i=0;i<(void 0===t.carouselVisible?2:1);i++){for(n=0;n<t.slideCount;n++)o.append(t.slides[n].cloneNode(!0));for(n=t.slideCount;n--;)o.prepend(t.slides[n].cloneNode(!0))}o.find(".cycle-slide-active").removeClass("cycle-slide-active"),t.slides.eq(t.startingSlide).addClass("cycle-slide-active")}t.pager&&!1===t.allowWrap&&(r=t.slideCount-s,e(t.pager).children().filter(":gt("+r+")").hide()),t._nextBoundry=t.slideCount-t.carouselVisible,this.prepareDimensions(t)},prepareDimensions:function(t){var n,i,r,o,a=t.carouselVertical,s=t.carouselVisible||t.slides.length;if(t.carouselFluid&&t.carouselVisible?t._carouselResizeThrottle||this.fluidSlides(t):t.carouselVisible&&t.carouselSlideDimension?(n=s*t.carouselSlideDimension,t.container[a?"height":"width"](n)):t.carouselVisible&&(n=s*e(t.slides[0])[a?"outerHeight":"outerWidth"](!0),t.container[a?"height":"width"](n)),i=t.carouselOffset||0,!1!==t.allowWrap)if(t.carouselSlideDimension)i-=(t.slideCount+t.currSlide)*t.carouselSlideDimension;else for(r=t._carouselWrap.children(),o=0;o<t.slideCount+t.currSlide;o++)i-=e(r[o])[a?"outerHeight":"outerWidth"](!0);t._carouselWrap.css(a?"top":"left",i)},fluidSlides:function(t){function n(){clearTimeout(r),r=setTimeout(i,20)}function i(){t._carouselWrap.stop(!1,!0);var e=t.container.width()/t.carouselVisible;e=Math.ceil(e-a),t._carouselWrap.children().width(e),t._sentinel&&t._sentinel.width(e),s(t)}var r,o=t.slides.eq(0),a=o.outerWidth()-o.width(),s=this.prepareDimensions;e(window).on("resize",n),t._carouselResizeThrottle=n,i()},transition:function(t,n,i,r,o){var a,s={},l=t.nextSlide-t.currSlide,c=t.carouselVertical,u=t.speed;if(!1===t.allowWrap){r=l>0;var d=t._currSlide,f=t.slideCount-t.carouselVisible;l>0&&t.nextSlide>f&&d==f?l=0:l>0&&t.nextSlide>f?l=t.nextSlide-d-(t.nextSlide-f):0>l&&t.currSlide>f&&t.nextSlide>f?l=0:0>l&&t.currSlide>f?l+=t.currSlide-f:d=t.currSlide,a=this.getScroll(t,c,d,l),t.API.opts()._currSlide=t.nextSlide>f?f:t.nextSlide}else r&&0===t.nextSlide?(a=this.getDim(t,t.currSlide,c),o=this.genCallback(t,r,c,o)):r||t.nextSlide!=t.slideCount-1?a=this.getScroll(t,c,t.currSlide,l):(a=this.getDim(t,t.currSlide,c),o=this.genCallback(t,r,c,o));s[c?"top":"left"]=r?"-="+a:"+="+a,t.throttleSpeed&&(u=a/e(t.slides[0])[c?"height":"width"]()*t.speed),t._carouselWrap.animate(s,u,t.easing,o)},getDim:function(t,n,i){return e(t.slides[n])[i?"outerHeight":"outerWidth"](!0)},getScroll:function(e,t,n,i){var r,o=0;if(i>0)for(r=n;n+i>r;r++)o+=this.getDim(e,r,t);else for(r=n;r>n+i;r--)o+=this.getDim(e,r,t);return o},genCallback:function(t,n,i,r){return function(){var n=0-e(t.slides[t.nextSlide]).position()[i?"top":"left"]+(t.carouselOffset||0);t._carouselWrap.css(t.carouselVertical?"top":"left",n),r()}},stopTransition:function(){var e=this.opts();e.slides.stop(!1,!0),e._carouselWrap.stop(!1,!0)},onDestroy:function(){var t=this.opts();t._carouselResizeThrottle&&e(window).off("resize",t._carouselResizeThrottle),t.slides.prependTo(t.container),t._carouselWrap.remove()}}}(jQuery),function(e,t,n,i){function r(e){return{url:e.split(" ").length>0?e.split(" ")[0]:i,w:c.test(e)?parseInt(c.exec(e)[0]):1/0,h:l.test(e)?parseInt(l.exec(e)[0]):1/0,dpi:parseFloat(u.test(e)?u.exec(e)[1]:1)}}function o(e){for(var t=e.data("srcset").split(","),n=[],i=0;i<t.length;i++){var o=r(t[i].replace(/^\s*|\s*$/gi,""));o.url?n.push(o):console.log("Couldn't parse URL; got %o",o)}return 0===n.length?(console.log("Couldn't parse srcset data for %o",e),!1):(e.data("srcset-sizes",n),n)}function a(n){if(n.data("srcset-sizes")){var r=e(t).height(),o=e(t).width(),a=t.devicePixelRatio||1,s=n.data("srcset-sizes");filteredData=e.grep(s,function(e,t){return e.w>=o&&e.h>=r&&e.dpi>=a});var l={data:i,diff:1/0};if(1===filteredData.length&&(l.data=filteredData[0]),0===filteredData.length)s.map(function(e,t){var n=o-e.w==-1/0?0:o*a-e.w,i=r-e.h==-1/0?0:r*a-e.h;return e.diff=n+i,e}).sort(function(e,t){return e.diff<t.diff}).forEach(function(e,t){l.data&&l.data.diff<=0||(l.data=e)});else for(var c=0;c<filteredData.length;c++){var u=(filteredData[c].w-o==1/0?0:filteredData[c].w*a-o)+(filteredData[c].h-r==1/0?0:filteredData[c].h*a-r);u<l.diff&&(l.diff=u,l.data=filteredData[c])}if((s=l.data)&&s.url){var d=(n.data("srcset-base")||"")+s.url+(n.data("srcset-ext")||"");n.attr("src")!==d&&(n.trigger("beforeSrcReplace"),n.attr("src",d),n.trigger("srcReplaced"))}else console.log("No src found for %o",n)}}var s={autoInit:!0,updateOnResize:!0},l=/\d+h/i,c=/\d+w/i,u=/([\d\.]+)x\b/i;e.srcset=e.extend(s,e.srcset||{}),e.fn.srcset=function(n){options=e.extend({},e.srcset,n||{}),e(this).each(function(){o(e(this)),a(e(this))}),options.updateOnResize&&(e(t).on("resize",function(){e(this).each(function(){a(e(this))})}.bind(this)),e(t))},e(function(){e.srcset.autoInit&&e("img[data-srcset]").srcset()})}(window.jQuery,window,document),function(e){var t=window.Chicago||{utils:{now:Date.now||function(){return(new Date).getTime()},uid:function(e){return(e||"id")+t.utils.now()+"RAND"+Math.ceil(1e5*Math.random())},is:{number:function(e){return!isNaN(parseFloat(e))&&isFinite(e)},fn:function(e){return"function"==typeof e},object:function(e){return"[object Object]"===Object.prototype.toString.call(e)}},debounce:function(e,t,n){var i;return function(){var r=this,o=arguments,a=n&&!i;i&&clearTimeout(i),i=setTimeout(function(){i=null,n||e.apply(r,o)},t),a&&e.apply(r,o)}}},$:window.jQuery||null};if("function"==typeof define&&define.amd&&define("chicago",function(){return t.load=function(e,n,i,r){var o=e.split(","),a=[],s=(r.config&&r.config.chicago&&r.config.chicago.base?r.config.chicago.base:"").replace(/\/+$/g,"");if(!s)throw new Error("Please define base path to jQuery resize.end in the requirejs config.");for(var l=0;l<o.length;){var c=o[l].replace(/\./g,"/");a.push(s+"/"+c),l+=1}n(a,function(){i(t)})},t}),window&&window.jQuery)return e(t,window,window.document);if(!window.jQuery)throw new Error("jQuery resize.end requires jQuery")}(function(e,t,n){e.$win=e.$(t),e.$doc=e.$(n),e.events||(e.events={}),e.events.resizeend={defaults:{delay:250},setup:function(){var t=arguments,n={delay:e.$.event.special.resizeend.defaults.delay};e.utils.is.fn(t[0])?t[0]:e.utils.is.number(t[0])?n.delay=t[0]:e.utils.is.object(t[0])&&(n=e.$.extend({},n,t[0]));var i=e.utils.uid("resizeend"),r=e.$.extend({delay:e.$.event.special.resizeend.defaults.delay},n),o=r,a=function(t){o&&clearTimeout(o),o=setTimeout(function(){return o=null,t.type="resizeend.chicago.dom",e.$(t.target).trigger("resizeend",t)},r.delay)};return e.$(this).data("chicago.event.resizeend.uid",i),e.$(this).on("resize",e.utils.debounce(a,100)).data(i,a)},teardown:function(){var t=e.$(this).data("chicago.event.resizeend.uid");return e.$(this).off("resize",e.$(this).data(t)),e.$(this).removeData(t),e.$(this).removeData("chicago.event.resizeend.uid")}},e.$.event.special.resizeend=e.events.resizeend,e.$.fn.resizeend=function(t,n){return this.each(function(){e.$(this).on("resizeend",t,n)})}}),function(e,t){"use strict";var n;n=void 0,e.fn.adjustWindow=function(){n.height()},e.fn.resizeWindow=function(){return e(window).resizeend(function(){n=e(window),e.fn.adjustWindow()})},e(document).ready(function(){n=e(window),e.fn.adjustWindow(),conditionizr.add("chrome",!!window.chrome&&/google/i.test(navigator.vendor)),conditionizr.add("firefox","InstallTrigger"in window),conditionizr.add("ie8",!!Function("/*@cc_on return (@_jscript_version > 5.7 && !/^(9|10)/.test(@_jscript_version)); @*/")()),conditionizr.add("ie9",!!Function("/*@cc_on return (/^9/.test(@_jscript_version) && /MSIE 9.0(?!.*IEMobile)/i.test(navigator.userAgent)); @*/")()),conditionizr.add("ie10",!!Function("/*@cc_on return (/^10/.test(@_jscript_version) && /MSIE 10.0(?!.*IEMobile)/i.test(navigator.userAgent)); @*/")()),conditionizr.add("ie11",/(?:\sTrident\/7\.0;.*\srv:11\.0)/i.test(navigator.userAgent)),conditionizr.add("ios",/iP(ad|hone|od)/i.test(navigator.userAgent)),conditionizr.add("safari",/Constructor/.test(window.HTMLElement)),conditionizr.add("windows",/win/i.test(navigator.platform)),conditionizr.config({tests:{chrome:["class"],firefox:["class"],ie8:["class"],ie9:["class"],ie10:["class"],ie11:["class"],ios:["class"],opera:["class"],safari:["class"],windows:["class"]}}),conditionizr.polyfill("assets/js/min/html5.min.js",["ie8","ie9"]),conditionizr.polyfill("assets/js/min/css3-mediaqueries.js",["ie8","ie9"]),conditionizr.polyfill("assets/js/min/selectivizr-min.js",["ie8","ie9"]),Modernizr.touchevents&&setTimeout(function(){window.scrollTo(0,1)},1e3)}),e(window).resize(function(){}),e(window).scroll(function(){})}(jQuery);