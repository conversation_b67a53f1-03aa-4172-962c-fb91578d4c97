!function(e,t){"use strict";e.fn.initLogin=function(){var t,i;if(e("body.login").length)return e("a.rememberPassword").on("click",function(t){return t.preventDefault(),e(".box .panel:first-child").slideToggle(),e(".box .panel:last-child").slideToggle(),e("form input, form select, textarea").tooltipster("hide")}),t=e(window).height()-e("footer").outerHeight(),(i=e(".site-header").outerHeight()+e("#content").outerHeight())>t?e("#mediabox").height(i):e("#mediabox").height(t),e(window).resizeend(function(){t=e(window).height()-e("footer").outerHeight(),(i=e(".site-header").outerHeight()+e("#content").outerHeight())>t?e("#mediabox").height(i):e("#mediabox").height(t)})}}(jQ<PERSON>y);