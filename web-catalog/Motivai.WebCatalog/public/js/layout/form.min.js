!function(e){var t,a=function(){var e=document.createElement("input");return e.setAttribute("onpaste",""),"function"==typeof e.onpaste?"paste":"input"}()+".mask",i=navigator.userAgent,n=/iphone/i.test(i),r=/android/i.test(i);e.mask={definitions:{9:"[0-9]",a:"[A-Za-z]","*":"[A-Za-z0-9]"},dataName:"rawMaskFn",placeholder:"_"},e.fn.extend({caret:function(e,t){var a;if(0!==this.length&&!this.is(":hidden"))return"number"==typeof e?(t="number"==typeof t?t:e,this.each(function(){this.setSelectionRange?this.setSelectionRange(e,t):this.createTextRange&&((a=this.createTextRange()).collapse(!0),a.moveEnd("character",t),a.moveStart("character",e),a.select())})):(this[0].setSelectionRange?(e=this[0].selectionStart,t=this[0].selectionEnd):document.selection&&document.selection.createRange&&(a=document.selection.createRange(),e=0-a.duplicate().moveStart("character",-1e5),t=e+a.text.length),{begin:e,end:t})},unmask:function(){return this.trigger("unmask")},mask:function(i,s){var o,d,l,u,c;return!i&&this.length>0?e(this[0]).data(e.mask.dataName)():(s=e.extend({placeholder:e.mask.placeholder,completed:null},s),o=e.mask.definitions,d=[],l=c=i.length,u=null,e.each(i.split(""),function(e,t){"?"==t?(c--,l=e):o[t]?(d.push(new RegExp(o[t])),null===u&&(u=d.length-1)):d.push(null)}),this.trigger("unmask").each(function(){function h(e){for(;++e<c&&!d[e];);return e}function f(e){for(;--e>=0&&!d[e];);return e}function m(e,t){var a,i;if(!(e<0)){for(a=e,i=h(t);a<c;a++)if(d[a]){if(!(i<c&&d[a].test(F[i])))break;F[a]=F[i],F[i]=s.placeholder,i=h(i)}g(),A.caret(Math.max(u,e))}}function p(e){var t,a,i,n;for(t=e,a=s.placeholder;t<c;t++)if(d[t]){if(i=h(t),n=F[t],F[t]=a,!(i<c&&d[i].test(n)))break;a=n}}function v(e,t){var a;for(a=e;a<t&&a<c;a++)d[a]&&(F[a]=s.placeholder)}function g(){A.val(F.join(""))}function b(e){var t,a,i=A.val(),n=-1;for(t=0,pos=0;t<c;t++)if(d[t]){for(F[t]=s.placeholder;pos++<i.length;)if(a=i.charAt(pos-1),d[t].test(a)){F[t]=a,n=t;break}if(pos>i.length)break}else F[t]===i.charAt(pos)&&t!==l&&(pos++,n=t);return e?g():n+1<l?(A.val(""),v(0,c)):(g(),A.val(A.val().substring(0,n+1))),l?t:u}var A=e(this),F=e.map(i.split(""),function(e,t){if("?"!=e)return o[e]?s.placeholder:e}),y=A.val();A.data(e.mask.dataName,function(){return e.map(F,function(e,t){return d[t]&&e!=s.placeholder?e:null}).join("")}),A.attr("readonly")||A.one("unmask",function(){A.unbind(".mask").removeData(e.mask.dataName)}).bind("focus.mask",function(){clearTimeout(t);var e;y=A.val(),e=b(),t=setTimeout(function(){g(),e==i.length?A.caret(0,e):A.caret(e)},10)}).bind("blur.mask",function(){b(),A.val()!=y&&A.change()}).bind("keydown.mask",function(e){var t,a,i,r=e.which;8===r||46===r||n&&127===r?(a=(t=A.caret()).begin,(i=t.end)-a==0&&(a=46!==r?f(a):i=h(a-1),i=46===r?h(i):i),v(a,i),m(a,i-1),e.preventDefault()):27==r&&(A.val(y),A.caret(0,b()),e.preventDefault())}).bind("keypress.mask",function(t){var a,i,n,o=t.which,l=A.caret();t.ctrlKey||t.altKey||t.metaKey||o<32||o&&(l.end-l.begin!=0&&(v(l.begin,l.end),m(l.begin,l.end-1)),(a=h(l.begin-1))<c&&(i=String.fromCharCode(o),d[a].test(i)&&(p(a),F[a]=i,g(),n=h(a),r?setTimeout(e.proxy(e.fn.caret,A,n),0):A.caret(n),s.completed&&n>=c&&s.completed.call(A))),t.preventDefault())}).bind(a,function(){setTimeout(function(){var e=b(!0);A.caret(e),s.completed&&e==A.val().length&&s.completed.call(A)},0)}),b()}))}})}(jQuery),function(e){"function"==typeof define&&define.amd?define(["jquery"],e):e(jQuery)}(function(e){e.extend(e.fn,{validate:function(t){if(this.length){var a=e.data(this[0],"validator");return a||(this.attr("novalidate","novalidate"),a=new e.validator(t,this[0]),e.data(this[0],"validator",a),a.settings.onsubmit&&(this.on("click.validate",":submit",function(t){a.settings.submitHandler&&(a.submitButton=t.target),e(this).hasClass("cancel")&&(a.cancelSubmit=!0),void 0!==e(this).attr("formnovalidate")&&(a.cancelSubmit=!0)}),this.on("submit.validate",function(t){function i(){var i,n;return!a.settings.submitHandler||(a.submitButton&&(i=e("<input type='hidden'/>").attr("name",a.submitButton.name).val(e(a.submitButton).val()).appendTo(a.currentForm)),n=a.settings.submitHandler.call(a,a.currentForm,t),a.submitButton&&i.remove(),void 0!==n&&n)}return a.settings.debug&&t.preventDefault(),a.cancelSubmit?(a.cancelSubmit=!1,i()):a.form()?a.pendingRequest?(a.formSubmitted=!0,!1):i():(a.focusInvalid(),!1)})),a)}t&&t.debug&&window.console&&console.warn("Nothing selected, can't validate, returning nothing.")},valid:function(){var t,a,i;return e(this[0]).is("form")?t=this.validate().form():(i=[],t=!0,a=e(this[0].form).validate(),this.each(function(){t=a.element(this)&&t,i=i.concat(a.errorList)}),a.errorList=i),t},rules:function(t,a){var i,n,r,s,o,d,l=this[0];if(t)switch(i=e.data(l.form,"validator").settings,n=i.rules,r=e.validator.staticRules(l),t){case"add":e.extend(r,e.validator.normalizeRule(a)),delete r.messages,n[l.name]=r,a.messages&&(i.messages[l.name]=e.extend(i.messages[l.name],a.messages));break;case"remove":return a?(d={},e.each(a.split(/\s/),function(t,a){d[a]=r[a],delete r[a],"required"===a&&e(l).removeAttr("aria-required")}),d):(delete n[l.name],r)}return(s=e.validator.normalizeRules(e.extend({},e.validator.classRules(l),e.validator.attributeRules(l),e.validator.dataRules(l),e.validator.staticRules(l)),l)).required&&(o=s.required,delete s.required,s=e.extend({required:o},s),e(l).attr("aria-required","true")),s.remote&&(o=s.remote,delete s.remote,s=e.extend(s,{remote:o})),s}}),e.extend(e.expr[":"],{blank:function(t){return!e.trim(""+e(t).val())},filled:function(t){return!!e.trim(""+e(t).val())},unchecked:function(t){return!e(t).prop("checked")}}),e.validator=function(t,a){this.settings=e.extend(!0,{},e.validator.defaults,t),this.currentForm=a,this.init()},e.validator.format=function(t,a){return 1===arguments.length?function(){var a=e.makeArray(arguments);return a.unshift(t),e.validator.format.apply(this,a)}:(arguments.length>2&&a.constructor!==Array&&(a=e.makeArray(arguments).slice(1)),a.constructor!==Array&&(a=[a]),e.each(a,function(e,a){t=t.replace(new RegExp("\\{"+e+"\\}","g"),function(){return a})}),t)},e.extend(e.validator,{defaults:{messages:{},groups:{},rules:{},errorClass:"error",validClass:"valid",errorElement:"label",focusCleanup:!1,focusInvalid:!0,errorContainer:e([]),errorLabelContainer:e([]),onsubmit:!0,ignore:":hidden",ignoreTitle:!1,onfocusin:function(e){this.lastActive=e,this.settings.focusCleanup&&(this.settings.unhighlight&&this.settings.unhighlight.call(this,e,this.settings.errorClass,this.settings.validClass),this.hideThese(this.errorsFor(e)))},onfocusout:function(e){this.checkable(e)||!(e.name in this.submitted)&&this.optional(e)||this.element(e)},onkeyup:function(t,a){var i=[16,17,18,20,35,36,37,38,39,40,45,144,225];9===a.which&&""===this.elementValue(t)||-1!==e.inArray(a.keyCode,i)||(t.name in this.submitted||t===this.lastElement)&&this.element(t)},onclick:function(e){e.name in this.submitted?this.element(e):e.parentNode.name in this.submitted&&this.element(e.parentNode)},highlight:function(t,a,i){"radio"===t.type?this.findByName(t.name).addClass(a).removeClass(i):e(t).addClass(a).removeClass(i)},unhighlight:function(t,a,i){"radio"===t.type?this.findByName(t.name).removeClass(a).addClass(i):e(t).removeClass(a).addClass(i)}},setDefaults:function(t){e.extend(e.validator.defaults,t)},messages:{required:"This field is required.",remote:"Please fix this field.",email:"Please enter a valid email address.",url:"Please enter a valid URL.",date:"Please enter a valid date.",dateISO:"Please enter a valid date ( ISO ).",number:"Please enter a valid number.",digits:"Please enter only digits.",creditcard:"Please enter a valid credit card number.",equalTo:"Please enter the same value again.",maxlength:e.validator.format("Please enter no more than {0} characters."),minlength:e.validator.format("Please enter at least {0} characters."),rangelength:e.validator.format("Please enter a value between {0} and {1} characters long."),range:e.validator.format("Please enter a value between {0} and {1}."),max:e.validator.format("Please enter a value less than or equal to {0}."),min:e.validator.format("Please enter a value greater than or equal to {0}.")},autoCreateRanges:!1,prototype:{init:function(){function t(t){var a=e.data(this.form,"validator"),i="on"+t.type.replace(/^validate/,""),n=a.settings;n[i]&&!e(this).is(n.ignore)&&n[i].call(a,this,t)}this.labelContainer=e(this.settings.errorLabelContainer),this.errorContext=this.labelContainer.length&&this.labelContainer||e(this.currentForm),this.containers=e(this.settings.errorContainer).add(this.settings.errorLabelContainer),this.submitted={},this.valueCache={},this.pendingRequest=0,this.pending={},this.invalid={},this.reset();var a,i=this.groups={};e.each(this.settings.groups,function(t,a){"string"==typeof a&&(a=a.split(/\s/)),e.each(a,function(e,a){i[a]=t})}),a=this.settings.rules,e.each(a,function(t,i){a[t]=e.validator.normalizeRule(i)}),e(this.currentForm).on("focusin.validate focusout.validate keyup.validate",":text, [type='password'], [type='file'], select, textarea, [type='number'], [type='search'], [type='tel'], [type='url'], [type='email'], [type='datetime'], [type='date'], [type='month'], [type='week'], [type='time'], [type='datetime-local'], [type='range'], [type='color'], [type='radio'], [type='checkbox']",t).on("click.validate","select, option, [type='radio'], [type='checkbox']",t),this.settings.invalidHandler&&e(this.currentForm).on("invalid-form.validate",this.settings.invalidHandler),e(this.currentForm).find("[required], [data-rule-required], .required").attr("aria-required","true")},form:function(){return this.checkForm(),e.extend(this.submitted,this.errorMap),this.invalid=e.extend({},this.errorMap),this.valid()||e(this.currentForm).triggerHandler("invalid-form",[this]),this.showErrors(),this.valid()},checkForm:function(){this.prepareForm();for(var e=0,t=this.currentElements=this.elements();t[e];e++)this.check(t[e]);return this.valid()},element:function(t){var a=this.clean(t),i=this.validationTargetFor(a),n=!0;return this.lastElement=i,void 0===i?delete this.invalid[a.name]:(this.prepareElement(i),this.currentElements=e(i),(n=!1!==this.check(i))?delete this.invalid[i.name]:this.invalid[i.name]=!0),e(t).attr("aria-invalid",!n),this.numberOfInvalids()||(this.toHide=this.toHide.add(this.containers)),this.showErrors(),n},showErrors:function(t){if(t){e.extend(this.errorMap,t),this.errorList=[];for(var a in t)this.errorList.push({message:t[a],element:this.findByName(a)[0]});this.successList=e.grep(this.successList,function(e){return!(e.name in t)})}this.settings.showErrors?this.settings.showErrors.call(this,this.errorMap,this.errorList):this.defaultShowErrors()},resetForm:function(){e.fn.resetForm&&e(this.currentForm).resetForm(),this.submitted={},this.lastElement=null,this.prepareForm(),this.hideErrors();var t,a=this.elements().removeData("previousValue").removeAttr("aria-invalid");if(this.settings.unhighlight)for(t=0;a[t];t++)this.settings.unhighlight.call(this,a[t],this.settings.errorClass,"");else a.removeClass(this.settings.errorClass)},numberOfInvalids:function(){return this.objectLength(this.invalid)},objectLength:function(e){var t,a=0;for(t in e)a++;return a},hideErrors:function(){this.hideThese(this.toHide)},hideThese:function(e){e.not(this.containers).text(""),this.addWrapper(e).hide()},valid:function(){return 0===this.size()},size:function(){return this.errorList.length},focusInvalid:function(){if(this.settings.focusInvalid)try{e(this.findLastActive()||this.errorList.length&&this.errorList[0].element||[]).filter(":visible").focus().trigger("focusin")}catch(e){}},findLastActive:function(){var t=this.lastActive;return t&&1===e.grep(this.errorList,function(e){return e.element.name===t.name}).length&&t},elements:function(){var t=this,a={};return e(this.currentForm).find("input, select, textarea").not(":submit, :reset, :image, :disabled").not(this.settings.ignore).filter(function(){return!this.name&&t.settings.debug&&window.console&&console.error("%o has no name assigned",this),!(this.name in a||!t.objectLength(e(this).rules()))&&(a[this.name]=!0,!0)})},clean:function(t){return e(t)[0]},errors:function(){var t=this.settings.errorClass.split(" ").join(".");return e(this.settings.errorElement+"."+t,this.errorContext)},reset:function(){this.successList=[],this.errorList=[],this.errorMap={},this.toShow=e([]),this.toHide=e([]),this.currentElements=e([])},prepareForm:function(){this.reset(),this.toHide=this.errors().add(this.containers)},prepareElement:function(e){this.reset(),this.toHide=this.errorsFor(e)},elementValue:function(t){var a,i=e(t),n=t.type;return"radio"===n||"checkbox"===n?this.findByName(t.name).filter(":checked").val():"number"===n&&void 0!==t.validity?!t.validity.badInput&&i.val():"string"==typeof(a=i.val())?a.replace(/\r/g,""):a},check:function(t){t=this.validationTargetFor(this.clean(t));var a,i,n,r=e(t).rules(),s=e.map(r,function(e,t){return t}).length,o=!1,d=this.elementValue(t);for(i in r){n={method:i,parameters:r[i]};try{if("dependency-mismatch"===(a=e.validator.methods[i].call(this,d,t,n.parameters))&&1===s){o=!0;continue}if(o=!1,"pending"===a)return void(this.toHide=this.toHide.not(this.errorsFor(t)));if(!a)return this.formatAndAdd(t,n),!1}catch(e){throw this.settings.debug&&window.console&&console.log("Exception occurred when checking element "+t.id+", check the '"+n.method+"' method.",e),e instanceof TypeError&&(e.message+=".  Exception occurred when checking element "+t.id+", check the '"+n.method+"' method."),e}}if(!o)return this.objectLength(r)&&this.successList.push(t),!0},customDataMessage:function(t,a){return e(t).data("msg"+a.charAt(0).toUpperCase()+a.substring(1).toLowerCase())||e(t).data("msg")},customMessage:function(e,t){var a=this.settings.messages[e];return a&&(a.constructor===String?a:a[t])},findDefined:function(){for(var e=0;e<arguments.length;e++)if(void 0!==arguments[e])return arguments[e]},defaultMessage:function(t,a){return this.findDefined(this.customMessage(t.name,a),this.customDataMessage(t,a),!this.settings.ignoreTitle&&t.title||void 0,e.validator.messages[a],"<strong>Warning: No message defined for "+t.name+"</strong>")},formatAndAdd:function(t,a){var i=this.defaultMessage(t,a.method),n=/\$?\{(\d+)\}/g;"function"==typeof i?i=i.call(this,a.parameters,t):n.test(i)&&(i=e.validator.format(i.replace(n,"{$1}"),a.parameters)),this.errorList.push({message:i,element:t,method:a.method}),this.errorMap[t.name]=i,this.submitted[t.name]=i},addWrapper:function(e){return this.settings.wrapper&&(e=e.add(e.parent(this.settings.wrapper))),e},defaultShowErrors:function(){var e,t,a;for(e=0;this.errorList[e];e++)a=this.errorList[e],this.settings.highlight&&this.settings.highlight.call(this,a.element,this.settings.errorClass,this.settings.validClass),this.showLabel(a.element,a.message);if(this.errorList.length&&(this.toShow=this.toShow.add(this.containers)),this.settings.success)for(e=0;this.successList[e];e++)this.showLabel(this.successList[e]);if(this.settings.unhighlight)for(e=0,t=this.validElements();t[e];e++)this.settings.unhighlight.call(this,t[e],this.settings.errorClass,this.settings.validClass);this.toHide=this.toHide.not(this.toShow),this.hideErrors(),this.addWrapper(this.toShow).show()},validElements:function(){return this.currentElements.not(this.invalidElements())},invalidElements:function(){return e(this.errorList).map(function(){return this.element})},showLabel:function(t,a){var i,n,r,s=this.errorsFor(t),o=this.idOrName(t),d=e(t).attr("aria-describedby");s.length?(s.removeClass(this.settings.validClass).addClass(this.settings.errorClass),s.html(a)):(s=e("<"+this.settings.errorElement+">").attr("id",o+"-error").addClass(this.settings.errorClass).html(a||""),i=s,this.settings.wrapper&&(i=s.hide().show().wrap("<"+this.settings.wrapper+"/>").parent()),this.labelContainer.length?this.labelContainer.append(i):this.settings.errorPlacement?this.settings.errorPlacement(i,e(t)):i.insertAfter(t),s.is("label")?s.attr("for",o):0===s.parents("label[for='"+o+"']").length&&(r=s.attr("id").replace(/(:|\.|\[|\]|\$)/g,"\\$1"),d?d.match(new RegExp("\\b"+r+"\\b"))||(d+=" "+r):d=r,e(t).attr("aria-describedby",d),(n=this.groups[t.name])&&e.each(this.groups,function(t,a){a===n&&e("[name='"+t+"']",this.currentForm).attr("aria-describedby",s.attr("id"))}))),!a&&this.settings.success&&(s.text(""),"string"==typeof this.settings.success?s.addClass(this.settings.success):this.settings.success(s,t)),this.toShow=this.toShow.add(s)},errorsFor:function(t){var a=this.idOrName(t),i=e(t).attr("aria-describedby"),n="label[for='"+a+"'], label[for='"+a+"'] *";return i&&(n=n+", #"+i.replace(/\s+/g,", #")),this.errors().filter(n)},idOrName:function(e){return this.groups[e.name]||(this.checkable(e)?e.name:e.id||e.name)},validationTargetFor:function(t){return this.checkable(t)&&(t=this.findByName(t.name)),e(t).not(this.settings.ignore)[0]},checkable:function(e){return/radio|checkbox/i.test(e.type)},findByName:function(t){return e(this.currentForm).find("[name='"+t+"']")},getLength:function(t,a){switch(a.nodeName.toLowerCase()){case"select":return e("option:selected",a).length;case"input":if(this.checkable(a))return this.findByName(a.name).filter(":checked").length}return t.length},depend:function(e,t){return!this.dependTypes[typeof e]||this.dependTypes[typeof e](e,t)},dependTypes:{boolean:function(e){return e},string:function(t,a){return!!e(t,a.form).length},function:function(e,t){return e(t)}},optional:function(t){var a=this.elementValue(t);return!e.validator.methods.required.call(this,a,t)&&"dependency-mismatch"},startRequest:function(e){this.pending[e.name]||(this.pendingRequest++,this.pending[e.name]=!0)},stopRequest:function(t,a){--this.pendingRequest<0&&(this.pendingRequest=0),delete this.pending[t.name],a&&0===this.pendingRequest&&this.formSubmitted&&this.form()?(e(this.currentForm).submit(),this.formSubmitted=!1):!a&&0===this.pendingRequest&&this.formSubmitted&&(e(this.currentForm).triggerHandler("invalid-form",[this]),this.formSubmitted=!1)},previousValue:function(t){return e.data(t,"previousValue")||e.data(t,"previousValue",{old:null,valid:!0,message:this.defaultMessage(t,"remote")})},destroy:function(){this.resetForm(),e(this.currentForm).off(".validate").removeData("validator")}},classRuleSettings:{required:{required:!0},email:{email:!0},url:{url:!0},date:{date:!0},dateISO:{dateISO:!0},number:{number:!0},digits:{digits:!0},creditcard:{creditcard:!0}},addClassRules:function(t,a){t.constructor===String?this.classRuleSettings[t]=a:e.extend(this.classRuleSettings,t)},classRules:function(t){var a={},i=e(t).attr("class");return i&&e.each(i.split(" "),function(){this in e.validator.classRuleSettings&&e.extend(a,e.validator.classRuleSettings[this])}),a},normalizeAttributeRule:function(e,t,a,i){/min|max/.test(a)&&(null===t||/number|range|text/.test(t))&&(i=Number(i),isNaN(i)&&(i=void 0)),i||0===i?e[a]=i:t===a&&"range"!==t&&(e[a]=!0)},attributeRules:function(t){var a,i,n={},r=e(t),s=t.getAttribute("type");for(a in e.validator.methods)"required"===a?(""===(i=t.getAttribute(a))&&(i=!0),i=!!i):i=r.attr(a),this.normalizeAttributeRule(n,s,a,i);return n.maxlength&&/-1|2147483647|524288/.test(n.maxlength)&&delete n.maxlength,n},dataRules:function(t){var a,i,n={},r=e(t),s=t.getAttribute("type");for(a in e.validator.methods)i=r.data("rule"+a.charAt(0).toUpperCase()+a.substring(1).toLowerCase()),this.normalizeAttributeRule(n,s,a,i);return n},staticRules:function(t){var a={},i=e.data(t.form,"validator");return i.settings.rules&&(a=e.validator.normalizeRule(i.settings.rules[t.name])||{}),a},normalizeRules:function(t,a){return e.each(t,function(i,n){if(!1!==n){if(n.param||n.depends){var r=!0;switch(typeof n.depends){case"string":r=!!e(n.depends,a.form).length;break;case"function":r=n.depends.call(a,a)}r?t[i]=void 0===n.param||n.param:delete t[i]}}else delete t[i]}),e.each(t,function(i,n){t[i]=e.isFunction(n)?n(a):n}),e.each(["minlength","maxlength"],function(){t[this]&&(t[this]=Number(t[this]))}),e.each(["rangelength","range"],function(){var a;t[this]&&(e.isArray(t[this])?t[this]=[Number(t[this][0]),Number(t[this][1])]:"string"==typeof t[this]&&(a=t[this].replace(/[\[\]]/g,"").split(/[\s,]+/),t[this]=[Number(a[0]),Number(a[1])]))}),e.validator.autoCreateRanges&&(null!=t.min&&null!=t.max&&(t.range=[t.min,t.max],delete t.min,delete t.max),null!=t.minlength&&null!=t.maxlength&&(t.rangelength=[t.minlength,t.maxlength],delete t.minlength,delete t.maxlength)),t},normalizeRule:function(t){if("string"==typeof t){var a={};e.each(t.split(/\s/),function(){a[this]=!0}),t=a}return t},addMethod:function(t,a,i){e.validator.methods[t]=a,e.validator.messages[t]=void 0!==i?i:e.validator.messages[t],a.length<3&&e.validator.addClassRules(t,e.validator.normalizeRule(t))},methods:{required:function(t,a,i){if(!this.depend(i,a))return"dependency-mismatch";if("select"===a.nodeName.toLowerCase()){var n=e(a).val();return n&&n.length>0}return this.checkable(a)?this.getLength(t,a)>0:t.length>0},email:function(e,t){return this.optional(t)||/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)},url:function(e,t){return this.optional(t)||/^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})).?)(?::\d{2,5})?(?:[/?#]\S*)?$/i.test(e)},date:function(e,t){return this.optional(t)||!/Invalid|NaN/.test(new Date(e).toString())},dateISO:function(e,t){return this.optional(t)||/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)},number:function(e,t){return this.optional(t)||/^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(e)},digits:function(e,t){return this.optional(t)||/^\d+$/.test(e)},creditcard:function(e,t){if(this.optional(t))return"dependency-mismatch";if(/[^0-9 \-]+/.test(e))return!1;var a,i,n=0,r=0,s=!1;if((e=e.replace(/\D/g,"")).length<13||e.length>19)return!1;for(a=e.length-1;a>=0;a--)i=e.charAt(a),r=parseInt(i,10),s&&(r*=2)>9&&(r-=9),n+=r,s=!s;return n%10==0},minlength:function(t,a,i){var n=e.isArray(t)?t.length:this.getLength(t,a);return this.optional(a)||n>=i},maxlength:function(t,a,i){var n=e.isArray(t)?t.length:this.getLength(t,a);return this.optional(a)||i>=n},rangelength:function(t,a,i){var n=e.isArray(t)?t.length:this.getLength(t,a);return this.optional(a)||n>=i[0]&&n<=i[1]},min:function(e,t,a){return this.optional(t)||e>=a},max:function(e,t,a){return this.optional(t)||a>=e},range:function(e,t,a){return this.optional(t)||e>=a[0]&&e<=a[1]},equalTo:function(t,a,i){var n=e(i);return this.settings.onfocusout&&n.off(".validate-equalTo").on("blur.validate-equalTo",function(){e(a).valid()}),t===n.val()},remote:function(t,a,i){if(this.optional(a))return"dependency-mismatch";var n,r,s=this.previousValue(a);return this.settings.messages[a.name]||(this.settings.messages[a.name]={}),s.originalMessage=this.settings.messages[a.name].remote,this.settings.messages[a.name].remote=s.message,i="string"==typeof i&&{url:i}||i,s.old===t?s.valid:(s.old=t,n=this,this.startRequest(a),r={},r[a.name]=t,e.ajax(e.extend(!0,{mode:"abort",port:"validate"+a.name,dataType:"json",data:r,context:n.currentForm,success:function(i){var r,o,d,l=!0===i||"true"===i;n.settings.messages[a.name].remote=s.originalMessage,l?(d=n.formSubmitted,n.prepareElement(a),n.formSubmitted=d,n.successList.push(a),delete n.invalid[a.name],n.showErrors()):(r={},o=i||n.defaultMessage(a,"remote"),r[a.name]=s.message=e.isFunction(o)?o(t):o,n.invalid[a.name]=!0,n.showErrors(r)),s.valid=l,n.stopRequest(a,l)}},i)),"pending")}}});var t,a={};e.ajaxPrefilter?e.ajaxPrefilter(function(e,t,i){var n=e.port;"abort"===e.mode&&(a[n]&&a[n].abort(),a[n]=i)}):(t=e.ajax,e.ajax=function(i){var n=("mode"in i?i:e.ajaxSettings).mode,r=("port"in i?i:e.ajaxSettings).port;return"abort"===n?(a[r]&&a[r].abort(),a[r]=t.apply(this,arguments),a[r]):t.apply(this,arguments)})}),function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.validate.min"],e):e(jQuery)}(function(e){!function(){function t(e){return e.replace(/<.[^<>]*?>/g," ").replace(/&nbsp;|&#160;/gi," ").replace(/[.(),;:!?%#$'\"_+=\/\-“”’]*/g,"")}e.validator.addMethod("maxWords",function(e,a,i){return this.optional(a)||t(e).match(/\b\w+\b/g).length<=i},e.validator.format("Please enter {0} words or less.")),e.validator.addMethod("minWords",function(e,a,i){return this.optional(a)||t(e).match(/\b\w+\b/g).length>=i},e.validator.format("Please enter at least {0} words.")),e.validator.addMethod("rangeWords",function(e,a,i){var n=t(e),r=/\b\w+\b/g;return this.optional(a)||n.match(r).length>=i[0]&&n.match(r).length<=i[1]},e.validator.format("Please enter between {0} and {1} words."))}(),e.validator.addMethod("accept",function(t,a,i){var n,r="string"==typeof i?i.replace(/\s/g,"").replace(/,/g,"|"):"image/*",s=this.optional(a);if(s)return s;if("file"===e(a).attr("type")&&(r=r.replace(/\*/g,".*"),a.files&&a.files.length))for(n=0;n<a.files.length;n++)if(!a.files[n].type.match(new RegExp("\\.?("+r+")$","i")))return!1;return!0},e.validator.format("Please enter a value with a valid mimetype.")),e.validator.addMethod("alphanumeric",function(e,t){return this.optional(t)||/^\w+$/i.test(e)},"Letters, numbers, and underscores only please"),e.validator.addMethod("bankaccountNL",function(e,t){if(this.optional(t))return!0;if(!/^[0-9]{9}|([0-9]{2} ){3}[0-9]{3}$/.test(e))return!1;var a,i,n,r=e.replace(/ /g,""),s=0,o=r.length;for(a=0;o>a;a++)i=o-a,n=r.substring(a,a+1),s+=i*n;return s%11==0},"Please specify a valid bank account number"),e.validator.addMethod("bankorgiroaccountNL",function(t,a){return this.optional(a)||e.validator.methods.bankaccountNL.call(this,t,a)||e.validator.methods.giroaccountNL.call(this,t,a)},"Please specify a valid bank or giro account number"),e.validator.addMethod("bic",function(e,t){return this.optional(t)||/^([A-Z]{6}[A-Z2-9][A-NP-Z1-2])(X{3}|[A-WY-Z0-9][A-Z0-9]{2})?$/.test(e)},"Please specify a valid BIC code"),e.validator.addMethod("cifES",function(e){"use strict";var t,a,i,n,r,s,o=[];if(!(e=e.toUpperCase()).match("((^[A-Z]{1}[0-9]{7}[A-Z0-9]{1}$|^[T]{1}[A-Z0-9]{8}$)|^[0-9]{8}[A-Z]{1}$)"))return!1;for(i=0;9>i;i++)o[i]=parseInt(e.charAt(i),10);for(a=o[2]+o[4]+o[6],n=1;8>n;n+=2)r=(2*o[n]).toString(),s=r.charAt(1),a+=parseInt(r.charAt(0),10)+(""===s?0:parseInt(s,10));return!!/^[ABCDEFGHJNPQRSUVW]{1}/.test(e)&&(a+="",t=10-parseInt(a.charAt(a.length-1),10),e+=t,o[8].toString()===String.fromCharCode(64+t)||o[8].toString()===e.charAt(e.length-1))},"Please specify a valid CIF number."),e.validator.addMethod("cpfBR",function(e){if(11!==(e=e.replace(/([~!@#$%^&*()_+=`{}\[\]\-|\\:;'<>,.\/? ])+/g,"")).length)return!1;var t,a,i,n,r=0;if(t=parseInt(e.substring(9,10),10),a=parseInt(e.substring(10,11),10),i=function(e,t){var a=10*e%11;return(10===a||11===a)&&(a=0),a===t},""===e||"00000000000"===e||"11111111111"===e||"22222222222"===e||"33333333333"===e||"44444444444"===e||"55555555555"===e||"66666666666"===e||"77777777777"===e||"88888888888"===e||"99999999999"===e)return!1;for(n=1;9>=n;n++)r+=parseInt(e.substring(n-1,n),10)*(11-n);if(i(r,t)){for(r=0,n=1;10>=n;n++)r+=parseInt(e.substring(n-1,n),10)*(12-n);return i(r,a)}return!1},"Please specify a valid CPF number"),e.validator.addMethod("creditcardtypes",function(e,t,a){if(/[^0-9\-]+/.test(e))return!1;e=e.replace(/\D/g,"");var i=0;return a.mastercard&&(i|=1),a.visa&&(i|=2),a.amex&&(i|=4),a.dinersclub&&(i|=8),a.enroute&&(i|=16),a.discover&&(i|=32),a.jcb&&(i|=64),a.unknown&&(i|=128),a.all&&(i=255),1&i&&/^(5[12345])/.test(e)?16===e.length:2&i&&/^(4)/.test(e)?16===e.length:4&i&&/^(3[47])/.test(e)?15===e.length:8&i&&/^(3(0[012345]|[68]))/.test(e)?14===e.length:16&i&&/^(2(014|149))/.test(e)?15===e.length:32&i&&/^(6011)/.test(e)?16===e.length:64&i&&/^(3)/.test(e)?16===e.length:64&i&&/^(2131|1800)/.test(e)?15===e.length:!!(128&i)},"Please enter a valid credit card number."),e.validator.addMethod("currency",function(e,t,a){var i,n="string"==typeof a,r=n?a:a[0],s=!!n||a[1];return r=r.replace(/,/g,""),r=s?r+"]":r+"]?",i="^["+r+"([1-9]{1}[0-9]{0,2}(\\,[0-9]{3})*(\\.[0-9]{0,2})?|[1-9]{1}[0-9]{0,}(\\.[0-9]{0,2})?|0(\\.[0-9]{0,2})?|(\\.[0-9]{1,2})?)$",i=new RegExp(i),this.optional(t)||i.test(e)},"Please specify a valid currency"),e.validator.addMethod("dateFA",function(e,t){return this.optional(t)||/^[1-4]\d{3}\/((0?[1-6]\/((3[0-1])|([1-2][0-9])|(0?[1-9])))|((1[0-2]|(0?[7-9]))\/(30|([1-2][0-9])|(0?[1-9]))))$/.test(e)},e.validator.messages.date),e.validator.addMethod("dateITA",function(e,t){var a,i,n,r,s,o=!1;return/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(e)?(a=e.split("/"),i=parseInt(a[0],10),n=parseInt(a[1],10),r=parseInt(a[2],10),s=new Date(Date.UTC(r,n-1,i,12,0,0,0)),o=s.getUTCFullYear()===r&&s.getUTCMonth()===n-1&&s.getUTCDate()===i):o=!1,this.optional(t)||o},e.validator.messages.date),e.validator.addMethod("dateNL",function(e,t){return this.optional(t)||/^(0?[1-9]|[12]\d|3[01])[\.\/\-](0?[1-9]|1[012])[\.\/\-]([12]\d)?(\d\d)$/.test(e)},e.validator.messages.date),e.validator.addMethod("extension",function(e,t,a){return a="string"==typeof a?a.replace(/,/g,"|"):"png|jpe?g|gif",this.optional(t)||e.match(new RegExp("\\.("+a+")$","i"))},e.validator.format("Please enter a value with a valid extension.")),e.validator.addMethod("giroaccountNL",function(e,t){return this.optional(t)||/^[0-9]{1,7}$/.test(e)},"Please specify a valid giro account number"),e.validator.addMethod("iban",function(e,t){if(this.optional(t))return!0;var a,i,n,r,s,o,d,l,u=e.replace(/ /g,"").toUpperCase(),c="",h=!0,f="",m="";if(a=u.substring(0,2),o={AL:"\\d{8}[\\dA-Z]{16}",AD:"\\d{8}[\\dA-Z]{12}",AT:"\\d{16}",AZ:"[\\dA-Z]{4}\\d{20}",BE:"\\d{12}",BH:"[A-Z]{4}[\\dA-Z]{14}",BA:"\\d{16}",BR:"\\d{23}[A-Z][\\dA-Z]",BG:"[A-Z]{4}\\d{6}[\\dA-Z]{8}",CR:"\\d{17}",HR:"\\d{17}",CY:"\\d{8}[\\dA-Z]{16}",CZ:"\\d{20}",DK:"\\d{14}",DO:"[A-Z]{4}\\d{20}",EE:"\\d{16}",FO:"\\d{14}",FI:"\\d{14}",FR:"\\d{10}[\\dA-Z]{11}\\d{2}",GE:"[\\dA-Z]{2}\\d{16}",DE:"\\d{18}",GI:"[A-Z]{4}[\\dA-Z]{15}",GR:"\\d{7}[\\dA-Z]{16}",GL:"\\d{14}",GT:"[\\dA-Z]{4}[\\dA-Z]{20}",HU:"\\d{24}",IS:"\\d{22}",IE:"[\\dA-Z]{4}\\d{14}",IL:"\\d{19}",IT:"[A-Z]\\d{10}[\\dA-Z]{12}",KZ:"\\d{3}[\\dA-Z]{13}",KW:"[A-Z]{4}[\\dA-Z]{22}",LV:"[A-Z]{4}[\\dA-Z]{13}",LB:"\\d{4}[\\dA-Z]{20}",LI:"\\d{5}[\\dA-Z]{12}",LT:"\\d{16}",LU:"\\d{3}[\\dA-Z]{13}",MK:"\\d{3}[\\dA-Z]{10}\\d{2}",MT:"[A-Z]{4}\\d{5}[\\dA-Z]{18}",MR:"\\d{23}",MU:"[A-Z]{4}\\d{19}[A-Z]{3}",MC:"\\d{10}[\\dA-Z]{11}\\d{2}",MD:"[\\dA-Z]{2}\\d{18}",ME:"\\d{18}",NL:"[A-Z]{4}\\d{10}",NO:"\\d{11}",PK:"[\\dA-Z]{4}\\d{16}",PS:"[\\dA-Z]{4}\\d{21}",PL:"\\d{24}",PT:"\\d{21}",RO:"[A-Z]{4}[\\dA-Z]{16}",SM:"[A-Z]\\d{10}[\\dA-Z]{12}",SA:"\\d{2}[\\dA-Z]{18}",RS:"\\d{18}",SK:"\\d{20}",SI:"\\d{15}",ES:"\\d{20}",SE:"\\d{20}",CH:"\\d{5}[\\dA-Z]{12}",TN:"\\d{20}",TR:"\\d{5}[\\dA-Z]{17}",AE:"\\d{3}\\d{16}",GB:"[A-Z]{4}\\d{14}",VG:"[\\dA-Z]{4}\\d{16}"},void 0!==(s=o[a])&&!new RegExp("^[A-Z]{2}\\d{2}"+s+"$","").test(u))return!1;for(i=u.substring(4,u.length)+u.substring(0,4),d=0;d<i.length;d++)"0"!==(n=i.charAt(d))&&(h=!1),h||(c+="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ".indexOf(n));for(l=0;l<c.length;l++)r=c.charAt(l),m=""+f+r,f=m%97;return 1===f},"Please specify a valid IBAN"),e.validator.addMethod("integer",function(e,t){return this.optional(t)||/^-?\d+$/.test(e)},"A positive or negative non-decimal number please"),e.validator.addMethod("ipv4",function(e,t){return this.optional(t)||/^(25[0-5]|2[0-4]\d|[01]?\d\d?)\.(25[0-5]|2[0-4]\d|[01]?\d\d?)\.(25[0-5]|2[0-4]\d|[01]?\d\d?)\.(25[0-5]|2[0-4]\d|[01]?\d\d?)$/i.test(e)},"Please enter a valid IP v4 address."),e.validator.addMethod("ipv6",function(e,t){return this.optional(t)||/^((([0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){0,5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))$/i.test(e)},"Please enter a valid IP v6 address."),e.validator.addMethod("lettersonly",function(e,t){return this.optional(t)||/^[a-z]+$/i.test(e)},"Letters only please"),e.validator.addMethod("letterswithbasicpunc",function(e,t){return this.optional(t)||/^[a-z\-.,()'"\s]+$/i.test(e)},"Letters or punctuation only please"),e.validator.addMethod("mobileNL",function(e,t){return this.optional(t)||/^((\+|00(\s|\s?\-\s?)?)31(\s|\s?\-\s?)?(\(0\)[\-\s]?)?|0)6((\s|\s?\-\s?)?[0-9]){8}$/.test(e)},"Please specify a valid mobile number"),e.validator.addMethod("mobileUK",function(e,t){return e=e.replace(/\(|\)|\s+|-/g,""),this.optional(t)||e.length>9&&e.match(/^(?:(?:(?:00\s?|\+)44\s?|0)7(?:[1345789]\d{2}|624)\s?\d{3}\s?\d{3})$/)},"Please specify a valid mobile number"),e.validator.addMethod("nieES",function(e){"use strict";return!!(e=e.toUpperCase()).match("((^[A-Z]{1}[0-9]{7}[A-Z0-9]{1}$|^[T]{1}[A-Z0-9]{8}$)|^[0-9]{8}[A-Z]{1}$)")&&(/^[T]{1}/.test(e)?e[8]===/^[T]{1}[A-Z0-9]{8}$/.test(e):!!/^[XYZ]{1}/.test(e)&&e[8]==="TRWAGMYFPDXBNJZSQVHLCKE".charAt(e.replace("X","0").replace("Y","1").replace("Z","2").substring(0,8)%23))},"Please specify a valid NIE number."),e.validator.addMethod("nifES",function(e){"use strict";return!!(e=e.toUpperCase()).match("((^[A-Z]{1}[0-9]{7}[A-Z0-9]{1}$|^[T]{1}[A-Z0-9]{8}$)|^[0-9]{8}[A-Z]{1}$)")&&(/^[0-9]{8}[A-Z]{1}$/.test(e)?"TRWAGMYFPDXBNJZSQVHLCKE".charAt(e.substring(8,0)%23)===e.charAt(8):!!/^[KLM]{1}/.test(e)&&e[8]===String.fromCharCode(64))},"Please specify a valid NIF number."),jQuery.validator.addMethod("notEqualTo",function(t,a,i){return this.optional(a)||!e.validator.methods.equalTo.call(this,t,a,i)},"Please enter a different value, values must not be the same."),e.validator.addMethod("nowhitespace",function(e,t){return this.optional(t)||/^\S+$/i.test(e)},"No white space please"),e.validator.addMethod("pattern",function(e,t,a){return!!this.optional(t)||("string"==typeof a&&(a=new RegExp("^(?:"+a+")$")),a.test(e))},"Invalid format."),e.validator.addMethod("phoneNL",function(e,t){return this.optional(t)||/^((\+|00(\s|\s?\-\s?)?)31(\s|\s?\-\s?)?(\(0\)[\-\s]?)?|0)[1-9]((\s|\s?\-\s?)?[0-9]){8}$/.test(e)},"Please specify a valid phone number."),e.validator.addMethod("phoneUK",function(e,t){return e=e.replace(/\(|\)|\s+|-/g,""),this.optional(t)||e.length>9&&e.match(/^(?:(?:(?:00\s?|\+)44\s?)|(?:\(?0))(?:\d{2}\)?\s?\d{4}\s?\d{4}|\d{3}\)?\s?\d{3}\s?\d{3,4}|\d{4}\)?\s?(?:\d{5}|\d{3}\s?\d{3})|\d{5}\)?\s?\d{4,5})$/)},"Please specify a valid phone number"),e.validator.addMethod("phoneUS",function(e,t){return e=e.replace(/\s+/g,""),this.optional(t)||e.length>9&&e.match(/^(\+?1-?)?(\([2-9]([02-9]\d|1[02-9])\)|[2-9]([02-9]\d|1[02-9]))-?[2-9]([02-9]\d|1[02-9])-?\d{4}$/)},"Please specify a valid phone number"),e.validator.addMethod("phonesUK",function(e,t){return e=e.replace(/\(|\)|\s+|-/g,""),this.optional(t)||e.length>9&&e.match(/^(?:(?:(?:00\s?|\+)44\s?|0)(?:1\d{8,9}|[23]\d{9}|7(?:[1345789]\d{8}|624\d{6})))$/)},"Please specify a valid uk phone number"),e.validator.addMethod("postalCodeCA",function(e,t){return this.optional(t)||/^[ABCEGHJKLMNPRSTVXY]\d[A-Z] \d[A-Z]\d$/.test(e)},"Please specify a valid postal code"),e.validator.addMethod("postalcodeBR",function(e,t){return this.optional(t)||/^\d{2}.\d{3}-\d{3}?$|^\d{5}-?\d{3}?$/.test(e)},"Informe um CEP válido."),e.validator.addMethod("postalcodeIT",function(e,t){return this.optional(t)||/^\d{5}$/.test(e)},"Please specify a valid postal code"),e.validator.addMethod("postalcodeNL",function(e,t){return this.optional(t)||/^[1-9][0-9]{3}\s?[a-zA-Z]{2}$/.test(e)},"Please specify a valid postal code"),e.validator.addMethod("postcodeUK",function(e,t){return this.optional(t)||/^((([A-PR-UWYZ][0-9])|([A-PR-UWYZ][0-9][0-9])|([A-PR-UWYZ][A-HK-Y][0-9])|([A-PR-UWYZ][A-HK-Y][0-9][0-9])|([A-PR-UWYZ][0-9][A-HJKSTUW])|([A-PR-UWYZ][A-HK-Y][0-9][ABEHMNPRVWXY]))\s?([0-9][ABD-HJLNP-UW-Z]{2})|(GIR)\s?(0AA))$/i.test(e)},"Please specify a valid UK postcode"),e.validator.addMethod("require_from_group",function(t,a,i){var n=e(i[1],a.form),r=n.eq(0),s=r.data("valid_req_grp")?r.data("valid_req_grp"):e.extend({},this),o=n.filter(function(){return s.elementValue(this)}).length>=i[0];return r.data("valid_req_grp",s),e(a).data("being_validated")||(n.data("being_validated",!0),n.each(function(){s.element(this)}),n.data("being_validated",!1)),o},e.validator.format("Please fill at least {0} of these fields.")),e.validator.addMethod("skip_or_fill_minimum",function(t,a,i){var n=e(i[1],a.form),r=n.eq(0),s=r.data("valid_skip")?r.data("valid_skip"):e.extend({},this),o=n.filter(function(){return s.elementValue(this)}).length,d=0===o||o>=i[0];return r.data("valid_skip",s),e(a).data("being_validated")||(n.data("being_validated",!0),n.each(function(){s.element(this)}),n.data("being_validated",!1)),d},e.validator.format("Please either skip these fields or fill at least {0} of them.")),e.validator.addMethod("stateUS",function(e,t,a){var i,n=void 0===a,r=!n&&void 0!==a.caseSensitive&&a.caseSensitive,s=!n&&void 0!==a.includeTerritories&&a.includeTerritories,o=!n&&void 0!==a.includeMilitary&&a.includeMilitary;return i=s||o?s&&o?"^(A[AEKLPRSZ]|C[AOT]|D[CE]|FL|G[AU]|HI|I[ADLN]|K[SY]|LA|M[ADEINOPST]|N[CDEHJMVY]|O[HKR]|P[AR]|RI|S[CD]|T[NX]|UT|V[AIT]|W[AIVY])$":s?"^(A[KLRSZ]|C[AOT]|D[CE]|FL|G[AU]|HI|I[ADLN]|K[SY]|LA|M[ADEINOPST]|N[CDEHJMVY]|O[HKR]|P[AR]|RI|S[CD]|T[NX]|UT|V[AIT]|W[AIVY])$":"^(A[AEKLPRZ]|C[AOT]|D[CE]|FL|GA|HI|I[ADLN]|K[SY]|LA|M[ADEINOST]|N[CDEHJMVY]|O[HKR]|PA|RI|S[CD]|T[NX]|UT|V[AT]|W[AIVY])$":"^(A[KLRZ]|C[AOT]|D[CE]|FL|GA|HI|I[ADLN]|K[SY]|LA|M[ADEINOST]|N[CDEHJMVY]|O[HKR]|PA|RI|S[CD]|T[NX]|UT|V[AT]|W[AIVY])$",i=r?new RegExp(i):new RegExp(i,"i"),this.optional(t)||i.test(e)},"Please specify a valid state"),e.validator.addMethod("strippedminlength",function(t,a,i){return e(t).text().length>=i},e.validator.format("Please enter at least {0} characters")),e.validator.addMethod("time",function(e,t){return this.optional(t)||/^([01]\d|2[0-3]|[0-9])(:[0-5]\d){1,2}$/.test(e)},"Please enter a valid time, between 00:00 and 23:59"),e.validator.addMethod("time12h",function(e,t){return this.optional(t)||/^((0?[1-9]|1[012])(:[0-5]\d){1,2}(\ ?[AP]M))$/i.test(e)},"Please enter a valid time in 12-hour am/pm format"),e.validator.addMethod("url2",function(e,t){return this.optional(t)||/^(https?|ftp):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)*(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i.test(e)},e.validator.messages.url),e.validator.addMethod("vinUS",function(e){if(17!==e.length)return!1;var t,a,i,n,r,s,o=["A","B","C","D","E","F","G","H","J","K","L","M","N","P","R","S","T","U","V","W","X","Y","Z"],d=[1,2,3,4,5,6,7,8,1,2,3,4,5,7,9,2,3,4,5,6,7,8,9],l=[8,7,6,5,4,3,2,10,0,9,8,7,6,5,4,3,2],u=0;for(t=0;17>t;t++){if(n=l[t],i=e.slice(t,t+1),8===t&&(s=i),isNaN(i)){for(a=0;a<o.length;a++)if(i.toUpperCase()===o[a]){i=d[a],i*=n,isNaN(s)&&8===a&&(s=o[a]);break}}else i*=n;u+=i}return 10===(r=u%11)&&(r="X"),r===s},"The specified vehicle identification number (VIN) is invalid."),e.validator.addMethod("zipcodeUS",function(e,t){return this.optional(t)||/^\d{5}(-\d{4})?$/.test(e)},"The specified US ZIP Code is invalid"),e.validator.addMethod("ziprange",function(e,t){return this.optional(t)||/^90[2-5]\d\{2\}-\d{4}$/.test(e)},"Your ZIP-code must be in the range 902xx-xxxx to 905xx-xxxx")}),function(e){"function"==typeof define&&define.amd?define(["jquery","../jquery.validate.min"],e):e(jQuery)}(function(e){e.extend(e.validator.messages,{required:"Este campo &eacute; requerido.",remote:"Por favor, corrija este campo.",email:"Por favor, forne&ccedil;a um endere&ccedil;o de email v&aacute;lido.",url:"Por favor, forne&ccedil;a uma URL v&aacute;lida.",date:"Por favor, forne&ccedil;a uma data v&aacute;lida.",dateISO:"Por favor, forne&ccedil;a uma data v&aacute;lida (ISO).",number:"Por favor, forne&ccedil;a um n&uacute;mero v&aacute;lido.",digits:"Por favor, forne&ccedil;a somente d&iacute;gitos.",creditcard:"Por favor, forne&ccedil;a um cart&atilde;o de cr&eacute;dito v&aacute;lido.",equalTo:"Por favor, forne&ccedil;a o mesmo valor novamente.",extension:"Por favor, forne&ccedil;a um valor com uma extens&atilde;o v&aacute;lida.",maxlength:e.validator.format("Por favor, forne&ccedil;a n&atilde;o mais que {0} caracteres."),minlength:e.validator.format("Por favor, forne&ccedil;a ao menos {0} caracteres."),rangelength:e.validator.format("Por favor, forne&ccedil;a um valor entre {0} e {1} caracteres de comprimento."),range:e.validator.format("Por favor, forne&ccedil;a um valor entre {0} e {1}."),max:e.validator.format("Por favor, forne&ccedil;a um valor menor ou igual a {0}."),min:e.validator.format("Por favor, forne&ccedil;a um valor maior ou igual a {0}."),nifES:"Por favor, forne&ccedil;a um NIF v&aacute;lido.",nieES:"Por favor, forne&ccedil;a um NIE v&aacute;lido.",cifEE:"Por favor, forne&ccedil;a um CIF v&aacute;lido.",postalcodeBR:"Por favor, forne&ccedil;a um CEP v&aacute;lido.",cpfBR:"Por favor, forne&ccedil;a um CPF v&aacute;lido."})}),function(e,t){"use strict";e.fn.formSuccess=function(){e(".validation-summary-success").slideDown()},e.fn.formError=function(){e(".validation-summary-errors").slideDown(),e("html, body").animate({scrollTop:e(".site-header").offset().top})},e.fn.placeholder=function(){e("[placeholder]").focus(function(){var t;(t=e(this)).val()===t.attr("placeholder")&&(t.val(""),t.removeClass("placeholder"))}).blur(function(){var t;""!==(t=e(this)).val()&&t.val()!==t.attr("placeholder")||(t.addClass("placeholder"),t.val(t.attr("placeholder")))}).blur(),e("[placeholder]").parents("form").submit(function(){e(this).find("[placeholder]").each(function(){var t;(t=e(this)).val()===t.attr("placeholder")&&t.val("")})})},e.fn.verifyCheckbox=function(){e("input.outros:checked").length>=1&&e("input.outros:checked").parent().find(".hidden").show()},e.fn.verifyChangePassword=function(){e("body.cadastro-completo").length>=1&&(e("#cbAlterarSenha").attr("checked")?(e("#cbAlterarSenha").parent().next().show(),e("#cbAlterarSenha").parent().next().next().show()):(e("#cbAlterarSenha").parent().next().hide(),e("#cbAlterarSenha").parent().next().next().hide()))},e.fn.maskInput=function(){e("input.cpf, input#cpf").mask("99999999999"),e("input.rg, input#rg").mask("99999999*"),e("input.cnpj, input#cnpj").mask("99999999999999"),e("input.telefone").mask("(99)9999-9999?9"),e("input.celular, input#Telefone").mask("(99)9999-9999?9"),e("input.cep, input.Endereco_Cep").mask("99999-999")},e.fn.initForm=function(t){return e(window),e.fn.maskInput(),e.fn.verifyCheckbox(),e.fn.verifyChangePassword(),e(t).each(function(){e(this).validate({focusInvalid:!1,invalidHandler:function(t,a){a.numberOfInvalids()&&e("html, body").animate({scrollTop:e(a.errorList[0].element).offset().top-100},500)},errorPlacement:function(t,a){var i,n;i=e(a).data("lastError"),n=e(t).text(),e(a).data("lastError",n),""!==n&&n!==i&&(e(a).tooltipster("content",n),e(a).tooltipster("show"))},success:function(t,a){e(a).tooltipster("hide")}})}),e(".variable.quantity .field").append('<div class="quantityController"><div class="more">+</div><div class="less">-</div></div>'),e(".variable.quantity .field .quantityController").height(e(".variable.quantity .field input").outerHeight()),e(".quantityController .more, .quantityController .less").on("click",function(){"var newVal";var t,a,i;i=(t=e(this)).closest(".field").find("input").val(),a="+"===t.text()?parseFloat(i)+1:i>1?parseFloat(i)-1:1,t.closest(".field").find("input").val(a)}),Modernizr.touchevents||e(".product-details .shippingCalc select").on("change",function(t,a){return"other"===a.selected?(e(this).parent().next().addClass("active"),e(".shippingCalc .result").removeClass("active")):""===a.selected?(e(this).parent().next().removeClass("active"),e(".shippingCalc .result").removeClass("active")):(e(this).parent().next().removeClass("active"),e(".shippingCalc .result").addClass("active"))}),Modernizr.touchevents&&e(".product-details .shippingCalc select").on("change",function(){return"other"===e(this).val()?(e(this).parent().next().addClass("active"),e(".shippingCalc .result").removeClass("active")):""===e(this).val()?(e(this).parent().next().removeClass("active"),e(".shippingCalc .result").removeClass("active")):(e(this).parent().next().removeClass("active"),e(".shippingCalc .result").addClass("active"))}),e(".product-details .shippingCalc .other button").on("click",function(t){return t.preventDefault(),e(".shippingCalc .result").addClass("active")}),Modernizr.touchevents||e(".pointsExpired .filters select, .pointsSummary .filters select, .ordersSummary .filters select, .pointsLocked .filters select").on("change",function(t,a){return"other"===a.selected?e(this).parent().next().slideDown():e(this).parent().next().slideUp()}),Modernizr.touchevents&&e(".pointsExpired .filters select, .pointsSummary .filters select, .ordersSummary .filters select, .pointsLocked .filters select").on("change",function(){return"other"===e(this).val()?e(this).parent().next().slideDown():e(this).parent().next().slideUp()}),Modernizr.touchevents||e(".cart .shippingCalc select").on("change",function(t,a){var i;if("other"===a.selected)return i=e("option[value="+a.selected+"]").attr("data-href"),e.fancybox({maxHeight:480,maxWidth:720,padding:0,href:i,type:"iframe",beforeShow:function(){this.width=e(".fancybox-iframe").contents().find("html").width(),this.height=e(".fancybox-iframe").contents().find("html").height()}})}),Modernizr.touchevents&&e(".cart .shippingCalc select").on("change",function(){var t;if("other"===e(this).val())return t=e("option[value="+e(this).val()+"]").attr("data-href"),e.fancybox({maxHeight:480,maxWidth:720,padding:0,href:t,type:"iframe",beforeShow:function(){this.width=e(".fancybox-iframe").contents().find("html").width(),this.height=e(".fancybox-iframe").contents().find("html").height()}})}),Modernizr.touchevents||e(".buyPoints select.pointsWanted").on("change",function(t,a){return e(".buyPoints select.pointsWanted").parent().next().find("input[type=text]").val(a.selected)}),Modernizr.touchevents&&e(".buyPoints select.pointsWanted").on("change",function(){return e(".buyPoints select.pointsWanted").parent().next().find("input[type=text]").val(params.selected)}),e(".acceptTerms input").on("change",function(){e(this).attr("checked")?e(this).parent().parent().find(".button").removeAttr("disabled"):e(this).parent().parent().find(".button").attr("disabled","disabled")}),e("input.password").keyup(function(t){var a,i,n,r;n=new RegExp("^(?=.{8,})(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*\\W).*$","g"),a=new RegExp("^(?=.{7,})(((?=.*[A-Z])(?=.*[a-z]))|((?=.*[A-Z])(?=.*[0-9]))|((?=.*[a-z])(?=.*[0-9]))).*$","g"),r=new RegExp("(?=.{6,}).*","g"),""===(i=e(this).val())?e(".passwordStrength .bar").width("100%"):!1===r.test(i)?e(".passwordStrength .bar").width("66%"):n.test(i)?e(".passwordStrength .bar").width("0%"):a.test(i)?e(".passwordStrength .bar").width("33%"):e(".passwordStrength .bar").width("100%")})}}(jQuery);