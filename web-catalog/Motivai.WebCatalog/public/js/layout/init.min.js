!function(i,n){"use strict";i.fn.initBlazy("article.list-products .photo img"),i.fn.initProductZoom(".product-details .photo img"),i.fn.initModal("a.modal"),i.fn.initModalZoom("a.zoom, a.details"),i.fn.initFaq(".faq-result ol li a:first-child"),i.fn.initSlider(".list-banners"),i.fn.initSlider(".mais-resgatados"),i.fn.initSlider(""),i.fn.initViewType(".viewType"),i.fn.initMediabox(".mediabox"),i.fn.initMobile(),i.fn.initLogin(),i.fn.initScrollbar(".scroll"),i.fn.initTabs(".tabs"),i.fn.initTooltip(".tooltip"),i.fn.initFormTooltip("form input, form select, textarea"),i.fn.initScroll(".scrollTo"),i.fn.initForm("form"),i.fn.initChosen("select.autostart"),i.fn.initCreditCardBrand("input.creditCard")}(jQuery);
