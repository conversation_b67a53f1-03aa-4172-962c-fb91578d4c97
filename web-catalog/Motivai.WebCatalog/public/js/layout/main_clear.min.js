!function(e,t){"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(e,t){function n(e){var t=e.length,n=re.type(e);return"function"!==n&&!re.isWindow(e)&&(!(1!==e.nodeType||!t)||("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e))}function i(e,t,n){if(re.isFunction(t))return re.grep(e,function(e,i){return!!t.call(e,i,e)!==n});if(t.nodeType)return re.grep(e,function(e){return e===t!==n});if("string"==typeof t){if(fe.test(t))return re.filter(t,e,n);t=re.filter(t,e)}return re.grep(e,function(e){return re.inArray(e,t)>=0!==n})}function r(e,t){do e=e[t];while(e&&1!==e.nodeType);return e}function o(e){var t=be[e]={};return re.each(e.match(xe)||[],function(e,n){t[n]=!0}),t}function a(){he.addEventListener?(he.removeEventListener("DOMContentLoaded",s,!1),e.removeEventListener("load",s,!1)):(he.detachEvent("onreadystatechange",s),e.detachEvent("onload",s))}function s(){(he.addEventListener||"load"===event.type||"complete"===he.readyState)&&(a(),re.ready())}function l(e,t,n){if(void 0===n&&1===e.nodeType){var i="data-"+t.replace(Ae,"-$1").toLowerCase();if(n=e.getAttribute(i),"string"==typeof n){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:Ce.test(n)?re.parseJSON(n):n)}catch(r){}re.data(e,t,n)}else n=void 0}return n}function c(e){var t;for(t in e)if(("data"!==t||!re.isEmptyObject(e[t]))&&"toJSON"!==t)return!1;return!0}function u(e,t,n,i){if(re.acceptData(e)){var r,o,a=re.expando,s=e.nodeType,l=s?re.cache:e,c=s?e[a]:e[a]&&a;if(c&&l[c]&&(i||l[c].data)||void 0!==n||"string"!=typeof t)return c||(c=s?e[a]=U.pop()||re.guid++:a),l[c]||(l[c]=s?{}:{toJSON:re.noop}),("object"==typeof t||"function"==typeof t)&&(i?l[c]=re.extend(l[c],t):l[c].data=re.extend(l[c].data,t)),o=l[c],i||(o.data||(o.data={}),o=o.data),void 0!==n&&(o[re.camelCase(t)]=n),"string"==typeof t?(r=o[t],null==r&&(r=o[re.camelCase(t)])):r=o,r}}function d(e,t,n){if(re.acceptData(e)){var i,r,o=e.nodeType,a=o?re.cache:e,s=o?e[re.expando]:re.expando;if(a[s]){if(t&&(i=n?a[s]:a[s].data)){re.isArray(t)?t=t.concat(re.map(t,re.camelCase)):t in i?t=[t]:(t=re.camelCase(t),t=t in i?[t]:t.split(" ")),r=t.length;for(;r--;)delete i[t[r]];if(n?!c(i):!re.isEmptyObject(i))return}(n||(delete a[s].data,c(a[s])))&&(o?re.cleanData([e],!0):ne.deleteExpando||a!=a.window?delete a[s]:a[s]=null)}}}function f(){return!0}function p(){return!1}function h(){try{return he.activeElement}catch(e){}}function g(e){var t=Oe.split("|"),n=e.createDocumentFragment();if(n.createElement)for(;t.length;)n.createElement(t.pop());return n}function m(e,t){var n,i,r=0,o=typeof e.getElementsByTagName!==Se?e.getElementsByTagName(t||"*"):typeof e.querySelectorAll!==Se?e.querySelectorAll(t||"*"):void 0;if(!o)for(o=[],n=e.childNodes||e;null!=(i=n[r]);r++)!t||re.nodeName(i,t)?o.push(i):re.merge(o,m(i,t));return void 0===t||t&&re.nodeName(e,t)?re.merge([e],o):o}function v(e){Pe.test(e.type)&&(e.defaultChecked=e.checked)}function y(e,t){return re.nodeName(e,"table")&&re.nodeName(11!==t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function x(e){return e.type=(null!==re.find.attr(e,"type"))+"/"+e.type,e}function b(e){var t=Xe.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function w(e,t){for(var n,i=0;null!=(n=e[i]);i++)re._data(n,"globalEval",!t||re._data(t[i],"globalEval"))}function T(e,t){if(1===t.nodeType&&re.hasData(e)){var n,i,r,o=re._data(e),a=re._data(t,o),s=o.events;if(s){delete a.handle,a.events={};for(n in s)for(i=0,r=s[n].length;r>i;i++)re.event.add(t,n,s[n][i])}a.data&&(a.data=re.extend({},a.data))}}function S(e,t){var n,i,r;if(1===t.nodeType){if(n=t.nodeName.toLowerCase(),!ne.noCloneEvent&&t[re.expando]){r=re._data(t);for(i in r.events)re.removeEvent(t,i,r.handle);t.removeAttribute(re.expando)}"script"===n&&t.text!==e.text?(x(t).text=e.text,b(t)):"object"===n?(t.parentNode&&(t.outerHTML=e.outerHTML),ne.html5Clone&&e.innerHTML&&!re.trim(t.innerHTML)&&(t.innerHTML=e.innerHTML)):"input"===n&&Pe.test(e.type)?(t.defaultChecked=t.checked=e.checked,t.value!==e.value&&(t.value=e.value)):"option"===n?t.defaultSelected=t.selected=e.defaultSelected:("input"===n||"textarea"===n)&&(t.defaultValue=e.defaultValue)}}function C(t,n){var i,r=re(n.createElement(t)).appendTo(n.body),o=e.getDefaultComputedStyle&&(i=e.getDefaultComputedStyle(r[0]))?i.display:re.css(r[0],"display");return r.detach(),o}function A(e){var t=he,n=Ke[e];return n||(n=C(e,t),"none"!==n&&n||(Ye=(Ye||re("<iframe frameborder='0' width='0' height='0'/>")).appendTo(t.documentElement),t=(Ye[0].contentWindow||Ye[0].contentDocument).document,t.write(),t.close(),n=C(e,t),Ye.detach()),Ke[e]=n),n}function k(e,t){return{get:function(){var n=e();if(null!=n)return n?void delete this.get:(this.get=t).apply(this,arguments)}}}function N(e,t){if(t in e)return t;for(var n=t.charAt(0).toUpperCase()+t.slice(1),i=t,r=ft.length;r--;)if(t=ft[r]+n,t in e)return t;return i}function E(e,t){for(var n,i,r,o=[],a=0,s=e.length;s>a;a++)i=e[a],i.style&&(o[a]=re._data(i,"olddisplay"),n=i.style.display,t?(o[a]||"none"!==n||(i.style.display=""),""===i.style.display&&Ee(i)&&(o[a]=re._data(i,"olddisplay",A(i.nodeName)))):(r=Ee(i),(n&&"none"!==n||!r)&&re._data(i,"olddisplay",r?n:re.css(i,"display"))));for(a=0;s>a;a++)i=e[a],i.style&&(t&&"none"!==i.style.display&&""!==i.style.display||(i.style.display=t?o[a]||"":"none"));return e}function I(e,t,n){var i=lt.exec(t);return i?Math.max(0,i[1]-(n||0))+(i[2]||"px"):t}function P(e,t,n,i,r){for(var o=n===(i?"border":"content")?4:"width"===t?1:0,a=0;4>o;o+=2)"margin"===n&&(a+=re.css(e,n+Ne[o],!0,r)),i?("content"===n&&(a-=re.css(e,"padding"+Ne[o],!0,r)),"margin"!==n&&(a-=re.css(e,"border"+Ne[o]+"Width",!0,r))):(a+=re.css(e,"padding"+Ne[o],!0,r),"padding"!==n&&(a+=re.css(e,"border"+Ne[o]+"Width",!0,r)));return a}function j(e,t,n){var i=!0,r="width"===t?e.offsetWidth:e.offsetHeight,o=et(e),a=ne.boxSizing&&"border-box"===re.css(e,"boxSizing",!1,o);if(0>=r||null==r){if(r=tt(e,t,o),(0>r||null==r)&&(r=e.style[t]),it.test(r))return r;i=a&&(ne.boxSizingReliable()||r===e.style[t]),r=parseFloat(r)||0}return r+P(e,t,n||(a?"border":"content"),i,o)+"px"}function _(e,t,n,i,r){return new _.prototype.init(e,t,n,i,r)}function D(){return setTimeout(function(){pt=void 0}),pt=re.now()}function H(e,t){var n,i={height:e},r=0;for(t=t?1:0;4>r;r+=2-t)n=Ne[r],i["margin"+n]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function L(e,t,n){for(var i,r=(xt[t]||[]).concat(xt["*"]),o=0,a=r.length;a>o;o++)if(i=r[o].call(n,t,e))return i}function O(e,t,n){var i,r,o,a,s,l,c,u,d=this,f={},p=e.style,h=e.nodeType&&Ee(e),g=re._data(e,"fxshow");n.queue||(s=re._queueHooks(e,"fx"),null==s.unqueued&&(s.unqueued=0,l=s.empty.fire,s.empty.fire=function(){s.unqueued||l()}),s.unqueued++,d.always(function(){d.always(function(){s.unqueued--,re.queue(e,"fx").length||s.empty.fire()})})),1===e.nodeType&&("height"in t||"width"in t)&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],c=re.css(e,"display"),u="none"===c?re._data(e,"olddisplay")||A(e.nodeName):c,"inline"===u&&"none"===re.css(e,"float")&&(ne.inlineBlockNeedsLayout&&"inline"!==A(e.nodeName)?p.zoom=1:p.display="inline-block")),n.overflow&&(p.overflow="hidden",ne.shrinkWrapBlocks()||d.always(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]}));for(i in t)if(r=t[i],gt.exec(r)){if(delete t[i],o=o||"toggle"===r,r===(h?"hide":"show")){if("show"!==r||!g||void 0===g[i])continue;h=!0}f[i]=g&&g[i]||re.style(e,i)}else c=void 0;if(re.isEmptyObject(f))"inline"===("none"===c?A(e.nodeName):c)&&(p.display=c);else{g?"hidden"in g&&(h=g.hidden):g=re._data(e,"fxshow",{}),o&&(g.hidden=!h),h?re(e).show():d.done(function(){re(e).hide()}),d.done(function(){var t;re._removeData(e,"fxshow");for(t in f)re.style(e,t,f[t])});for(i in f)a=L(h?g[i]:0,i,d),i in g||(g[i]=a.start,h&&(a.end=a.start,a.start="width"===i||"height"===i?1:0))}}function z(e,t){var n,i,r,o,a;for(n in e)if(i=re.camelCase(n),r=t[i],o=e[n],re.isArray(o)&&(r=o[1],o=e[n]=o[0]),n!==i&&(e[i]=o,delete e[n]),a=re.cssHooks[i],a&&"expand"in a){o=a.expand(o),delete e[i];for(n in o)n in e||(e[n]=o[n],t[n]=r)}else t[i]=r}function F(e,t,n){var i,r,o=0,a=yt.length,s=re.Deferred().always(function(){delete l.elem}),l=function(){if(r)return!1;for(var t=pt||D(),n=Math.max(0,c.startTime+c.duration-t),i=n/c.duration||0,o=1-i,a=0,l=c.tweens.length;l>a;a++)c.tweens[a].run(o);return s.notifyWith(e,[c,o,n]),1>o&&l?n:(s.resolveWith(e,[c]),!1)},c=s.promise({elem:e,props:re.extend({},t),opts:re.extend(!0,{specialEasing:{}},n),originalProperties:t,originalOptions:n,startTime:pt||D(),duration:n.duration,tweens:[],createTween:function(t,n){var i=re.Tween(e,c.opts,t,n,c.opts.specialEasing[t]||c.opts.easing);return c.tweens.push(i),i},stop:function(t){var n=0,i=t?c.tweens.length:0;if(r)return this;for(r=!0;i>n;n++)c.tweens[n].run(1);return t?s.resolveWith(e,[c,t]):s.rejectWith(e,[c,t]),this}}),u=c.props;for(z(u,c.opts.specialEasing);a>o;o++)if(i=yt[o].call(c,e,u,c.opts))return i;return re.map(u,L,c),re.isFunction(c.opts.start)&&c.opts.start.call(e,c),re.fx.timer(re.extend(l,{elem:e,anim:c,queue:c.opts.queue})),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always)}function M(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var i,r=0,o=t.toLowerCase().match(xe)||[];if(re.isFunction(n))for(;i=o[r++];)"+"===i.charAt(0)?(i=i.slice(1)||"*",(e[i]=e[i]||[]).unshift(n)):(e[i]=e[i]||[]).push(n)}}function q(e,t,n,i){function r(s){var l;return o[s]=!0,re.each(e[s]||[],function(e,s){var c=s(t,n,i);return"string"!=typeof c||a||o[c]?a?!(l=c):void 0:(t.dataTypes.unshift(c),r(c),!1)}),l}var o={},a=e===Bt;return r(t.dataTypes[0])||!o["*"]&&r("*")}function R(e,t){var n,i,r=re.ajaxSettings.flatOptions||{};for(i in t)void 0!==t[i]&&((r[i]?e:n||(n={}))[i]=t[i]);return n&&re.extend(!0,e,n),e}function W(e,t,n){for(var i,r,o,a,s=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(a in s)if(s[a]&&s[a].test(r)){l.unshift(a);break}if(l[0]in n)o=l[0];else{for(a in n){if(!l[0]||e.converters[a+" "+l[0]]){o=a;break}i||(i=a)}o=o||i}return o?(o!==l[0]&&l.unshift(o),n[o]):void 0}function B(e,t,n,i){var r,o,a,s,l,c={},u=e.dataTypes.slice();if(u[1])for(a in e.converters)c[a.toLowerCase()]=e.converters[a];for(o=u.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=u.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(a=c[l+" "+o]||c["* "+o],!a)for(r in c)if(s=r.split(" "),s[1]===o&&(a=c[l+" "+s[0]]||c["* "+s[0]])){a===!0?a=c[r]:c[r]!==!0&&(o=s[0],u.unshift(s[1]));break}if(a!==!0)if(a&&e["throws"])t=a(t);else try{t=a(t)}catch(d){return{state:"parsererror",error:a?d:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}function $(e,t,n,i){var r;if(re.isArray(t))re.each(t,function(t,r){n||Xt.test(e)?i(e,r):$(e+"["+("object"==typeof r?t:"")+"]",r,n,i)});else if(n||"object"!==re.type(t))i(e,t);else for(r in t)$(e+"["+r+"]",t[r],n,i)}function V(){try{return new e.XMLHttpRequest}catch(t){}}function Q(){try{return new e.ActiveXObject("Microsoft.XMLHTTP")}catch(t){}}function X(e){return re.isWindow(e)?e:9===e.nodeType&&(e.defaultView||e.parentWindow)}var U=[],J=U.slice,G=U.concat,Z=U.push,Y=U.indexOf,K={},ee=K.toString,te=K.hasOwnProperty,ne={},ie="1.11.1",re=function(e,t){return new re.fn.init(e,t)},oe=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,ae=/^-ms-/,se=/-([\da-z])/gi,le=function(e,t){return t.toUpperCase()};re.fn=re.prototype={jquery:ie,constructor:re,selector:"",length:0,toArray:function(){return J.call(this)},get:function(e){return null!=e?0>e?this[e+this.length]:this[e]:J.call(this)},pushStack:function(e){var t=re.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e,t){return re.each(this,e,t)},map:function(e){return this.pushStack(re.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(J.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(0>e?t:0);return this.pushStack(n>=0&&t>n?[this[n]]:[])},end:function(){return this.prevObject||this.constructor(null)},push:Z,sort:U.sort,splice:U.splice},re.extend=re.fn.extend=function(){var e,t,n,i,r,o,a=arguments[0]||{},s=1,l=arguments.length,c=!1;for("boolean"==typeof a&&(c=a,a=arguments[s]||{},s++),"object"==typeof a||re.isFunction(a)||(a={}),s===l&&(a=this,s--);l>s;s++)if(null!=(r=arguments[s]))for(i in r)e=a[i],n=r[i],a!==n&&(c&&n&&(re.isPlainObject(n)||(t=re.isArray(n)))?(t?(t=!1,o=e&&re.isArray(e)?e:[]):o=e&&re.isPlainObject(e)?e:{},a[i]=re.extend(c,o,n)):void 0!==n&&(a[i]=n));return a},re.extend({expando:"jQuery"+(ie+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===re.type(e)},isArray:Array.isArray||function(e){return"array"===re.type(e)},isWindow:function(e){return null!=e&&e==e.window},isNumeric:function(e){return!re.isArray(e)&&e-parseFloat(e)>=0},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},isPlainObject:function(e){var t;if(!e||"object"!==re.type(e)||e.nodeType||re.isWindow(e))return!1;try{if(e.constructor&&!te.call(e,"constructor")&&!te.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(n){return!1}if(ne.ownLast)for(t in e)return te.call(e,t);for(t in e);return void 0===t||te.call(e,t)},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?K[ee.call(e)]||"object":typeof e},globalEval:function(t){t&&re.trim(t)&&(e.execScript||function(t){e.eval.call(e,t)})(t)},camelCase:function(e){return e.replace(ae,"ms-").replace(se,le)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t,i){var r,o=0,a=e.length,s=n(e);if(i){if(s)for(;a>o&&(r=t.apply(e[o],i),r!==!1);o++);else for(o in e)if(r=t.apply(e[o],i),r===!1)break}else if(s)for(;a>o&&(r=t.call(e[o],o,e[o]),r!==!1);o++);else for(o in e)if(r=t.call(e[o],o,e[o]),r===!1)break;return e},trim:function(e){return null==e?"":(e+"").replace(oe,"")},makeArray:function(e,t){var i=t||[];return null!=e&&(n(Object(e))?re.merge(i,"string"==typeof e?[e]:e):Z.call(i,e)),i},inArray:function(e,t,n){var i;if(t){if(Y)return Y.call(t,e,n);for(i=t.length,n=n?0>n?Math.max(0,i+n):n:0;i>n;n++)if(n in t&&t[n]===e)return n}return-1},merge:function(e,t){for(var n=+t.length,i=0,r=e.length;n>i;)e[r++]=t[i++];if(n!==n)for(;void 0!==t[i];)e[r++]=t[i++];return e.length=r,e},grep:function(e,t,n){for(var i,r=[],o=0,a=e.length,s=!n;a>o;o++)i=!t(e[o],o),i!==s&&r.push(e[o]);return r},map:function(e,t,i){var r,o=0,a=e.length,s=n(e),l=[];if(s)for(;a>o;o++)r=t(e[o],o,i),null!=r&&l.push(r);else for(o in e)r=t(e[o],o,i),null!=r&&l.push(r);return G.apply([],l)},guid:1,proxy:function(e,t){var n,i,r;return"string"==typeof t&&(r=e[t],t=e,e=r),re.isFunction(e)?(n=J.call(arguments,2),i=function(){return e.apply(t||this,n.concat(J.call(arguments)))},i.guid=e.guid=e.guid||re.guid++,i):void 0},now:function(){return+new Date},support:ne}),re.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(e,t){K["[object "+t+"]"]=t.toLowerCase()});var ce=function(e){function t(e,t,n,i){var r,o,a,s,l,c,d,p,h,g;if((t?t.ownerDocument||t:q)!==_&&j(t),t=t||_,n=n||[],!e||"string"!=typeof e)return n;if(1!==(s=t.nodeType)&&9!==s)return[];if(H&&!i){if(r=ye.exec(e))if(a=r[1]){if(9===s){if(o=t.getElementById(a),!o||!o.parentNode)return n;if(o.id===a)return n.push(o),n}else if(t.ownerDocument&&(o=t.ownerDocument.getElementById(a))&&F(t,o)&&o.id===a)return n.push(o),n}else{if(r[2])return K.apply(n,t.getElementsByTagName(e)),n;if((a=r[3])&&w.getElementsByClassName&&t.getElementsByClassName)return K.apply(n,t.getElementsByClassName(a)),n}if(w.qsa&&(!L||!L.test(e))){if(p=d=M,h=t,g=9===s&&e,1===s&&"object"!==t.nodeName.toLowerCase()){for(c=A(e),(d=t.getAttribute("id"))?p=d.replace(be,"\\$&"):t.setAttribute("id",p),p="[id='"+p+"'] ",l=c.length;l--;)c[l]=p+f(c[l]);h=xe.test(e)&&u(t.parentNode)||t,g=c.join(",")}if(g)try{return K.apply(n,h.querySelectorAll(g)),n}catch(m){}finally{d||t.removeAttribute("id")}}}return N(e.replace(le,"$1"),t,n,i)}function n(){function e(n,i){return t.push(n+" ")>T.cacheLength&&delete e[t.shift()],e[n+" "]=i}var t=[];return e}function i(e){return e[M]=!0,e}function r(e){var t=_.createElement("div");try{return!!e(t)}catch(n){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function o(e,t){for(var n=e.split("|"),i=e.length;i--;)T.attrHandle[n[i]]=t}function a(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||U)-(~e.sourceIndex||U);if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function s(e){return function(t){var n=t.nodeName.toLowerCase();return"input"===n&&t.type===e}}function l(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function c(e){return i(function(t){return t=+t,i(function(n,i){for(var r,o=e([],n.length,t),a=o.length;a--;)n[r=o[a]]&&(n[r]=!(i[r]=n[r]))})})}function u(e){return e&&typeof e.getElementsByTagName!==X&&e}function d(){}function f(e){for(var t=0,n=e.length,i="";n>t;t++)i+=e[t].value;return i}function p(e,t,n){var i=t.dir,r=n&&"parentNode"===i,o=W++;return t.first?function(t,n,o){for(;t=t[i];)if(1===t.nodeType||r)return e(t,n,o)}:function(t,n,a){var s,l,c=[R,o];if(a){for(;t=t[i];)if((1===t.nodeType||r)&&e(t,n,a))return!0}else for(;t=t[i];)if(1===t.nodeType||r){if(l=t[M]||(t[M]={}),(s=l[i])&&s[0]===R&&s[1]===o)return c[2]=s[2];if(l[i]=c,c[2]=e(t,n,a))return!0}}}function h(e){return e.length>1?function(t,n,i){for(var r=e.length;r--;)if(!e[r](t,n,i))return!1;return!0}:e[0]}function g(e,n,i){for(var r=0,o=n.length;o>r;r++)t(e,n[r],i);return i}function m(e,t,n,i,r){for(var o,a=[],s=0,l=e.length,c=null!=t;l>s;s++)(o=e[s])&&(!n||n(o,i,r))&&(a.push(o),c&&t.push(s));return a}function v(e,t,n,r,o,a){return r&&!r[M]&&(r=v(r)),o&&!o[M]&&(o=v(o,a)),i(function(i,a,s,l){var c,u,d,f=[],p=[],h=a.length,v=i||g(t||"*",s.nodeType?[s]:s,[]),y=!e||!i&&t?v:m(v,f,e,s,l),x=n?o||(i?e:h||r)?[]:a:y;if(n&&n(y,x,s,l),r)for(c=m(x,p),r(c,[],s,l),u=c.length;u--;)(d=c[u])&&(x[p[u]]=!(y[p[u]]=d));if(i){if(o||e){if(o){for(c=[],u=x.length;u--;)(d=x[u])&&c.push(y[u]=d);o(null,x=[],c,l)}for(u=x.length;u--;)(d=x[u])&&(c=o?te.call(i,d):f[u])>-1&&(i[c]=!(a[c]=d))}}else x=m(x===a?x.splice(h,x.length):x),o?o(null,a,x,l):K.apply(a,x)})}function y(e){for(var t,n,i,r=e.length,o=T.relative[e[0].type],a=o||T.relative[" "],s=o?1:0,l=p(function(e){return e===t},a,!0),c=p(function(e){return te.call(t,e)>-1},a,!0),u=[function(e,n,i){return!o&&(i||n!==E)||((t=n).nodeType?l(e,n,i):c(e,n,i))}];r>s;s++)if(n=T.relative[e[s].type])u=[p(h(u),n)];else{if(n=T.filter[e[s].type].apply(null,e[s].matches),n[M]){for(i=++s;r>i&&!T.relative[e[i].type];i++);return v(s>1&&h(u),s>1&&f(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(le,"$1"),n,i>s&&y(e.slice(s,i)),r>i&&y(e=e.slice(i)),r>i&&f(e))}u.push(n)}return h(u)}function x(e,n){var r=n.length>0,o=e.length>0,a=function(i,a,s,l,c){var u,d,f,p=0,h="0",g=i&&[],v=[],y=E,x=i||o&&T.find.TAG("*",c),b=R+=null==y?1:Math.random()||.1,w=x.length;for(c&&(E=a!==_&&a);h!==w&&null!=(u=x[h]);h++){if(o&&u){for(d=0;f=e[d++];)if(f(u,a,s)){l.push(u);break}c&&(R=b)}r&&((u=!f&&u)&&p--,i&&g.push(u))}if(p+=h,r&&h!==p){for(d=0;f=n[d++];)f(g,v,a,s);if(i){if(p>0)for(;h--;)g[h]||v[h]||(v[h]=Z.call(l));v=m(v)}K.apply(l,v),c&&!i&&v.length>0&&p+n.length>1&&t.uniqueSort(l)}return c&&(R=b,E=y),g};return r?i(a):a}var b,w,T,S,C,A,k,N,E,I,P,j,_,D,H,L,O,z,F,M="sizzle"+-new Date,q=e.document,R=0,W=0,B=n(),$=n(),V=n(),Q=function(e,t){return e===t&&(P=!0),0},X="undefined",U=1<<31,J={}.hasOwnProperty,G=[],Z=G.pop,Y=G.push,K=G.push,ee=G.slice,te=G.indexOf||function(e){for(var t=0,n=this.length;n>t;t++)if(this[t]===e)return t;return-1},ne="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",ie="[\\x20\\t\\r\\n\\f]",re="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",oe=re.replace("w","w#"),ae="\\["+ie+"*("+re+")(?:"+ie+"*([*^$|!~]?=)"+ie+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+oe+"))|)"+ie+"*\\]",se=":("+re+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+ae+")*)|.*)\\)|)",le=new RegExp("^"+ie+"+|((?:^|[^\\\\])(?:\\\\.)*)"+ie+"+$","g"),ce=new RegExp("^"+ie+"*,"+ie+"*"),ue=new RegExp("^"+ie+"*([>+~]|"+ie+")"+ie+"*"),de=new RegExp("="+ie+"*([^\\]'\"]*?)"+ie+"*\\]","g"),fe=new RegExp(se),pe=new RegExp("^"+oe+"$"),he={ID:new RegExp("^#("+re+")"),CLASS:new RegExp("^\\.("+re+")"),TAG:new RegExp("^("+re.replace("w","w*")+")"),ATTR:new RegExp("^"+ae),PSEUDO:new RegExp("^"+se),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+ie+"*(even|odd|(([+-]|)(\\d*)n|)"+ie+"*(?:([+-]|)"+ie+"*(\\d+)|))"+ie+"*\\)|)","i"),bool:new RegExp("^(?:"+ne+")$","i"),needsContext:new RegExp("^"+ie+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+ie+"*((?:-\\d)?\\d*)"+ie+"*\\)|)(?=[^-]|$)","i")},ge=/^(?:input|select|textarea|button)$/i,me=/^h\d$/i,ve=/^[^{]+\{\s*\[native \w/,ye=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,xe=/[+~]/,be=/'|\\/g,we=new RegExp("\\\\([\\da-f]{1,6}"+ie+"?|("+ie+")|.)","ig"),Te=function(e,t,n){var i="0x"+t-65536;return i!==i||n?t:0>i?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320)};try{K.apply(G=ee.call(q.childNodes),q.childNodes),G[q.childNodes.length].nodeType}catch(Se){K={apply:G.length?function(e,t){Y.apply(e,ee.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}w=t.support={},C=t.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},j=t.setDocument=function(e){var t,n=e?e.ownerDocument||e:q,i=n.defaultView;return n!==_&&9===n.nodeType&&n.documentElement?(_=n,D=n.documentElement,H=!C(n),i&&i!==i.top&&(i.addEventListener?i.addEventListener("unload",function(){j()},!1):i.attachEvent&&i.attachEvent("onunload",function(){j()})),w.attributes=r(function(e){return e.className="i",!e.getAttribute("className")}),w.getElementsByTagName=r(function(e){return e.appendChild(n.createComment("")),!e.getElementsByTagName("*").length}),w.getElementsByClassName=ve.test(n.getElementsByClassName)&&r(function(e){return e.innerHTML="<div class='a'></div><div class='a i'></div>",e.firstChild.className="i",2===e.getElementsByClassName("i").length}),w.getById=r(function(e){return D.appendChild(e).id=M,!n.getElementsByName||!n.getElementsByName(M).length}),w.getById?(T.find.ID=function(e,t){if(typeof t.getElementById!==X&&H){var n=t.getElementById(e);return n&&n.parentNode?[n]:[]}},T.filter.ID=function(e){var t=e.replace(we,Te);return function(e){return e.getAttribute("id")===t}}):(delete T.find.ID,T.filter.ID=function(e){var t=e.replace(we,Te);return function(e){var n=typeof e.getAttributeNode!==X&&e.getAttributeNode("id");return n&&n.value===t}}),T.find.TAG=w.getElementsByTagName?function(e,t){return typeof t.getElementsByTagName!==X?t.getElementsByTagName(e):void 0}:function(e,t){var n,i=[],r=0,o=t.getElementsByTagName(e);if("*"===e){for(;n=o[r++];)1===n.nodeType&&i.push(n);return i}return o},T.find.CLASS=w.getElementsByClassName&&function(e,t){return typeof t.getElementsByClassName!==X&&H?t.getElementsByClassName(e):void 0},O=[],L=[],(w.qsa=ve.test(n.querySelectorAll))&&(r(function(e){e.innerHTML="<select msallowclip=''><option selected=''></option></select>",e.querySelectorAll("[msallowclip^='']").length&&L.push("[*^$]="+ie+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||L.push("\\["+ie+"*(?:value|"+ne+")"),e.querySelectorAll(":checked").length||L.push(":checked")}),r(function(e){var t=n.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&L.push("name"+ie+"*[*^$|!~]?="),e.querySelectorAll(":enabled").length||L.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),L.push(",.*:")})),(w.matchesSelector=ve.test(z=D.matches||D.webkitMatchesSelector||D.mozMatchesSelector||D.oMatchesSelector||D.msMatchesSelector))&&r(function(e){w.disconnectedMatch=z.call(e,"div"),z.call(e,"[s!='']:x"),O.push("!=",se)}),L=L.length&&new RegExp(L.join("|")),O=O.length&&new RegExp(O.join("|")),t=ve.test(D.compareDocumentPosition),F=t||ve.test(D.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,i=t&&t.parentNode;return e===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):e.compareDocumentPosition&&16&e.compareDocumentPosition(i)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},Q=t?function(e,t){if(e===t)return P=!0,0;var i=!e.compareDocumentPosition-!t.compareDocumentPosition;return i?i:(i=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1,1&i||!w.sortDetached&&t.compareDocumentPosition(e)===i?e===n||e.ownerDocument===q&&F(q,e)?-1:t===n||t.ownerDocument===q&&F(q,t)?1:I?te.call(I,e)-te.call(I,t):0:4&i?-1:1)}:function(e,t){if(e===t)return P=!0,0;var i,r=0,o=e.parentNode,s=t.parentNode,l=[e],c=[t];if(!o||!s)return e===n?-1:t===n?1:o?-1:s?1:I?te.call(I,e)-te.call(I,t):0;if(o===s)return a(e,t);for(i=e;i=i.parentNode;)l.unshift(i);for(i=t;i=i.parentNode;)c.unshift(i);for(;l[r]===c[r];)r++;return r?a(l[r],c[r]):l[r]===q?-1:c[r]===q?1:0},n):_},t.matches=function(e,n){return t(e,null,null,n)},t.matchesSelector=function(e,n){if((e.ownerDocument||e)!==_&&j(e),n=n.replace(de,"='$1']"),!(!w.matchesSelector||!H||O&&O.test(n)||L&&L.test(n)))try{var i=z.call(e,n);if(i||w.disconnectedMatch||e.document&&11!==e.document.nodeType)return i}catch(r){}return t(n,_,null,[e]).length>0},t.contains=function(e,t){return(e.ownerDocument||e)!==_&&j(e),F(e,t)},t.attr=function(e,t){(e.ownerDocument||e)!==_&&j(e);var n=T.attrHandle[t.toLowerCase()],i=n&&J.call(T.attrHandle,t.toLowerCase())?n(e,t,!H):void 0;return void 0!==i?i:w.attributes||!H?e.getAttribute(t):(i=e.getAttributeNode(t))&&i.specified?i.value:null},t.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},t.uniqueSort=function(e){var t,n=[],i=0,r=0;if(P=!w.detectDuplicates,I=!w.sortStable&&e.slice(0),e.sort(Q),P){for(;t=e[r++];)t===e[r]&&(i=n.push(r));for(;i--;)e.splice(n[i],1)}return I=null,e},S=t.getText=function(e){var t,n="",i=0,r=e.nodeType;if(r){if(1===r||9===r||11===r){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=S(e)}else if(3===r||4===r)return e.nodeValue}else for(;t=e[i++];)n+=S(t);return n},T=t.selectors={cacheLength:50,createPseudo:i,match:he,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(we,Te),e[3]=(e[3]||e[4]||e[5]||"").replace(we,Te),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||t.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&t.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return he.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&fe.test(n)&&(t=A(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(we,Te).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=B[e+" "];return t||(t=new RegExp("(^|"+ie+")"+e+"("+ie+"|$)"))&&B(e,function(e){return t.test("string"==typeof e.className&&e.className||typeof e.getAttribute!==X&&e.getAttribute("class")||"")})},ATTR:function(e,n,i){return function(r){var o=t.attr(r,e);return null==o?"!="===n:!n||(o+="","="===n?o===i:"!="===n?o!==i:"^="===n?i&&0===o.indexOf(i):"*="===n?i&&o.indexOf(i)>-1:"$="===n?i&&o.slice(-i.length)===i:"~="===n?(" "+o+" ").indexOf(i)>-1:"|="===n&&(o===i||o.slice(0,i.length+1)===i+"-"))}},CHILD:function(e,t,n,i,r){var o="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===i&&0===r?function(e){return!!e.parentNode}:function(t,n,l){var c,u,d,f,p,h,g=o!==a?"nextSibling":"previousSibling",m=t.parentNode,v=s&&t.nodeName.toLowerCase(),y=!l&&!s;if(m){if(o){for(;g;){for(d=t;d=d[g];)if(s?d.nodeName.toLowerCase()===v:1===d.nodeType)return!1;h=g="only"===e&&!h&&"nextSibling"}return!0}if(h=[a?m.firstChild:m.lastChild],a&&y){for(u=m[M]||(m[M]={}),c=u[e]||[],p=c[0]===R&&c[1],f=c[0]===R&&c[2],d=p&&m.childNodes[p];d=++p&&d&&d[g]||(f=p=0)||h.pop();)if(1===d.nodeType&&++f&&d===t){u[e]=[R,p,f];break}}else if(y&&(c=(t[M]||(t[M]={}))[e])&&c[0]===R)f=c[1];else for(;(d=++p&&d&&d[g]||(f=p=0)||h.pop())&&((s?d.nodeName.toLowerCase()!==v:1!==d.nodeType)||!++f||(y&&((d[M]||(d[M]={}))[e]=[R,f]),d!==t)););return f-=r,f===i||f%i===0&&f/i>=0}}},PSEUDO:function(e,n){var r,o=T.pseudos[e]||T.setFilters[e.toLowerCase()]||t.error("unsupported pseudo: "+e);return o[M]?o(n):o.length>1?(r=[e,e,"",n],T.setFilters.hasOwnProperty(e.toLowerCase())?i(function(e,t){for(var i,r=o(e,n),a=r.length;a--;)i=te.call(e,r[a]),e[i]=!(t[i]=r[a])}):function(e){return o(e,0,r)}):o}},pseudos:{not:i(function(e){var t=[],n=[],r=k(e.replace(le,"$1"));return r[M]?i(function(e,t,n,i){for(var o,a=r(e,null,i,[]),s=e.length;s--;)(o=a[s])&&(e[s]=!(t[s]=o))}):function(e,i,o){return t[0]=e,r(t,null,o,n),!n.pop()}}),has:i(function(e){return function(n){return t(e,n).length>0}}),contains:i(function(e){return function(t){return(t.textContent||t.innerText||S(t)).indexOf(e)>-1}}),lang:i(function(e){return pe.test(e||"")||t.error("unsupported lang: "+e),e=e.replace(we,Te).toLowerCase(),function(t){var n;do if(n=H?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return n=n.toLowerCase(),n===e||0===n.indexOf(e+"-");while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===D},focus:function(e){return e===_.activeElement&&(!_.hasFocus||_.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return e.disabled===!1},disabled:function(e){return e.disabled===!0},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,e.selected===!0},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!T.pseudos.empty(e)},header:function(e){return me.test(e.nodeName)},input:function(e){return ge.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:c(function(){return[0]}),last:c(function(e,t){return[t-1]}),eq:c(function(e,t,n){return[0>n?n+t:n]}),even:c(function(e,t){for(var n=0;t>n;n+=2)e.push(n);return e}),odd:c(function(e,t){for(var n=1;t>n;n+=2)e.push(n);return e}),lt:c(function(e,t,n){for(var i=0>n?n+t:n;--i>=0;)e.push(i);return e}),gt:c(function(e,t,n){for(var i=0>n?n+t:n;++i<t;)e.push(i);return e})}},T.pseudos.nth=T.pseudos.eq;for(b in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})T.pseudos[b]=s(b);for(b in{submit:!0,reset:!0})T.pseudos[b]=l(b);return d.prototype=T.filters=T.pseudos,T.setFilters=new d,A=t.tokenize=function(e,n){var i,r,o,a,s,l,c,u=$[e+" "];if(u)return n?0:u.slice(0);for(s=e,l=[],c=T.preFilter;s;){(!i||(r=ce.exec(s)))&&(r&&(s=s.slice(r[0].length)||s),l.push(o=[])),i=!1,(r=ue.exec(s))&&(i=r.shift(),o.push({value:i,type:r[0].replace(le," ")}),s=s.slice(i.length));for(a in T.filter)!(r=he[a].exec(s))||c[a]&&!(r=c[a](r))||(i=r.shift(),o.push({value:i,type:a,matches:r
}),s=s.slice(i.length));if(!i)break}return n?s.length:s?t.error(e):$(e,l).slice(0)},k=t.compile=function(e,t){var n,i=[],r=[],o=V[e+" "];if(!o){for(t||(t=A(e)),n=t.length;n--;)o=y(t[n]),o[M]?i.push(o):r.push(o);o=V(e,x(r,i)),o.selector=e}return o},N=t.select=function(e,t,n,i){var r,o,a,s,l,c="function"==typeof e&&e,d=!i&&A(e=c.selector||e);if(n=n||[],1===d.length){if(o=d[0]=d[0].slice(0),o.length>2&&"ID"===(a=o[0]).type&&w.getById&&9===t.nodeType&&H&&T.relative[o[1].type]){if(t=(T.find.ID(a.matches[0].replace(we,Te),t)||[])[0],!t)return n;c&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(r=he.needsContext.test(e)?0:o.length;r--&&(a=o[r],!T.relative[s=a.type]);)if((l=T.find[s])&&(i=l(a.matches[0].replace(we,Te),xe.test(o[0].type)&&u(t.parentNode)||t))){if(o.splice(r,1),e=i.length&&f(o),!e)return K.apply(n,i),n;break}}return(c||k(e,d))(i,t,!H,n,xe.test(e)&&u(t.parentNode)||t),n},w.sortStable=M.split("").sort(Q).join("")===M,w.detectDuplicates=!!P,j(),w.sortDetached=r(function(e){return 1&e.compareDocumentPosition(_.createElement("div"))}),r(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||o("type|href|height|width",function(e,t,n){return n?void 0:e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),w.attributes&&r(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||o("value",function(e,t,n){return n||"input"!==e.nodeName.toLowerCase()?void 0:e.defaultValue}),r(function(e){return null==e.getAttribute("disabled")})||o(ne,function(e,t,n){var i;return n?void 0:e[t]===!0?t.toLowerCase():(i=e.getAttributeNode(t))&&i.specified?i.value:null}),t}(e);re.find=ce,re.expr=ce.selectors,re.expr[":"]=re.expr.pseudos,re.unique=ce.uniqueSort,re.text=ce.getText,re.isXMLDoc=ce.isXML,re.contains=ce.contains;var ue=re.expr.match.needsContext,de=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,fe=/^.[^:#\[\.,]*$/;re.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?re.find.matchesSelector(i,e)?[i]:[]:re.find.matches(e,re.grep(t,function(e){return 1===e.nodeType}))},re.fn.extend({find:function(e){var t,n=[],i=this,r=i.length;if("string"!=typeof e)return this.pushStack(re(e).filter(function(){for(t=0;r>t;t++)if(re.contains(i[t],this))return!0}));for(t=0;r>t;t++)re.find(e,i[t],n);return n=this.pushStack(r>1?re.unique(n):n),n.selector=this.selector?this.selector+" "+e:e,n},filter:function(e){return this.pushStack(i(this,e||[],!1))},not:function(e){return this.pushStack(i(this,e||[],!0))},is:function(e){return!!i(this,"string"==typeof e&&ue.test(e)?re(e):e||[],!1).length}});var pe,he=e.document,ge=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,me=re.fn.init=function(e,t){var n,i;if(!e)return this;if("string"==typeof e){if(n="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:ge.exec(e),!n||!n[1]&&t)return!t||t.jquery?(t||pe).find(e):this.constructor(t).find(e);if(n[1]){if(t=t instanceof re?t[0]:t,re.merge(this,re.parseHTML(n[1],t&&t.nodeType?t.ownerDocument||t:he,!0)),de.test(n[1])&&re.isPlainObject(t))for(n in t)re.isFunction(this[n])?this[n](t[n]):this.attr(n,t[n]);return this}if(i=he.getElementById(n[2]),i&&i.parentNode){if(i.id!==n[2])return pe.find(e);this.length=1,this[0]=i}return this.context=he,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):re.isFunction(e)?"undefined"!=typeof pe.ready?pe.ready(e):e(re):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),re.makeArray(e,this))};me.prototype=re.fn,pe=re(he);var ve=/^(?:parents|prev(?:Until|All))/,ye={children:!0,contents:!0,next:!0,prev:!0};re.extend({dir:function(e,t,n){for(var i=[],r=e[t];r&&9!==r.nodeType&&(void 0===n||1!==r.nodeType||!re(r).is(n));)1===r.nodeType&&i.push(r),r=r[t];return i},sibling:function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}}),re.fn.extend({has:function(e){var t,n=re(e,this),i=n.length;return this.filter(function(){for(t=0;i>t;t++)if(re.contains(this,n[t]))return!0})},closest:function(e,t){for(var n,i=0,r=this.length,o=[],a=ue.test(e)||"string"!=typeof e?re(e,t||this.context):0;r>i;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?a.index(n)>-1:1===n.nodeType&&re.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?re.unique(o):o)},index:function(e){return e?"string"==typeof e?re.inArray(this[0],re(e)):re.inArray(e.jquery?e[0]:e,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(re.unique(re.merge(this.get(),re(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),re.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return re.dir(e,"parentNode")},parentsUntil:function(e,t,n){return re.dir(e,"parentNode",n)},next:function(e){return r(e,"nextSibling")},prev:function(e){return r(e,"previousSibling")},nextAll:function(e){return re.dir(e,"nextSibling")},prevAll:function(e){return re.dir(e,"previousSibling")},nextUntil:function(e,t,n){return re.dir(e,"nextSibling",n)},prevUntil:function(e,t,n){return re.dir(e,"previousSibling",n)},siblings:function(e){return re.sibling((e.parentNode||{}).firstChild,e)},children:function(e){return re.sibling(e.firstChild)},contents:function(e){return re.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:re.merge([],e.childNodes)}},function(e,t){re.fn[e]=function(n,i){var r=re.map(this,t,n);return"Until"!==e.slice(-5)&&(i=n),i&&"string"==typeof i&&(r=re.filter(i,r)),this.length>1&&(ye[e]||(r=re.unique(r)),ve.test(e)&&(r=r.reverse())),this.pushStack(r)}});var xe=/\S+/g,be={};re.Callbacks=function(e){e="string"==typeof e?be[e]||o(e):re.extend({},e);var t,n,i,r,a,s,l=[],c=!e.once&&[],u=function(o){for(n=e.memory&&o,i=!0,a=s||0,s=0,r=l.length,t=!0;l&&r>a;a++)if(l[a].apply(o[0],o[1])===!1&&e.stopOnFalse){n=!1;break}t=!1,l&&(c?c.length&&u(c.shift()):n?l=[]:d.disable())},d={add:function(){if(l){var i=l.length;!function o(t){re.each(t,function(t,n){var i=re.type(n);"function"===i?e.unique&&d.has(n)||l.push(n):n&&n.length&&"string"!==i&&o(n)})}(arguments),t?r=l.length:n&&(s=i,u(n))}return this},remove:function(){return l&&re.each(arguments,function(e,n){for(var i;(i=re.inArray(n,l,i))>-1;)l.splice(i,1),t&&(r>=i&&r--,a>=i&&a--)}),this},has:function(e){return e?re.inArray(e,l)>-1:!(!l||!l.length)},empty:function(){return l=[],r=0,this},disable:function(){return l=c=n=void 0,this},disabled:function(){return!l},lock:function(){return c=void 0,n||d.disable(),this},locked:function(){return!c},fireWith:function(e,n){return!l||i&&!c||(n=n||[],n=[e,n.slice?n.slice():n],t?c.push(n):u(n)),this},fire:function(){return d.fireWith(this,arguments),this},fired:function(){return!!i}};return d},re.extend({Deferred:function(e){var t=[["resolve","done",re.Callbacks("once memory"),"resolved"],["reject","fail",re.Callbacks("once memory"),"rejected"],["notify","progress",re.Callbacks("memory")]],n="pending",i={state:function(){return n},always:function(){return r.done(arguments).fail(arguments),this},then:function(){var e=arguments;return re.Deferred(function(n){re.each(t,function(t,o){var a=re.isFunction(e[t])&&e[t];r[o[1]](function(){var e=a&&a.apply(this,arguments);e&&re.isFunction(e.promise)?e.promise().done(n.resolve).fail(n.reject).progress(n.notify):n[o[0]+"With"](this===i?n.promise():this,a?[e]:arguments)})}),e=null}).promise()},promise:function(e){return null!=e?re.extend(e,i):i}},r={};return i.pipe=i.then,re.each(t,function(e,o){var a=o[2],s=o[3];i[o[1]]=a.add,s&&a.add(function(){n=s},t[1^e][2].disable,t[2][2].lock),r[o[0]]=function(){return r[o[0]+"With"](this===r?i:this,arguments),this},r[o[0]+"With"]=a.fireWith}),i.promise(r),e&&e.call(r,r),r},when:function(e){var t,n,i,r=0,o=J.call(arguments),a=o.length,s=1!==a||e&&re.isFunction(e.promise)?a:0,l=1===s?e:re.Deferred(),c=function(e,n,i){return function(r){n[e]=this,i[e]=arguments.length>1?J.call(arguments):r,i===t?l.notifyWith(n,i):--s||l.resolveWith(n,i)}};if(a>1)for(t=new Array(a),n=new Array(a),i=new Array(a);a>r;r++)o[r]&&re.isFunction(o[r].promise)?o[r].promise().done(c(r,i,o)).fail(l.reject).progress(c(r,n,t)):--s;return s||l.resolveWith(i,o),l.promise()}});var we;re.fn.ready=function(e){return re.ready.promise().done(e),this},re.extend({isReady:!1,readyWait:1,holdReady:function(e){e?re.readyWait++:re.ready(!0)},ready:function(e){if(e===!0?!--re.readyWait:!re.isReady){if(!he.body)return setTimeout(re.ready);re.isReady=!0,e!==!0&&--re.readyWait>0||(we.resolveWith(he,[re]),re.fn.triggerHandler&&(re(he).triggerHandler("ready"),re(he).off("ready")))}}}),re.ready.promise=function(t){if(!we)if(we=re.Deferred(),"complete"===he.readyState)setTimeout(re.ready);else if(he.addEventListener)he.addEventListener("DOMContentLoaded",s,!1),e.addEventListener("load",s,!1);else{he.attachEvent("onreadystatechange",s),e.attachEvent("onload",s);var n=!1;try{n=null==e.frameElement&&he.documentElement}catch(i){}n&&n.doScroll&&!function r(){if(!re.isReady){try{n.doScroll("left")}catch(e){return setTimeout(r,50)}a(),re.ready()}}()}return we.promise(t)};var Te,Se="undefined";for(Te in re(ne))break;ne.ownLast="0"!==Te,ne.inlineBlockNeedsLayout=!1,re(function(){var e,t,n,i;n=he.getElementsByTagName("body")[0],n&&n.style&&(t=he.createElement("div"),i=he.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(t),typeof t.style.zoom!==Se&&(t.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",ne.inlineBlockNeedsLayout=e=3===t.offsetWidth,e&&(n.style.zoom=1)),n.removeChild(i))}),function(){var e=he.createElement("div");if(null==ne.deleteExpando){ne.deleteExpando=!0;try{delete e.test}catch(t){ne.deleteExpando=!1}}e=null}(),re.acceptData=function(e){var t=re.noData[(e.nodeName+" ").toLowerCase()],n=+e.nodeType||1;return(1===n||9===n)&&(!t||t!==!0&&e.getAttribute("classid")===t)};var Ce=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Ae=/([A-Z])/g;re.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"},hasData:function(e){return e=e.nodeType?re.cache[e[re.expando]]:e[re.expando],!!e&&!c(e)},data:function(e,t,n){return u(e,t,n)},removeData:function(e,t){return d(e,t)},_data:function(e,t,n){return u(e,t,n,!0)},_removeData:function(e,t){return d(e,t,!0)}}),re.fn.extend({data:function(e,t){var n,i,r,o=this[0],a=o&&o.attributes;if(void 0===e){if(this.length&&(r=re.data(o),1===o.nodeType&&!re._data(o,"parsedAttrs"))){for(n=a.length;n--;)a[n]&&(i=a[n].name,0===i.indexOf("data-")&&(i=re.camelCase(i.slice(5)),l(o,i,r[i])));re._data(o,"parsedAttrs",!0)}return r}return"object"==typeof e?this.each(function(){re.data(this,e)}):arguments.length>1?this.each(function(){re.data(this,e,t)}):o?l(o,e,re.data(o,e)):void 0},removeData:function(e){return this.each(function(){re.removeData(this,e)})}}),re.extend({queue:function(e,t,n){var i;return e?(t=(t||"fx")+"queue",i=re._data(e,t),n&&(!i||re.isArray(n)?i=re._data(e,t,re.makeArray(n)):i.push(n)),i||[]):void 0},dequeue:function(e,t){t=t||"fx";var n=re.queue(e,t),i=n.length,r=n.shift(),o=re._queueHooks(e,t),a=function(){re.dequeue(e,t)};"inprogress"===r&&(r=n.shift(),i--),r&&("fx"===t&&n.unshift("inprogress"),delete o.stop,r.call(e,a,o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return re._data(e,n)||re._data(e,n,{empty:re.Callbacks("once memory").add(function(){re._removeData(e,t+"queue"),re._removeData(e,n)})})}}),re.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?re.queue(this[0],e):void 0===t?this:this.each(function(){var n=re.queue(this,e,t);re._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&re.dequeue(this,e)})},dequeue:function(e){return this.each(function(){re.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,r=re.Deferred(),o=this,a=this.length,s=function(){--i||r.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)n=re._data(o[a],e+"queueHooks"),n&&n.empty&&(i++,n.empty.add(s));return s(),r.promise(t)}});var ke=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Ne=["Top","Right","Bottom","Left"],Ee=function(e,t){return e=t||e,"none"===re.css(e,"display")||!re.contains(e.ownerDocument,e)},Ie=re.access=function(e,t,n,i,r,o,a){var s=0,l=e.length,c=null==n;if("object"===re.type(n)){r=!0;for(s in n)re.access(e,t,s,n[s],!0,o,a)}else if(void 0!==i&&(r=!0,re.isFunction(i)||(a=!0),c&&(a?(t.call(e,i),t=null):(c=t,t=function(e,t,n){return c.call(re(e),n)})),t))for(;l>s;s++)t(e[s],n,a?i:i.call(e[s],s,t(e[s],n)));return r?e:c?t.call(e):l?t(e[0],n):o},Pe=/^(?:checkbox|radio)$/i;!function(){var e=he.createElement("input"),t=he.createElement("div"),n=he.createDocumentFragment();if(t.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",ne.leadingWhitespace=3===t.firstChild.nodeType,ne.tbody=!t.getElementsByTagName("tbody").length,ne.htmlSerialize=!!t.getElementsByTagName("link").length,ne.html5Clone="<:nav></:nav>"!==he.createElement("nav").cloneNode(!0).outerHTML,e.type="checkbox",e.checked=!0,n.appendChild(e),ne.appendChecked=e.checked,t.innerHTML="<textarea>x</textarea>",ne.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue,n.appendChild(t),t.innerHTML="<input type='radio' checked='checked' name='t'/>",ne.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,ne.noCloneEvent=!0,t.attachEvent&&(t.attachEvent("onclick",function(){ne.noCloneEvent=!1}),t.cloneNode(!0).click()),null==ne.deleteExpando){ne.deleteExpando=!0;try{delete t.test}catch(i){ne.deleteExpando=!1}}}(),function(){var t,n,i=he.createElement("div");for(t in{submit:!0,change:!0,focusin:!0})n="on"+t,(ne[t+"Bubbles"]=n in e)||(i.setAttribute(n,"t"),ne[t+"Bubbles"]=i.attributes[n].expando===!1);i=null}();var je=/^(?:input|select|textarea)$/i,_e=/^key/,De=/^(?:mouse|pointer|contextmenu)|click/,He=/^(?:focusinfocus|focusoutblur)$/,Le=/^([^.]*)(?:\.(.+)|)$/;re.event={global:{},add:function(e,t,n,i,r){var o,a,s,l,c,u,d,f,p,h,g,m=re._data(e);if(m){for(n.handler&&(l=n,n=l.handler,r=l.selector),n.guid||(n.guid=re.guid++),(a=m.events)||(a=m.events={}),(u=m.handle)||(u=m.handle=function(e){return typeof re===Se||e&&re.event.triggered===e.type?void 0:re.event.dispatch.apply(u.elem,arguments)},u.elem=e),t=(t||"").match(xe)||[""],s=t.length;s--;)o=Le.exec(t[s])||[],p=g=o[1],h=(o[2]||"").split(".").sort(),p&&(c=re.event.special[p]||{},p=(r?c.delegateType:c.bindType)||p,c=re.event.special[p]||{},d=re.extend({type:p,origType:g,data:i,handler:n,guid:n.guid,selector:r,needsContext:r&&re.expr.match.needsContext.test(r),namespace:h.join(".")},l),(f=a[p])||(f=a[p]=[],f.delegateCount=0,c.setup&&c.setup.call(e,i,h,u)!==!1||(e.addEventListener?e.addEventListener(p,u,!1):e.attachEvent&&e.attachEvent("on"+p,u))),c.add&&(c.add.call(e,d),d.handler.guid||(d.handler.guid=n.guid)),r?f.splice(f.delegateCount++,0,d):f.push(d),re.event.global[p]=!0);e=null}},remove:function(e,t,n,i,r){var o,a,s,l,c,u,d,f,p,h,g,m=re.hasData(e)&&re._data(e);if(m&&(u=m.events)){for(t=(t||"").match(xe)||[""],c=t.length;c--;)if(s=Le.exec(t[c])||[],p=g=s[1],h=(s[2]||"").split(".").sort(),p){for(d=re.event.special[p]||{},p=(i?d.delegateType:d.bindType)||p,f=u[p]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),l=o=f.length;o--;)a=f[o],!r&&g!==a.origType||n&&n.guid!==a.guid||s&&!s.test(a.namespace)||i&&i!==a.selector&&("**"!==i||!a.selector)||(f.splice(o,1),a.selector&&f.delegateCount--,d.remove&&d.remove.call(e,a));l&&!f.length&&(d.teardown&&d.teardown.call(e,h,m.handle)!==!1||re.removeEvent(e,p,m.handle),delete u[p])}else for(p in u)re.event.remove(e,p+t[c],n,i,!0);re.isEmptyObject(u)&&(delete m.handle,re._removeData(e,"events"))}},trigger:function(t,n,i,r){var o,a,s,l,c,u,d,f=[i||he],p=te.call(t,"type")?t.type:t,h=te.call(t,"namespace")?t.namespace.split("."):[];if(s=u=i=i||he,3!==i.nodeType&&8!==i.nodeType&&!He.test(p+re.event.triggered)&&(p.indexOf(".")>=0&&(h=p.split("."),p=h.shift(),h.sort()),a=p.indexOf(":")<0&&"on"+p,t=t[re.expando]?t:new re.Event(p,"object"==typeof t&&t),t.isTrigger=r?2:3,t.namespace=h.join("."),t.namespace_re=t.namespace?new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=i),n=null==n?[t]:re.makeArray(n,[t]),c=re.event.special[p]||{},r||!c.trigger||c.trigger.apply(i,n)!==!1)){if(!r&&!c.noBubble&&!re.isWindow(i)){for(l=c.delegateType||p,He.test(l+p)||(s=s.parentNode);s;s=s.parentNode)f.push(s),u=s;u===(i.ownerDocument||he)&&f.push(u.defaultView||u.parentWindow||e)}for(d=0;(s=f[d++])&&!t.isPropagationStopped();)t.type=d>1?l:c.bindType||p,o=(re._data(s,"events")||{})[t.type]&&re._data(s,"handle"),o&&o.apply(s,n),o=a&&s[a],o&&o.apply&&re.acceptData(s)&&(t.result=o.apply(s,n),t.result===!1&&t.preventDefault());if(t.type=p,!r&&!t.isDefaultPrevented()&&(!c._default||c._default.apply(f.pop(),n)===!1)&&re.acceptData(i)&&a&&i[p]&&!re.isWindow(i)){u=i[a],u&&(i[a]=null),re.event.triggered=p;try{i[p]()}catch(g){}re.event.triggered=void 0,u&&(i[a]=u)}return t.result}},dispatch:function(e){e=re.event.fix(e);var t,n,i,r,o,a=[],s=J.call(arguments),l=(re._data(this,"events")||{})[e.type]||[],c=re.event.special[e.type]||{};if(s[0]=e,e.delegateTarget=this,!c.preDispatch||c.preDispatch.call(this,e)!==!1){for(a=re.event.handlers.call(this,e,l),t=0;(r=a[t++])&&!e.isPropagationStopped();)for(e.currentTarget=r.elem,o=0;(i=r.handlers[o++])&&!e.isImmediatePropagationStopped();)(!e.namespace_re||e.namespace_re.test(i.namespace))&&(e.handleObj=i,e.data=i.data,n=((re.event.special[i.origType]||{}).handle||i.handler).apply(r.elem,s),void 0!==n&&(e.result=n)===!1&&(e.preventDefault(),e.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,i,r,o,a=[],s=t.delegateCount,l=e.target;if(s&&l.nodeType&&(!e.button||"click"!==e.type))for(;l!=this;l=l.parentNode||this)if(1===l.nodeType&&(l.disabled!==!0||"click"!==e.type)){for(r=[],o=0;s>o;o++)i=t[o],n=i.selector+" ",void 0===r[n]&&(r[n]=i.needsContext?re(n,this).index(l)>=0:re.find(n,this,null,[l]).length),r[n]&&r.push(i);r.length&&a.push({elem:l,handlers:r})}return s<t.length&&a.push({elem:this,handlers:t.slice(s)}),a},fix:function(e){if(e[re.expando])return e;var t,n,i,r=e.type,o=e,a=this.fixHooks[r];for(a||(this.fixHooks[r]=a=De.test(r)?this.mouseHooks:_e.test(r)?this.keyHooks:{}),i=a.props?this.props.concat(a.props):this.props,e=new re.Event(o),t=i.length;t--;)n=i[t],e[n]=o[n];return e.target||(e.target=o.srcElement||he),3===e.target.nodeType&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,a.filter?a.filter(e,o):e},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,i,r,o=t.button,a=t.fromElement;return null==e.pageX&&null!=t.clientX&&(i=e.target.ownerDocument||he,r=i.documentElement,n=i.body,e.pageX=t.clientX+(r&&r.scrollLeft||n&&n.scrollLeft||0)-(r&&r.clientLeft||n&&n.clientLeft||0),e.pageY=t.clientY+(r&&r.scrollTop||n&&n.scrollTop||0)-(r&&r.clientTop||n&&n.clientTop||0)),!e.relatedTarget&&a&&(e.relatedTarget=a===e.target?t.toElement:a),e.which||void 0===o||(e.which=1&o?1:2&o?3:4&o?2:0),e}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==h()&&this.focus)try{return this.focus(),!1}catch(e){}},delegateType:"focusin"},blur:{trigger:function(){return this===h()&&this.blur?(this.blur(),!1):void 0},delegateType:"focusout"},click:{trigger:function(){return re.nodeName(this,"input")&&"checkbox"===this.type&&this.click?(this.click(),!1):void 0},_default:function(e){return re.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n,i){var r=re.extend(new re.Event,n,{type:e,isSimulated:!0,originalEvent:{}});i?re.event.trigger(r,null,t):re.event.dispatch.call(t,r),r.isDefaultPrevented()&&n.preventDefault()}},re.removeEvent=he.removeEventListener?function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)}:function(e,t,n){var i="on"+t;e.detachEvent&&(typeof e[i]===Se&&(e[i]=null),e.detachEvent(i,n))},re.Event=function(e,t){return this instanceof re.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&e.returnValue===!1?f:p):this.type=e,t&&re.extend(this,t),this.timeStamp=e&&e.timeStamp||re.now(),void(this[re.expando]=!0)):new re.Event(e,t)},re.Event.prototype={isDefaultPrevented:p,isPropagationStopped:p,isImmediatePropagationStopped:p,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=f,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=f,e&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0)},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=f,e&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),this.stopPropagation()}},re.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){re.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,i=this,r=e.relatedTarget,o=e.handleObj;return(!r||r!==i&&!re.contains(i,r))&&(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}}),ne.submitBubbles||(re.event.special.submit={setup:function(){return!re.nodeName(this,"form")&&void re.event.add(this,"click._submit keypress._submit",function(e){var t=e.target,n=re.nodeName(t,"input")||re.nodeName(t,"button")?t.form:void 0;n&&!re._data(n,"submitBubbles")&&(re.event.add(n,"submit._submit",function(e){e._submit_bubble=!0}),re._data(n,"submitBubbles",!0))})},postDispatch:function(e){e._submit_bubble&&(delete e._submit_bubble,this.parentNode&&!e.isTrigger&&re.event.simulate("submit",this.parentNode,e,!0))},teardown:function(){return!re.nodeName(this,"form")&&void re.event.remove(this,"._submit")}}),ne.changeBubbles||(re.event.special.change={setup:function(){return je.test(this.nodeName)?(("checkbox"===this.type||"radio"===this.type)&&(re.event.add(this,"propertychange._change",function(e){"checked"===e.originalEvent.propertyName&&(this._just_changed=!0)}),re.event.add(this,"click._change",function(e){this._just_changed&&!e.isTrigger&&(this._just_changed=!1),re.event.simulate("change",this,e,!0)})),!1):void re.event.add(this,"beforeactivate._change",function(e){var t=e.target;je.test(t.nodeName)&&!re._data(t,"changeBubbles")&&(re.event.add(t,"change._change",function(e){!this.parentNode||e.isSimulated||e.isTrigger||re.event.simulate("change",this.parentNode,e,!0)}),re._data(t,"changeBubbles",!0))})},handle:function(e){var t=e.target;return this!==t||e.isSimulated||e.isTrigger||"radio"!==t.type&&"checkbox"!==t.type?e.handleObj.handler.apply(this,arguments):void 0},teardown:function(){return re.event.remove(this,"._change"),!je.test(this.nodeName)}}),ne.focusinBubbles||re.each({focus:"focusin",blur:"focusout"},function(e,t){var n=function(e){re.event.simulate(t,e.target,re.event.fix(e),!0)};re.event.special[t]={setup:function(){var i=this.ownerDocument||this,r=re._data(i,t);r||i.addEventListener(e,n,!0),re._data(i,t,(r||0)+1)},teardown:function(){var i=this.ownerDocument||this,r=re._data(i,t)-1;r?re._data(i,t,r):(i.removeEventListener(e,n,!0),re._removeData(i,t))}}}),re.fn.extend({on:function(e,t,n,i,r){var o,a;if("object"==typeof e){"string"!=typeof t&&(n=n||t,t=void 0);for(o in e)this.on(o,t,n,e[o],r);return this}if(null==n&&null==i?(i=t,n=t=void 0):null==i&&("string"==typeof t?(i=n,n=void 0):(i=n,n=t,t=void 0)),i===!1)i=p;else if(!i)return this;return 1===r&&(a=i,i=function(e){return re().off(e),a.apply(this,arguments)},i.guid=a.guid||(a.guid=re.guid++)),this.each(function(){re.event.add(this,e,i,n,t)})},one:function(e,t,n,i){return this.on(e,t,n,i,1)},off:function(e,t,n){var i,r;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,re(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof e){for(r in e)this.off(r,t,e[r]);return this}return(t===!1||"function"==typeof t)&&(n=t,t=void 0),n===!1&&(n=p),this.each(function(){re.event.remove(this,e,n,t)})},trigger:function(e,t){return this.each(function(){re.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];return n?re.event.trigger(e,t,n,!0):void 0}});var Oe="abbr|article|aside|audio|bdi|canvas|data|datalist|details|figcaption|figure|footer|header|hgroup|mark|meter|nav|output|progress|section|summary|time|video",ze=/ jQuery\d+="(?:null|\d+)"/g,Fe=new RegExp("<(?:"+Oe+")[\\s/>]","i"),Me=/^\s+/,qe=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,Re=/<([\w:]+)/,We=/<tbody/i,Be=/<|&#?\w+;/,$e=/<(?:script|style|link)/i,Ve=/checked\s*(?:[^=]|=\s*.checked.)/i,Qe=/^$|\/(?:java|ecma)script/i,Xe=/^true\/(.*)/,Ue=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,Je={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:ne.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]},Ge=g(he),Ze=Ge.appendChild(he.createElement("div"));Je.optgroup=Je.option,Je.tbody=Je.tfoot=Je.colgroup=Je.caption=Je.thead,Je.th=Je.td,re.extend({clone:function(e,t,n){var i,r,o,a,s,l=re.contains(e.ownerDocument,e);if(ne.html5Clone||re.isXMLDoc(e)||!Fe.test("<"+e.nodeName+">")?o=e.cloneNode(!0):(Ze.innerHTML=e.outerHTML,Ze.removeChild(o=Ze.firstChild)),!(ne.noCloneEvent&&ne.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||re.isXMLDoc(e)))for(i=m(o),s=m(e),a=0;null!=(r=s[a]);++a)i[a]&&S(r,i[a]);if(t)if(n)for(s=s||m(e),i=i||m(o),a=0;null!=(r=s[a]);a++)T(r,i[a]);else T(e,o);return i=m(o,"script"),i.length>0&&w(i,!l&&m(e,"script")),i=s=r=null,o},buildFragment:function(e,t,n,i){for(var r,o,a,s,l,c,u,d=e.length,f=g(t),p=[],h=0;d>h;h++)if(o=e[h],o||0===o)if("object"===re.type(o))re.merge(p,o.nodeType?[o]:o);else if(Be.test(o)){for(s=s||f.appendChild(t.createElement("div")),l=(Re.exec(o)||["",""])[1].toLowerCase(),u=Je[l]||Je._default,s.innerHTML=u[1]+o.replace(qe,"<$1></$2>")+u[2],r=u[0];r--;)s=s.lastChild;if(!ne.leadingWhitespace&&Me.test(o)&&p.push(t.createTextNode(Me.exec(o)[0])),!ne.tbody)for(o="table"!==l||We.test(o)?"<table>"!==u[1]||We.test(o)?0:s:s.firstChild,r=o&&o.childNodes.length;r--;)re.nodeName(c=o.childNodes[r],"tbody")&&!c.childNodes.length&&o.removeChild(c);for(re.merge(p,s.childNodes),s.textContent="";s.firstChild;)s.removeChild(s.firstChild);s=f.lastChild}else p.push(t.createTextNode(o));for(s&&f.removeChild(s),ne.appendChecked||re.grep(m(p,"input"),v),h=0;o=p[h++];)if((!i||-1===re.inArray(o,i))&&(a=re.contains(o.ownerDocument,o),s=m(f.appendChild(o),"script"),a&&w(s),n))for(r=0;o=s[r++];)Qe.test(o.type||"")&&n.push(o);return s=null,f},cleanData:function(e,t){for(var n,i,r,o,a=0,s=re.expando,l=re.cache,c=ne.deleteExpando,u=re.event.special;null!=(n=e[a]);a++)if((t||re.acceptData(n))&&(r=n[s],o=r&&l[r])){if(o.events)for(i in o.events)u[i]?re.event.remove(n,i):re.removeEvent(n,i,o.handle);l[r]&&(delete l[r],c?delete n[s]:typeof n.removeAttribute!==Se?n.removeAttribute(s):n[s]=null,U.push(r))}}}),re.fn.extend({text:function(e){return Ie(this,function(e){return void 0===e?re.text(this):this.empty().append((this[0]&&this[0].ownerDocument||he).createTextNode(e))},null,e,arguments.length)},append:function(){return this.domManip(arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=y(this,e);t.appendChild(e)}})},prepend:function(){return this.domManip(arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=y(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},remove:function(e,t){for(var n,i=e?re.filter(e,this):this,r=0;null!=(n=i[r]);r++)t||1!==n.nodeType||re.cleanData(m(n)),n.parentNode&&(t&&re.contains(n.ownerDocument,n)&&w(m(n,"script")),n.parentNode.removeChild(n));return this},empty:function(){for(var e,t=0;null!=(e=this[t]);t++){for(1===e.nodeType&&re.cleanData(m(e,!1));e.firstChild;)e.removeChild(e.firstChild);e.options&&re.nodeName(e,"select")&&(e.options.length=0)}return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return re.clone(this,e,t)})},html:function(e){return Ie(this,function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e)return 1===t.nodeType?t.innerHTML.replace(ze,""):void 0;if(!("string"!=typeof e||$e.test(e)||!ne.htmlSerialize&&Fe.test(e)||!ne.leadingWhitespace&&Me.test(e)||Je[(Re.exec(e)||["",""])[1].toLowerCase()])){e=e.replace(qe,"<$1></$2>");try{for(;i>n;n++)t=this[n]||{},1===t.nodeType&&(re.cleanData(m(t,!1)),t.innerHTML=e);t=0}catch(r){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=arguments[0];return this.domManip(arguments,function(t){e=this.parentNode,re.cleanData(m(this)),e&&e.replaceChild(t,this)}),e&&(e.length||e.nodeType)?this:this.remove()},detach:function(e){return this.remove(e,!0)},domManip:function(e,t){e=G.apply([],e);var n,i,r,o,a,s,l=0,c=this.length,u=this,d=c-1,f=e[0],p=re.isFunction(f);if(p||c>1&&"string"==typeof f&&!ne.checkClone&&Ve.test(f))return this.each(function(n){var i=u.eq(n);p&&(e[0]=f.call(this,n,i.html())),i.domManip(e,t)});if(c&&(s=re.buildFragment(e,this[0].ownerDocument,!1,this),n=s.firstChild,1===s.childNodes.length&&(s=n),n)){for(o=re.map(m(s,"script"),x),r=o.length;c>l;l++)i=s,l!==d&&(i=re.clone(i,!0,!0),r&&re.merge(o,m(i,"script"))),t.call(this[l],i,l);if(r)for(a=o[o.length-1].ownerDocument,re.map(o,b),l=0;r>l;l++)i=o[l],Qe.test(i.type||"")&&!re._data(i,"globalEval")&&re.contains(a,i)&&(i.src?re._evalUrl&&re._evalUrl(i.src):re.globalEval((i.text||i.textContent||i.innerHTML||"").replace(Ue,"")));s=n=null}return this}}),re.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){re.fn[e]=function(e){for(var n,i=0,r=[],o=re(e),a=o.length-1;a>=i;i++)n=i===a?this:this.clone(!0),re(o[i])[t](n),Z.apply(r,n.get());return this.pushStack(r)}});var Ye,Ke={};!function(){var e;ne.shrinkWrapBlocks=function(){if(null!=e)return e;e=!1;var t,n,i;return n=he.getElementsByTagName("body")[0],n&&n.style?(t=he.createElement("div"),i=he.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(t),typeof t.style.zoom!==Se&&(t.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1",t.appendChild(he.createElement("div")).style.width="5px",e=3!==t.offsetWidth),n.removeChild(i),e):void 0}}();var et,tt,nt=/^margin/,it=new RegExp("^("+ke+")(?!px)[a-z%]+$","i"),rt=/^(top|right|bottom|left)$/;e.getComputedStyle?(et=function(e){return e.ownerDocument.defaultView.getComputedStyle(e,null)},tt=function(e,t,n){var i,r,o,a,s=e.style;return n=n||et(e),a=n?n.getPropertyValue(t)||n[t]:void 0,n&&(""!==a||re.contains(e.ownerDocument,e)||(a=re.style(e,t)),it.test(a)&&nt.test(t)&&(i=s.width,r=s.minWidth,o=s.maxWidth,s.minWidth=s.maxWidth=s.width=a,a=n.width,s.width=i,s.minWidth=r,s.maxWidth=o)),
void 0===a?a:a+""}):he.documentElement.currentStyle&&(et=function(e){return e.currentStyle},tt=function(e,t,n){var i,r,o,a,s=e.style;return n=n||et(e),a=n?n[t]:void 0,null==a&&s&&s[t]&&(a=s[t]),it.test(a)&&!rt.test(t)&&(i=s.left,r=e.runtimeStyle,o=r&&r.left,o&&(r.left=e.currentStyle.left),s.left="fontSize"===t?"1em":a,a=s.pixelLeft+"px",s.left=i,o&&(r.left=o)),void 0===a?a:a+""||"auto"}),!function(){function t(){var t,n,i,r;n=he.getElementsByTagName("body")[0],n&&n.style&&(t=he.createElement("div"),i=he.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(t),t.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;display:block;margin-top:1%;top:1%;border:1px;padding:1px;width:4px;position:absolute",o=a=!1,l=!0,e.getComputedStyle&&(o="1%"!==(e.getComputedStyle(t,null)||{}).top,a="4px"===(e.getComputedStyle(t,null)||{width:"4px"}).width,r=t.appendChild(he.createElement("div")),r.style.cssText=t.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",r.style.marginRight=r.style.width="0",t.style.width="1px",l=!parseFloat((e.getComputedStyle(r,null)||{}).marginRight)),t.innerHTML="<table><tr><td></td><td>t</td></tr></table>",r=t.getElementsByTagName("td"),r[0].style.cssText="margin:0;border:0;padding:0;display:none",s=0===r[0].offsetHeight,s&&(r[0].style.display="",r[1].style.display="none",s=0===r[0].offsetHeight),n.removeChild(i))}var n,i,r,o,a,s,l;n=he.createElement("div"),n.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",r=n.getElementsByTagName("a")[0],(i=r&&r.style)&&(i.cssText="float:left;opacity:.5",ne.opacity="0.5"===i.opacity,ne.cssFloat=!!i.cssFloat,n.style.backgroundClip="content-box",n.cloneNode(!0).style.backgroundClip="",ne.clearCloneStyle="content-box"===n.style.backgroundClip,ne.boxSizing=""===i.boxSizing||""===i.MozBoxSizing||""===i.WebkitBoxSizing,re.extend(ne,{reliableHiddenOffsets:function(){return null==s&&t(),s},boxSizingReliable:function(){return null==a&&t(),a},pixelPosition:function(){return null==o&&t(),o},reliableMarginRight:function(){return null==l&&t(),l}}))}(),re.swap=function(e,t,n,i){var r,o,a={};for(o in t)a[o]=e.style[o],e.style[o]=t[o];r=n.apply(e,i||[]);for(o in t)e.style[o]=a[o];return r};var ot=/alpha\([^)]*\)/i,at=/opacity\s*=\s*([^)]*)/,st=/^(none|table(?!-c[ea]).+)/,lt=new RegExp("^("+ke+")(.*)$","i"),ct=new RegExp("^([+-])=("+ke+")","i"),ut={position:"absolute",visibility:"hidden",display:"block"},dt={letterSpacing:"0",fontWeight:"400"},ft=["Webkit","O","Moz","ms"];re.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=tt(e,"opacity");return""===n?"1":n}}}},cssNumber:{columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{"float":ne.cssFloat?"cssFloat":"styleFloat"},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var r,o,a,s=re.camelCase(t),l=e.style;if(t=re.cssProps[s]||(re.cssProps[s]=N(l,s)),a=re.cssHooks[t]||re.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(r=a.get(e,!1,i))?r:l[t];if(o=typeof n,"string"===o&&(r=ct.exec(n))&&(n=(r[1]+1)*r[2]+parseFloat(re.css(e,t)),o="number"),null!=n&&n===n&&("number"!==o||re.cssNumber[s]||(n+="px"),ne.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),!(a&&"set"in a&&void 0===(n=a.set(e,n,i)))))try{l[t]=n}catch(c){}}},css:function(e,t,n,i){var r,o,a,s=re.camelCase(t);return t=re.cssProps[s]||(re.cssProps[s]=N(e.style,s)),a=re.cssHooks[t]||re.cssHooks[s],a&&"get"in a&&(o=a.get(e,!0,n)),void 0===o&&(o=tt(e,t,i)),"normal"===o&&t in dt&&(o=dt[t]),""===n||n?(r=parseFloat(o),n===!0||re.isNumeric(r)?r||0:o):o}}),re.each(["height","width"],function(e,t){re.cssHooks[t]={get:function(e,n,i){return n?st.test(re.css(e,"display"))&&0===e.offsetWidth?re.swap(e,ut,function(){return j(e,t,i)}):j(e,t,i):void 0},set:function(e,n,i){var r=i&&et(e);return I(e,n,i?P(e,t,i,ne.boxSizing&&"border-box"===re.css(e,"boxSizing",!1,r),r):0)}}}),ne.opacity||(re.cssHooks.opacity={get:function(e,t){return at.test((t&&e.currentStyle?e.currentStyle.filter:e.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":t?"1":""},set:function(e,t){var n=e.style,i=e.currentStyle,r=re.isNumeric(t)?"alpha(opacity="+100*t+")":"",o=i&&i.filter||n.filter||"";n.zoom=1,(t>=1||""===t)&&""===re.trim(o.replace(ot,""))&&n.removeAttribute&&(n.removeAttribute("filter"),""===t||i&&!i.filter)||(n.filter=ot.test(o)?o.replace(ot,r):o+" "+r)}}),re.cssHooks.marginRight=k(ne.reliableMarginRight,function(e,t){return t?re.swap(e,{display:"inline-block"},tt,[e,"marginRight"]):void 0}),re.each({margin:"",padding:"",border:"Width"},function(e,t){re.cssHooks[e+t]={expand:function(n){for(var i=0,r={},o="string"==typeof n?n.split(" "):[n];4>i;i++)r[e+Ne[i]+t]=o[i]||o[i-2]||o[0];return r}},nt.test(e)||(re.cssHooks[e+t].set=I)}),re.fn.extend({css:function(e,t){return Ie(this,function(e,t,n){var i,r,o={},a=0;if(re.isArray(t)){for(i=et(e),r=t.length;r>a;a++)o[t[a]]=re.css(e,t[a],!1,i);return o}return void 0!==n?re.style(e,t,n):re.css(e,t)},e,t,arguments.length>1)},show:function(){return E(this,!0)},hide:function(){return E(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){Ee(this)?re(this).show():re(this).hide()})}}),re.Tween=_,_.prototype={constructor:_,init:function(e,t,n,i,r,o){this.elem=e,this.prop=n,this.easing=r||"swing",this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=o||(re.cssNumber[n]?"":"px")},cur:function(){var e=_.propHooks[this.prop];return e&&e.get?e.get(this):_.propHooks._default.get(this)},run:function(e){var t,n=_.propHooks[this.prop];return this.pos=t=this.options.duration?re.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):_.propHooks._default.set(this),this}},_.prototype.init.prototype=_.prototype,_.propHooks={_default:{get:function(e){var t;return null==e.elem[e.prop]||e.elem.style&&null!=e.elem.style[e.prop]?(t=re.css(e.elem,e.prop,""),t&&"auto"!==t?t:0):e.elem[e.prop]},set:function(e){re.fx.step[e.prop]?re.fx.step[e.prop](e):e.elem.style&&(null!=e.elem.style[re.cssProps[e.prop]]||re.cssHooks[e.prop])?re.style(e.elem,e.prop,e.now+e.unit):e.elem[e.prop]=e.now}}},_.propHooks.scrollTop=_.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},re.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2}},re.fx=_.prototype.init,re.fx.step={};var pt,ht,gt=/^(?:toggle|show|hide)$/,mt=new RegExp("^(?:([+-])=|)("+ke+")([a-z%]*)$","i"),vt=/queueHooks$/,yt=[O],xt={"*":[function(e,t){var n=this.createTween(e,t),i=n.cur(),r=mt.exec(t),o=r&&r[3]||(re.cssNumber[e]?"":"px"),a=(re.cssNumber[e]||"px"!==o&&+i)&&mt.exec(re.css(n.elem,e)),s=1,l=20;if(a&&a[3]!==o){o=o||a[3],r=r||[],a=+i||1;do s=s||".5",a/=s,re.style(n.elem,e,a+o);while(s!==(s=n.cur()/i)&&1!==s&&--l)}return r&&(a=n.start=+a||+i||0,n.unit=o,n.end=r[1]?a+(r[1]+1)*r[2]:+r[2]),n}]};re.Animation=re.extend(F,{tweener:function(e,t){re.isFunction(e)?(t=e,e=["*"]):e=e.split(" ");for(var n,i=0,r=e.length;r>i;i++)n=e[i],xt[n]=xt[n]||[],xt[n].unshift(t)},prefilter:function(e,t){t?yt.unshift(e):yt.push(e)}}),re.speed=function(e,t,n){var i=e&&"object"==typeof e?re.extend({},e):{complete:n||!n&&t||re.isFunction(e)&&e,duration:e,easing:n&&t||t&&!re.isFunction(t)&&t};return i.duration=re.fx.off?0:"number"==typeof i.duration?i.duration:i.duration in re.fx.speeds?re.fx.speeds[i.duration]:re.fx.speeds._default,(null==i.queue||i.queue===!0)&&(i.queue="fx"),i.old=i.complete,i.complete=function(){re.isFunction(i.old)&&i.old.call(this),i.queue&&re.dequeue(this,i.queue)},i},re.fn.extend({fadeTo:function(e,t,n,i){return this.filter(Ee).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(e,t,n,i){var r=re.isEmptyObject(e),o=re.speed(t,n,i),a=function(){var t=F(this,re.extend({},e),o);(r||re._data(this,"finish"))&&t.stop(!0)};return a.finish=a,r||o.queue===!1?this.each(a):this.queue(o.queue,a)},stop:function(e,t,n){var i=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&e!==!1&&this.queue(e||"fx",[]),this.each(function(){var t=!0,r=null!=e&&e+"queueHooks",o=re.timers,a=re._data(this);if(r)a[r]&&a[r].stop&&i(a[r]);else for(r in a)a[r]&&a[r].stop&&vt.test(r)&&i(a[r]);for(r=o.length;r--;)o[r].elem!==this||null!=e&&o[r].queue!==e||(o[r].anim.stop(n),t=!1,o.splice(r,1));(t||!n)&&re.dequeue(this,e)})},finish:function(e){return e!==!1&&(e=e||"fx"),this.each(function(){var t,n=re._data(this),i=n[e+"queue"],r=n[e+"queueHooks"],o=re.timers,a=i?i.length:0;for(n.finish=!0,re.queue(this,e,[]),r&&r.stop&&r.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;a>t;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete n.finish})}}),re.each(["toggle","show","hide"],function(e,t){var n=re.fn[t];re.fn[t]=function(e,i,r){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(H(t,!0),e,i,r)}}),re.each({slideDown:H("show"),slideUp:H("hide"),slideToggle:H("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){re.fn[e]=function(e,n,i){return this.animate(t,e,n,i)}}),re.timers=[],re.fx.tick=function(){var e,t=re.timers,n=0;for(pt=re.now();n<t.length;n++)e=t[n],e()||t[n]!==e||t.splice(n--,1);t.length||re.fx.stop(),pt=void 0},re.fx.timer=function(e){re.timers.push(e),e()?re.fx.start():re.timers.pop()},re.fx.interval=13,re.fx.start=function(){ht||(ht=setInterval(re.fx.tick,re.fx.interval))},re.fx.stop=function(){clearInterval(ht),ht=null},re.fx.speeds={slow:600,fast:200,_default:400},re.fn.delay=function(e,t){return e=re.fx?re.fx.speeds[e]||e:e,t=t||"fx",this.queue(t,function(t,n){var i=setTimeout(t,e);n.stop=function(){clearTimeout(i)}})},function(){var e,t,n,i,r;t=he.createElement("div"),t.setAttribute("className","t"),t.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",i=t.getElementsByTagName("a")[0],n=he.createElement("select"),r=n.appendChild(he.createElement("option")),e=t.getElementsByTagName("input")[0],i.style.cssText="top:1px",ne.getSetAttribute="t"!==t.className,ne.style=/top/.test(i.getAttribute("style")),ne.hrefNormalized="/a"===i.getAttribute("href"),ne.checkOn=!!e.value,ne.optSelected=r.selected,ne.enctype=!!he.createElement("form").enctype,n.disabled=!0,ne.optDisabled=!r.disabled,e=he.createElement("input"),e.setAttribute("value",""),ne.input=""===e.getAttribute("value"),e.value="t",e.setAttribute("type","radio"),ne.radioValue="t"===e.value}();var bt=/\r/g;re.fn.extend({val:function(e){var t,n,i,r=this[0];return arguments.length?(i=re.isFunction(e),this.each(function(n){var r;1===this.nodeType&&(r=i?e.call(this,n,re(this).val()):e,null==r?r="":"number"==typeof r?r+="":re.isArray(r)&&(r=re.map(r,function(e){return null==e?"":e+""})),t=re.valHooks[this.type]||re.valHooks[this.nodeName.toLowerCase()],t&&"set"in t&&void 0!==t.set(this,r,"value")||(this.value=r))})):r?(t=re.valHooks[r.type]||re.valHooks[r.nodeName.toLowerCase()],t&&"get"in t&&void 0!==(n=t.get(r,"value"))?n:(n=r.value,"string"==typeof n?n.replace(bt,""):null==n?"":n)):void 0}}),re.extend({valHooks:{option:{get:function(e){var t=re.find.attr(e,"value");return null!=t?t:re.trim(re.text(e))}},select:{get:function(e){for(var t,n,i=e.options,r=e.selectedIndex,o="select-one"===e.type||0>r,a=o?null:[],s=o?r+1:i.length,l=0>r?s:o?r:0;s>l;l++)if(n=i[l],!(!n.selected&&l!==r||(ne.optDisabled?n.disabled:null!==n.getAttribute("disabled"))||n.parentNode.disabled&&re.nodeName(n.parentNode,"optgroup"))){if(t=re(n).val(),o)return t;a.push(t)}return a},set:function(e,t){for(var n,i,r=e.options,o=re.makeArray(t),a=r.length;a--;)if(i=r[a],re.inArray(re.valHooks.option.get(i),o)>=0)try{i.selected=n=!0}catch(s){i.scrollHeight}else i.selected=!1;return n||(e.selectedIndex=-1),r}}}}),re.each(["radio","checkbox"],function(){re.valHooks[this]={set:function(e,t){return re.isArray(t)?e.checked=re.inArray(re(e).val(),t)>=0:void 0}},ne.checkOn||(re.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var wt,Tt,St=re.expr.attrHandle,Ct=/^(?:checked|selected)$/i,At=ne.getSetAttribute,kt=ne.input;re.fn.extend({attr:function(e,t){return Ie(this,re.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){re.removeAttr(this,e)})}}),re.extend({attr:function(e,t,n){var i,r,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return typeof e.getAttribute===Se?re.prop(e,t,n):(1===o&&re.isXMLDoc(e)||(t=t.toLowerCase(),i=re.attrHooks[t]||(re.expr.match.bool.test(t)?Tt:wt)),void 0===n?i&&"get"in i&&null!==(r=i.get(e,t))?r:(r=re.find.attr(e,t),null==r?void 0:r):null!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):void re.removeAttr(e,t))},removeAttr:function(e,t){var n,i,r=0,o=t&&t.match(xe);if(o&&1===e.nodeType)for(;n=o[r++];)i=re.propFix[n]||n,re.expr.match.bool.test(n)?kt&&At||!Ct.test(n)?e[i]=!1:e[re.camelCase("default-"+n)]=e[i]=!1:re.attr(e,n,""),e.removeAttribute(At?n:i)},attrHooks:{type:{set:function(e,t){if(!ne.radioValue&&"radio"===t&&re.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}}}),Tt={set:function(e,t,n){return t===!1?re.removeAttr(e,n):kt&&At||!Ct.test(n)?e.setAttribute(!At&&re.propFix[n]||n,n):e[re.camelCase("default-"+n)]=e[n]=!0,n}},re.each(re.expr.match.bool.source.match(/\w+/g),function(e,t){var n=St[t]||re.find.attr;St[t]=kt&&At||!Ct.test(t)?function(e,t,i){var r,o;return i||(o=St[t],St[t]=r,r=null!=n(e,t,i)?t.toLowerCase():null,St[t]=o),r}:function(e,t,n){return n?void 0:e[re.camelCase("default-"+t)]?t.toLowerCase():null}}),kt&&At||(re.attrHooks.value={set:function(e,t,n){return re.nodeName(e,"input")?void(e.defaultValue=t):wt&&wt.set(e,t,n)}}),At||(wt={set:function(e,t,n){var i=e.getAttributeNode(n);return i||e.setAttributeNode(i=e.ownerDocument.createAttribute(n)),i.value=t+="","value"===n||t===e.getAttribute(n)?t:void 0}},St.id=St.name=St.coords=function(e,t,n){var i;return n?void 0:(i=e.getAttributeNode(t))&&""!==i.value?i.value:null},re.valHooks.button={get:function(e,t){var n=e.getAttributeNode(t);return n&&n.specified?n.value:void 0},set:wt.set},re.attrHooks.contenteditable={set:function(e,t,n){wt.set(e,""!==t&&t,n)}},re.each(["width","height"],function(e,t){re.attrHooks[t]={set:function(e,n){return""===n?(e.setAttribute(t,"auto"),n):void 0}}})),ne.style||(re.attrHooks.style={get:function(e){return e.style.cssText||void 0},set:function(e,t){return e.style.cssText=t+""}});var Nt=/^(?:input|select|textarea|button|object)$/i,Et=/^(?:a|area)$/i;re.fn.extend({prop:function(e,t){return Ie(this,re.prop,e,t,arguments.length>1)},removeProp:function(e){return e=re.propFix[e]||e,this.each(function(){try{this[e]=void 0,delete this[e]}catch(t){}})}}),re.extend({propFix:{"for":"htmlFor","class":"className"},prop:function(e,t,n){var i,r,o,a=e.nodeType;if(e&&3!==a&&8!==a&&2!==a)return o=1!==a||!re.isXMLDoc(e),o&&(t=re.propFix[t]||t,r=re.propHooks[t]),void 0!==n?r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:e[t]=n:r&&"get"in r&&null!==(i=r.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=re.find.attr(e,"tabindex");return t?parseInt(t,10):Nt.test(e.nodeName)||Et.test(e.nodeName)&&e.href?0:-1}}}}),ne.hrefNormalized||re.each(["href","src"],function(e,t){re.propHooks[t]={get:function(e){return e.getAttribute(t,4)}}}),ne.optSelected||(re.propHooks.selected={get:function(e){var t=e.parentNode;return t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex),null}}),re.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){re.propFix[this.toLowerCase()]=this}),ne.enctype||(re.propFix.enctype="encoding");var It=/[\t\r\n\f]/g;re.fn.extend({addClass:function(e){var t,n,i,r,o,a,s=0,l=this.length,c="string"==typeof e&&e;if(re.isFunction(e))return this.each(function(t){re(this).addClass(e.call(this,t,this.className))});if(c)for(t=(e||"").match(xe)||[];l>s;s++)if(n=this[s],i=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(It," "):" ")){for(o=0;r=t[o++];)i.indexOf(" "+r+" ")<0&&(i+=r+" ");a=re.trim(i),n.className!==a&&(n.className=a)}return this},removeClass:function(e){var t,n,i,r,o,a,s=0,l=this.length,c=0===arguments.length||"string"==typeof e&&e;if(re.isFunction(e))return this.each(function(t){re(this).removeClass(e.call(this,t,this.className))});if(c)for(t=(e||"").match(xe)||[];l>s;s++)if(n=this[s],i=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(It," "):"")){for(o=0;r=t[o++];)for(;i.indexOf(" "+r+" ")>=0;)i=i.replace(" "+r+" "," ");a=e?re.trim(i):"",n.className!==a&&(n.className=a)}return this},toggleClass:function(e,t){var n=typeof e;return"boolean"==typeof t&&"string"===n?t?this.addClass(e):this.removeClass(e):this.each(re.isFunction(e)?function(n){re(this).toggleClass(e.call(this,n,this.className,t),t)}:function(){if("string"===n)for(var t,i=0,r=re(this),o=e.match(xe)||[];t=o[i++];)r.hasClass(t)?r.removeClass(t):r.addClass(t);else(n===Se||"boolean"===n)&&(this.className&&re._data(this,"__className__",this.className),this.className=this.className||e===!1?"":re._data(this,"__className__")||"")})},hasClass:function(e){for(var t=" "+e+" ",n=0,i=this.length;i>n;n++)if(1===this[n].nodeType&&(" "+this[n].className+" ").replace(It," ").indexOf(t)>=0)return!0;return!1}}),re.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,t){re.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}),re.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)},bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}});var Pt=re.now(),jt=/\?/,_t=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;re.parseJSON=function(t){if(e.JSON&&e.JSON.parse)return e.JSON.parse(t+"");var n,i=null,r=re.trim(t+"");return r&&!re.trim(r.replace(_t,function(e,t,r,o){return n&&t&&(i=0),0===i?e:(n=r||t,i+=!o-!r,"")}))?Function("return "+r)():re.error("Invalid JSON: "+t)},re.parseXML=function(t){var n,i;if(!t||"string"!=typeof t)return null;try{e.DOMParser?(i=new DOMParser,n=i.parseFromString(t,"text/xml")):(n=new ActiveXObject("Microsoft.XMLDOM"),n.async="false",n.loadXML(t))}catch(r){n=void 0}return n&&n.documentElement&&!n.getElementsByTagName("parsererror").length||re.error("Invalid XML: "+t),n};var Dt,Ht,Lt=/#.*$/,Ot=/([?&])_=[^&]*/,zt=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,Ft=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Mt=/^(?:GET|HEAD)$/,qt=/^\/\//,Rt=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Wt={},Bt={},$t="*/".concat("*");try{Ht=location.href}catch(Vt){Ht=he.createElement("a"),Ht.href="",Ht=Ht.href}Dt=Rt.exec(Ht.toLowerCase())||[],re.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ht,type:"GET",isLocal:Ft.test(Dt[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":$t,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":re.parseJSON,"text xml":re.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?R(R(e,re.ajaxSettings),t):R(re.ajaxSettings,e)},ajaxPrefilter:M(Wt),ajaxTransport:M(Bt),ajax:function(e,t){function n(e,t,n,i){var r,u,v,y,b,T=t;2!==x&&(x=2,s&&clearTimeout(s),c=void 0,a=i||"",w.readyState=e>0?4:0,r=e>=200&&300>e||304===e,n&&(y=W(d,w,n)),y=B(d,y,w,r),r?(d.ifModified&&(b=w.getResponseHeader("Last-Modified"),b&&(re.lastModified[o]=b),b=w.getResponseHeader("etag"),b&&(re.etag[o]=b)),204===e||"HEAD"===d.type?T="nocontent":304===e?T="notmodified":(T=y.state,u=y.data,v=y.error,r=!v)):(v=T,(e||!T)&&(T="error",0>e&&(e=0))),w.status=e,w.statusText=(t||T)+"",r?h.resolveWith(f,[u,T,w]):h.rejectWith(f,[w,T,v]),w.statusCode(m),m=void 0,l&&p.trigger(r?"ajaxSuccess":"ajaxError",[w,d,r?u:v]),g.fireWith(f,[w,T]),l&&(p.trigger("ajaxComplete",[w,d]),--re.active||re.event.trigger("ajaxStop")))}"object"==typeof e&&(t=e,e=void 0),t=t||{};var i,r,o,a,s,l,c,u,d=re.ajaxSetup({},t),f=d.context||d,p=d.context&&(f.nodeType||f.jquery)?re(f):re.event,h=re.Deferred(),g=re.Callbacks("once memory"),m=d.statusCode||{},v={},y={},x=0,b="canceled",w={readyState:0,getResponseHeader:function(e){var t;if(2===x){if(!u)for(u={};t=zt.exec(a);)u[t[1].toLowerCase()]=t[2];t=u[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===x?a:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return x||(e=y[n]=y[n]||e,v[e]=t),this},overrideMimeType:function(e){return x||(d.mimeType=e),this},statusCode:function(e){var t;if(e)if(2>x)for(t in e)m[t]=[m[t],e[t]];else w.always(e[w.status]);return this},abort:function(e){var t=e||b;return c&&c.abort(t),n(0,t),this}};if(h.promise(w).complete=g.add,w.success=w.done,w.error=w.fail,d.url=((e||d.url||Ht)+"").replace(Lt,"").replace(qt,Dt[1]+"//"),d.type=t.method||t.type||d.method||d.type,d.dataTypes=re.trim(d.dataType||"*").toLowerCase().match(xe)||[""],null==d.crossDomain&&(i=Rt.exec(d.url.toLowerCase()),d.crossDomain=!(!i||i[1]===Dt[1]&&i[2]===Dt[2]&&(i[3]||("http:"===i[1]?"80":"443"))===(Dt[3]||("http:"===Dt[1]?"80":"443")))),d.data&&d.processData&&"string"!=typeof d.data&&(d.data=re.param(d.data,d.traditional)),q(Wt,d,t,w),2===x)return w;l=d.global,l&&0===re.active++&&re.event.trigger("ajaxStart"),d.type=d.type.toUpperCase(),d.hasContent=!Mt.test(d.type),o=d.url,d.hasContent||(d.data&&(o=d.url+=(jt.test(o)?"&":"?")+d.data,delete d.data),d.cache===!1&&(d.url=Ot.test(o)?o.replace(Ot,"$1_="+Pt++):o+(jt.test(o)?"&":"?")+"_="+Pt++)),d.ifModified&&(re.lastModified[o]&&w.setRequestHeader("If-Modified-Since",re.lastModified[o]),re.etag[o]&&w.setRequestHeader("If-None-Match",re.etag[o])),(d.data&&d.hasContent&&d.contentType!==!1||t.contentType)&&w.setRequestHeader("Content-Type",d.contentType),w.setRequestHeader("Accept",d.dataTypes[0]&&d.accepts[d.dataTypes[0]]?d.accepts[d.dataTypes[0]]+("*"!==d.dataTypes[0]?", "+$t+"; q=0.01":""):d.accepts["*"]);for(r in d.headers)w.setRequestHeader(r,d.headers[r]);if(d.beforeSend&&(d.beforeSend.call(f,w,d)===!1||2===x))return w.abort();b="abort";for(r in{success:1,error:1,complete:1})w[r](d[r]);if(c=q(Bt,d,t,w)){w.readyState=1,l&&p.trigger("ajaxSend",[w,d]),d.async&&d.timeout>0&&(s=setTimeout(function(){w.abort("timeout")},d.timeout));try{x=1,c.send(v,n)}catch(T){if(!(2>x))throw T;n(-1,T)}}else n(-1,"No Transport");return w},getJSON:function(e,t,n){return re.get(e,t,n,"json")},getScript:function(e,t){return re.get(e,void 0,t,"script")}}),re.each(["get","post"],function(e,t){re[t]=function(e,n,i,r){return re.isFunction(n)&&(r=r||i,i=n,n=void 0),re.ajax({url:e,type:t,dataType:r,data:n,success:i})}}),re.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){re.fn[t]=function(e){return this.on(t,e)}}),re._evalUrl=function(e){return re.ajax({url:e,type:"GET",dataType:"script",async:!1,global:!1,"throws":!0})},re.fn.extend({wrapAll:function(e){if(re.isFunction(e))return this.each(function(t){re(this).wrapAll(e.call(this,t))});if(this[0]){var t=re(e,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstChild&&1===e.firstChild.nodeType;)e=e.firstChild;return e}).append(this)}return this},wrapInner:function(e){return this.each(re.isFunction(e)?function(t){re(this).wrapInner(e.call(this,t))}:function(){var t=re(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=re.isFunction(e);return this.each(function(n){re(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(){return this.parent().each(function(){re.nodeName(this,"body")||re(this).replaceWith(this.childNodes)}).end()}}),re.expr.filters.hidden=function(e){return e.offsetWidth<=0&&e.offsetHeight<=0||!ne.reliableHiddenOffsets()&&"none"===(e.style&&e.style.display||re.css(e,"display"))},re.expr.filters.visible=function(e){return!re.expr.filters.hidden(e)};var Qt=/%20/g,Xt=/\[\]$/,Ut=/\r?\n/g,Jt=/^(?:submit|button|image|reset|file)$/i,Gt=/^(?:input|select|textarea|keygen)/i;re.param=function(e,t){var n,i=[],r=function(e,t){t=re.isFunction(t)?t():null==t?"":t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(void 0===t&&(t=re.ajaxSettings&&re.ajaxSettings.traditional),re.isArray(e)||e.jquery&&!re.isPlainObject(e))re.each(e,function(){r(this.name,this.value)});else for(n in e)$(n,e[n],t,r);return i.join("&").replace(Qt,"+")},re.fn.extend({serialize:function(){return re.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=re.prop(this,"elements");return e?re.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!re(this).is(":disabled")&&Gt.test(this.nodeName)&&!Jt.test(e)&&(this.checked||!Pe.test(e))}).map(function(e,t){var n=re(this).val();return null==n?null:re.isArray(n)?re.map(n,function(e){return{name:t.name,value:e.replace(Ut,"\r\n")}}):{name:t.name,value:n.replace(Ut,"\r\n")}}).get()}}),re.ajaxSettings.xhr=void 0!==e.ActiveXObject?function(){return!this.isLocal&&/^(get|post|head|put|delete|options)$/i.test(this.type)&&V()||Q()}:V;var Zt=0,Yt={},Kt=re.ajaxSettings.xhr();e.ActiveXObject&&re(e).on("unload",function(){for(var e in Yt)Yt[e](void 0,!0)}),ne.cors=!!Kt&&"withCredentials"in Kt,Kt=ne.ajax=!!Kt,Kt&&re.ajaxTransport(function(e){if(!e.crossDomain||ne.cors){var t;return{send:function(n,i){var r,o=e.xhr(),a=++Zt;if(o.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(r in e.xhrFields)o[r]=e.xhrFields[r];e.mimeType&&o.overrideMimeType&&o.overrideMimeType(e.mimeType),e.crossDomain||n["X-Requested-With"]||(n["X-Requested-With"]="XMLHttpRequest");for(r in n)void 0!==n[r]&&o.setRequestHeader(r,n[r]+"");o.send(e.hasContent&&e.data||null),t=function(n,r){var s,l,c;if(t&&(r||4===o.readyState))if(delete Yt[a],t=void 0,o.onreadystatechange=re.noop,r)4!==o.readyState&&o.abort();else{c={},s=o.status,"string"==typeof o.responseText&&(c.text=o.responseText);try{l=o.statusText}catch(u){l=""}s||!e.isLocal||e.crossDomain?1223===s&&(s=204):s=c.text?200:404}c&&i(s,l,c,o.getAllResponseHeaders())},e.async?4===o.readyState?setTimeout(t):o.onreadystatechange=Yt[a]=t:t()},abort:function(){t&&t(void 0,!0)}}}}),re.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(e){return re.globalEval(e),e}}}),re.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)}),re.ajaxTransport("script",function(e){if(e.crossDomain){var t,n=he.head||re("head")[0]||he.documentElement;return{send:function(i,r){t=he.createElement("script"),t.async=!0,e.scriptCharset&&(t.charset=e.scriptCharset),t.src=e.url,t.onload=t.onreadystatechange=function(e,n){(n||!t.readyState||/loaded|complete/.test(t.readyState))&&(t.onload=t.onreadystatechange=null,t.parentNode&&t.parentNode.removeChild(t),t=null,n||r(200,"success"))},n.insertBefore(t,n.firstChild)},abort:function(){t&&t.onload(void 0,!0)}}}});var en=[],tn=/(=)\?(?=&|$)|\?\?/;re.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=en.pop()||re.expando+"_"+Pt++;return this[e]=!0,e}}),re.ajaxPrefilter("json jsonp",function(t,n,i){var r,o,a,s=t.jsonp!==!1&&(tn.test(t.url)?"url":"string"==typeof t.data&&!(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&tn.test(t.data)&&"data");return s||"jsonp"===t.dataTypes[0]?(r=t.jsonpCallback=re.isFunction(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,s?t[s]=t[s].replace(tn,"$1"+r):t.jsonp!==!1&&(t.url+=(jt.test(t.url)?"&":"?")+t.jsonp+"="+r),t.converters["script json"]=function(){return a||re.error(r+" was not called"),a[0]},t.dataTypes[0]="json",o=e[r],e[r]=function(){a=arguments},i.always(function(){e[r]=o,t[r]&&(t.jsonpCallback=n.jsonpCallback,en.push(r)),a&&re.isFunction(o)&&o(a[0]),a=o=void 0}),"script"):void 0}),re.parseHTML=function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||he;var i=de.exec(e),r=!n&&[];return i?[t.createElement(i[1])]:(i=re.buildFragment([e],t,r),r&&r.length&&re(r).remove(),re.merge([],i.childNodes))};var nn=re.fn.load;re.fn.load=function(e,t,n){if("string"!=typeof e&&nn)return nn.apply(this,arguments);var i,r,o,a=this,s=e.indexOf(" ");return s>=0&&(i=re.trim(e.slice(s,e.length)),e=e.slice(0,s)),re.isFunction(t)?(n=t,t=void 0):t&&"object"==typeof t&&(o="POST"),a.length>0&&re.ajax({url:e,type:o,dataType:"html",data:t}).done(function(e){r=arguments,a.html(i?re("<div>").append(re.parseHTML(e)).find(i):e)}).complete(n&&function(e,t){a.each(n,r||[e.responseText,t,e])}),this},re.expr.filters.animated=function(e){return re.grep(re.timers,function(t){return e===t.elem}).length};var rn=e.document.documentElement;re.offset={setOffset:function(e,t,n){var i,r,o,a,s,l,c,u=re.css(e,"position"),d=re(e),f={};"static"===u&&(e.style.position="relative"),s=d.offset(),o=re.css(e,"top"),l=re.css(e,"left"),c=("absolute"===u||"fixed"===u)&&re.inArray("auto",[o,l])>-1,c?(i=d.position(),a=i.top,r=i.left):(a=parseFloat(o)||0,r=parseFloat(l)||0),re.isFunction(t)&&(t=t.call(e,n,s)),null!=t.top&&(f.top=t.top-s.top+a),null!=t.left&&(f.left=t.left-s.left+r),"using"in t?t.using.call(e,f):d.css(f)}},re.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){re.offset.setOffset(this,e,t)});var t,n,i={top:0,left:0},r=this[0],o=r&&r.ownerDocument;return o?(t=o.documentElement,re.contains(t,r)?(typeof r.getBoundingClientRect!==Se&&(i=r.getBoundingClientRect()),n=X(o),{top:i.top+(n.pageYOffset||t.scrollTop)-(t.clientTop||0),left:i.left+(n.pageXOffset||t.scrollLeft)-(t.clientLeft||0)}):i):void 0},position:function(){if(this[0]){var e,t,n={top:0,left:0},i=this[0];return"fixed"===re.css(i,"position")?t=i.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),re.nodeName(e[0],"html")||(n=e.offset()),n.top+=re.css(e[0],"borderTopWidth",!0),n.left+=re.css(e[0],"borderLeftWidth",!0)),{top:t.top-n.top-re.css(i,"marginTop",!0),left:t.left-n.left-re.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent||rn;e&&!re.nodeName(e,"html")&&"static"===re.css(e,"position");)e=e.offsetParent;return e||rn})}}),re.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n=/Y/.test(t);re.fn[e]=function(i){return Ie(this,function(e,i,r){var o=X(e);return void 0===r?o?t in o?o[t]:o.document.documentElement[i]:e[i]:void(o?o.scrollTo(n?re(o).scrollLeft():r,n?r:re(o).scrollTop()):e[i]=r)},e,i,arguments.length,null)}}),re.each(["top","left"],function(e,t){re.cssHooks[t]=k(ne.pixelPosition,function(e,n){return n?(n=tt(e,t),it.test(n)?re(e).position()[t]+"px":n):void 0})}),re.each({Height:"height",Width:"width"},function(e,t){re.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,i){re.fn[i]=function(i,r){var o=arguments.length&&(n||"boolean"!=typeof i),a=n||(i===!0||r===!0?"margin":"border");return Ie(this,function(t,n,i){var r;return re.isWindow(t)?t.document.documentElement["client"+e]:9===t.nodeType?(r=t.documentElement,Math.max(t.body["scroll"+e],r["scroll"+e],t.body["offset"+e],r["offset"+e],r["client"+e])):void 0===i?re.css(t,n,a):re.style(t,n,i,a);
},t,o?i:void 0,o,null)}})}),re.fn.size=function(){return this.length},re.fn.andSelf=re.fn.addBack,"function"==typeof define&&define.amd&&define("jquery",[],function(){return re});var on=e.jQuery,an=e.$;return re.noConflict=function(t){return e.$===re&&(e.$=an),t&&e.jQuery===re&&(e.jQuery=on),re},typeof t===Se&&(e.jQuery=e.$=re),re}),void 0===jQuery.migrateMute&&(jQuery.migrateMute=!0),function(e,t,n){function i(n){var i=t.console;o[n]||(o[n]=!0,e.migrateWarnings.push(n),i&&i.warn&&!e.migrateMute&&(i.warn("JQMIGRATE: "+n),e.migrateTrace&&i.trace&&i.trace()))}function r(t,r,o,a){if(Object.defineProperty)try{return Object.defineProperty(t,r,{configurable:!0,enumerable:!0,get:function(){return i(a),o},set:function(e){i(a),o=e}}),n}catch(s){}e._definePropertyBroken=!0,t[r]=o}var o={};e.migrateWarnings=[],!e.migrateMute&&t.console&&t.console.log&&t.console.log("JQMIGRATE: Logging is active"),e.migrateTrace===n&&(e.migrateTrace=!0),e.migrateReset=function(){o={},e.migrateWarnings.length=0},"BackCompat"===document.compatMode&&i("jQuery is not compatible with Quirks Mode");var a=e("<input/>",{size:1}).attr("size")&&e.attrFn,s=e.attr,l=e.attrHooks.value&&e.attrHooks.value.get||function(){return null},c=e.attrHooks.value&&e.attrHooks.value.set||function(){return n},u=/^(?:input|button)$/i,d=/^[238]$/,f=/^(?:autofocus|autoplay|async|checked|controls|defer|disabled|hidden|loop|multiple|open|readonly|required|scoped|selected)$/i,p=/^(?:checked|selected)$/i;r(e,"attrFn",a||{},"jQuery.attrFn is deprecated"),e.attr=function(t,r,o,l){var c=r.toLowerCase(),h=t&&t.nodeType;return l&&(4>s.length&&i("jQuery.fn.attr( props, pass ) is deprecated"),t&&!d.test(h)&&(a?r in a:e.isFunction(e.fn[r])))?e(t)[r](o):("type"===r&&o!==n&&u.test(t.nodeName)&&t.parentNode&&i("Can't change the 'type' of an input or button in IE 6/7/8"),!e.attrHooks[c]&&f.test(c)&&(e.attrHooks[c]={get:function(t,i){var r,o=e.prop(t,i);return o===!0||"boolean"!=typeof o&&(r=t.getAttributeNode(i))&&r.nodeValue!==!1?i.toLowerCase():n},set:function(t,n,i){var r;return n===!1?e.removeAttr(t,i):(r=e.propFix[i]||i,r in t&&(t[r]=!0),t.setAttribute(i,i.toLowerCase())),i}},p.test(c)&&i("jQuery.fn.attr('"+c+"') may use property instead of attribute")),s.call(e,t,r,o))},e.attrHooks.value={get:function(e,t){var n=(e.nodeName||"").toLowerCase();return"button"===n?l.apply(this,arguments):("input"!==n&&"option"!==n&&i("jQuery.fn.attr('value') no longer gets properties"),t in e?e.value:null)},set:function(e,t){var r=(e.nodeName||"").toLowerCase();return"button"===r?c.apply(this,arguments):("input"!==r&&"option"!==r&&i("jQuery.fn.attr('value', val) no longer sets properties"),e.value=t,n)}};var h,g,m=e.fn.init,v=e.parseJSON,y=/^([^<]*)(<[\w\W]+>)([^>]*)$/;e.fn.init=function(t,n,r){var o;return t&&"string"==typeof t&&!e.isPlainObject(n)&&(o=y.exec(e.trim(t)))&&o[0]&&("<"!==t.charAt(0)&&i("$(html) HTML strings must start with '<' character"),o[3]&&i("$(html) HTML text after last tag is ignored"),"#"===o[0].charAt(0)&&(i("HTML string cannot start with a '#' character"),e.error("JQMIGRATE: Invalid selector string (XSS)")),n&&n.context&&(n=n.context),e.parseHTML)?m.call(this,e.parseHTML(o[2],n,!0),n,r):m.apply(this,arguments)},e.fn.init.prototype=e.fn,e.parseJSON=function(e){return e||null===e?v.apply(this,arguments):(i("jQuery.parseJSON requires a valid JSON string"),null)},e.uaMatch=function(e){e=e.toLowerCase();var t=/(chrome)[ \/]([\w.]+)/.exec(e)||/(webkit)[ \/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||0>e.indexOf("compatible")&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(e)||[];return{browser:t[1]||"",version:t[2]||"0"}},e.browser||(h=e.uaMatch(navigator.userAgent),g={},h.browser&&(g[h.browser]=!0,g.version=h.version),g.chrome?g.webkit=!0:g.webkit&&(g.safari=!0),e.browser=g),r(e,"browser",e.browser,"jQuery.browser is deprecated"),e.sub=function(){function t(e,n){return new t.fn.init(e,n)}e.extend(!0,t,this),t.superclass=this,t.fn=t.prototype=this(),t.fn.constructor=t,t.sub=this.sub,t.fn.init=function(i,r){return r&&r instanceof e&&!(r instanceof t)&&(r=t(r)),e.fn.init.call(this,i,r,n)},t.fn.init.prototype=t.fn;var n=t(document);return i("jQuery.sub() is deprecated"),t},e.ajaxSetup({converters:{"text json":e.parseJSON}});var x=e.fn.data;e.fn.data=function(t){var r,o,a=this[0];return!a||"events"!==t||1!==arguments.length||(r=e.data(a,t),o=e._data(a,t),r!==n&&r!==o||o===n)?x.apply(this,arguments):(i("Use of jQuery.fn.data('events') is deprecated"),o)};var b=/\/(java|ecma)script/i,w=e.fn.andSelf||e.fn.addBack;e.fn.andSelf=function(){return i("jQuery.fn.andSelf() replaced by jQuery.fn.addBack()"),w.apply(this,arguments)},e.clean||(e.clean=function(t,r,o,a){r=r||document,r=!r.nodeType&&r[0]||r,r=r.ownerDocument||r,i("jQuery.clean() is deprecated");var s,l,c,u,d=[];if(e.merge(d,e.buildFragment(t,r).childNodes),o)for(c=function(e){return!e.type||b.test(e.type)?a?a.push(e.parentNode?e.parentNode.removeChild(e):e):o.appendChild(e):n},s=0;null!=(l=d[s]);s++)e.nodeName(l,"script")&&c(l)||(o.appendChild(l),l.getElementsByTagName!==n&&(u=e.grep(e.merge([],l.getElementsByTagName("script")),c),d.splice.apply(d,[s+1,0].concat(u)),s+=u.length));return d});var T=e.event.add,S=e.event.remove,C=e.event.trigger,A=e.fn.toggle,k=e.fn.live,N=e.fn.die,E="ajaxStart|ajaxStop|ajaxSend|ajaxComplete|ajaxError|ajaxSuccess",I=RegExp("\\b(?:"+E+")\\b"),P=/(?:^|\s)hover(\.\S+|)\b/,j=function(t){return"string"!=typeof t||e.event.special.hover?t:(P.test(t)&&i("'hover' pseudo-event is deprecated, use 'mouseenter mouseleave'"),t&&t.replace(P,"mouseenter$1 mouseleave$1"))};e.event.props&&"attrChange"!==e.event.props[0]&&e.event.props.unshift("attrChange","attrName","relatedNode","srcElement"),e.event.dispatch&&r(e.event,"handle",e.event.dispatch,"jQuery.event.handle is undocumented and deprecated"),e.event.add=function(e,t,n,r,o){e!==document&&I.test(t)&&i("AJAX events should be attached to document: "+t),T.call(this,e,j(t||""),n,r,o)},e.event.remove=function(e,t,n,i,r){S.call(this,e,j(t)||"",n,i,r)},e.fn.error=function(){var e=Array.prototype.slice.call(arguments,0);return i("jQuery.fn.error() is deprecated"),e.splice(0,0,"error"),arguments.length?this.bind.apply(this,e):(this.triggerHandler.apply(this,e),this)},e.fn.toggle=function(t,n){if(!e.isFunction(t)||!e.isFunction(n))return A.apply(this,arguments);i("jQuery.fn.toggle(handler, handler...) is deprecated");var r=arguments,o=t.guid||e.guid++,a=0,s=function(n){var i=(e._data(this,"lastToggle"+t.guid)||0)%a;return e._data(this,"lastToggle"+t.guid,i+1),n.preventDefault(),r[i].apply(this,arguments)||!1};for(s.guid=o;r.length>a;)r[a++].guid=o;return this.click(s)},e.fn.live=function(t,n,r){return i("jQuery.fn.live() is deprecated"),k?k.apply(this,arguments):(e(this.context).on(t,this.selector,n,r),this)},e.fn.die=function(t,n){return i("jQuery.fn.die() is deprecated"),N?N.apply(this,arguments):(e(this.context).off(t,this.selector||"**",n),this)},e.event.trigger=function(e,t,n,r){return n||I.test(e)||i("Global events are undocumented and deprecated"),C.call(this,e,t,n||document,r)},e.each(E.split("|"),function(t,n){e.event.special[n]={setup:function(){var t=this;return t!==document&&(e.event.add(document,n+"."+e.guid,function(){e.event.trigger(n,null,t,!0)}),e._data(this,n,e.guid++)),!1},teardown:function(){return this!==document&&e.event.remove(document,n+"."+e._data(this,n)),!1}}})}(jQuery,window),!function(e,t,n){function i(e,t){return typeof e===t}function r(){var e,t,n,r,o,a,s;for(var l in x)if(x.hasOwnProperty(l)){if(e=[],t=x[l],t.name&&(e.push(t.name.toLowerCase()),t.options&&t.options.aliases&&t.options.aliases.length))for(n=0;n<t.options.aliases.length;n++)e.push(t.options.aliases[n].toLowerCase());for(r=i(t.fn,"function")?t.fn():t.fn,o=0;o<e.length;o++)a=e[o],s=a.split("."),1===s.length?T[s[0]]=r:(!T[s[0]]||T[s[0]]instanceof Boolean||(T[s[0]]=new Boolean(T[s[0]])),T[s[0]][s[1]]=r),y.push((r?"":"no-")+s.join("-"))}}function o(e){var t=S.className,n=T._config.classPrefix||"";if(C&&(t=t.baseVal),T._config.enableJSClass){var i=new RegExp("(^|\\s)"+n+"no-js(\\s|$)");t=t.replace(i,"$1"+n+"js$2")}T._config.enableClasses&&(t+=" "+n+e.join(" "+n),C?S.className.baseVal=t:S.className=t)}function a(e){return e.replace(/([a-z])-([a-z])/g,function(e,t,n){return t+n.toUpperCase()}).replace(/^-/,"")}function s(){return"function"!=typeof t.createElement?t.createElement(arguments[0]):C?t.createElementNS.call(t,"http://www.w3.org/2000/svg",arguments[0]):t.createElement.apply(t,arguments)}function l(e,t){return!!~(""+e).indexOf(t)}function c(){var e=t.body;return e||(e=s(C?"svg":"body"),e.fake=!0),e}function u(e,n,i,r){var o,a,l,u,d="modernizr",f=s("div"),p=c();if(parseInt(i,10))for(;i--;)l=s("div"),l.id=r?r[i]:d+(i+1),f.appendChild(l);return o=s("style"),o.type="text/css",o.id="s"+d,(p.fake?p:f).appendChild(o),p.appendChild(f),o.styleSheet?o.styleSheet.cssText=e:o.appendChild(t.createTextNode(e)),f.id=d,p.fake&&(p.style.background="",p.style.overflow="hidden",u=S.style.overflow,S.style.overflow="hidden",S.appendChild(p)),a=n(f,e),p.fake?(p.parentNode.removeChild(p),S.style.overflow=u,S.offsetHeight):f.parentNode.removeChild(f),!!a}function d(e,t){return function(){return e.apply(t,arguments)}}function f(e,t,n){var r;for(var o in e)if(e[o]in t)return n===!1?e[o]:(r=t[e[o]],i(r,"function")?d(r,n||t):r);return!1}function p(e){return e.replace(/([A-Z])/g,function(e,t){return"-"+t.toLowerCase()}).replace(/^ms-/,"-ms-")}function h(t,i){var r=t.length;if("CSS"in e&&"supports"in e.CSS){for(;r--;)if(e.CSS.supports(p(t[r]),i))return!0;return!1}if("CSSSupportsRule"in e){for(var o=[];r--;)o.push("("+p(t[r])+":"+i+")");return o=o.join(" or "),u("@supports ("+o+") { #modernizr { position: absolute; } }",function(e){return"absolute"==getComputedStyle(e,null).position})}return n}function g(e,t,r,o){function c(){d&&(delete R.style,delete R.modElem)}if(o=!i(o,"undefined")&&o,!i(r,"undefined")){var u=h(e,r);if(!i(u,"undefined"))return u}for(var d,f,p,g,m,v=["modernizr","tspan"];!R.style;)d=!0,R.modElem=s(v.shift()),R.style=R.modElem.style;for(p=e.length,f=0;p>f;f++)if(g=e[f],m=R.style[g],l(g,"-")&&(g=a(g)),R.style[g]!==n){if(o||i(r,"undefined"))return c(),"pfx"!=t||g;try{R.style[g]=r}catch(y){}if(R.style[g]!=m)return c(),"pfx"!=t||g}return c(),!1}function m(e,t,n,r,o){var a=e.charAt(0).toUpperCase()+e.slice(1),s=(e+" "+O.join(a+" ")+a).split(" ");return i(t,"string")||i(t,"undefined")?g(s,t,r,o):(s=(e+" "+k.join(a+" ")+a).split(" "),f(s,t,n))}function v(e,t,i){return m(e,n,n,t,i)}var y=[],x=[],b={_version:"3.2.0",_config:{classPrefix:"",enableClasses:!0,enableJSClass:!0,usePrefixes:!0},_q:[],on:function(e,t){var n=this;setTimeout(function(){t(n[e])},0)},addTest:function(e,t,n){x.push({name:e,fn:t,options:n})},addAsyncTest:function(e){x.push({name:null,fn:e})}},w=b._config.usePrefixes?" -webkit- -moz- -o- -ms- ".split(" "):[];b._prefixes=w;var T=function(){};T.prototype=b,T=new T,T.addTest("applicationcache","applicationCache"in e),T.addTest("geolocation","geolocation"in navigator),T.addTest("history",function(){var t=navigator.userAgent;return(-1===t.indexOf("Android 2.")&&-1===t.indexOf("Android 4.0")||-1===t.indexOf("Mobile Safari")||-1!==t.indexOf("Chrome")||-1!==t.indexOf("Windows Phone"))&&(e.history&&"pushState"in e.history)}),T.addTest("postmessage","postMessage"in e),T.addTest("svg",!!t.createElementNS&&!!t.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect),T.addTest("websockets","WebSocket"in e&&2===e.WebSocket.CLOSING),T.addTest("sessionstorage",function(){var e="modernizr";try{return sessionStorage.setItem(e,e),sessionStorage.removeItem(e),!0}catch(t){return!1}}),T.addTest("localstorage",function(){var e="modernizr";try{return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch(t){return!1}}),T.addTest("websqldatabase","openDatabase"in e),T.addTest("webworkers","Worker"in e);var S=t.documentElement,C="svg"===S.nodeName.toLowerCase(),A="Moz O ms Webkit",k=b._config.usePrefixes?A.toLowerCase().split(" "):[];b._domPrefixes=k;var N=function(){function e(e,t){var r;return!!e&&(t&&"string"!=typeof t||(t=s(t||"div")),e="on"+e,r=e in t,!r&&i&&(t.setAttribute||(t=s("div")),t.setAttribute(e,""),r="function"==typeof t[e],t[e]!==n&&(t[e]=n),t.removeAttribute(e)),r)}var i=!("onblur"in t.documentElement);return e}();b.hasEvent=N,T.addTest("hashchange",function(){return N("hashchange",e)!==!1&&(t.documentMode===n||t.documentMode>7)}),T.addTest("audio",function(){var e=s("audio"),t=!1;try{(t=!!e.canPlayType)&&(t=new Boolean(t),t.ogg=e.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),t.mp3=e.canPlayType('audio/mpeg; codecs="mp3"').replace(/^no$/,""),t.opus=e.canPlayType('audio/ogg; codecs="opus"').replace(/^no$/,""),t.wav=e.canPlayType('audio/wav; codecs="1"').replace(/^no$/,""),t.m4a=(e.canPlayType("audio/x-m4a;")||e.canPlayType("audio/aac;")).replace(/^no$/,""))}catch(n){}return t}),T.addTest("canvas",function(){var e=s("canvas");return!(!e.getContext||!e.getContext("2d"))}),T.addTest("canvastext",function(){return T.canvas!==!1&&"function"==typeof s("canvas").getContext("2d").fillText}),T.addTest("video",function(){var e=s("video"),t=!1;try{(t=!!e.canPlayType)&&(t=new Boolean(t),t.ogg=e.canPlayType('video/ogg; codecs="theora"').replace(/^no$/,""),t.h264=e.canPlayType('video/mp4; codecs="avc1.42E01E"').replace(/^no$/,""),t.webm=e.canPlayType('video/webm; codecs="vp8, vorbis"').replace(/^no$/,""),t.vp9=e.canPlayType('video/webm; codecs="vp9"').replace(/^no$/,""),t.hls=e.canPlayType('application/x-mpegURL; codecs="avc1.42E01E"').replace(/^no$/,""))}catch(n){}return t}),T.addTest("webgl",function(){var t=s("canvas"),n="probablySupportsContext"in t?"probablySupportsContext":"supportsContext";return n in t?t[n]("webgl")||t[n]("experimental-webgl"):"WebGLRenderingContext"in e}),T.addTest("cssgradients",function(){for(var e,t="background-image:",n="gradient(linear,left top,right bottom,from(#9f9),to(white));",i="",r=0,o=w.length-1;o>r;r++)e=0===r?"to ":"",i+=t+w[r]+"linear-gradient("+e+"left top, #9f9, white);";T._config.usePrefixes&&(i+=t+"-webkit-"+n);var a=s("a"),l=a.style;return l.cssText=i,(""+l.backgroundImage).indexOf("gradient")>-1}),T.addTest("multiplebgs",function(){var e=s("a").style;return e.cssText="background:url(https://),url(https://),red url(https://)",/(url\s*\(.*?){3}/.test(e.background)}),T.addTest("opacity",function(){var e=s("a").style;return e.cssText=w.join("opacity:.55;"),/^0.55$/.test(e.opacity)}),T.addTest("rgba",function(){var e=s("a").style;return e.cssText="background-color:rgba(150,255,150,.5)",(""+e.backgroundColor).indexOf("rgba")>-1}),T.addTest("inlinesvg",function(){var e=s("div");return e.innerHTML="<svg/>","http://www.w3.org/2000/svg"==("undefined"!=typeof SVGRect&&e.firstChild&&e.firstChild.namespaceURI)}),T.addTest("hsla",function(){var e=s("a").style;return e.cssText="background-color:hsla(120,40%,100%,.5)",l(e.backgroundColor,"rgba")||l(e.backgroundColor,"hsla")});var E=s("input"),I="autocomplete autofocus list placeholder max min multiple pattern required step".split(" "),P={};T.input=function(t){for(var n=0,i=t.length;i>n;n++)P[t[n]]=!!(t[n]in E);return P.list&&(P.list=!(!s("datalist")||!e.HTMLDataListElement)),P}(I);var j="search tel url email datetime date month week time datetime-local number range color".split(" "),_={};T.inputtypes=function(e){for(var i,r,o,a=e.length,s="1)",l=0;a>l;l++)E.setAttribute("type",i=e[l]),o="text"!==E.type&&"style"in E,o&&(E.value=s,E.style.cssText="position:absolute;visibility:hidden;",/^range$/.test(i)&&E.style.WebkitAppearance!==n?(S.appendChild(E),r=t.defaultView,o=r.getComputedStyle&&"textfield"!==r.getComputedStyle(E,null).WebkitAppearance&&0!==E.offsetHeight,S.removeChild(E)):/^(search|tel)$/.test(i)||(o=/^(url|email)$/.test(i)?E.checkValidity&&E.checkValidity()===!1:E.value!=s)),_[e[l]]=!!o;return _}(j);var D="CSS"in e&&"supports"in e.CSS,H="supportsCSS"in e;T.addTest("supports",D||H);var L={}.toString;T.addTest("svgclippaths",function(){return!!t.createElementNS&&/SVGClipPath/.test(L.call(t.createElementNS("http://www.w3.org/2000/svg","clipPath")))}),T.addTest("smil",function(){return!!t.createElementNS&&/SVGAnimate/.test(L.call(t.createElementNS("http://www.w3.org/2000/svg","animate")))});var O=b._config.usePrefixes?A.split(" "):[];b._cssomPrefixes=O;var z=function(t){var i,r=w.length,o=e.CSSRule;if("undefined"==typeof o)return n;if(!t)return!1;if(t=t.replace(/^@/,""),i=t.replace(/-/g,"_").toUpperCase()+"_RULE",i in o)return"@"+t;for(var a=0;r>a;a++){var s=w[a],l=s.toUpperCase()+"_"+i;if(l in o)return"@-"+s.toLowerCase()+"-"+t}return!1};b.atRule=z;var F=b.testStyles=u,M=function(){var e=navigator.userAgent,t=e.match(/applewebkit\/([0-9]+)/gi)&&parseFloat(RegExp.$1),n=e.match(/w(eb)?osbrowser/gi),i=e.match(/windows phone/gi)&&e.match(/iemobile\/([0-9])+/gi)&&parseFloat(RegExp.$1)>=9,r=533>t&&e.match(/android/gi);return n||r||i}();M?T.addTest("fontface",!1):F('@font-face {font-family:"font";src:url("https://")}',function(e,n){var i=t.getElementById("smodernizr"),r=i.sheet||i.styleSheet,o=r?r.cssRules&&r.cssRules[0]?r.cssRules[0].cssText:r.cssText||"":"",a=/src/i.test(o)&&0===o.indexOf(n.split(" ")[0]);T.addTest("fontface",a)}),F('#modernizr{font:0/0 a}#modernizr:after{content:":)";visibility:hidden;font:7px/1 a}',function(e){T.addTest("generatedcontent",e.offsetHeight>=7)});var q={elem:s("modernizr")};T._q.push(function(){delete q.elem});var R={style:q.elem.style};T._q.unshift(function(){delete R.style});var W=b.testProp=function(e,t,i){return g([e],n,t,i)};T.addTest("textshadow",W("textShadow","1px 1px")),b.testAllProps=m;var B=b.prefixed=function(e,t,n){return 0===e.indexOf("@")?z(e):(-1!=e.indexOf("-")&&(e=a(e)),t?m(e,t,n):m(e,"pfx"))},$=B("indexedDB",e);T.addTest("indexeddb",!!$),$&&T.addTest("indexeddb.deletedatabase","deleteDatabase"in $),b.testAllProps=v,T.addTest("cssanimations",v("animationName","a",!0)),T.addTest("backgroundsize",v("backgroundSize","100%",!0)),T.addTest("borderimage",v("borderImage","url() 1",!0)),T.addTest("borderradius",v("borderRadius","0px",!0)),T.addTest("boxshadow",v("boxShadow","1px 1px",!0)),function(){T.addTest("csscolumns",function(){var e=!1,t=v("columnCount");try{(e=!!t)&&(e=new Boolean(e))}catch(n){}return e});for(var e,t,n=["Width","Span","Fill","Gap","Rule","RuleColor","RuleStyle","RuleWidth","BreakBefore","BreakAfter","BreakInside"],i=0;i<n.length;i++)e=n[i].toLowerCase(),t=v("column"+n[i]),("breakbefore"===e||"breakafter"===e||"breakinside"==e)&&(t=t||v(n[i])),T.addTest("csscolumns."+e,t)}(),T.addTest("flexbox",v("flexBasis","1px",!0)),T.addTest("flexboxlegacy",v("boxDirection","reverse",!0)),T.addTest("cssreflections",v("boxReflect","above",!0)),T.addTest("csstransforms",function(){return-1===navigator.userAgent.indexOf("Android 2.")&&v("transform","scale(1)",!0)}),T.addTest("csstransforms3d",function(){var e=!!v("perspective","1px",!0),t=T._config.usePrefixes;if(e&&(!t||"webkitPerspective"in S.style)){var n,i="#modernizr{width:0;height:0}";T.supports?n="@supports (perspective: 1px)":(n="@media (transform-3d)",t&&(n+=",(-webkit-transform-3d)")),n+="{#modernizr{width:7px;height:18px;margin:0;padding:0;border:0}}",F(i+n,function(t){e=7===t.offsetWidth&&18===t.offsetHeight})}return e}),T.addTest("csstransitions",v("transition","all",!0)),r(),o(y),delete b.addTest,delete b.addAsyncTest;for(var V=0;V<T._q.length;V++)T._q[V]();e.Modernizr=T}(window,document),!function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t:e.conditionizr=t()}(this,function(){"use strict";function e(e,n,i){function r(n){var r,o=i?e:t+e+("style"===n?".css":".js");switch(n){case"script":r=document.createElement("script"),r.src=o;break;case"style":r=document.createElement("link"),r.href=o,r.rel="stylesheet";break;case"class":document.documentElement.className+=" "+e}!!r&&(document.head||document.getElementsByTagName("head")[0]).appendChild(r)}for(var o=n.length;o--;)r(n[o])}var t,n={};return n.config=function(i){t=i.assets||"";for(var r in i.tests)n[r]&&e(r,i.tests[r])},n.add=function(e,t){n[e]="function"==typeof t?t():t},n.on=function(e,t){(n[e]||/\!/.test(e)&&!n[e.slice(1)])&&t()},n.load=n.polyfill=function(t,i){for(var r=i.length;r--;)n[i[r]]&&e(t,[/\.js$/.test(t)?"script":"style"],!0)},n}),!function(e){"use strict";function t(e){return(e||"").toLowerCase()}var n="2.1.6";e.fn.cycle=function(n){var i;return 0!==this.length||e.isReady?this.each(function(){var i,r,o,a,s=e(this),l=e.fn.cycle.log;if(!s.data("cycle.opts")){(s.data("cycle-log")===!1||n&&n.log===!1||r&&r.log===!1)&&(l=e.noop),l("--c2 init--"),i=s.data();for(var c in i)i.hasOwnProperty(c)&&/^cycle[A-Z]+/.test(c)&&(a=i[c],o=c.match(/^cycle(.*)/)[1].replace(/^[A-Z]/,t),l(o+":",a,"("+typeof a+")"),i[o]=a);r=e.extend({},e.fn.cycle.defaults,i,n||{}),r.timeoutId=0,r.paused=r.paused||!1,r.container=s,r._maxZ=r.maxZ,r.API=e.extend({_container:s},e.fn.cycle.API),r.API.log=l,r.API.trigger=function(e,t){return r.container.trigger(e,t),r.API},s.data("cycle.opts",r),s.data("cycle.API",r.API),r.API.trigger("cycle-bootstrap",[r,r.API]),r.API.addInitialSlides(),r.API.preInitSlideshow(),r.slides.length&&r.API.initSlideshow()}}):(i={s:this.selector,c:this.context},e.fn.cycle.log("requeuing slideshow (dom not ready)"),e(function(){e(i.s,i.c).cycle(n)}),this)},e.fn.cycle.API={opts:function(){return this._container.data("cycle.opts")},addInitialSlides:function(){var t=this.opts(),n=t.slides;t.slideCount=0,t.slides=e(),n=n.jquery?n:t.container.find(n),t.random&&n.sort(function(){return Math.random()-.5}),t.API.add(n)},preInitSlideshow:function(){var t=this.opts();t.API.trigger("cycle-pre-initialize",[t]);var n=e.fn.cycle.transitions[t.fx];n&&e.isFunction(n.preInit)&&n.preInit(t),t._preInitialized=!0},postInitSlideshow:function(){var t=this.opts();t.API.trigger("cycle-post-initialize",[t]);var n=e.fn.cycle.transitions[t.fx];n&&e.isFunction(n.postInit)&&n.postInit(t)},initSlideshow:function(){var t,n=this.opts(),i=n.container;n.API.calcFirstSlide(),"static"==n.container.css("position")&&n.container.css("position","relative"),e(n.slides[n.currSlide]).css({opacity:1,display:"block",visibility:"visible"}),n.API.stackSlides(n.slides[n.currSlide],n.slides[n.nextSlide],!n.reverse),n.pauseOnHover&&(n.pauseOnHover!==!0&&(i=e(n.pauseOnHover)),i.hover(function(){n.API.pause(!0)},function(){n.API.resume(!0)})),n.timeout&&(t=n.API.getSlideOpts(n.currSlide),n.API.queueTransition(t,t.timeout+n.delay)),n._initialized=!0,n.API.updateView(!0),n.API.trigger("cycle-initialized",[n]),n.API.postInitSlideshow()},pause:function(t){var n=this.opts(),i=n.API.getSlideOpts(),r=n.hoverPaused||n.paused;t?n.hoverPaused=!0:n.paused=!0,r||(n.container.addClass("cycle-paused"),n.API.trigger("cycle-paused",[n]).log("cycle-paused"),i.timeout&&(clearTimeout(n.timeoutId),n.timeoutId=0,n._remainingTimeout-=e.now()-n._lastQueue,(n._remainingTimeout<0||isNaN(n._remainingTimeout))&&(n._remainingTimeout=void 0)))},resume:function(e){var t=this.opts(),n=!t.hoverPaused&&!t.paused;e?t.hoverPaused=!1:t.paused=!1,n||(t.container.removeClass("cycle-paused"),0===t.slides.filter(":animated").length&&t.API.queueTransition(t.API.getSlideOpts(),t._remainingTimeout),t.API.trigger("cycle-resumed",[t,t._remainingTimeout]).log("cycle-resumed"))},add:function(t,n){var i,r=this.opts(),o=r.slideCount,a=!1;"string"==e.type(t)&&(t=e.trim(t)),e(t).each(function(){var t,i=e(this);n?r.container.prepend(i):r.container.append(i),r.slideCount++,t=r.API.buildSlideOpts(i),r.slides=n?e(i).add(r.slides):r.slides.add(i),r.API.initSlide(t,i,--r._maxZ),i.data("cycle.opts",t),r.API.trigger("cycle-slide-added",[r,t,i])}),r.API.updateView(!0),a=r._preInitialized&&2>o&&r.slideCount>=1,a&&(r._initialized?r.timeout&&(i=r.slides.length,r.nextSlide=r.reverse?i-1:1,r.timeoutId||r.API.queueTransition(r)):r.API.initSlideshow())},calcFirstSlide:function(){var e,t=this.opts();e=parseInt(t.startingSlide||0,10),(e>=t.slides.length||0>e)&&(e=0),t.currSlide=e,t.reverse?(t.nextSlide=e-1,t.nextSlide<0&&(t.nextSlide=t.slides.length-1)):(t.nextSlide=e+1,t.nextSlide==t.slides.length&&(t.nextSlide=0))},calcNextSlide:function(){var e,t=this.opts();t.reverse?(e=t.nextSlide-1<0,t.nextSlide=e?t.slideCount-1:t.nextSlide-1,t.currSlide=e?0:t.nextSlide+1):(e=t.nextSlide+1==t.slides.length,t.nextSlide=e?0:t.nextSlide+1,t.currSlide=e?t.slides.length-1:t.nextSlide-1)},calcTx:function(t,n){var i,r=t;return r._tempFx?i=e.fn.cycle.transitions[r._tempFx]:n&&r.manualFx&&(i=e.fn.cycle.transitions[r.manualFx]),i||(i=e.fn.cycle.transitions[r.fx]),r._tempFx=null,this.opts()._tempFx=null,i||(i=e.fn.cycle.transitions.fade,r.API.log('Transition "'+r.fx+'" not found.  Using fade.')),i},prepareTx:function(e,t){var n,i,r,o,a,s=this.opts();return s.slideCount<2?void(s.timeoutId=0):(!e||s.busy&&!s.manualTrump||(s.API.stopTransition(),s.busy=!1,clearTimeout(s.timeoutId),s.timeoutId=0),void(s.busy||(0!==s.timeoutId||e)&&(i=s.slides[s.currSlide],r=s.slides[s.nextSlide],o=s.API.getSlideOpts(s.nextSlide),a=s.API.calcTx(o,e),s._tx=a,e&&void 0!==o.manualSpeed&&(o.speed=o.manualSpeed),s.nextSlide!=s.currSlide&&(e||!s.paused&&!s.hoverPaused&&s.timeout)?(s.API.trigger("cycle-before",[o,i,r,t]),a.before&&a.before(o,i,r,t),n=function(){s.busy=!1,s.container.data("cycle.opts")&&(a.after&&a.after(o,i,r,t),s.API.trigger("cycle-after",[o,i,r,t]),s.API.queueTransition(o),s.API.updateView(!0))},s.busy=!0,a.transition?a.transition(o,i,r,t,n):s.API.doTransition(o,i,r,t,n),s.API.calcNextSlide(),s.API.updateView()):s.API.queueTransition(o))))},doTransition:function(t,n,i,r,o){var a=t,s=e(n),l=e(i),c=function(){l.animate(a.animIn||{opacity:1},a.speed,a.easeIn||a.easing,o)};l.css(a.cssBefore||{}),s.animate(a.animOut||{},a.speed,a.easeOut||a.easing,function(){s.css(a.cssAfter||{}),a.sync||c()}),a.sync&&c()},queueTransition:function(t,n){var i=this.opts(),r=void 0!==n?n:t.timeout;return 0===i.nextSlide&&0===--i.loop?(i.API.log("terminating; loop=0"),i.timeout=0,r?setTimeout(function(){i.API.trigger("cycle-finished",[i])},r):i.API.trigger("cycle-finished",[i]),void(i.nextSlide=i.currSlide)):void 0!==i.continueAuto&&(i.continueAuto===!1||e.isFunction(i.continueAuto)&&i.continueAuto()===!1)?(i.API.log("terminating automatic transitions"),i.timeout=0,void(i.timeoutId&&clearTimeout(i.timeoutId))):void(r&&(i._lastQueue=e.now(),void 0===n&&(i._remainingTimeout=t.timeout),i.paused||i.hoverPaused||(i.timeoutId=setTimeout(function(){i.API.prepareTx(!1,!i.reverse)},r))))},stopTransition:function(){var e=this.opts();e.slides.filter(":animated").length&&(e.slides.stop(!1,!0),e.API.trigger("cycle-transition-stopped",[e])),e._tx&&e._tx.stopTransition&&e._tx.stopTransition(e)},advanceSlide:function(e){var t=this.opts();return clearTimeout(t.timeoutId),t.timeoutId=0,t.nextSlide=t.currSlide+e,t.nextSlide<0?t.nextSlide=t.slides.length-1:t.nextSlide>=t.slides.length&&(t.nextSlide=0),t.API.prepareTx(!0,e>=0),!1},buildSlideOpts:function(n){var i,r,o=this.opts(),a=n.data()||{};for(var s in a)a.hasOwnProperty(s)&&/^cycle[A-Z]+/.test(s)&&(i=a[s],r=s.match(/^cycle(.*)/)[1].replace(/^[A-Z]/,t),o.API.log("["+(o.slideCount-1)+"]",r+":",i,"("+typeof i+")"),a[r]=i);a=e.extend({},e.fn.cycle.defaults,o,a),a.slideNum=o.slideCount;try{delete a.API,delete a.slideCount,delete a.currSlide,delete a.nextSlide,delete a.slides}catch(l){}return a},getSlideOpts:function(t){var n=this.opts();void 0===t&&(t=n.currSlide);var i=n.slides[t],r=e(i).data("cycle.opts");return e.extend({},n,r)},initSlide:function(t,n,i){var r=this.opts();n.css(t.slideCss||{}),i>0&&n.css("zIndex",i),isNaN(t.speed)&&(t.speed=e.fx.speeds[t.speed]||e.fx.speeds._default),t.sync||(t.speed=t.speed/2),n.addClass(r.slideClass)},updateView:function(e,t){var n=this.opts();if(n._initialized){var i=n.API.getSlideOpts(),r=n.slides[n.currSlide];!e&&t!==!0&&(n.API.trigger("cycle-update-view-before",[n,i,r]),n.updateView<0)||(n.slideActiveClass&&n.slides.removeClass(n.slideActiveClass).eq(n.currSlide).addClass(n.slideActiveClass),e&&n.hideNonActive&&n.slides.filter(":not(."+n.slideActiveClass+")").css("visibility","hidden"),0===n.updateView&&setTimeout(function(){n.API.trigger("cycle-update-view",[n,i,r,e])},i.speed/(n.sync?2:1)),0!==n.updateView&&n.API.trigger("cycle-update-view",[n,i,r,e]),e&&n.API.trigger("cycle-update-view-after",[n,i,r]))}},getComponent:function(t){var n=this.opts(),i=n[t];return"string"==typeof i?/^\s*[\>|\+|~]/.test(i)?n.container.find(i):e(i):i.jquery?i:e(i)},stackSlides:function(t,n,i){var r=this.opts();t||(t=r.slides[r.currSlide],n=r.slides[r.nextSlide],i=!r.reverse),e(t).css("zIndex",r.maxZ);var o,a=r.maxZ-2,s=r.slideCount;if(i){for(o=r.currSlide+1;s>o;o++)e(r.slides[o]).css("zIndex",a--);for(o=0;o<r.currSlide;o++)e(r.slides[o]).css("zIndex",a--)}else{for(o=r.currSlide-1;o>=0;o--)e(r.slides[o]).css("zIndex",a--);for(o=s-1;o>r.currSlide;o--)e(r.slides[o]).css("zIndex",a--)}e(n).css("zIndex",r.maxZ-1)},getSlideIndex:function(e){return this.opts().slides.index(e)}},e.fn.cycle.log=function(){window.console&&console.log&&console.log("[cycle2] "+Array.prototype.join.call(arguments," "))},e.fn.cycle.version=function(){return"Cycle2: "+n},e.fn.cycle.transitions={custom:{},none:{before:function(e,t,n,i){e.API.stackSlides(n,t,i),e.cssBefore={opacity:1,visibility:"visible",display:"block"}}},fade:{before:function(t,n,i,r){var o=t.API.getSlideOpts(t.nextSlide).slideCss||{};t.API.stackSlides(n,i,r),t.cssBefore=e.extend(o,{opacity:0,visibility:"visible",display:"block"}),t.animIn={opacity:1},t.animOut={opacity:0}}},fadeout:{before:function(t,n,i,r){var o=t.API.getSlideOpts(t.nextSlide).slideCss||{};t.API.stackSlides(n,i,r),t.cssBefore=e.extend(o,{opacity:1,visibility:"visible",display:"block"}),t.animOut={opacity:0}}},scrollHorz:{before:function(e,t,n,i){e.API.stackSlides(t,n,i);var r=e.container.css("overflow","hidden").width();e.cssBefore={left:i?r:-r,top:0,opacity:1,visibility:"visible",display:"block"},e.cssAfter={zIndex:e._maxZ-2,left:0},e.animIn={left:0},e.animOut={left:i?-r:r}}}},e.fn.cycle.defaults={allowWrap:!0,autoSelector:".cycle-slideshow[data-cycle-auto-init!=false]",delay:0,easing:null,fx:"fade",hideNonActive:!0,loop:0,manualFx:void 0,manualSpeed:void 0,manualTrump:!0,maxZ:100,pauseOnHover:!1,reverse:!1,slideActiveClass:"cycle-slide-active",slideClass:"cycle-slide",slideCss:{position:"absolute",top:0,left:0},slides:"> img",speed:500,startingSlide:0,sync:!0,timeout:4e3,updateView:0},e(document).ready(function(){e(e.fn.cycle.defaults.autoSelector).cycle()})}(jQuery),function(e){"use strict";function t(t,i){var r,o,a,s=i.autoHeight;if("container"==s)o=e(i.slides[i.currSlide]).outerHeight(),i.container.height(o);else if(i._autoHeightRatio)i.container.height(i.container.width()/i._autoHeightRatio);else if("calc"===s||"number"==e.type(s)&&s>=0){if(a="calc"===s?n(t,i):s>=i.slides.length?0:s,a==i._sentinelIndex)return;i._sentinelIndex=a,i._sentinel&&i._sentinel.remove(),r=e(i.slides[a].cloneNode(!0)),r.removeAttr("id name rel").find("[id],[name],[rel]").removeAttr("id name rel"),r.css({position:"static",visibility:"hidden",display:"block"}).prependTo(i.container).addClass("cycle-sentinel cycle-slide").removeClass("cycle-slide-active"),r.find("*").css("visibility","hidden"),i._sentinel=r}}function n(t,n){var i=0,r=-1;return n.slides.each(function(t){var n=e(this).height();n>r&&(r=n,i=t)}),i}function i(t,n,i,r){var o=e(r).outerHeight();n.container.animate({height:o},n.autoHeightSpeed,n.autoHeightEasing)}function r(n,o){o._autoHeightOnResize&&(e(window).off("resize orientationchange",o._autoHeightOnResize),o._autoHeightOnResize=null),o.container.off("cycle-slide-added cycle-slide-removed",t),o.container.off("cycle-destroyed",r),o.container.off("cycle-before",i),o._sentinel&&(o._sentinel.remove(),o._sentinel=null)}e.extend(e.fn.cycle.defaults,{autoHeight:0,autoHeightSpeed:250,autoHeightEasing:null}),e(document).on("cycle-initialized",function(n,o){function a(){t(n,o)}var s,l=o.autoHeight,c=e.type(l),u=null;("string"===c||"number"===c)&&(o.container.on("cycle-slide-added cycle-slide-removed",t),
o.container.on("cycle-destroyed",r),"container"==l?o.container.on("cycle-before",i):"string"===c&&/\d+\:\d+/.test(l)&&(s=l.match(/(\d+)\:(\d+)/),s=s[1]/s[2],o._autoHeightRatio=s),"number"!==c&&(o._autoHeightOnResize=function(){clearTimeout(u),u=setTimeout(a,50)},e(window).on("resize orientationchange",o._autoHeightOnResize)),setTimeout(a,30))})}(jQuery),function(e){"use strict";e.extend(e.fn.cycle.defaults,{caption:"> .cycle-caption",captionTemplate:"{{slideNum}} / {{slideCount}}",overlay:"> .cycle-overlay",overlayTemplate:"<div>{{title}}</div><div>{{desc}}</div>",captionModule:"caption"}),e(document).on("cycle-update-view",function(t,n,i,r){"caption"===n.captionModule&&e.each(["caption","overlay"],function(){var e=this,t=i[e+"Template"],o=n.API.getComponent(e);o.length&&t?(o.html(n.API.tmpl(t,i,n,r)),o.show()):o.hide()})}),e(document).on("cycle-destroyed",function(t,n){var i;e.each(["caption","overlay"],function(){var e=this,t=n[e+"Template"];n[e]&&t&&(i=n.API.getComponent("caption"),i.empty())})})}(jQuery),function(e){"use strict";var t=e.fn.cycle;e.fn.cycle=function(n){var i,r,o,a=e.makeArray(arguments);return"number"==e.type(n)?this.cycle("goto",n):"string"==e.type(n)?this.each(function(){var s;return i=n,o=e(this).data("cycle.opts"),void 0===o?void t.log('slideshow must be initialized before sending commands; "'+i+'" ignored'):(i="goto"==i?"jump":i,r=o.API[i],e.isFunction(r)?(s=e.makeArray(a),s.shift(),r.apply(o.API,s)):void t.log("unknown command: ",i))}):t.apply(this,arguments)},e.extend(e.fn.cycle,t),e.extend(t.API,{next:function(){var e=this.opts();if(!e.busy||e.manualTrump){var t=e.reverse?-1:1;e.allowWrap===!1&&e.currSlide+t>=e.slideCount||(e.API.advanceSlide(t),e.API.trigger("cycle-next",[e]).log("cycle-next"))}},prev:function(){var e=this.opts();if(!e.busy||e.manualTrump){var t=e.reverse?1:-1;e.allowWrap===!1&&e.currSlide+t<0||(e.API.advanceSlide(t),e.API.trigger("cycle-prev",[e]).log("cycle-prev"))}},destroy:function(){this.stop();var t=this.opts(),n=e.isFunction(e._data)?e._data:e.noop;clearTimeout(t.timeoutId),t.timeoutId=0,t.API.stop(),t.API.trigger("cycle-destroyed",[t]).log("cycle-destroyed"),t.container.removeData(),n(t.container[0],"parsedAttrs",!1),t.retainStylesOnDestroy||(t.container.removeAttr("style"),t.slides.removeAttr("style"),t.slides.removeClass(t.slideActiveClass)),t.slides.each(function(){var i=e(this);i.removeData(),i.removeClass(t.slideClass),n(this,"parsedAttrs",!1)})},jump:function(e,t){var n,i=this.opts();if(!i.busy||i.manualTrump){var r=parseInt(e,10);if(isNaN(r)||0>r||r>=i.slides.length)return void i.API.log("goto: invalid slide index: "+r);if(r==i.currSlide)return void i.API.log("goto: skipping, already on slide",r);i.nextSlide=r,clearTimeout(i.timeoutId),i.timeoutId=0,i.API.log("goto: ",r," (zero-index)"),n=i.currSlide<i.nextSlide,i._tempFx=t,i.API.prepareTx(!0,n)}},stop:function(){var t=this.opts(),n=t.container;clearTimeout(t.timeoutId),t.timeoutId=0,t.API.stopTransition(),t.pauseOnHover&&(t.pauseOnHover!==!0&&(n=e(t.pauseOnHover)),n.off("mouseenter mouseleave")),t.API.trigger("cycle-stopped",[t]).log("cycle-stopped")},reinit:function(){var e=this.opts();e.API.destroy(),e.container.cycle()},remove:function(t){for(var n,i,r=this.opts(),o=[],a=1,s=0;s<r.slides.length;s++)n=r.slides[s],s==t?i=n:(o.push(n),e(n).data("cycle.opts").slideNum=a,a++);i&&(r.slides=e(o),r.slideCount--,e(i).remove(),t==r.currSlide?r.API.advanceSlide(1):t<r.currSlide?r.currSlide--:r.currSlide++,r.API.trigger("cycle-slide-removed",[r,t,i]).log("cycle-slide-removed"),r.API.updateView())}}),e(document).on("click.cycle","[data-cycle-cmd]",function(t){t.preventDefault();var n=e(this),i=n.data("cycle-cmd"),r=n.data("cycle-context")||".cycle-slideshow";e(r).cycle(i,n.data("cycle-arg"))})}(jQuery),function(e){"use strict";function t(t,n){var i;return t._hashFence?void(t._hashFence=!1):(i=window.location.hash.substring(1),void t.slides.each(function(r){if(e(this).data("cycle-hash")==i){if(n===!0)t.startingSlide=r;else{var o=t.currSlide<r;t.nextSlide=r,t.API.prepareTx(!0,o)}return!1}}))}e(document).on("cycle-pre-initialize",function(n,i){t(i,!0),i._onHashChange=function(){t(i,!1)},e(window).on("hashchange",i._onHashChange)}),e(document).on("cycle-update-view",function(e,t,n){n.hash&&"#"+n.hash!=window.location.hash&&(t._hashFence=!0,window.location.hash=n.hash)}),e(document).on("cycle-destroyed",function(t,n){n._onHashChange&&e(window).off("hashchange",n._onHashChange)})}(jQuery),function(e){"use strict";e.extend(e.fn.cycle.defaults,{loader:!1}),e(document).on("cycle-bootstrap",function(t,n){function i(t,i){function o(t){var o;"wait"==n.loader?(s.push(t),0===c&&(s.sort(a),r.apply(n.API,[s,i]),n.container.removeClass("cycle-loading"))):(o=e(n.slides[n.currSlide]),r.apply(n.API,[t,i]),o.show(),n.container.removeClass("cycle-loading"))}function a(e,t){return e.data("index")-t.data("index")}var s=[];if("string"==e.type(t))t=e.trim(t);else if("array"===e.type(t))for(var l=0;l<t.length;l++)t[l]=e(t[l])[0];t=e(t);var c=t.length;c&&(t.css("visibility","hidden").appendTo("body").each(function(t){function a(){0===--l&&(--c,o(u))}var l=0,u=e(this),d=u.is("img")?u:u.find("img");return u.data("index",t),d=d.filter(":not(.cycle-loader-ignore)").filter(':not([src=""])'),d.length?(l=d.length,void d.each(function(){this.complete?a():e(this).load(function(){a()}).on("error",function(){0===--l&&(n.API.log("slide skipped; img not loaded:",this.src),0===--c&&"wait"==n.loader&&r.apply(n.API,[s,i]))})})):(--c,void s.push(u))}),c&&n.container.addClass("cycle-loading"))}var r;n.loader&&(r=n.API.add,n.API.add=i)})}(jQuery),function(e){"use strict";function t(t,n,i){var r,o=t.API.getComponent("pager");o.each(function(){var o=e(this);if(n.pagerTemplate){var a=t.API.tmpl(n.pagerTemplate,n,t,i[0]);r=e(a).appendTo(o)}else r=o.children().eq(t.slideCount-1);r.on(t.pagerEvent,function(e){t.pagerEventBubble||e.preventDefault(),t.API.page(o,e.currentTarget)})})}function n(e,t){var n=this.opts();if(!n.busy||n.manualTrump){var i=e.children().index(t),r=i,o=n.currSlide<r;n.currSlide!=r&&(n.nextSlide=r,n._tempFx=n.pagerFx,n.API.prepareTx(!0,o),n.API.trigger("cycle-pager-activated",[n,e,t]))}}e.extend(e.fn.cycle.defaults,{pager:"> .cycle-pager",pagerActiveClass:"cycle-pager-active",pagerEvent:"click.cycle",pagerEventBubble:void 0,pagerTemplate:"<span>&bull;</span>"}),e(document).on("cycle-bootstrap",function(e,n,i){i.buildPagerLink=t}),e(document).on("cycle-slide-added",function(e,t,i,r){t.pager&&(t.API.buildPagerLink(t,i,r),t.API.page=n)}),e(document).on("cycle-slide-removed",function(t,n,i){if(n.pager){var r=n.API.getComponent("pager");r.each(function(){var t=e(this);e(t.children()[i]).remove()})}}),e(document).on("cycle-update-view",function(t,n){var i;n.pager&&(i=n.API.getComponent("pager"),i.each(function(){e(this).children().removeClass(n.pagerActiveClass).eq(n.currSlide).addClass(n.pagerActiveClass)}))}),e(document).on("cycle-destroyed",function(e,t){var n=t.API.getComponent("pager");n&&(n.children().off(t.pagerEvent),t.pagerTemplate&&n.empty())})}(jQuery),function(e){"use strict";e.extend(e.fn.cycle.defaults,{next:"> .cycle-next",nextEvent:"click.cycle",disabledClass:"disabled",prev:"> .cycle-prev",prevEvent:"click.cycle",swipe:!1}),e(document).on("cycle-initialized",function(e,t){if(t.API.getComponent("next").on(t.nextEvent,function(e){e.preventDefault(),t.API.next()}),t.API.getComponent("prev").on(t.prevEvent,function(e){e.preventDefault(),t.API.prev()}),t.swipe){var n=t.swipeVert?"swipeUp.cycle":"swipeLeft.cycle swipeleft.cycle",i=t.swipeVert?"swipeDown.cycle":"swipeRight.cycle swiperight.cycle";t.container.on(n,function(){t._tempFx=t.swipeFx,t.API.next()}),t.container.on(i,function(){t._tempFx=t.swipeFx,t.API.prev()})}}),e(document).on("cycle-update-view",function(e,t){if(!t.allowWrap){var n=t.disabledClass,i=t.API.getComponent("next"),r=t.API.getComponent("prev"),o=t._prevBoundry||0,a=void 0!==t._nextBoundry?t._nextBoundry:t.slideCount-1;t.currSlide==a?i.addClass(n).prop("disabled",!0):i.removeClass(n).prop("disabled",!1),t.currSlide===o?r.addClass(n).prop("disabled",!0):r.removeClass(n).prop("disabled",!1)}}),e(document).on("cycle-destroyed",function(e,t){t.API.getComponent("prev").off(t.nextEvent),t.API.getComponent("next").off(t.prevEvent),t.container.off("swipeleft.cycle swiperight.cycle swipeLeft.cycle swipeRight.cycle swipeUp.cycle swipeDown.cycle")})}(jQuery),function(e){"use strict";e.extend(e.fn.cycle.defaults,{progressive:!1}),e(document).on("cycle-pre-initialize",function(t,n){if(n.progressive){var i,r,o=n.API,a=o.next,s=o.prev,l=o.prepareTx,c=e.type(n.progressive);if("array"==c)i=n.progressive;else if(e.isFunction(n.progressive))i=n.progressive(n);else if("string"==c){if(r=e(n.progressive),i=e.trim(r.html()),!i)return;if(/^(\[)/.test(i))try{i=e.parseJSON(i)}catch(u){return void o.log("error parsing progressive slides",u)}else i=i.split(new RegExp(r.data("cycle-split")||"\n")),i[i.length-1]||i.pop()}l&&(o.prepareTx=function(e,t){var r,o;return e||0===i.length?void l.apply(n.API,[e,t]):void(t&&n.currSlide==n.slideCount-1?(o=i[0],i=i.slice(1),n.container.one("cycle-slide-added",function(e,t){setTimeout(function(){t.API.advanceSlide(1)},50)}),n.API.add(o)):t||0!==n.currSlide?l.apply(n.API,[e,t]):(r=i.length-1,o=i[r],i=i.slice(0,r),n.container.one("cycle-slide-added",function(e,t){setTimeout(function(){t.currSlide=1,t.API.advanceSlide(-1)},50)}),n.API.add(o,!0)))}),a&&(o.next=function(){var e=this.opts();if(i.length&&e.currSlide==e.slideCount-1){var t=i[0];i=i.slice(1),e.container.one("cycle-slide-added",function(e,t){a.apply(t.API),t.container.removeClass("cycle-loading")}),e.container.addClass("cycle-loading"),e.API.add(t)}else a.apply(e.API)}),s&&(o.prev=function(){var e=this.opts();if(i.length&&0===e.currSlide){var t=i.length-1,n=i[t];i=i.slice(0,t),e.container.one("cycle-slide-added",function(e,t){t.currSlide=1,t.API.advanceSlide(-1),t.container.removeClass("cycle-loading")}),e.container.addClass("cycle-loading"),e.API.add(n,!0)}else s.apply(e.API)})}})}(jQuery),function(e){"use strict";e.extend(e.fn.cycle.defaults,{tmplRegex:"{{((.)?.*?)}}"}),e.extend(e.fn.cycle.API,{tmpl:function(t,n){var i=new RegExp(n.tmplRegex||e.fn.cycle.defaults.tmplRegex,"g"),r=e.makeArray(arguments);return r.shift(),t.replace(i,function(t,n){var i,o,a,s,l=n.split(".");for(i=0;i<r.length;i++)if(a=r[i]){if(l.length>1)for(s=a,o=0;o<l.length;o++)a=s,s=s[l[o]]||n;else s=a[n];if(e.isFunction(s))return s.apply(a,r);if(void 0!==s&&null!==s&&s!=n)return s}return n})}})}(jQuery),!function(e){"use strict";e(document).on("cycle-bootstrap",function(e,t,n){"carousel"===t.fx&&(n.getSlideIndex=function(e){var t=this.opts()._carouselWrap.children(),n=t.index(e);return n%t.length},n.next=function(){var e=t.reverse?-1:1;t.allowWrap===!1&&t.currSlide+e>t.slideCount-t.carouselVisible||(t.API.advanceSlide(e),t.API.trigger("cycle-next",[t]).log("cycle-next"))})}),e.fn.cycle.transitions.carousel={preInit:function(t){t.hideNonActive=!1,t.container.on("cycle-destroyed",e.proxy(this.onDestroy,t.API)),t.API.stopTransition=this.stopTransition;for(var n=0;n<t.startingSlide;n++)t.container.append(t.slides[0])},postInit:function(t){var n,i,r,o,a=t.carouselVertical;t.carouselVisible&&t.carouselVisible>t.slideCount&&(t.carouselVisible=t.slideCount-1);var s=t.carouselVisible||t.slides.length,l={display:a?"block":"inline-block",position:"static"};if(t.container.css({position:"relative",overflow:"hidden"}),t.slides.css(l),t._currSlide=t.currSlide,o=e('<div class="cycle-carousel-wrap"></div>').prependTo(t.container).css({margin:0,padding:0,top:0,left:0,position:"absolute"}).append(t.slides),t._carouselWrap=o,a||o.css("white-space","nowrap"),t.allowWrap!==!1){for(i=0;i<(void 0===t.carouselVisible?2:1);i++){for(n=0;n<t.slideCount;n++)o.append(t.slides[n].cloneNode(!0));for(n=t.slideCount;n--;)o.prepend(t.slides[n].cloneNode(!0))}o.find(".cycle-slide-active").removeClass("cycle-slide-active"),t.slides.eq(t.startingSlide).addClass("cycle-slide-active")}t.pager&&t.allowWrap===!1&&(r=t.slideCount-s,e(t.pager).children().filter(":gt("+r+")").hide()),t._nextBoundry=t.slideCount-t.carouselVisible,this.prepareDimensions(t)},prepareDimensions:function(t){var n,i,r,o,a=t.carouselVertical,s=t.carouselVisible||t.slides.length;if(t.carouselFluid&&t.carouselVisible?t._carouselResizeThrottle||this.fluidSlides(t):t.carouselVisible&&t.carouselSlideDimension?(n=s*t.carouselSlideDimension,t.container[a?"height":"width"](n)):t.carouselVisible&&(n=s*e(t.slides[0])[a?"outerHeight":"outerWidth"](!0),t.container[a?"height":"width"](n)),i=t.carouselOffset||0,t.allowWrap!==!1)if(t.carouselSlideDimension)i-=(t.slideCount+t.currSlide)*t.carouselSlideDimension;else for(r=t._carouselWrap.children(),o=0;o<t.slideCount+t.currSlide;o++)i-=e(r[o])[a?"outerHeight":"outerWidth"](!0);t._carouselWrap.css(a?"top":"left",i)},fluidSlides:function(t){function n(){clearTimeout(r),r=setTimeout(i,20)}function i(){t._carouselWrap.stop(!1,!0);var e=t.container.width()/t.carouselVisible;e=Math.ceil(e-a),t._carouselWrap.children().width(e),t._sentinel&&t._sentinel.width(e),s(t)}var r,o=t.slides.eq(0),a=o.outerWidth()-o.width(),s=this.prepareDimensions;e(window).on("resize",n),t._carouselResizeThrottle=n,i()},transition:function(t,n,i,r,o){var a,s={},l=t.nextSlide-t.currSlide,c=t.carouselVertical,u=t.speed;if(t.allowWrap===!1){r=l>0;var d=t._currSlide,f=t.slideCount-t.carouselVisible;l>0&&t.nextSlide>f&&d==f?l=0:l>0&&t.nextSlide>f?l=t.nextSlide-d-(t.nextSlide-f):0>l&&t.currSlide>f&&t.nextSlide>f?l=0:0>l&&t.currSlide>f?l+=t.currSlide-f:d=t.currSlide,a=this.getScroll(t,c,d,l),t.API.opts()._currSlide=t.nextSlide>f?f:t.nextSlide}else r&&0===t.nextSlide?(a=this.getDim(t,t.currSlide,c),o=this.genCallback(t,r,c,o)):r||t.nextSlide!=t.slideCount-1?a=this.getScroll(t,c,t.currSlide,l):(a=this.getDim(t,t.currSlide,c),o=this.genCallback(t,r,c,o));s[c?"top":"left"]=r?"-="+a:"+="+a,t.throttleSpeed&&(u=a/e(t.slides[0])[c?"height":"width"]()*t.speed),t._carouselWrap.animate(s,u,t.easing,o)},getDim:function(t,n,i){var r=e(t.slides[n]);return r[i?"outerHeight":"outerWidth"](!0)},getScroll:function(e,t,n,i){var r,o=0;if(i>0)for(r=n;n+i>r;r++)o+=this.getDim(e,r,t);else for(r=n;r>n+i;r--)o+=this.getDim(e,r,t);return o},genCallback:function(t,n,i,r){return function(){var n=e(t.slides[t.nextSlide]).position(),o=0-n[i?"top":"left"]+(t.carouselOffset||0);t._carouselWrap.css(t.carouselVertical?"top":"left",o),r()}},stopTransition:function(){var e=this.opts();e.slides.stop(!1,!0),e._carouselWrap.stop(!1,!0)},onDestroy:function(){var t=this.opts();t._carouselResizeThrottle&&e(window).off("resize",t._carouselResizeThrottle),t.slides.prependTo(t.container),t._carouselWrap.remove()}}}(jQuery),function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery"],e):"undefined"!=typeof module&&module.exports?module.exports=e(require("jquery")):e(jQuery)}(function(e){"use strict";function t(t){return!t.nodeName||-1!==e.inArray(t.nodeName.toLowerCase(),["iframe","#document","html","body"])}function n(t){return e.isFunction(t)||e.isPlainObject(t)?t:{top:t,left:t}}var i=e.scrollTo=function(t,n,i){return e(window).scrollTo(t,n,i)};return i.defaults={axis:"xy",duration:0,limit:!0},e.fn.scrollTo=function(r,o,a){"object"==typeof o&&(a=o,o=0),"function"==typeof a&&(a={onAfter:a}),"max"===r&&(r=9e9),a=e.extend({},i.defaults,a),o=o||a.duration;var s=a.queue&&1<a.axis.length;return s&&(o/=2),a.offset=n(a.offset),a.over=n(a.over),this.each(function(){function l(t){var n=e.extend({},a,{queue:!0,duration:o,complete:t&&function(){t.call(d,p,a)}});f.animate(h,n)}if(null!==r){var c,u=t(this),d=u?this.contentWindow||window:this,f=e(d),p=r,h={};switch(typeof p){case"number":case"string":if(/^([+-]=?)?\d+(\.\d+)?(px|%)?$/.test(p)){p=n(p);break}p=u?e(p):e(p,d);case"object":if(0===p.length)return;(p.is||p.style)&&(c=(p=e(p)).offset())}var g=e.isFunction(a.offset)&&a.offset(d,p)||a.offset;e.each(a.axis.split(""),function(e,t){var n="x"===t?"Left":"Top",r=n.toLowerCase(),o="scroll"+n,m=f[o](),v=i.max(d,t);c?(h[o]=c[r]+(u?0:m-f.offset()[r]),a.margin&&(h[o]-=parseInt(p.css("margin"+n),10)||0,h[o]-=parseInt(p.css("border"+n+"Width"),10)||0),h[o]+=g[r]||0,a.over[r]&&(h[o]+=p["x"===t?"width":"height"]()*a.over[r])):(n=p[r],h[o]=n.slice&&"%"===n.slice(-1)?parseFloat(n)/100*v:n),a.limit&&/^\d+$/.test(h[o])&&(h[o]=0>=h[o]?0:Math.min(h[o],v)),!e&&1<a.axis.length&&(m===h[o]?h={}:s&&(l(a.onAfterFirst),h={}))}),l(a.onAfter)}})},i.max=function(n,i){var r="x"===i?"Width":"Height",o="scroll"+r;if(!t(n))return n[o]-e(n)[r.toLowerCase()]();var r="client"+r,a=n.ownerDocument||n.document,s=a.documentElement,a=a.body;return Math.max(s[o],a[o])-Math.min(s[r],a[r])},e.Tween.propHooks.scrollLeft=e.Tween.propHooks.scrollTop={get:function(t){return e(t.elem)[t.prop]()},set:function(t){var n=this.get(t);if(t.options.interrupt&&t._last&&t._last!==n)return e(t.elem).stop();var i=Math.round(t.now);n!==i&&(e(t.elem)[t.prop](i),t._last=this.get(t))}},i}),function(e,t,n,i){function r(e){return{url:e.split(" ").length>0?e.split(" ")[0]:i,w:c.test(e)?parseInt(c.exec(e)[0]):1/0,h:l.test(e)?parseInt(l.exec(e)[0]):1/0,dpi:parseFloat(u.test(e)?u.exec(e)[1]:1)}}function o(e){for(var t=e.data("srcset"),n=t.split(","),i=[],o=0;o<n.length;o++){var a=r(n[o].replace(/^\s*|\s*$/gi,""));a.url?i.push(a):console.log("Couldn't parse URL; got %o",a)}return 0===i.length?(console.log("Couldn't parse srcset data for %o",e),!1):(e.data("srcset-sizes",i),i)}function a(n){if(n.data("srcset-sizes")){var r=e(t).height(),o=e(t).width(),a=t.devicePixelRatio||1,s=n.data("srcset-sizes");filteredData=e.grep(s,function(e,t){return e.w>=o&&e.h>=r&&e.dpi>=a});var l={data:i,diff:1/0};if(1===filteredData.length&&(l.data=filteredData[0]),0===filteredData.length)s.map(function(e,t){var n=o-e.w===-(1/0)?0:o*a-e.w,i=r-e.h===-(1/0)?0:r*a-e.h;return e.diff=n+i,e}).sort(function(e,t){return e.diff<t.diff}).forEach(function(e,t){l.data&&l.data.diff<=0||(l.data=e)});else for(var c=0;c<filteredData.length;c++){var u=filteredData[c].w-o===1/0?0:filteredData[c].w*a-o,d=filteredData[c].h-r===1/0?0:filteredData[c].h*a-r,f=u+d;f<l.diff&&(l.diff=f,l.data=filteredData[c])}if(s=l.data,!s||!s.url)return void console.log("No src found for %o",n);var p=(n.data("srcset-base")||"")+s.url+(n.data("srcset-ext")||"");n.attr("src")!==p&&(n.trigger("beforeSrcReplace"),n.attr("src",p),n.trigger("srcReplaced"))}}var s={autoInit:!0,updateOnResize:!0},l=/\d+h/i,c=/\d+w/i,u=/([\d\.]+)x\b/i;e.srcset=e.extend(s,e.srcset||{}),e.fn.srcset=function(n){options=e.extend({},e.srcset,n||{}),e(this).each(function(){o(e(this)),a(e(this))}),options.updateOnResize&&(e(t).on("resize",function(){e(this).each(function(){a(e(this))})}.bind(this)),e(t))},e(function(){e.srcset.autoInit&&e("img[data-srcset]").srcset()})}(window.jQuery,window,document),$(function(){$("input.password").keyup(function(e){var t=new RegExp("^(?=.{8,})(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*\\W).*$","g"),n=new RegExp("^(?=.{7,})(((?=.*[A-Z])(?=.*[a-z]))|((?=.*[A-Z])(?=.*[0-9]))|((?=.*[a-z])(?=.*[0-9]))).*$","g"),i=new RegExp("(?=.{6,}).*","g"),r=$(this).val();""==r?$(".passwordStrength .bar").width("100%"):0==i.test(r)?$(".passwordStrength .bar").width("66%"):t.test(r)?$(".passwordStrength .bar").width("0%"):n.test(r)?$(".passwordStrength .bar").width("33%"):$(".passwordStrength .bar").width("100%")})}),function(e,t){"use strict";var n;n=void 0,e.fn.adjustWindow=function(){var e;e=n.height()},e(document).ready(function(){n=e(window),e.fn.adjustWindow(),conditionizr.add("chrome",!!window.chrome&&/google/i.test(navigator.vendor)),conditionizr.add("firefox","InstallTrigger"in window),conditionizr.add("ie8",!!Function("/*@cc_on return (@_jscript_version > 5.7 && !/^(9|10)/.test(@_jscript_version)); @*/")()),conditionizr.add("ie9",!!Function("/*@cc_on return (/^9/.test(@_jscript_version) && /MSIE 9.0(?!.*IEMobile)/i.test(navigator.userAgent)); @*/")()),conditionizr.add("ie10",!!Function("/*@cc_on return (/^10/.test(@_jscript_version) && /MSIE 10.0(?!.*IEMobile)/i.test(navigator.userAgent)); @*/")()),conditionizr.add("ie11",/(?:\sTrident\/7\.0;.*\srv:11\.0)/i.test(navigator.userAgent)),conditionizr.add("ios",/iP(ad|hone|od)/i.test(navigator.userAgent)),conditionizr.add("safari",/Constructor/.test(window.HTMLElement)),conditionizr.add("windows",/win/i.test(navigator.platform)),conditionizr.config({tests:{chrome:["class"],firefox:["class"],ie8:["class"],ie9:["class"],ie10:["class"],ie11:["class"],ios:["class"],opera:["class"],safari:["class"],windows:["class"]}}),conditionizr.polyfill("assets/js/min/html5.min.js",["ie8","ie9"]),conditionizr.polyfill("assets/js/min/css3-mediaqueries.js",["ie8","ie9"]),conditionizr.polyfill("assets/js/min/selectivizr-min.js",["ie8","ie9"]),e(".scrollTo").on("click",function(t){var n;return t.preventDefault(),n="."+e(this).attr("href").substring(1),console.info(n),e(window).scrollTo(n,500,{axis:"y"})}),Modernizr.touchevents&&setTimeout(function(){window.scrollTo(0,1)},1e3)}),e(window).resize(function(){n=e(window),e.fn.adjustWindow()}),e(window).scroll(function(){})}(jQuery,this);