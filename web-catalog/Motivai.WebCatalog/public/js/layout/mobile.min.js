!function(e,a){"use strict";e.fn.initMobile=function(){var a,n,r;return e(".site-header .search").prepend("<span></span>"),e(".site-header .search form").prepend('<a href="#" class="close"></a>'),e(".site-header .search span").on("click",function(a){if(a.preventDefault(),!e(".site-header .search form").hasClass("active"))return e(".site-header .search form").addClass("active").fadeIn}),e(".site-header .search form a.close").on("click",function(a){return a.preventDefault(),e(".site-header .search form").removeClass("active").fadeOut}),e(".list-category").length>=1&&e("aside.sidebar nav").prepend('<a href="#" class="cat-filters"></a>'),e("aside.sidebar .cat-filters").on("click",function(a){return a.preventDefault(),e("aside.sidebar nav").toggleClass("active")}),r=e("#primary-navigation .menu-container"),a=r.find(".menu-toggle"),(n=r.find(".nav-menu"))&&n.children().length||a.hide(),e(".menu-toggle").on("click",function(){r.toggleClass("toggled-on")})}}(jQuery);