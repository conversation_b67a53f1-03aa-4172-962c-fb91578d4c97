!function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery"],e):"undefined"!=typeof module&&module.exports?module.exports=e(require("jquery")):e(jQuery)}(function(e){"use strict";function t(t){return!t.nodeName||-1!==e.inArray(t.nodeName.toLowerCase(),["iframe","#document","html","body"])}function n(t){return e.isFunction(t)||e.isPlainObject(t)?t:{top:t,left:t}}var o=e.scrollTo=function(t,n,o){return e(window).scrollTo(t,n,o)};return o.defaults={axis:"xy",duration:0,limit:!0},e.fn.scrollTo=function(r,i,s){"object"==typeof i&&(s=i,i=0),"function"==typeof s&&(s={onAfter:s}),"max"===r&&(r=9e9),s=e.extend({},o.defaults,s),i=i||s.duration;var u=s.queue&&1<s.axis.length;return u&&(i/=2),s.offset=n(s.offset),s.over=n(s.over),this.each(function(){function f(t){var n=e.extend({},s,{queue:!0,duration:i,complete:t&&function(){t.call(l,m,s)}});d.animate(p,n)}if(null!==r){var a,c=t(this),l=c?this.contentWindow||window:this,d=e(l),m=r,p={};switch(typeof m){case"number":case"string":if(/^([+-]=?)?\d+(\.\d+)?(px|%)?$/.test(m)){m=n(m);break}m=c?e(m):e(m,l);case"object":if(0===m.length)return;(m.is||m.style)&&(a=(m=e(m)).offset())}var h=e.isFunction(s.offset)&&s.offset(l,m)||s.offset;e.each(s.axis.split(""),function(e,t){var n="x"===t?"Left":"Top",r=n.toLowerCase(),i="scroll"+n,x=d[i](),v=o.max(l,t);a?(p[i]=a[r]+(c?0:x-d.offset()[r]),s.margin&&(p[i]-=parseInt(m.css("margin"+n),10)||0,p[i]-=parseInt(m.css("border"+n+"Width"),10)||0),p[i]+=h[r]||0,s.over[r]&&(p[i]+=m["x"===t?"width":"height"]()*s.over[r])):(n=m[r],p[i]=n.slice&&"%"===n.slice(-1)?parseFloat(n)/100*v:n),s.limit&&/^\d+$/.test(p[i])&&(p[i]=0>=p[i]?0:Math.min(p[i],v)),!e&&1<s.axis.length&&(x===p[i]?p={}:u&&(f(s.onAfterFirst),p={}))}),f(s.onAfter)}})},o.max=function(n,o){var r="scroll"+(i="x"===o?"Width":"Height");if(!t(n))return n[r]-e(n)[i.toLowerCase()]();var i="client"+i,s=(u=n.ownerDocument||n.document).documentElement,u=u.body;return Math.max(s[r],u[r])-Math.min(s[i],u[i])},e.Tween.propHooks.scrollLeft=e.Tween.propHooks.scrollTop={get:function(t){return e(t.elem)[t.prop]()},set:function(t){var n=this.get(t);if(t.options.interrupt&&t._last&&t._last!==n)return e(t.elem).stop();var o=Math.round(t.now);n!==o&&(e(t.elem)[t.prop](o),t._last=this.get(t))}},o}),function(e,t){"use strict";e.fn.initScroll=function(t){return e(t).on("click",function(t){var n;return t.preventDefault(),n="."+e(this).attr("href").substring(1),console.info(n),e(window).scrollTo(n,500,{axis:"y"})})}}(jQuery);