!function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.Blazy=t()}(this,function(){function e(e){var o=e._util;o.elements=a(e.options.selector),o.count=o.elements.length,o.destroyed&&(o.destroyed=!1,e.options.container&&f(e.options.container,function(e){l(e,"scroll",o.validateT)}),l(window,"resize",o.save_viewportOffsetT),l(window,"resize",o.validateT),l(window,"scroll",o.validateT)),t(e)}function t(e){for(var t=e._util,o=0;o<t.count;o++){var n=t.elements[o],s=n.getBoundingClientRect();(s.right>=v.left&&s.bottom>=v.top&&s.left<=v.right&&s.top<=v.bottom||i(n,e.options.successClass))&&(e.load(n),t.elements.splice(o,1),t.count--,o--)}0===t.count&&e.destroy()}function o(e,t,o){if(!i(e,o.successClass)&&(t||o.loadInvisible||0<e.offsetWidth&&0<e.offsetHeight))if(t=e.getAttribute(p)||e.getAttribute(o.src)){var a=(t=t.split(o.separator))[m&&1<t.length?1:0],c="img"===e.nodeName.toLowerCase();if(c||void 0===e.src){var d=new Image,v=function(){o.error&&o.error(e,"invalid"),r(e,o.errorClass),u(d,"error",v),u(d,"load",h)},h=function(){if(c){e.src=a,s(e,"srcset",o.srcset);var t=e.parentNode;t&&"picture"===t.nodeName.toLowerCase()&&f(t.getElementsByTagName("source"),function(e){s(e,"srcset",o.srcset)})}else e.style.backgroundImage='url("'+a+'")';n(e,o),u(d,"load",h),u(d,"error",v)};l(d,"error",v),l(d,"load",h),d.src=a}else e.src=a,n(e,o)}else"video"===e.nodeName.toLowerCase()?(f(e.getElementsByTagName("source"),function(e){s(e,"src",o.src)}),e.load(),n(e,o)):(o.error&&o.error(e,"missing"),r(e,o.errorClass))}function n(e,t){r(e,t.successClass),t.success&&t.success(e),e.removeAttribute(t.src),f(t.breakpoints,function(t){e.removeAttribute(t.src)})}function s(e,t,o){var n=e.getAttribute(o);n&&(e[t]=n,e.removeAttribute(o))}function i(e,t){return-1!==(" "+e.className+" ").indexOf(" "+t+" ")}function r(e,t){i(e,t)||(e.className+=" "+t)}function a(e){for(var t=[],o=(e=document.querySelectorAll(e)).length;o--;t.unshift(e[o]));return t}function c(e){v.bottom=(window.innerHeight||document.documentElement.clientHeight)+e,v.right=(window.innerWidth||document.documentElement.clientWidth)+e}function l(e,t,o){e.attachEvent?e.attachEvent&&e.attachEvent("on"+t,o):e.addEventListener(t,o,!1)}function u(e,t,o){e.detachEvent?e.detachEvent&&e.detachEvent("on"+t,o):e.removeEventListener(t,o,!1)}function f(e,t){if(e&&t)for(var o=e.length,n=0;n<o&&!1!==t(e[n],n);n++);}function d(e,t,o){var n=0;return function(){var s=+new Date;s-n<t||(n=s,e.apply(o,arguments))}}var p,v,m;return function(n){if(!document.querySelectorAll){var s=document.createStyleSheet();document.querySelectorAll=function(e,t,o,n,i){for(i=document.all,t=[],o=(e=e.replace(/\[for\b/gi,"[htmlFor").split(",")).length;o--;){for(s.addRule(e[o],"k:v"),n=i.length;n--;)i[n].currentStyle.k&&t.push(i[n]);s.removeRule(0)}return t}}var i=this,r=i._util={};r.elements=[],r.destroyed=!0,i.options=n||{},i.options.error=i.options.error||!1,i.options.offset=i.options.offset||100,i.options.success=i.options.success||!1,i.options.selector=i.options.selector||".b-lazy",i.options.separator=i.options.separator||"|",i.options.container=!!i.options.container&&document.querySelectorAll(i.options.container),i.options.errorClass=i.options.errorClass||"b-error",i.options.breakpoints=i.options.breakpoints||!1,i.options.loadInvisible=i.options.loadInvisible||!1,i.options.successClass=i.options.successClass||"b-loaded",i.options.validateDelay=i.options.validateDelay||25,i.options.save_viewportOffsetDelay=i.options.save_viewportOffsetDelay||50,i.options.srcset=i.options.srcset||"data-srcset",i.options.src=p=i.options.src||"data-src",m=1<window.devicePixelRatio,(v={}).top=0-i.options.offset,v.left=0-i.options.offset,i.revalidate=function(){e(this)},i.load=function(e,t){var n=this.options;void 0===e.length?o(e,t,n):f(e,function(e){o(e,t,n)})},i.destroy=function(){var e=this._util;this.options.container&&f(this.options.container,function(t){u(t,"scroll",e.validateT)}),u(window,"scroll",e.validateT),u(window,"resize",e.validateT),u(window,"resize",e.save_viewportOffsetT),e.count=0,e.elements.length=0,e.destroyed=!0},r.validateT=d(function(){t(i)},i.options.validateDelay,i),r.save_viewportOffsetT=d(function(){c(i.options.offset)},i.options.save_viewportOffsetDelay,i),c(i.options.offset),f(i.options.breakpoints,function(e){if(e.width>=window.screen.width)return p=e.src,!1}),setTimeout(function(){e(i)})}}),function(e,t){"use strict";e.fn.initBlazy=function(t,o){return null==o&&(o=null),o||(o="data-src"),window.bLazy=new Blazy({src:o,selector:t,loadInvisible:!0,success:function(t){setTimeout(function(){e(t).parent().removeClass("loading")},200),e.Deferred().resolve()},error:function(t,o){"missing"!==o&&"invalid"!==o||e(t).attr("src","/assets/img/imgError.png"),e.Deferred().resolve()}})},e.Deferred()}(jQuery);