require=function(){function e(t,n,r){function s(a,o){if(!n[a]){if(!t[a]){var u="function"==typeof require&&require;if(!o&&u)return u(a,!0);if(i)return i(a,!0);var l=new Error("Cannot find module '"+a+"'");throw l.code="MODULE_NOT_FOUND",l}var c=n[a]={exports:{}};t[a][0].call(c.exports,function(e){var n=t[a][1][e];return s(n||e)},c,c.exports,e,t,n,r)}return n[a].exports}for(var i="function"==typeof require&&require,a=0;a<r.length;a++)s(r[a]);return s}return e}()({1:[function(e,t,n){!function(e,r){"function"==typeof define&&define.amd?define([],r):"object"==typeof n?t.exports=r():e.BrV=r()}(this,function(){function e(e,t){var n=t.algorithmSteps,r=a.handleStr[n[0]](e),s=a.sum[n[1]](r,t.pesos),i=a.rest[n[2]](s),o=parseInt(r[t.dvpos]),u=a.expectedDV[n[3]](i,r);return o===u}function t(t,n){if(n.match&&!n.match.test(t))return!1;for(var r=0;r<n.dvs.length;r++)if(!e(t,n.dvs[r]))return!1;return!0}var n={};n.validate=function(e){var t=[6,5,4,3,2,9,8,7,6,5,4,3,2];e=e.replace(/[^\d]/g,"");var n=/^(0{14}|1{14}|2{14}|3{14}|4{14}|5{14}|6{14}|7{14}|8{14}|9{14})$/;if(!e||14!==e.length||n.test(e))return!1;e=e.split("");for(var r=0,s=0;r<12;r++)s+=e[r]*t[r+1];if(s=11-s%11,s=s>=10?0:s,parseInt(e[12])!==s)return!1;for(r=0,s=0;r<=12;r++)s+=e[r]*t[r];return s=11-s%11,s=s>=10?0:s,parseInt(e[13])===s};var r={};r.validate=function(e){function t(t){for(var n=0,r=t-9,s=0;s<9;s++)n+=parseInt(e.charAt(s+r))*(s+1);return n%11%10===parseInt(e.charAt(t))}e=e.replace(/[^\d]+/g,"");var n=/^(0{11}|1{11}|2{11}|3{11}|4{11}|5{11}|6{11}|7{11}|8{11}|9{11})$/;return!(!e||11!==e.length||n.test(e))&&(t(9)&&t(10))};var s=function(e){return this instanceof s?(this.rules=i[e]||[],this.rule,s.prototype._defineRule=function(e){this.rule=void 0;for(var t=0;t<this.rules.length&&void 0===this.rule;t++){var n=e.replace(/[^\d]/g,""),r=this.rules[t];n.length!==r.chars||r.match&&!r.match.test(e)||(this.rule=r)}return!!this.rule},void(s.prototype.validate=function(e){return!(!e||!this._defineRule(e))&&this.rule.validate(e)})):new s(e)},i={},a={handleStr:{onlyNumbers:function(e){return e.replace(/[^\d]/g,"").split("")},mgSpec:function(e){var t=e.replace(/[^\d]/g,"");return t=t.substr(0,3)+"0"+t.substr(3,t.length),t.split("")}},sum:{normalSum:function(e,t){for(var n=e,r=0,s=0;s<t.length;s++)r+=parseInt(n[s])*t[s];return r},individualSum:function(e,t){for(var n=e,r=0,s=0;s<t.length;s++){var i=parseInt(n[s])*t[s];r+=i%10+parseInt(i/10)}return r},apSpec:function(e,t){var n=this.normalSum(e,t),r=e.join("");return r>="030000010"&&r<="030170009"?n+5:r>="030170010"&&r<="030190229"?n+9:n}},rest:{mod11:function(e){return e%11},mod10:function(e){return e%10},mod9:function(e){return e%9}},expectedDV:{minusRestOf11:function(e){return e<2?0:11-e},minusRestOf11v2:function(e){return e<2?11-e-10:11-e},minusRestOf10:function(e){return e<1?0:10-e},mod10:function(e){return e%10},goSpec:function(e,t){var n=t.join("");return 1===e?n>="101031050"&&n<="101199979"?1:0:0===e?0:11-e},apSpec:function(e,t){var n=t.join("");return 0===e?n>="030170010"&&n<="030190229"?1:0:1===e?0:11-e},voidFn:function(e){return e}}};return i.PE=[{chars:9,dvs:[{dvpos:7,pesos:[8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]},{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}},{chars:14,pesos:[[1,2,3,4,5,9,8,7,6,5,4,3,2]],dvs:[{dvpos:13,pesos:[5,4,3,2,1,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11v2"]}],validate:function(e){return t(e,this)}}],i.RS=[{chars:10,dvs:[{dvpos:9,pesos:[2,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.AC=[{chars:13,match:/^01/,dvs:[{dvpos:11,pesos:[4,3,2,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]},{dvpos:12,pesos:[5,4,3,2,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.MG=[{chars:13,dvs:[{dvpos:12,pesos:[1,2,1,2,1,2,1,2,1,2,1,2],algorithmSteps:["mgSpec","individualSum","mod10","minusRestOf10"]},{dvpos:12,pesos:[3,2,11,10,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.SP=[{chars:12,match:/^[0-9]/,dvs:[{dvpos:8,pesos:[1,3,4,5,6,7,8,10],algorithmSteps:["onlyNumbers","normalSum","mod11","mod10"]},{dvpos:11,pesos:[3,2,10,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","mod10"]}],validate:function(e){return t(e,this)}},{chars:12,match:/^P/i,dvs:[{dvpos:8,pesos:[1,3,4,5,6,7,8,10],algorithmSteps:["onlyNumbers","normalSum","mod11","mod10"]}],validate:function(e){return t(e,this)}}],i.DF=[{chars:13,dvs:[{dvpos:11,pesos:[4,3,2,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]},{dvpos:12,pesos:[5,4,3,2,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.ES=[{chars:9,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.BA=[{chars:8,match:/^[0123458]/,dvs:[{dvpos:7,pesos:[7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod10","minusRestOf10"]},{dvpos:6,pesos:[8,7,6,5,4,3,0,2],algorithmSteps:["onlyNumbers","normalSum","mod10","minusRestOf10"]}],validate:function(e){return t(e,this)}},{chars:8,match:/^[679]/,dvs:[{dvpos:7,pesos:[7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]},{dvpos:6,pesos:[8,7,6,5,4,3,0,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}},{chars:9,match:/^[0-9][0123458]/,dvs:[{dvpos:8,pesos:[8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod10","minusRestOf10"]},{dvpos:7,pesos:[9,8,7,6,5,4,3,0,2],algorithmSteps:["onlyNumbers","normalSum","mod10","minusRestOf10"]}],validate:function(e){return t(e,this)}},{chars:9,match:/^[0-9][679]/,dvs:[{dvpos:8,pesos:[8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]},{dvpos:7,pesos:[9,8,7,6,5,4,3,0,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.AM=[{chars:9,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.RN=[{chars:9,match:/^20/,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}},{chars:10,match:/^20/,dvs:[{dvpos:8,pesos:[10,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.RO=[{chars:14,dvs:[{dvpos:13,pesos:[6,5,4,3,2,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.PR=[{chars:10,dvs:[{dvpos:8,pesos:[3,2,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]},{dvpos:9,pesos:[4,3,2,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.SC=[{chars:9,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.RJ=[{chars:8,dvs:[{dvpos:7,pesos:[2,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.PA=[{chars:9,match:/^15/,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.SE=[{chars:9,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.PB=[{chars:9,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.CE=[{chars:9,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.PI=[{chars:9,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.MA=[{chars:9,match:/^12/,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.MT=[{chars:11,dvs:[{dvpos:10,pesos:[3,2,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.MS=[{chars:9,match:/^28/,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.TO=[{chars:11,match:/^[0-9]{2}((0[123])|(99))/,dvs:[{dvpos:10,pesos:[9,8,0,0,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.AL=[{chars:9,match:/^24[03578]/,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return t(e,this)}}],i.RR=[{chars:9,match:/^24/,dvs:[{dvpos:8,pesos:[1,2,3,4,5,6,7,8],algorithmSteps:["onlyNumbers","normalSum","mod9","voidFn"]}],validate:function(e){return t(e,this)}}],i.GO=[{chars:9,match:/^1[015]/,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","goSpec"]}],validate:function(e){return t(e,this)}}],i.AP=[{chars:9,match:/^03/,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","apSpec","mod11","apSpec"]}],validate:function(e){return t(e,this)}}],{ie:s,cpf:r,cnpj:n}})},{}],2:[function(e,t,n){!function(e,r){"object"==typeof n&&"undefined"!=typeof t?t.exports=r():"function"==typeof define&&define.amd?define(r):e.moment=r()}(this,function(){"use strict";function n(){return Nr.apply(null,arguments)}function r(e){Nr=e}function s(e){return e instanceof Array||"[object Array]"===Object.prototype.toString.call(e)}function i(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)}function a(e){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(e).length;var t;for(t in e)if(e.hasOwnProperty(t))return!1;return!0}function o(e){return void 0===e}function u(e){return"number"==typeof e||"[object Number]"===Object.prototype.toString.call(e)}function l(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function c(e,t){var n,r=[];for(n=0;n<e.length;++n)r.push(t(e[n],n));return r}function d(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function h(e,t){for(var n in t)d(t,n)&&(e[n]=t[n]);return d(t,"toString")&&(e.toString=t.toString),d(t,"valueOf")&&(e.valueOf=t.valueOf),e}function f(e,t,n,r){return bt(e,t,n,r,!0).utc()}function m(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],meridiem:null,rfc2822:!1,weekdayMismatch:!1}}function p(e){return null==e._pf&&(e._pf=m()),e._pf}function v(e){if(null==e._isValid){var t=p(e),n=Tr.call(t.parsedDateParts,function(e){return null!=e}),r=!isNaN(e._d.getTime())&&t.overflow<0&&!t.empty&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&n);if(e._strict&&(r=r&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour),null!=Object.isFrozen&&Object.isFrozen(e))return r;e._isValid=r}return e._isValid}function g(e){var t=f(NaN);return null!=e?h(p(t),e):p(t).userInvalidated=!0,t}function y(e,t){var n,r,s;if(o(t._isAMomentObject)||(e._isAMomentObject=t._isAMomentObject),o(t._i)||(e._i=t._i),o(t._f)||(e._f=t._f),o(t._l)||(e._l=t._l),o(t._strict)||(e._strict=t._strict),o(t._tzm)||(e._tzm=t._tzm),o(t._isUTC)||(e._isUTC=t._isUTC),o(t._offset)||(e._offset=t._offset),o(t._pf)||(e._pf=p(t)),o(t._locale)||(e._locale=t._locale),Pr.length>0)for(n=0;n<Pr.length;n++)r=Pr[n],s=t[r],o(s)||(e[r]=s);return e}function _(e){y(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),Rr===!1&&(Rr=!0,n.updateOffset(this),Rr=!1)}function S(e){return e instanceof _||null!=e&&null!=e._isAMomentObject}function k(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function w(e){var t=+e,n=0;return 0!==t&&isFinite(t)&&(n=k(t)),n}function M(e,t,n){var r,s=Math.min(e.length,t.length),i=Math.abs(e.length-t.length),a=0;for(r=0;r<s;r++)(n&&e[r]!==t[r]||!n&&w(e[r])!==w(t[r]))&&a++;return a+i}function D(e){n.suppressDeprecationWarnings===!1&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function Y(e,t){var r=!0;return h(function(){if(null!=n.deprecationHandler&&n.deprecationHandler(null,e),r){for(var s,i=[],a=0;a<arguments.length;a++){if(s="","object"==typeof arguments[a]){s+="\n["+a+"] ";for(var o in arguments[0])s+=o+": "+arguments[0][o]+", ";s=s.slice(0,-2)}else s=arguments[a];i.push(s)}D(e+"\nArguments: "+Array.prototype.slice.call(i).join("")+"\n"+(new Error).stack),r=!1}return t.apply(this,arguments)},t)}function b(e,t){null!=n.deprecationHandler&&n.deprecationHandler(e,t),$r[e]||(D(t),$r[e]=!0)}function O(e){return e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}function x(e){var t,n;for(n in e)t=e[n],O(t)?this[n]=t:this["_"+n]=t;this._config=e,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)}function N(e,t){var n,r=h({},e);for(n in t)d(t,n)&&(i(e[n])&&i(t[n])?(r[n]={},h(r[n],e[n]),h(r[n],t[n])):null!=t[n]?r[n]=t[n]:delete r[n]);for(n in e)d(e,n)&&!d(t,n)&&i(e[n])&&(r[n]=h({},r[n]));return r}function T(e){null!=e&&this.set(e)}function P(e,t,n){var r=this._calendar[e]||this._calendar.sameElse;return O(r)?r.call(t,n):r}function R(e){var t=this._longDateFormat[e],n=this._longDateFormat[e.toUpperCase()];return t||!n?t:(this._longDateFormat[e]=n.replace(/MMMM|MM|DD|dddd/g,function(e){return e.slice(1)}),this._longDateFormat[e])}function $(){return this._invalidDate}function C(e){return this._ordinal.replace("%d",e)}function V(e,t,n,r){var s=this._relativeTime[n];return O(s)?s(e,t,n,r):s.replace(/%d/i,e)}function E(e,t){var n=this._relativeTime[e>0?"future":"past"];return O(n)?n(t):n.replace(/%s/i,t)}function A(e,t){var n=e.toLowerCase();Lr[n]=Lr[n+"s"]=Lr[t]=e}function F(e){return"string"==typeof e?Lr[e]||Lr[e.toLowerCase()]:void 0}function W(e){var t,n,r={};for(n in e)d(e,n)&&(t=F(n),t&&(r[t]=e[n]));return r}function U(e,t){Hr[e]=t}function L(e){var t=[];for(var n in e)t.push({unit:n,priority:Hr[n]});return t.sort(function(e,t){return e.priority-t.priority}),t}function H(e,t,n){var r=""+Math.abs(e),s=t-r.length,i=e>=0;return(i?n?"+":"":"-")+Math.pow(10,Math.max(0,s)).toString().substr(1)+r}function j(e,t,n,r){var s=r;"string"==typeof r&&(s=function(){return this[r]()}),e&&(Zr[e]=s),t&&(Zr[t[0]]=function(){return H(s.apply(this,arguments),t[1],t[2])}),n&&(Zr[n]=function(){return this.localeData().ordinal(s.apply(this,arguments),e)})}function I(e){return e.match(/\[[\s\S]/)?e.replace(/^\[|\]$/g,""):e.replace(/\\/g,"")}function G(e){var t,n,r=e.match(jr);for(t=0,n=r.length;t<n;t++)Zr[r[t]]?r[t]=Zr[r[t]]:r[t]=I(r[t]);return function(t){var s,i="";for(s=0;s<n;s++)i+=O(r[s])?r[s].call(t,e):r[s];return i}}function Z(e,t){return e.isValid()?(t=z(t,e.localeData()),Gr[t]=Gr[t]||G(t),Gr[t](e)):e.localeData().invalidDate()}function z(e,t){function n(e){return t.longDateFormat(e)||e}var r=5;for(Ir.lastIndex=0;r>=0&&Ir.test(e);)e=e.replace(Ir,n),Ir.lastIndex=0,r-=1;return e}function B(e,t,n){cs[e]=O(t)?t:function(e,r){return e&&n?n:t}}function q(e,t){return d(cs,e)?cs[e](t._strict,t._locale):new RegExp(J(e))}function J(e){return Q(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(e,t,n,r,s){return t||n||r||s}))}function Q(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function X(e,t){var n,r=t;for("string"==typeof e&&(e=[e]),u(t)&&(r=function(e,n){n[t]=w(e)}),n=0;n<e.length;n++)ds[e[n]]=r}function K(e,t){X(e,function(e,n,r,s){r._w=r._w||{},t(e,r._w,r,s)})}function ee(e,t,n){null!=t&&d(ds,e)&&ds[e](t,n._a,n,e)}function te(e){return ne(e)?366:365}function ne(e){return e%4===0&&e%100!==0||e%400===0}function re(){return ne(this.year())}function se(e,t){return function(r){return null!=r?(ae(this,e,r),n.updateOffset(this,t),this):ie(this,e)}}function ie(e,t){return e.isValid()?e._d["get"+(e._isUTC?"UTC":"")+t]():NaN}function ae(e,t,n){e.isValid()&&!isNaN(n)&&("FullYear"===t&&ne(e.year())&&1===e.month()&&29===e.date()?e._d["set"+(e._isUTC?"UTC":"")+t](n,e.month(),ce(n,e.month())):e._d["set"+(e._isUTC?"UTC":"")+t](n))}function oe(e){return e=F(e),O(this[e])?this[e]():this}function ue(e,t){if("object"==typeof e){e=W(e);for(var n=L(e),r=0;r<n.length;r++)this[n[r].unit](e[n[r].unit])}else if(e=F(e),O(this[e]))return this[e](t);return this}function le(e,t){return(e%t+t)%t}function ce(e,t){if(isNaN(e)||isNaN(t))return NaN;var n=le(t,12);return e+=(t-n)/12,1===n?ne(e)?29:28:31-n%7%2}function de(e,t){return e?s(this._months)?this._months[e.month()]:this._months[(this._months.isFormat||Ms).test(t)?"format":"standalone"][e.month()]:s(this._months)?this._months:this._months.standalone}function he(e,t){return e?s(this._monthsShort)?this._monthsShort[e.month()]:this._monthsShort[Ms.test(t)?"format":"standalone"][e.month()]:s(this._monthsShort)?this._monthsShort:this._monthsShort.standalone}function fe(e,t,n){var r,s,i,a=e.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],r=0;r<12;++r)i=f([2e3,r]),this._shortMonthsParse[r]=this.monthsShort(i,"").toLocaleLowerCase(),this._longMonthsParse[r]=this.months(i,"").toLocaleLowerCase();return n?"MMM"===t?(s=ks.call(this._shortMonthsParse,a),s!==-1?s:null):(s=ks.call(this._longMonthsParse,a),s!==-1?s:null):"MMM"===t?(s=ks.call(this._shortMonthsParse,a),s!==-1?s:(s=ks.call(this._longMonthsParse,a),s!==-1?s:null)):(s=ks.call(this._longMonthsParse,a),s!==-1?s:(s=ks.call(this._shortMonthsParse,a),s!==-1?s:null))}function me(e,t,n){var r,s,i;if(this._monthsParseExact)return fe.call(this,e,t,n);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),r=0;r<12;r++){if(s=f([2e3,r]),n&&!this._longMonthsParse[r]&&(this._longMonthsParse[r]=new RegExp("^"+this.months(s,"").replace(".","")+"$","i"),this._shortMonthsParse[r]=new RegExp("^"+this.monthsShort(s,"").replace(".","")+"$","i")),n||this._monthsParse[r]||(i="^"+this.months(s,"")+"|^"+this.monthsShort(s,""),this._monthsParse[r]=new RegExp(i.replace(".",""),"i")),n&&"MMMM"===t&&this._longMonthsParse[r].test(e))return r;if(n&&"MMM"===t&&this._shortMonthsParse[r].test(e))return r;if(!n&&this._monthsParse[r].test(e))return r}}function pe(e,t){var n;if(!e.isValid())return e;if("string"==typeof t)if(/^\d+$/.test(t))t=w(t);else if(t=e.localeData().monthsParse(t),!u(t))return e;return n=Math.min(e.date(),ce(e.year(),t)),e._d["set"+(e._isUTC?"UTC":"")+"Month"](t,n),e}function ve(e){return null!=e?(pe(this,e),n.updateOffset(this,!0),this):ie(this,"Month")}function ge(){return ce(this.year(),this.month())}function ye(e){return this._monthsParseExact?(d(this,"_monthsRegex")||Se.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(d(this,"_monthsShortRegex")||(this._monthsShortRegex=bs),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)}function _e(e){return this._monthsParseExact?(d(this,"_monthsRegex")||Se.call(this),e?this._monthsStrictRegex:this._monthsRegex):(d(this,"_monthsRegex")||(this._monthsRegex=Os),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)}function Se(){function e(e,t){return t.length-e.length}var t,n,r=[],s=[],i=[];for(t=0;t<12;t++)n=f([2e3,t]),r.push(this.monthsShort(n,"")),s.push(this.months(n,"")),i.push(this.months(n,"")),i.push(this.monthsShort(n,""));for(r.sort(e),s.sort(e),i.sort(e),t=0;t<12;t++)r[t]=Q(r[t]),s[t]=Q(s[t]);for(t=0;t<24;t++)i[t]=Q(i[t]);this._monthsRegex=new RegExp("^("+i.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+s.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+r.join("|")+")","i")}function ke(e,t,n,r,s,i,a){var o=new Date(e,t,n,r,s,i,a);return e<100&&e>=0&&isFinite(o.getFullYear())&&o.setFullYear(e),o}function we(e){var t=new Date(Date.UTC.apply(null,arguments));return e<100&&e>=0&&isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e),t}function Me(e,t,n){var r=7+t-n,s=(7+we(e,0,r).getUTCDay()-t)%7;return-s+r-1}function De(e,t,n,r,s){var i,a,o=(7+n-r)%7,u=Me(e,r,s),l=1+7*(t-1)+o+u;return l<=0?(i=e-1,a=te(i)+l):l>te(e)?(i=e+1,a=l-te(e)):(i=e,a=l),{year:i,dayOfYear:a}}function Ye(e,t,n){var r,s,i=Me(e.year(),t,n),a=Math.floor((e.dayOfYear()-i-1)/7)+1;return a<1?(s=e.year()-1,r=a+be(s,t,n)):a>be(e.year(),t,n)?(r=a-be(e.year(),t,n),s=e.year()+1):(s=e.year(),r=a),{week:r,year:s}}function be(e,t,n){var r=Me(e,t,n),s=Me(e+1,t,n);return(te(e)-r+s)/7}function Oe(e){return Ye(e,this._week.dow,this._week.doy).week}function xe(){return this._week.dow}function Ne(){return this._week.doy}function Te(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")}function Pe(e){var t=Ye(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")}function Re(e,t){return"string"!=typeof e?e:isNaN(e)?(e=t.weekdaysParse(e),"number"==typeof e?e:null):parseInt(e,10)}function $e(e,t){return"string"==typeof e?t.weekdaysParse(e)%7||7:isNaN(e)?null:e}function Ce(e,t){return e?s(this._weekdays)?this._weekdays[e.day()]:this._weekdays[this._weekdays.isFormat.test(t)?"format":"standalone"][e.day()]:s(this._weekdays)?this._weekdays:this._weekdays.standalone}function Ve(e){return e?this._weekdaysShort[e.day()]:this._weekdaysShort}function Ee(e){return e?this._weekdaysMin[e.day()]:this._weekdaysMin}function Ae(e,t,n){var r,s,i,a=e.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],r=0;r<7;++r)i=f([2e3,1]).day(r),this._minWeekdaysParse[r]=this.weekdaysMin(i,"").toLocaleLowerCase(),this._shortWeekdaysParse[r]=this.weekdaysShort(i,"").toLocaleLowerCase(),this._weekdaysParse[r]=this.weekdays(i,"").toLocaleLowerCase();return n?"dddd"===t?(s=ks.call(this._weekdaysParse,a),s!==-1?s:null):"ddd"===t?(s=ks.call(this._shortWeekdaysParse,a),s!==-1?s:null):(s=ks.call(this._minWeekdaysParse,a),s!==-1?s:null):"dddd"===t?(s=ks.call(this._weekdaysParse,a),s!==-1?s:(s=ks.call(this._shortWeekdaysParse,a),s!==-1?s:(s=ks.call(this._minWeekdaysParse,a),s!==-1?s:null))):"ddd"===t?(s=ks.call(this._shortWeekdaysParse,a),s!==-1?s:(s=ks.call(this._weekdaysParse,a),s!==-1?s:(s=ks.call(this._minWeekdaysParse,a),s!==-1?s:null))):(s=ks.call(this._minWeekdaysParse,a),s!==-1?s:(s=ks.call(this._weekdaysParse,a),s!==-1?s:(s=ks.call(this._shortWeekdaysParse,a),s!==-1?s:null)))}function Fe(e,t,n){var r,s,i;if(this._weekdaysParseExact)return Ae.call(this,e,t,n);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),r=0;r<7;r++){if(s=f([2e3,1]).day(r),n&&!this._fullWeekdaysParse[r]&&(this._fullWeekdaysParse[r]=new RegExp("^"+this.weekdays(s,"").replace(".",".?")+"$","i"),this._shortWeekdaysParse[r]=new RegExp("^"+this.weekdaysShort(s,"").replace(".",".?")+"$","i"),this._minWeekdaysParse[r]=new RegExp("^"+this.weekdaysMin(s,"").replace(".",".?")+"$","i")),this._weekdaysParse[r]||(i="^"+this.weekdays(s,"")+"|^"+this.weekdaysShort(s,"")+"|^"+this.weekdaysMin(s,""),this._weekdaysParse[r]=new RegExp(i.replace(".",""),"i")),n&&"dddd"===t&&this._fullWeekdaysParse[r].test(e))return r;if(n&&"ddd"===t&&this._shortWeekdaysParse[r].test(e))return r;if(n&&"dd"===t&&this._minWeekdaysParse[r].test(e))return r;if(!n&&this._weekdaysParse[r].test(e))return r}}function We(e){if(!this.isValid())return null!=e?this:NaN;var t=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=e?(e=Re(e,this.localeData()),this.add(e-t,"d")):t}function Ue(e){if(!this.isValid())return null!=e?this:NaN;var t=(this.day()+7-this.localeData()._week.dow)%7;return null==e?t:this.add(e-t,"d")}function Le(e){if(!this.isValid())return null!=e?this:NaN;if(null!=e){var t=$e(e,this.localeData());return this.day(this.day()%7?t:t-7)}return this.day()||7}function He(e){return this._weekdaysParseExact?(d(this,"_weekdaysRegex")||Ge.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(d(this,"_weekdaysRegex")||(this._weekdaysRegex=Rs),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)}function je(e){return this._weekdaysParseExact?(d(this,"_weekdaysRegex")||Ge.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(d(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=$s),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)}function Ie(e){return this._weekdaysParseExact?(d(this,"_weekdaysRegex")||Ge.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(d(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=Cs),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)}function Ge(){function e(e,t){return t.length-e.length}var t,n,r,s,i,a=[],o=[],u=[],l=[];for(t=0;t<7;t++)n=f([2e3,1]).day(t),r=this.weekdaysMin(n,""),s=this.weekdaysShort(n,""),i=this.weekdays(n,""),a.push(r),o.push(s),u.push(i),l.push(r),l.push(s),l.push(i);for(a.sort(e),o.sort(e),u.sort(e),l.sort(e),t=0;t<7;t++)o[t]=Q(o[t]),u[t]=Q(u[t]),l[t]=Q(l[t]);this._weekdaysRegex=new RegExp("^("+l.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+u.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+a.join("|")+")","i")}function Ze(){return this.hours()%12||12}function ze(){return this.hours()||24}function Be(e,t){j(e,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)})}function qe(e,t){return t._meridiemParse}function Je(e){return"p"===(e+"").toLowerCase().charAt(0)}function Qe(e,t,n){return e>11?n?"pm":"PM":n?"am":"AM"}function Xe(e){return e?e.toLowerCase().replace("_","-"):e}function Ke(e){for(var t,n,r,s,i=0;i<e.length;){for(s=Xe(e[i]).split("-"),t=s.length,n=Xe(e[i+1]),n=n?n.split("-"):null;t>0;){if(r=et(s.slice(0,t).join("-")))return r;if(n&&n.length>=t&&M(s,n,!0)>=t-1)break;t--}i++}return Vs}function et(n){var r=null;if(!Ws[n]&&"undefined"!=typeof t&&t&&t.exports)try{r=Vs._abbr;var s=e;s("./locale/"+n),tt(r)}catch(i){}return Ws[n]}function tt(e,t){var n;return e&&(n=o(t)?st(e):nt(e,t),n?Vs=n:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+e+" not found. Did you forget to load it?")),Vs._abbr}function nt(e,t){if(null!==t){var n,r=Fs;if(t.abbr=e,null!=Ws[e])b("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),r=Ws[e]._config;else if(null!=t.parentLocale)if(null!=Ws[t.parentLocale])r=Ws[t.parentLocale]._config;else{if(n=et(t.parentLocale),null==n)return Us[t.parentLocale]||(Us[t.parentLocale]=[]),Us[t.parentLocale].push({name:e,config:t}),null;r=n._config}return Ws[e]=new T(N(r,t)),Us[e]&&Us[e].forEach(function(e){nt(e.name,e.config)}),tt(e),Ws[e]}return delete Ws[e],null}function rt(e,t){if(null!=t){var n,r,s=Fs;r=et(e),null!=r&&(s=r._config),t=N(s,t),n=new T(t),n.parentLocale=Ws[e],Ws[e]=n,tt(e)}else null!=Ws[e]&&(null!=Ws[e].parentLocale?Ws[e]=Ws[e].parentLocale:null!=Ws[e]&&delete Ws[e]);return Ws[e]}function st(e){var t;if(e&&e._locale&&e._locale._abbr&&(e=e._locale._abbr),!e)return Vs;if(!s(e)){if(t=et(e))return t;e=[e]}return Ke(e)}function it(){return Cr(Ws)}function at(e){var t,n=e._a;return n&&p(e).overflow===-2&&(t=n[fs]<0||n[fs]>11?fs:n[ms]<1||n[ms]>ce(n[hs],n[fs])?ms:n[ps]<0||n[ps]>24||24===n[ps]&&(0!==n[vs]||0!==n[gs]||0!==n[ys])?ps:n[vs]<0||n[vs]>59?vs:n[gs]<0||n[gs]>59?gs:n[ys]<0||n[ys]>999?ys:-1,p(e)._overflowDayOfYear&&(t<hs||t>ms)&&(t=ms),p(e)._overflowWeeks&&t===-1&&(t=_s),p(e)._overflowWeekday&&t===-1&&(t=Ss),p(e).overflow=t),e}function ot(e,t,n){return null!=e?e:null!=t?t:n}function ut(e){var t=new Date(n.now());return e._useUTC?[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()]:[t.getFullYear(),t.getMonth(),t.getDate()]}function lt(e){var t,n,r,s,i,a=[];if(!e._d){for(r=ut(e),e._w&&null==e._a[ms]&&null==e._a[fs]&&ct(e),null!=e._dayOfYear&&(i=ot(e._a[hs],r[hs]),(e._dayOfYear>te(i)||0===e._dayOfYear)&&(p(e)._overflowDayOfYear=!0),n=we(i,0,e._dayOfYear),e._a[fs]=n.getUTCMonth(),e._a[ms]=n.getUTCDate()),t=0;t<3&&null==e._a[t];++t)e._a[t]=a[t]=r[t];for(;t<7;t++)e._a[t]=a[t]=null==e._a[t]?2===t?1:0:e._a[t];24===e._a[ps]&&0===e._a[vs]&&0===e._a[gs]&&0===e._a[ys]&&(e._nextDay=!0,e._a[ps]=0),e._d=(e._useUTC?we:ke).apply(null,a),s=e._useUTC?e._d.getUTCDay():e._d.getDay(),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[ps]=24),e._w&&"undefined"!=typeof e._w.d&&e._w.d!==s&&(p(e).weekdayMismatch=!0)}}function ct(e){var t,n,r,s,i,a,o,u;if(t=e._w,null!=t.GG||null!=t.W||null!=t.E)i=1,a=4,n=ot(t.GG,e._a[hs],Ye(Ot(),1,4).year),r=ot(t.W,1),s=ot(t.E,1),(s<1||s>7)&&(u=!0);else{i=e._locale._week.dow,a=e._locale._week.doy;var l=Ye(Ot(),i,a);n=ot(t.gg,e._a[hs],l.year),r=ot(t.w,l.week),null!=t.d?(s=t.d,(s<0||s>6)&&(u=!0)):null!=t.e?(s=t.e+i,(t.e<0||t.e>6)&&(u=!0)):s=i}r<1||r>be(n,i,a)?p(e)._overflowWeeks=!0:null!=u?p(e)._overflowWeekday=!0:(o=De(n,r,s,i,a),e._a[hs]=o.year,e._dayOfYear=o.dayOfYear)}function dt(e){var t,n,r,s,i,a,o=e._i,u=Ls.exec(o)||Hs.exec(o);if(u){for(p(e).iso=!0,t=0,n=Is.length;t<n;t++)if(Is[t][1].exec(u[1])){s=Is[t][0],r=Is[t][2]!==!1;break}if(null==s)return void(e._isValid=!1);if(u[3]){for(t=0,n=Gs.length;t<n;t++)if(Gs[t][1].exec(u[3])){i=(u[2]||" ")+Gs[t][0];break}if(null==i)return void(e._isValid=!1)}if(!r&&null!=i)return void(e._isValid=!1);if(u[4]){if(!js.exec(u[4]))return void(e._isValid=!1);a="Z"}e._f=s+(i||"")+(a||""),_t(e)}else e._isValid=!1}function ht(e,t,n,r,s,i){var a=[ft(e),Ys.indexOf(t),parseInt(n,10),parseInt(r,10),parseInt(s,10)];return i&&a.push(parseInt(i,10)),a}function ft(e){var t=parseInt(e,10);return t<=49?2e3+t:t<=999?1900+t:t}function mt(e){return e.replace(/\([^)]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}function pt(e,t,n){if(e){var r=Ts.indexOf(e),s=new Date(t[0],t[1],t[2]).getDay();if(r!==s)return p(n).weekdayMismatch=!0,n._isValid=!1,!1}return!0}function vt(e,t,n){if(e)return Bs[e];if(t)return 0;var r=parseInt(n,10),s=r%100,i=(r-s)/100;return 60*i+s}function gt(e){var t=zs.exec(mt(e._i));if(t){var n=ht(t[4],t[3],t[2],t[5],t[6],t[7]);if(!pt(t[1],n,e))return;e._a=n,e._tzm=vt(t[8],t[9],t[10]),e._d=we.apply(null,e._a),e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),p(e).rfc2822=!0}else e._isValid=!1}function yt(e){var t=Zs.exec(e._i);return null!==t?void(e._d=new Date((+t[1]))):(dt(e),void(e._isValid===!1&&(delete e._isValid,gt(e),e._isValid===!1&&(delete e._isValid,n.createFromInputFallback(e)))))}function _t(e){if(e._f===n.ISO_8601)return void dt(e);if(e._f===n.RFC_2822)return void gt(e);e._a=[],p(e).empty=!0;var t,r,s,i,a,o=""+e._i,u=o.length,l=0;for(s=z(e._f,e._locale).match(jr)||[],t=0;t<s.length;t++)i=s[t],r=(o.match(q(i,e))||[])[0],r&&(a=o.substr(0,o.indexOf(r)),a.length>0&&p(e).unusedInput.push(a),o=o.slice(o.indexOf(r)+r.length),l+=r.length),Zr[i]?(r?p(e).empty=!1:p(e).unusedTokens.push(i),ee(i,r,e)):e._strict&&!r&&p(e).unusedTokens.push(i);p(e).charsLeftOver=u-l,o.length>0&&p(e).unusedInput.push(o),e._a[ps]<=12&&p(e).bigHour===!0&&e._a[ps]>0&&(p(e).bigHour=void 0),p(e).parsedDateParts=e._a.slice(0),
p(e).meridiem=e._meridiem,e._a[ps]=St(e._locale,e._a[ps],e._meridiem),lt(e),at(e)}function St(e,t,n){var r;return null==n?t:null!=e.meridiemHour?e.meridiemHour(t,n):null!=e.isPM?(r=e.isPM(n),r&&t<12&&(t+=12),r||12!==t||(t=0),t):t}function kt(e){var t,n,r,s,i;if(0===e._f.length)return p(e).invalidFormat=!0,void(e._d=new Date(NaN));for(s=0;s<e._f.length;s++)i=0,t=y({},e),null!=e._useUTC&&(t._useUTC=e._useUTC),t._f=e._f[s],_t(t),v(t)&&(i+=p(t).charsLeftOver,i+=10*p(t).unusedTokens.length,p(t).score=i,(null==r||i<r)&&(r=i,n=t));h(e,n||t)}function wt(e){if(!e._d){var t=W(e._i);e._a=c([t.year,t.month,t.day||t.date,t.hour,t.minute,t.second,t.millisecond],function(e){return e&&parseInt(e,10)}),lt(e)}}function Mt(e){var t=new _(at(Dt(e)));return t._nextDay&&(t.add(1,"d"),t._nextDay=void 0),t}function Dt(e){var t=e._i,n=e._f;return e._locale=e._locale||st(e._l),null===t||void 0===n&&""===t?g({nullInput:!0}):("string"==typeof t&&(e._i=t=e._locale.preparse(t)),S(t)?new _(at(t)):(l(t)?e._d=t:s(n)?kt(e):n?_t(e):Yt(e),v(e)||(e._d=null),e))}function Yt(e){var t=e._i;o(t)?e._d=new Date(n.now()):l(t)?e._d=new Date(t.valueOf()):"string"==typeof t?yt(e):s(t)?(e._a=c(t.slice(0),function(e){return parseInt(e,10)}),lt(e)):i(t)?wt(e):u(t)?e._d=new Date(t):n.createFromInputFallback(e)}function bt(e,t,n,r,o){var u={};return n!==!0&&n!==!1||(r=n,n=void 0),(i(e)&&a(e)||s(e)&&0===e.length)&&(e=void 0),u._isAMomentObject=!0,u._useUTC=u._isUTC=o,u._l=n,u._i=e,u._f=t,u._strict=r,Mt(u)}function Ot(e,t,n,r){return bt(e,t,n,r,!1)}function xt(e,t){var n,r;if(1===t.length&&s(t[0])&&(t=t[0]),!t.length)return Ot();for(n=t[0],r=1;r<t.length;++r)t[r].isValid()&&!t[r][e](n)||(n=t[r]);return n}function Nt(){var e=[].slice.call(arguments,0);return xt("isBefore",e)}function Tt(){var e=[].slice.call(arguments,0);return xt("isAfter",e)}function Pt(e){for(var t in e)if(ks.call(Xs,t)===-1||null!=e[t]&&isNaN(e[t]))return!1;for(var n=!1,r=0;r<Xs.length;++r)if(e[Xs[r]]){if(n)return!1;parseFloat(e[Xs[r]])!==w(e[Xs[r]])&&(n=!0)}return!0}function Rt(){return this._isValid}function $t(){return Xt(NaN)}function Ct(e){var t=W(e),n=t.year||0,r=t.quarter||0,s=t.month||0,i=t.week||0,a=t.day||0,o=t.hour||0,u=t.minute||0,l=t.second||0,c=t.millisecond||0;this._isValid=Pt(t),this._milliseconds=+c+1e3*l+6e4*u+1e3*o*60*60,this._days=+a+7*i,this._months=+s+3*r+12*n,this._data={},this._locale=st(),this._bubble()}function Vt(e){return e instanceof Ct}function Et(e){return e<0?Math.round(-1*e)*-1:Math.round(e)}function At(e,t){j(e,0,0,function(){var e=this.utcOffset(),n="+";return e<0&&(e=-e,n="-"),n+H(~~(e/60),2)+t+H(~~e%60,2)})}function Ft(e,t){var n=(t||"").match(e);if(null===n)return null;var r=n[n.length-1]||[],s=(r+"").match(Ks)||["-",0,0],i=+(60*s[1])+w(s[2]);return 0===i?0:"+"===s[0]?i:-i}function Wt(e,t){var r,s;return t._isUTC?(r=t.clone(),s=(S(e)||l(e)?e.valueOf():Ot(e).valueOf())-r.valueOf(),r._d.setTime(r._d.valueOf()+s),n.updateOffset(r,!1),r):Ot(e).local()}function Ut(e){return 15*-Math.round(e._d.getTimezoneOffset()/15)}function Lt(e,t,r){var s,i=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null!=e){if("string"==typeof e){if(e=Ft(os,e),null===e)return this}else Math.abs(e)<16&&!r&&(e=60*e);return!this._isUTC&&t&&(s=Ut(this)),this._offset=e,this._isUTC=!0,null!=s&&this.add(s,"m"),i!==e&&(!t||this._changeInProgress?rn(this,Xt(e-i,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,n.updateOffset(this,!0),this._changeInProgress=null)),this}return this._isUTC?i:Ut(this)}function Ht(e,t){return null!=e?("string"!=typeof e&&(e=-e),this.utcOffset(e,t),this):-this.utcOffset()}function jt(e){return this.utcOffset(0,e)}function It(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e&&this.subtract(Ut(this),"m")),this}function Gt(){if(null!=this._tzm)this.utcOffset(this._tzm,!1,!0);else if("string"==typeof this._i){var e=Ft(as,this._i);null!=e?this.utcOffset(e):this.utcOffset(0,!0)}return this}function Zt(e){return!!this.isValid()&&(e=e?Ot(e).utcOffset():0,(this.utcOffset()-e)%60===0)}function zt(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()}function Bt(){if(!o(this._isDSTShifted))return this._isDSTShifted;var e={};if(y(e,this),e=Dt(e),e._a){var t=e._isUTC?f(e._a):Ot(e._a);this._isDSTShifted=this.isValid()&&M(e._a,t.toArray())>0}else this._isDSTShifted=!1;return this._isDSTShifted}function qt(){return!!this.isValid()&&!this._isUTC}function Jt(){return!!this.isValid()&&this._isUTC}function Qt(){return!!this.isValid()&&(this._isUTC&&0===this._offset)}function Xt(e,t){var n,r,s,i=e,a=null;return Vt(e)?i={ms:e._milliseconds,d:e._days,M:e._months}:u(e)?(i={},t?i[t]=e:i.milliseconds=e):(a=ei.exec(e))?(n="-"===a[1]?-1:1,i={y:0,d:w(a[ms])*n,h:w(a[ps])*n,m:w(a[vs])*n,s:w(a[gs])*n,ms:w(Et(1e3*a[ys]))*n}):(a=ti.exec(e))?(n="-"===a[1]?-1:("+"===a[1],1),i={y:Kt(a[2],n),M:Kt(a[3],n),w:Kt(a[4],n),d:Kt(a[5],n),h:Kt(a[6],n),m:Kt(a[7],n),s:Kt(a[8],n)}):null==i?i={}:"object"==typeof i&&("from"in i||"to"in i)&&(s=tn(Ot(i.from),Ot(i.to)),i={},i.ms=s.milliseconds,i.M=s.months),r=new Ct(i),Vt(e)&&d(e,"_locale")&&(r._locale=e._locale),r}function Kt(e,t){var n=e&&parseFloat(e.replace(",","."));return(isNaN(n)?0:n)*t}function en(e,t){var n={milliseconds:0,months:0};return n.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(n.months,"M").isAfter(t)&&--n.months,n.milliseconds=+t-+e.clone().add(n.months,"M"),n}function tn(e,t){var n;return e.isValid()&&t.isValid()?(t=Wt(t,e),e.isBefore(t)?n=en(e,t):(n=en(t,e),n.milliseconds=-n.milliseconds,n.months=-n.months),n):{milliseconds:0,months:0}}function nn(e,t){return function(n,r){var s,i;return null===r||isNaN(+r)||(b(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),i=n,n=r,r=i),n="string"==typeof n?+n:n,s=Xt(n,r),rn(this,s,e),this}}function rn(e,t,r,s){var i=t._milliseconds,a=Et(t._days),o=Et(t._months);e.isValid()&&(s=null==s||s,o&&pe(e,ie(e,"Month")+o*r),a&&ae(e,"Date",ie(e,"Date")+a*r),i&&e._d.setTime(e._d.valueOf()+i*r),s&&n.updateOffset(e,a||o))}function sn(e,t){var n=e.diff(t,"days",!0);return n<-6?"sameElse":n<-1?"lastWeek":n<0?"lastDay":n<1?"sameDay":n<2?"nextDay":n<7?"nextWeek":"sameElse"}function an(e,t){var r=e||Ot(),s=Wt(r,this).startOf("day"),i=n.calendarFormat(this,s)||"sameElse",a=t&&(O(t[i])?t[i].call(this,r):t[i]);return this.format(a||this.localeData().calendar(i,this,Ot(r)))}function on(){return new _(this)}function un(e,t){var n=S(e)?e:Ot(e);return!(!this.isValid()||!n.isValid())&&(t=F(o(t)?"millisecond":t),"millisecond"===t?this.valueOf()>n.valueOf():n.valueOf()<this.clone().startOf(t).valueOf())}function ln(e,t){var n=S(e)?e:Ot(e);return!(!this.isValid()||!n.isValid())&&(t=F(o(t)?"millisecond":t),"millisecond"===t?this.valueOf()<n.valueOf():this.clone().endOf(t).valueOf()<n.valueOf())}function cn(e,t,n,r){return r=r||"()",("("===r[0]?this.isAfter(e,n):!this.isBefore(e,n))&&(")"===r[1]?this.isBefore(t,n):!this.isAfter(t,n))}function dn(e,t){var n,r=S(e)?e:Ot(e);return!(!this.isValid()||!r.isValid())&&(t=F(t||"millisecond"),"millisecond"===t?this.valueOf()===r.valueOf():(n=r.valueOf(),this.clone().startOf(t).valueOf()<=n&&n<=this.clone().endOf(t).valueOf()))}function hn(e,t){return this.isSame(e,t)||this.isAfter(e,t)}function fn(e,t){return this.isSame(e,t)||this.isBefore(e,t)}function mn(e,t,n){var r,s,i;if(!this.isValid())return NaN;if(r=Wt(e,this),!r.isValid())return NaN;switch(s=6e4*(r.utcOffset()-this.utcOffset()),t=F(t)){case"year":i=pn(this,r)/12;break;case"month":i=pn(this,r);break;case"quarter":i=pn(this,r)/3;break;case"second":i=(this-r)/1e3;break;case"minute":i=(this-r)/6e4;break;case"hour":i=(this-r)/36e5;break;case"day":i=(this-r-s)/864e5;break;case"week":i=(this-r-s)/6048e5;break;default:i=this-r}return n?i:k(i)}function pn(e,t){var n,r,s=12*(t.year()-e.year())+(t.month()-e.month()),i=e.clone().add(s,"months");return t-i<0?(n=e.clone().add(s-1,"months"),r=(t-i)/(i-n)):(n=e.clone().add(s+1,"months"),r=(t-i)/(n-i)),-(s+r)||0}function vn(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")}function gn(e){if(!this.isValid())return null;var t=e!==!0,n=t?this.clone().utc():this;return n.year()<0||n.year()>9999?Z(n,t?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):O(Date.prototype.toISOString)?t?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",Z(n,"Z")):Z(n,t?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")}function yn(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var e="moment",t="";this.isLocal()||(e=0===this.utcOffset()?"moment.utc":"moment.parseZone",t="Z");var n="["+e+'("]',r=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",s="-MM-DD[T]HH:mm:ss.SSS",i=t+'[")]';return this.format(n+r+s+i)}function _n(e){e||(e=this.isUtc()?n.defaultFormatUtc:n.defaultFormat);var t=Z(this,e);return this.localeData().postformat(t)}function Sn(e,t){return this.isValid()&&(S(e)&&e.isValid()||Ot(e).isValid())?Xt({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()}function kn(e){return this.from(Ot(),e)}function wn(e,t){return this.isValid()&&(S(e)&&e.isValid()||Ot(e).isValid())?Xt({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()}function Mn(e){return this.to(Ot(),e)}function Dn(e){var t;return void 0===e?this._locale._abbr:(t=st(e),null!=t&&(this._locale=t),this)}function Yn(){return this._locale}function bn(e){switch(e=F(e)){case"year":this.month(0);case"quarter":case"month":this.date(1);case"week":case"isoWeek":case"day":case"date":this.hours(0);case"hour":this.minutes(0);case"minute":this.seconds(0);case"second":this.milliseconds(0)}return"week"===e&&this.weekday(0),"isoWeek"===e&&this.isoWeekday(1),"quarter"===e&&this.month(3*Math.floor(this.month()/3)),this}function On(e){return e=F(e),void 0===e||"millisecond"===e?this:("date"===e&&(e="day"),this.startOf(e).add(1,"isoWeek"===e?"week":e).subtract(1,"ms"))}function xn(){return this._d.valueOf()-6e4*(this._offset||0)}function Nn(){return Math.floor(this.valueOf()/1e3)}function Tn(){return new Date(this.valueOf())}function Pn(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]}function Rn(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}}function $n(){return this.isValid()?this.toISOString():null}function Cn(){return v(this)}function Vn(){return h({},p(this))}function En(){return p(this).overflow}function An(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}}function Fn(e,t){j(0,[e,e.length],0,t)}function Wn(e){return jn.call(this,e,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)}function Un(e){return jn.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)}function Ln(){return be(this.year(),1,4)}function Hn(){var e=this.localeData()._week;return be(this.year(),e.dow,e.doy)}function jn(e,t,n,r,s){var i;return null==e?Ye(this,r,s).year:(i=be(e,r,s),t>i&&(t=i),In.call(this,e,t,n,r,s))}function In(e,t,n,r,s){var i=De(e,t,n,r,s),a=we(i.year,0,i.dayOfYear);return this.year(a.getUTCFullYear()),this.month(a.getUTCMonth()),this.date(a.getUTCDate()),this}function Gn(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)}function Zn(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")}function zn(e,t){t[ys]=w(1e3*("0."+e))}function Bn(){return this._isUTC?"UTC":""}function qn(){return this._isUTC?"Coordinated Universal Time":""}function Jn(e){return Ot(1e3*e)}function Qn(){return Ot.apply(null,arguments).parseZone()}function Xn(e){return e}function Kn(e,t,n,r){var s=st(),i=f().set(r,t);return s[n](i,e)}function er(e,t,n){if(u(e)&&(t=e,e=void 0),e=e||"",null!=t)return Kn(e,t,n,"month");var r,s=[];for(r=0;r<12;r++)s[r]=Kn(e,r,n,"month");return s}function tr(e,t,n,r){"boolean"==typeof e?(u(t)&&(n=t,t=void 0),t=t||""):(t=e,n=t,e=!1,u(t)&&(n=t,t=void 0),t=t||"");var s=st(),i=e?s._week.dow:0;if(null!=n)return Kn(t,(n+i)%7,r,"day");var a,o=[];for(a=0;a<7;a++)o[a]=Kn(t,(a+i)%7,r,"day");return o}function nr(e,t){return er(e,t,"months")}function rr(e,t){return er(e,t,"monthsShort")}function sr(e,t,n){return tr(e,t,n,"weekdays")}function ir(e,t,n){return tr(e,t,n,"weekdaysShort")}function ar(e,t,n){return tr(e,t,n,"weekdaysMin")}function or(){var e=this._data;return this._milliseconds=hi(this._milliseconds),this._days=hi(this._days),this._months=hi(this._months),e.milliseconds=hi(e.milliseconds),e.seconds=hi(e.seconds),e.minutes=hi(e.minutes),e.hours=hi(e.hours),e.months=hi(e.months),e.years=hi(e.years),this}function ur(e,t,n,r){var s=Xt(t,n);return e._milliseconds+=r*s._milliseconds,e._days+=r*s._days,e._months+=r*s._months,e._bubble()}function lr(e,t){return ur(this,e,t,1)}function cr(e,t){return ur(this,e,t,-1)}function dr(e){return e<0?Math.floor(e):Math.ceil(e)}function hr(){var e,t,n,r,s,i=this._milliseconds,a=this._days,o=this._months,u=this._data;return i>=0&&a>=0&&o>=0||i<=0&&a<=0&&o<=0||(i+=864e5*dr(mr(o)+a),a=0,o=0),u.milliseconds=i%1e3,e=k(i/1e3),u.seconds=e%60,t=k(e/60),u.minutes=t%60,n=k(t/60),u.hours=n%24,a+=k(n/24),s=k(fr(a)),o+=s,a-=dr(mr(s)),r=k(o/12),o%=12,u.days=a,u.months=o,u.years=r,this}function fr(e){return 4800*e/146097}function mr(e){return 146097*e/4800}function pr(e){if(!this.isValid())return NaN;var t,n,r=this._milliseconds;if(e=F(e),"month"===e||"year"===e)return t=this._days+r/864e5,n=this._months+fr(t),"month"===e?n:n/12;switch(t=this._days+Math.round(mr(this._months)),e){case"week":return t/7+r/6048e5;case"day":return t+r/864e5;case"hour":return 24*t+r/36e5;case"minute":return 1440*t+r/6e4;case"second":return 86400*t+r/1e3;case"millisecond":return Math.floor(864e5*t)+r;default:throw new Error("Unknown unit "+e)}}function vr(){return this.isValid()?this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*w(this._months/12):NaN}function gr(e){return function(){return this.as(e)}}function yr(){return Xt(this)}function _r(e){return e=F(e),this.isValid()?this[e+"s"]():NaN}function Sr(e){return function(){return this.isValid()?this._data[e]:NaN}}function kr(){return k(this.days()/7)}function wr(e,t,n,r,s){return s.relativeTime(t||1,!!n,e,r)}function Mr(e,t,n){var r=Xt(e).abs(),s=xi(r.as("s")),i=xi(r.as("m")),a=xi(r.as("h")),o=xi(r.as("d")),u=xi(r.as("M")),l=xi(r.as("y")),c=s<=Ni.ss&&["s",s]||s<Ni.s&&["ss",s]||i<=1&&["m"]||i<Ni.m&&["mm",i]||a<=1&&["h"]||a<Ni.h&&["hh",a]||o<=1&&["d"]||o<Ni.d&&["dd",o]||u<=1&&["M"]||u<Ni.M&&["MM",u]||l<=1&&["y"]||["yy",l];return c[2]=t,c[3]=+e>0,c[4]=n,wr.apply(null,c)}function Dr(e){return void 0===e?xi:"function"==typeof e&&(xi=e,!0)}function Yr(e,t){return void 0!==Ni[e]&&(void 0===t?Ni[e]:(Ni[e]=t,"s"===e&&(Ni.ss=t-1),!0))}function br(e){if(!this.isValid())return this.localeData().invalidDate();var t=this.localeData(),n=Mr(this,!e,t);return e&&(n=t.pastFuture(+this,n)),t.postformat(n)}function Or(e){return(e>0)-(e<0)||+e}function xr(){if(!this.isValid())return this.localeData().invalidDate();var e,t,n,r=Ti(this._milliseconds)/1e3,s=Ti(this._days),i=Ti(this._months);e=k(r/60),t=k(e/60),r%=60,e%=60,n=k(i/12),i%=12;var a=n,o=i,u=s,l=t,c=e,d=r?r.toFixed(3).replace(/\.?0+$/,""):"",h=this.asSeconds();if(!h)return"P0D";var f=h<0?"-":"",m=Or(this._months)!==Or(h)?"-":"",p=Or(this._days)!==Or(h)?"-":"",v=Or(this._milliseconds)!==Or(h)?"-":"";return f+"P"+(a?m+a+"Y":"")+(o?m+o+"M":"")+(u?p+u+"D":"")+(l||c||d?"T":"")+(l?v+l+"H":"")+(c?v+c+"M":"")+(d?v+d+"S":"")}var Nr,Tr;Tr=Array.prototype.some?Array.prototype.some:function(e){for(var t=Object(this),n=t.length>>>0,r=0;r<n;r++)if(r in t&&e.call(this,t[r],r,t))return!0;return!1};var Pr=n.momentProperties=[],Rr=!1,$r={};n.suppressDeprecationWarnings=!1,n.deprecationHandler=null;var Cr;Cr=Object.keys?Object.keys:function(e){var t,n=[];for(t in e)d(e,t)&&n.push(t);return n};var Vr={sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},Er={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},Ar="Invalid date",Fr="%d",Wr=/\d{1,2}/,Ur={future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},Lr={},Hr={},jr=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,Ir=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,Gr={},Zr={},zr=/\d/,Br=/\d\d/,qr=/\d{3}/,Jr=/\d{4}/,Qr=/[+-]?\d{6}/,Xr=/\d\d?/,Kr=/\d\d\d\d?/,es=/\d\d\d\d\d\d?/,ts=/\d{1,3}/,ns=/\d{1,4}/,rs=/[+-]?\d{1,6}/,ss=/\d+/,is=/[+-]?\d+/,as=/Z|[+-]\d\d:?\d\d/gi,os=/Z|[+-]\d\d(?::?\d\d)?/gi,us=/[+-]?\d+(\.\d{1,3})?/,ls=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,cs={},ds={},hs=0,fs=1,ms=2,ps=3,vs=4,gs=5,ys=6,_s=7,Ss=8;j("Y",0,0,function(){var e=this.year();return e<=9999?""+e:"+"+e}),j(0,["YY",2],0,function(){return this.year()%100}),j(0,["YYYY",4],0,"year"),j(0,["YYYYY",5],0,"year"),j(0,["YYYYYY",6,!0],0,"year"),A("year","y"),U("year",1),B("Y",is),B("YY",Xr,Br),B("YYYY",ns,Jr),B("YYYYY",rs,Qr),B("YYYYYY",rs,Qr),X(["YYYYY","YYYYYY"],hs),X("YYYY",function(e,t){t[hs]=2===e.length?n.parseTwoDigitYear(e):w(e)}),X("YY",function(e,t){t[hs]=n.parseTwoDigitYear(e)}),X("Y",function(e,t){t[hs]=parseInt(e,10)}),n.parseTwoDigitYear=function(e){return w(e)+(w(e)>68?1900:2e3)};var ks,ws=se("FullYear",!0);ks=Array.prototype.indexOf?Array.prototype.indexOf:function(e){var t;for(t=0;t<this.length;++t)if(this[t]===e)return t;return-1},j("M",["MM",2],"Mo",function(){return this.month()+1}),j("MMM",0,0,function(e){return this.localeData().monthsShort(this,e)}),j("MMMM",0,0,function(e){return this.localeData().months(this,e)}),A("month","M"),U("month",8),B("M",Xr),B("MM",Xr,Br),B("MMM",function(e,t){return t.monthsShortRegex(e)}),B("MMMM",function(e,t){return t.monthsRegex(e)}),X(["M","MM"],function(e,t){t[fs]=w(e)-1}),X(["MMM","MMMM"],function(e,t,n,r){var s=n._locale.monthsParse(e,r,n._strict);null!=s?t[fs]=s:p(n).invalidMonth=e});var Ms=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Ds="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Ys="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),bs=ls,Os=ls;j("w",["ww",2],"wo","week"),j("W",["WW",2],"Wo","isoWeek"),A("week","w"),A("isoWeek","W"),U("week",5),U("isoWeek",5),B("w",Xr),B("ww",Xr,Br),B("W",Xr),B("WW",Xr,Br),K(["w","ww","W","WW"],function(e,t,n,r){t[r.substr(0,1)]=w(e)});var xs={dow:0,doy:6};j("d",0,"do","day"),j("dd",0,0,function(e){return this.localeData().weekdaysMin(this,e)}),j("ddd",0,0,function(e){return this.localeData().weekdaysShort(this,e)}),j("dddd",0,0,function(e){return this.localeData().weekdays(this,e)}),j("e",0,0,"weekday"),j("E",0,0,"isoWeekday"),A("day","d"),A("weekday","e"),A("isoWeekday","E"),U("day",11),U("weekday",11),U("isoWeekday",11),B("d",Xr),B("e",Xr),B("E",Xr),B("dd",function(e,t){return t.weekdaysMinRegex(e)}),B("ddd",function(e,t){return t.weekdaysShortRegex(e)}),B("dddd",function(e,t){return t.weekdaysRegex(e)}),K(["dd","ddd","dddd"],function(e,t,n,r){var s=n._locale.weekdaysParse(e,r,n._strict);null!=s?t.d=s:p(n).invalidWeekday=e}),K(["d","e","E"],function(e,t,n,r){t[r]=w(e)});var Ns="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Ts="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Ps="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),Rs=ls,$s=ls,Cs=ls;j("H",["HH",2],0,"hour"),j("h",["hh",2],0,Ze),j("k",["kk",2],0,ze),j("hmm",0,0,function(){return""+Ze.apply(this)+H(this.minutes(),2)}),j("hmmss",0,0,function(){return""+Ze.apply(this)+H(this.minutes(),2)+H(this.seconds(),2)}),j("Hmm",0,0,function(){return""+this.hours()+H(this.minutes(),2)}),j("Hmmss",0,0,function(){return""+this.hours()+H(this.minutes(),2)+H(this.seconds(),2)}),Be("a",!0),Be("A",!1),A("hour","h"),U("hour",13),B("a",qe),B("A",qe),B("H",Xr),B("h",Xr),B("k",Xr),B("HH",Xr,Br),B("hh",Xr,Br),B("kk",Xr,Br),B("hmm",Kr),B("hmmss",es),B("Hmm",Kr),B("Hmmss",es),X(["H","HH"],ps),X(["k","kk"],function(e,t,n){var r=w(e);t[ps]=24===r?0:r}),X(["a","A"],function(e,t,n){n._isPm=n._locale.isPM(e),n._meridiem=e}),X(["h","hh"],function(e,t,n){t[ps]=w(e),p(n).bigHour=!0}),X("hmm",function(e,t,n){var r=e.length-2;t[ps]=w(e.substr(0,r)),t[vs]=w(e.substr(r)),p(n).bigHour=!0}),X("hmmss",function(e,t,n){var r=e.length-4,s=e.length-2;t[ps]=w(e.substr(0,r)),t[vs]=w(e.substr(r,2)),t[gs]=w(e.substr(s)),p(n).bigHour=!0}),X("Hmm",function(e,t,n){var r=e.length-2;t[ps]=w(e.substr(0,r)),t[vs]=w(e.substr(r))}),X("Hmmss",function(e,t,n){var r=e.length-4,s=e.length-2;t[ps]=w(e.substr(0,r)),t[vs]=w(e.substr(r,2)),t[gs]=w(e.substr(s))});var Vs,Es=/[ap]\.?m?\.?/i,As=se("Hours",!0),Fs={calendar:Vr,longDateFormat:Er,invalidDate:Ar,ordinal:Fr,dayOfMonthOrdinalParse:Wr,relativeTime:Ur,months:Ds,monthsShort:Ys,week:xs,weekdays:Ns,weekdaysMin:Ps,weekdaysShort:Ts,meridiemParse:Es},Ws={},Us={},Ls=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,Hs=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,js=/Z|[+-]\d\d(?::?\d\d)?/,Is=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/]],Gs=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],Zs=/^\/?Date\((\-?\d+)/i,zs=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,Bs={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};n.createFromInputFallback=Y("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged and will be removed in an upcoming major release. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))}),n.ISO_8601=function(){},n.RFC_2822=function(){};var qs=Y("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=Ot.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:g()}),Js=Y("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=Ot.apply(null,arguments);return this.isValid()&&e.isValid()?e>this?this:e:g()}),Qs=function(){return Date.now?Date.now():+new Date},Xs=["year","quarter","month","week","day","hour","minute","second","millisecond"];At("Z",":"),At("ZZ",""),B("Z",os),B("ZZ",os),X(["Z","ZZ"],function(e,t,n){n._useUTC=!0,n._tzm=Ft(os,e)});var Ks=/([\+\-]|\d\d)/gi;n.updateOffset=function(){};var ei=/^(\-|\+)?(?:(\d*)[. ])?(\d+)\:(\d+)(?:\:(\d+)(\.\d*)?)?$/,ti=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;Xt.fn=Ct.prototype,Xt.invalid=$t;var ni=nn(1,"add"),ri=nn(-1,"subtract");n.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",n.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";var si=Y("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(e){return void 0===e?this.localeData():this.locale(e)});j(0,["gg",2],0,function(){return this.weekYear()%100}),j(0,["GG",2],0,function(){return this.isoWeekYear()%100}),Fn("gggg","weekYear"),Fn("ggggg","weekYear"),Fn("GGGG","isoWeekYear"),Fn("GGGGG","isoWeekYear"),A("weekYear","gg"),A("isoWeekYear","GG"),U("weekYear",1),U("isoWeekYear",1),B("G",is),B("g",is),B("GG",Xr,Br),B("gg",Xr,Br),B("GGGG",ns,Jr),B("gggg",ns,Jr),B("GGGGG",rs,Qr),B("ggggg",rs,Qr),K(["gggg","ggggg","GGGG","GGGGG"],function(e,t,n,r){t[r.substr(0,2)]=w(e)}),K(["gg","GG"],function(e,t,r,s){t[s]=n.parseTwoDigitYear(e)}),j("Q",0,"Qo","quarter"),A("quarter","Q"),U("quarter",7),B("Q",zr),X("Q",function(e,t){t[fs]=3*(w(e)-1)}),j("D",["DD",2],"Do","date"),A("date","D"),U("date",9),B("D",Xr),B("DD",Xr,Br),B("Do",function(e,t){return e?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient}),X(["D","DD"],ms),X("Do",function(e,t){t[ms]=w(e.match(Xr)[0])});var ii=se("Date",!0);j("DDD",["DDDD",3],"DDDo","dayOfYear"),A("dayOfYear","DDD"),U("dayOfYear",4),B("DDD",ts),B("DDDD",qr),X(["DDD","DDDD"],function(e,t,n){n._dayOfYear=w(e)}),j("m",["mm",2],0,"minute"),A("minute","m"),U("minute",14),B("m",Xr),B("mm",Xr,Br),X(["m","mm"],vs);var ai=se("Minutes",!1);j("s",["ss",2],0,"second"),A("second","s"),U("second",15),B("s",Xr),B("ss",Xr,Br),X(["s","ss"],gs);var oi=se("Seconds",!1);j("S",0,0,function(){return~~(this.millisecond()/100)}),j(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),j(0,["SSS",3],0,"millisecond"),j(0,["SSSS",4],0,function(){return 10*this.millisecond()}),j(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),j(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),j(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),j(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),j(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),A("millisecond","ms"),U("millisecond",16),B("S",ts,zr),B("SS",ts,Br),B("SSS",ts,qr);var ui;for(ui="SSSS";ui.length<=9;ui+="S")B(ui,ss);for(ui="S";ui.length<=9;ui+="S")X(ui,zn);var li=se("Milliseconds",!1);j("z",0,0,"zoneAbbr"),j("zz",0,0,"zoneName");var ci=_.prototype;ci.add=ni,ci.calendar=an,ci.clone=on,ci.diff=mn,ci.endOf=On,ci.format=_n,ci.from=Sn,ci.fromNow=kn,ci.to=wn,ci.toNow=Mn,ci.get=oe,ci.invalidAt=En,ci.isAfter=un,ci.isBefore=ln,ci.isBetween=cn,ci.isSame=dn,ci.isSameOrAfter=hn,ci.isSameOrBefore=fn,ci.isValid=Cn,ci.lang=si,ci.locale=Dn,ci.localeData=Yn,ci.max=Js,ci.min=qs,ci.parsingFlags=Vn,ci.set=ue,ci.startOf=bn,ci.subtract=ri,ci.toArray=Pn,ci.toObject=Rn,ci.toDate=Tn,ci.toISOString=gn,ci.inspect=yn,ci.toJSON=$n,ci.toString=vn,ci.unix=Nn,ci.valueOf=xn,ci.creationData=An,ci.year=ws,ci.isLeapYear=re,ci.weekYear=Wn,ci.isoWeekYear=Un,ci.quarter=ci.quarters=Gn,ci.month=ve,ci.daysInMonth=ge,ci.week=ci.weeks=Te,ci.isoWeek=ci.isoWeeks=Pe,ci.weeksInYear=Hn,ci.isoWeeksInYear=Ln,ci.date=ii,ci.day=ci.days=We,ci.weekday=Ue,ci.isoWeekday=Le,ci.dayOfYear=Zn,ci.hour=ci.hours=As,ci.minute=ci.minutes=ai,ci.second=ci.seconds=oi,ci.millisecond=ci.milliseconds=li,ci.utcOffset=Lt,ci.utc=jt,ci.local=It,ci.parseZone=Gt,ci.hasAlignedHourOffset=Zt,ci.isDST=zt,ci.isLocal=qt,ci.isUtcOffset=Jt,ci.isUtc=Qt,ci.isUTC=Qt,ci.zoneAbbr=Bn,ci.zoneName=qn,ci.dates=Y("dates accessor is deprecated. Use date instead.",ii),ci.months=Y("months accessor is deprecated. Use month instead",ve),ci.years=Y("years accessor is deprecated. Use year instead",ws),ci.zone=Y("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",Ht),ci.isDSTShifted=Y("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",Bt);var di=T.prototype;di.calendar=P,di.longDateFormat=R,di.invalidDate=$,di.ordinal=C,di.preparse=Xn,di.postformat=Xn,di.relativeTime=V,di.pastFuture=E,di.set=x,di.months=de,di.monthsShort=he,di.monthsParse=me,di.monthsRegex=_e,di.monthsShortRegex=ye,di.week=Oe,di.firstDayOfYear=Ne,di.firstDayOfWeek=xe,di.weekdays=Ce,di.weekdaysMin=Ee,di.weekdaysShort=Ve,di.weekdaysParse=Fe,di.weekdaysRegex=He,di.weekdaysShortRegex=je,di.weekdaysMinRegex=Ie,di.isPM=Je,di.meridiem=Qe,tt("en",{dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10,n=1===w(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th";return e+n}}),n.lang=Y("moment.lang is deprecated. Use moment.locale instead.",tt),n.langData=Y("moment.langData is deprecated. Use moment.localeData instead.",st);var hi=Math.abs,fi=gr("ms"),mi=gr("s"),pi=gr("m"),vi=gr("h"),gi=gr("d"),yi=gr("w"),_i=gr("M"),Si=gr("y"),ki=Sr("milliseconds"),wi=Sr("seconds"),Mi=Sr("minutes"),Di=Sr("hours"),Yi=Sr("days"),bi=Sr("months"),Oi=Sr("years"),xi=Math.round,Ni={ss:44,s:45,m:45,h:22,d:26,M:11},Ti=Math.abs,Pi=Ct.prototype;return Pi.isValid=Rt,Pi.abs=or,Pi.add=lr,Pi.subtract=cr,Pi.as=pr,Pi.asMilliseconds=fi,Pi.asSeconds=mi,Pi.asMinutes=pi,Pi.asHours=vi,Pi.asDays=gi,Pi.asWeeks=yi,Pi.asMonths=_i,Pi.asYears=Si,Pi.valueOf=vr,Pi._bubble=hr,Pi.clone=yr,Pi.get=_r,Pi.milliseconds=ki,Pi.seconds=wi,Pi.minutes=Mi,Pi.hours=Di,Pi.days=Yi,Pi.weeks=kr,Pi.months=bi,Pi.years=Oi,Pi.humanize=br,Pi.toISOString=xr,Pi.toString=xr,Pi.toJSON=xr,Pi.locale=Dn,Pi.localeData=Yn,Pi.toIsoString=Y("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",xr),Pi.lang=si,j("X",0,0,"unix"),j("x",0,0,"valueOf"),B("x",is),B("X",us),X("X",function(e,t,n){n._d=new Date(1e3*parseFloat(e,10))}),X("x",function(e,t,n){n._d=new Date(w(e))}),n.version="2.22.1",r(Ot),n.fn=ci,n.min=Nt,n.max=Tt,n.now=Qs,n.utc=f,n.unix=Jn,n.months=nr,n.isDate=l,n.locale=tt,n.invalid=g,n.duration=Xt,n.isMoment=S,n.weekdays=sr,n.parseZone=Qn,n.localeData=st,n.isDuration=Vt,n.monthsShort=rr,n.weekdaysMin=ar,n.defineLocale=nt,n.updateLocale=rt,n.locales=it,n.weekdaysShort=ir,n.normalizeUnits=F,n.relativeTimeRounding=Dr,n.relativeTimeThreshold=Yr,n.calendarFormat=sn,n.prototype=ci,n.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"YYYY-[W]WW",MONTH:"YYYY-MM"},n})},{}],3:[function(e,t,n){!function(e,r){"function"==typeof define&&define.amd?define([],r):"object"==typeof n?t.exports=r():e.StringMask=r()}(this,function(){function e(e,t){for(var n=0,r=t-1,s={escape:!0};r>=0&&s&&s.escape;)s=o[e.charAt(r)],n+=s&&s.escape?1:0,r--;return n>0&&n%2===1}function t(e,t){var n=e.replace(/[^0]/g,"").length,r=t.replace(/[^\d]/g,"").length;return r-n}function n(e,t,n,r){return r&&"function"==typeof r.transform&&(t=r.transform(t)),n.reverse?t+e:e+t}function r(e,t,n){var s=e.charAt(t),i=o[s];return""!==s&&(!(!i||i.escape)||r(e,t+n,n))}function s(e,t,n){var r=e.charAt(t),i=o[r];return""!==r&&(!(!i||!i.recursive)||s(e,t+n,n))}function i(e,t,n){var r=e.split("");return r.splice(n,0,t),r.join("")}function a(e,t){this.options=t||{},this.options={reverse:this.options.reverse||!1,usedefaults:this.options.usedefaults||this.options.reverse},this.pattern=e}var o={0:{pattern:/\d/,_default:"0"},9:{pattern:/\d/,optional:!0},"#":{pattern:/\d/,optional:!0,recursive:!0},A:{pattern:/[a-zA-Z0-9]/},S:{pattern:/[a-zA-Z]/},U:{pattern:/[a-zA-Z]/,transform:function(e){return e.toLocaleUpperCase()}},L:{pattern:/[a-zA-Z]/,transform:function(e){return e.toLocaleLowerCase()}},$:{escape:!0}};return a.prototype.process=function(a){function u(e){if(!g&&!v.length&&r(l,f,y.inc))return!0;if(!g&&v.length&&s(l,f,y.inc))return!0;if(g||(g=v.length>0),g){var t=v.shift();if(v.push(t),e.reverse&&h>=0)return f++,l=i(l,t,f),!0;if(!e.reverse&&h<a.length)return l=i(l,t,f),
!0}return f<l.length&&f>=0}if(!a)return{result:"",valid:!1};a+="";var l=this.pattern,c=!0,d="",h=this.options.reverse?a.length-1:0,f=0,m=t(l,a),p=!1,v=[],g=!1,y={start:this.options.reverse?l.length-1:0,end:this.options.reverse?-1:l.length,inc:this.options.reverse?-1:1};for(f=y.start;u(this.options);f+=y.inc){var _=a.charAt(h),S=l.charAt(f),k=o[S];if(v.length&&k&&!k.recursive&&(k=null),!g||_){if(this.options.reverse&&e(l,f)){d=n(d,S,this.options,k),f+=y.inc;continue}if(!this.options.reverse&&p){d=n(d,S,this.options,k),p=!1;continue}if(!this.options.reverse&&k&&k.escape){p=!0;continue}}if(!g&&k&&k.recursive)v.push(S);else{if(g&&!_){d=n(d,S,this.options,k);continue}if(!g&&v.length>0&&!_)continue}if(k)if(k.optional){if(k.pattern.test(_)&&m)d=n(d,_,this.options,k),h+=y.inc,m--;else if(v.length>0&&_){c=!1;break}}else if(k.pattern.test(_))d=n(d,_,this.options,k),h+=y.inc;else{if(_||!k._default||!this.options.usedefaults){c=!1;break}d=n(d,k._default,this.options,k)}else d=n(d,S,this.options,k),!g&&v.length&&v.push(S)}return{result:d,valid:c}},a.prototype.apply=function(e){return this.process(e).result},a.prototype.validate=function(e){return this.process(e).valid},a.process=function(e,t,n){return new a(t,n).process(e)},a.apply=function(e,t,n){return new a(t,n).apply(e)},a.validate=function(e,t,n){return new a(t,n).validate(e)},a})},{}],4:[function(e,t,n){"use strict";t.exports=angular.module("ui.utils.masks",[e("./global/global-masks"),e("./br/br-masks"),e("./us/us-masks"),e("./ch/ch-masks")]).name},{"./br/br-masks":6,"./ch/ch-masks":15,"./global/global-masks":19,"./us/us-masks":27}],5:[function(e,t,n){"use strict";var r=e("string-mask"),s=e("mask-factory"),i=new r("00000.00000 00000.000000 00000.000000 0 00000000000000");t.exports=s({clearValue:function(e){return e.replace(/[^0-9]/g,"").slice(0,47)},format:function(e){return 0===e.length?e:i.apply(e).replace(/[^0-9]$/,"")},validations:{brBoletoBancario:function(e){return 47===e.length}}})},{"mask-factory":"mask-factory","string-mask":3}],6:[function(e,t,n){"use strict";var r=angular.module("ui.utils.masks.br",[e("../helpers")]).directive("uiBrBoletoBancarioMask",e("./boleto-bancario/boleto-bancario")).directive("uiBrCepMask",e("./cep/cep")).directive("uiBrCnpjMask",e("./cnpj/cnpj")).directive("uiBrCpfMask",e("./cpf/cpf")).directive("uiBrCpfcnpjMask",e("./cpf-cnpj/cpf-cnpj")).directive("uiBrIeMask",e("./inscricao-estadual/ie")).directive("uiNfeAccessKeyMask",e("./nfe/nfe")).directive("uiBrCarPlateMask",e("./car-plate/car-plate")).directive("uiBrPhoneNumber",e("./phone/br-phone"));t.exports=r.name},{"../helpers":25,"./boleto-bancario/boleto-bancario":5,"./car-plate/car-plate":7,"./cep/cep":8,"./cnpj/cnpj":9,"./cpf-cnpj/cpf-cnpj":10,"./cpf/cpf":11,"./inscricao-estadual/ie":12,"./nfe/nfe":13,"./phone/br-phone":14}],7:[function(e,t,n){"use strict";var r=e("string-mask"),s=e("mask-factory"),i=new r("UUU-0000");t.exports=s({clearValue:function(e){return e.replace(/[^a-zA-Z0-9]/g,"").slice(0,7)},format:function(e){return(i.apply(e)||"").replace(/[^a-zA-Z0-9]$/,"")},validations:{carPlate:function(e){return 7===e.length}}})},{"mask-factory":"mask-factory","string-mask":3}],8:[function(e,t,n){"use strict";var r=e("string-mask"),s=e("mask-factory"),i=new r("00000-000");t.exports=s({clearValue:function(e){return e.toString().replace(/[^0-9]/g,"").slice(0,8)},format:function(e){return(i.apply(e)||"").replace(/[^0-9]$/,"")},validations:{cep:function(e){return 8===e.length}}})},{"mask-factory":"mask-factory","string-mask":3}],9:[function(e,t,n){"use strict";var r=e("string-mask"),s=e("br-validations"),i=e("mask-factory"),a=new r("00.000.000/0000-00");t.exports=i({clearValue:function(e){return e.replace(/[^\d]/g,"").slice(0,14)},format:function(e){return(a.apply(e)||"").trim().replace(/[^0-9]$/,"")},validations:{cnpj:function(e){return s.cnpj.validate(e)}}})},{"br-validations":1,"mask-factory":"mask-factory","string-mask":3}],10:[function(e,t,n){"use strict";var r=e("string-mask"),s=e("br-validations"),i=e("mask-factory"),a=new r("00.000.000/0000-00"),o=new r("000.000.000-00");t.exports=i({clearValue:function(e){return e.replace(/[^\d]/g,"").slice(0,14)},format:function(e){var t;return t=e.length>11?a.apply(e):o.apply(e)||"",t.trim().replace(/[^0-9]$/,"")},validations:{cpf:function(e){return e.length>11||s.cpf.validate(e)},cnpj:function(e){return e.length<=11||s.cnpj.validate(e)}}})},{"br-validations":1,"mask-factory":"mask-factory","string-mask":3}],11:[function(e,t,n){"use strict";var r=e("string-mask"),s=e("br-validations"),i=e("mask-factory"),a=new r("000.000.000-00");t.exports=i({clearValue:function(e){return e.replace(/[^\d]/g,"").slice(0,11)},format:function(e){return(a.apply(e)||"").trim().replace(/[^0-9]$/,"")},validations:{cpf:function(e){return s.cpf.validate(e)}}})},{"br-validations":1,"mask-factory":"mask-factory","string-mask":3}],12:[function(e,t,n){"use strict";function r(e){function t(e){return e?e.replace(/[^0-9]/g,""):e}function n(e,n){if(e&&a[e]){if("SP"===e&&/^P/i.test(n))return a.SP[1].mask;for(var r=a[e],s=0;r[s].chars&&r[s].chars<t(n).length&&s<r.length-1;)s++;return r[s].mask}}function r(e,r){var s=n(r,e);if(!s)return e;var i=s.process(t(e)),a=i.result||"";return a=a.trim().replace(/[^0-9]$/,""),"SP"===r&&/^p/i.test(e)?"P"+a:a}var a={AC:[{mask:new s("00.000.000/000-00")}],AL:[{mask:new s("000000000")}],AM:[{mask:new s("00.000.000-0")}],AP:[{mask:new s("000000000")}],BA:[{chars:8,mask:new s("000000-00")},{mask:new s("0000000-00")}],CE:[{mask:new s("00000000-0")}],DF:[{mask:new s("00000000000-00")}],ES:[{mask:new s("00000000-0")}],GO:[{mask:new s("00.000.000-0")}],MA:[{mask:new s("000000000")}],MG:[{mask:new s("000.000.000/0000")}],MS:[{mask:new s("000000000")}],MT:[{mask:new s("0000000000-0")}],PA:[{mask:new s("00-000000-0")}],PB:[{mask:new s("00000000-0")}],PE:[{chars:9,mask:new s("0000000-00")},{mask:new s("00.0.000.0000000-0")}],PI:[{mask:new s("000000000")}],PR:[{mask:new s("000.00000-00")}],RJ:[{mask:new s("00.000.00-0")}],RN:[{chars:9,mask:new s("00.000.000-0")},{mask:new s("00.0.000.000-0")}],RO:[{mask:new s("0000000000000-0")}],RR:[{mask:new s("00000000-0")}],RS:[{mask:new s("000/0000000")}],SC:[{mask:new s("000.000.000")}],SE:[{mask:new s("00000000-0")}],SP:[{mask:new s("000.000.000.000")},{mask:new s("-00000000.0/000")}],TO:[{mask:new s("00000000000")}]};return{restrict:"A",require:"ngModel",link:function(n,s,a,o){function u(e){return o.$isEmpty(e)?e:r(e,c)}function l(e){if(o.$isEmpty(e))return e;var n=r(e,c),s=t(n);return o.$viewValue!==n&&(o.$setViewValue(n),o.$render()),c&&"SP"===c.toUpperCase()&&/^p/i.test(e)?"P"+s:s}var c=(e(a.uiBrIeMask)(n)||"").toUpperCase();o.$formatters.push(u),o.$parsers.push(l),o.$validators.ie=function(e){return o.$isEmpty(e)||i.ie(c).validate(e)},n.$watch(a.uiBrIeMask,function(e){c=(e||"").toUpperCase(),l(o.$viewValue),o.$validate()})}}}var s=e("string-mask"),i=e("br-validations");r.$inject=["$parse"],t.exports=r},{"br-validations":1,"string-mask":3}],13:[function(e,t,n){"use strict";var r=e("string-mask"),s=e("mask-factory"),i=new r("0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000");t.exports=s({clearValue:function(e){return e.replace(/[^0-9]/g,"").slice(0,44)},format:function(e){return(i.apply(e)||"").replace(/[^0-9]$/,"")},validations:{nfeAccessKey:function(e){return 44===e.length}}})},{"mask-factory":"mask-factory","string-mask":3}],14:[function(e,t,n){"use strict";var r=e("string-mask"),s=e("mask-factory"),i={areaCode:new r("(00) 0000-0000"),simple:new r("0000-0000")},a={areaCode:new r("(00) 00000-0000"),simple:new r("00000-0000")},o={areaCode:null,simple:new r("0000-000-0000")};t.exports=s({clearValue:function(e){return e.toString().replace(/[^0-9]/g,"").slice(0,11)},format:function(e){var t;return t=0===e.indexOf("0800")?o.simple.apply(e):e.length<9?i.simple.apply(e)||"":e.length<10?a.simple.apply(e):e.length<11?i.areaCode.apply(e):a.areaCode.apply(e),t.trim().replace(/[^0-9]$/,"")},getModelValue:function(e,t){var n=this.clearValue(e);return"number"===t?parseInt(n):n},validations:{brPhoneNumber:function(e){var t=e&&e.toString().length;return t>=8&&t<=11}}})},{"mask-factory":"mask-factory","string-mask":3}],15:[function(e,t,n){"use strict";var r=angular.module("ui.utils.masks.ch",[e("../helpers")]).directive("uiChPhoneNumber",e("./phone/ch-phone"));t.exports=r.name},{"../helpers":25,"./phone/ch-phone":16}],16:[function(e,t,n){"use strict";var r=e("string-mask"),s=e("mask-factory"),i=new r("+00 00 000 00 00");t.exports=s({clearValue:function(e){return e.toString().replace(/[^0-9]/g,"").slice(0,11)},format:function(e){var t;return t=i.apply(e)||"",t.trim().replace(/[^0-9]$/,"")},validations:{chPhoneNumber:function(e){var t=e&&e.toString().length;return 11===t}}})},{"mask-factory":"mask-factory","string-mask":3}],17:[function(e,t,n){"use strict";var r=e("string-mask"),s=e("mask-factory"),i=16,a=new r("0000 0000 0000 0000");t.exports=s({clearValue:function(e){return e.toString().replace(/[^0-9]/g,"").slice(0,i)},format:function(e){var t;return t=a.apply(e)||"",t.trim().replace(/[^0-9]$/,"")},validations:{creditCard:function(e){var t=e&&e.toString().length;return t===i}}})},{"mask-factory":"mask-factory","string-mask":3}],18:[function(e,t,n){"use strict";function r(e){return/^[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]{3}([-+][0-9]{2}:[0-9]{2}|Z)$/.test(e.toString())}function s(e){var t={"pt-br":"DD/MM/YYYY",ru:"DD.MM.YYYY"},n=t[e.id]||"YYYY-MM-DD";return{restrict:"A",require:"ngModel",link:function(e,t,s,o){function u(e){if(o.$isEmpty(e))return e;var t=e;("object"==typeof e||r(e))&&(t=i(e).format(n)),t=t.replace(/[^0-9]/g,"");var s=l.apply(t)||"";return s.trim().replace(/[^0-9]$/,"")}s.parse=s.parse||"true",n=s.uiDateMask||n;var l=new a(n.replace(/[YMD]/g,"0"));o.$formatters.push(u),o.$parsers.push(function(e){if(o.$isEmpty(e))return e;var t=u(e);return o.$viewValue!==t&&(o.$setViewValue(t),o.$render()),"false"===s.parse?t:i(t,n).toDate()}),o.$validators.date=function(e,t){return!!o.$isEmpty(e)||i(t,n).isValid()&&t.length===n.length}}}}var i=e("moment"),a=e("string-mask");s.$inject=["$locale"],t.exports=s},{moment:2,"string-mask":3}],19:[function(e,t,n){"use strict";var r=angular.module("ui.utils.masks.global",[e("../helpers")]).directive("uiDateMask",e("./date/date")).directive("uiMoneyMask",e("./money/money")).directive("uiNumberMask",e("./number/number")).directive("uiPercentageMask",e("./percentage/percentage")).directive("uiScientificNotationMask",e("./scientific-notation/scientific-notation")).directive("uiTimeMask",e("./time/time")).directive("uiCreditCard",e("./credit-card/credit-card"));t.exports=r.name},{"../helpers":25,"./credit-card/credit-card":17,"./date/date":18,"./money/money":20,"./number/number":21,"./percentage/percentage":22,"./scientific-notation/scientific-notation":23,"./time/time":24}],20:[function(e,t,n){"use strict";function r(e,t,n){return{restrict:"A",require:"ngModel",link:function(r,a,o,u){function l(e){var t=e>0?h+new Array(e+1).join("0"):"",n="#"+f+"##0"+t;return angular.isDefined(o.uiCurrencyAfter)?n+=p:n=p+n,new s(n,{reverse:!0})}function c(e){if(u.$isEmpty(e))return e;var t=angular.isDefined(o.uiNegativeNumber)&&e<0?"-":"",r=n.prepareNumberToFormatter(e,v);return angular.isDefined(o.uiCurrencyAfter)?t+y.apply(r)+m:t+m+y.apply(r)}function d(e){if(u.$isEmpty(e))return e;var t,n=e.replace(/[^\d]+/g,"");if(n=n.replace(/^[0]+([1-9])/,"$1"),n=n||"0",g&&angular.isDefined(o.uiCurrencyAfter)&&0!==n&&(n=n.substring(0,n.length-1),g=!1),t=angular.isDefined(o.uiCurrencyAfter)?y.apply(n)+m:m+y.apply(n),angular.isDefined(o.uiNegativeNumber)){var r="-"===e[0],s="-"===e.slice(-1);s^r&&n&&(n*=-1,t="-"+t)}return e!==t&&(u.$setViewValue(t),u.$render()),t?parseInt(t.replace(/[^\d\-]+/g,""))/Math.pow(10,v):null}var h=e.NUMBER_FORMATS.DECIMAL_SEP,f=e.NUMBER_FORMATS.GROUP_SEP,m=e.NUMBER_FORMATS.CURRENCY_SYM,p=" ",v=t(o.uiMoneyMask)(r),g=!1;a.bind("keydown keypress",function(e){g=8===e.which}),angular.isDefined(o.uiDecimalDelimiter)&&(h=o.uiDecimalDelimiter),angular.isDefined(o.uiThousandsDelimiter)&&(f=o.uiThousandsDelimiter),angular.isDefined(o.uiHideGroupSep)&&(f=""),angular.isDefined(o.uiHideSpace)&&(p=""),angular.isDefined(o.currencySymbol)&&(m=o.currencySymbol,0===o.currencySymbol.length&&(p="")),isNaN(v)&&(v=2),v=parseInt(v);var y=l(v);if(u.$formatters.push(c),u.$parsers.push(d),o.uiMoneyMask&&r.$watch(o.uiMoneyMask,function(e){v=isNaN(e)?2:e,v=parseInt(v),y=l(v),d(u.$viewValue)}),o.min){var _;u.$validators.min=function(e){return i.minNumber(u,e,_)},r.$watch(o.min,function(e){_=e,u.$validate()})}if(o.max){var S;u.$validators.max=function(e){return i.maxNumber(u,e,S)},r.$watch(o.max,function(e){S=e,u.$validate()})}}}}var s=e("string-mask"),i=e("validators");r.$inject=["$locale","$parse","PreFormatters"],t.exports=r},{"string-mask":3,validators:"validators"}],21:[function(e,t,n){"use strict";function r(e,t,n,r){return{restrict:"A",require:"ngModel",link:function(i,a,o,u){function l(e){if(u.$isEmpty(e))return null;var t=n.clearDelimitersAndLeadingZeros(e)||"0",r=p.apply(t),s=parseFloat(v.apply(t));if(angular.isDefined(o.uiNegativeNumber)){var i="-"===e[0],a="-"===e.slice(-1);(a^i||"-"===e)&&(s*=-1,r="-"+(0!==s?r:""))}return u.$viewValue!==r&&(u.$setViewValue(r),u.$render()),s}function c(e){if(u.$isEmpty(e))return e;var t=angular.isDefined(o.uiNegativeNumber)&&e<0?"-":"",r=n.prepareNumberToFormatter(e,m);return t+p.apply(r)}function d(){"-"===u.$viewValue&&(u.$setViewValue(""),u.$render())}var h=e.NUMBER_FORMATS.DECIMAL_SEP,f=e.NUMBER_FORMATS.GROUP_SEP,m=t(o.uiNumberMask)(i);angular.isDefined(o.uiHideGroupSep)&&(f=""),isNaN(m)&&(m=2);var p=r.viewMask(m,h,f),v=r.modelMask(m);if(a.on("blur",d),u.$formatters.push(c),u.$parsers.push(l),o.uiNumberMask&&i.$watch(o.uiNumberMask,function(e){m=isNaN(e)?2:e,p=r.viewMask(m,h,f),v=r.modelMask(m),l(u.$viewValue)}),o.min){var g;u.$validators.min=function(e){return s.minNumber(u,e,g)},i.$watch(o.min,function(e){g=e,u.$validate()})}if(o.max){var y;u.$validators.max=function(e){return s.maxNumber(u,e,y)},i.$watch(o.max,function(e){y=e,u.$validate()})}}}}var s=e("validators");r.$inject=["$locale","$parse","PreFormatters","NumberMasks"],t.exports=r},{validators:"validators"}],22:[function(e,t,n){"use strict";function r(e,t,n,r){function i(e,t,r){return n.clearDelimitersAndLeadingZeros((parseFloat(e)*r).toFixed(t))}return{restrict:"A",require:"ngModel",link:function(t,a,o,u){function l(e){if(u.$isEmpty(e))return e;var t=i(e,f,v.multiplier);return y.apply(t)+(m?"%":" %")}function c(e){if(u.$isEmpty(e))return null;var t=n.clearDelimitersAndLeadingZeros(e)||"0";e.length>1&&e.indexOf("%")===-1&&(t=t.slice(0,t.length-1)),p&&1===e.length&&"%"!==e&&(t="0");var r=m?"%":" %",s=y.apply(t)+r,i=parseFloat(_.apply(t));return u.$viewValue!==s&&(u.$setViewValue(s),u.$render()),i}var d=e.NUMBER_FORMATS.DECIMAL_SEP,h=e.NUMBER_FORMATS.GROUP_SEP,f=parseInt(o.uiPercentageMask),m=!1,p=!1;a.bind("keydown keypress",function(e){p=8===e.which});var v={multiplier:100,decimalMask:2};angular.isDefined(o.uiHideGroupSep)&&(h=""),angular.isDefined(o.uiHideSpace)&&(m=!0),angular.isDefined(o.uiPercentageValue)&&(v.multiplier=1,v.decimalMask=0),isNaN(f)&&(f=2);var g=f+v.decimalMask,y=r.viewMask(f,d,h),_=r.modelMask(g);if(u.$formatters.push(l),u.$parsers.push(c),o.uiPercentageMask&&t.$watch(o.uiPercentageMask,function(e){f=isNaN(e)?2:e,angular.isDefined(o.uiPercentageValue)&&(v.multiplier=1,v.decimalMask=0),g=f+v.decimalMask,y=r.viewMask(f,d,h),_=r.modelMask(g),c(u.$viewValue)}),o.min){var S;u.$validators.min=function(e){return s.minNumber(u,e,S)},t.$watch(o.min,function(e){S=e,u.$validate()})}if(o.max){var k;u.$validators.max=function(e){return s.maxNumber(u,e,k)},t.$watch(o.max,function(e){k=e,u.$validate()})}}}}var s=e("validators");r.$inject=["$locale","$parse","PreFormatters","NumberMasks"],t.exports=r},{validators:"validators"}],23:[function(e,t,n){"use strict";function r(e,t){function n(e){var t="0";if(e>0){t+=r;for(var n=0;n<e;n++)t+="0"}return new s(t,{reverse:!0})}var r=e.NUMBER_FORMATS.DECIMAL_SEP,i=2;return{restrict:"A",require:"ngModel",link:function(e,s,a,o){function u(e){var t=e.toString(),n=t.match(/(-?[0-9]*)[\.]?([0-9]*)?[Ee]?([\+-]?[0-9]*)?/);return{integerPartOfSignificand:n[1],decimalPartOfSignificand:n[2],exponent:0|n[3]}}function l(e){if(o.$isEmpty(e))return e;"string"==typeof e?e=e.replace(r,"."):"number"==typeof e&&(e=e.toExponential(d));var t,n,s=u(e),i=s.integerPartOfSignificand||0,a=i.toString();angular.isDefined(s.decimalPartOfSignificand)&&(a+=s.decimalPartOfSignificand);var l=(i>=1||i<=-1)&&(angular.isDefined(s.decimalPartOfSignificand)&&s.decimalPartOfSignificand.length>d||0===d&&a.length>=2);return l&&(n=a.slice(d+1,a.length),a=a.slice(0,d+1)),t=h.apply(a),0!==s.exponent&&(n=s.exponent),angular.isDefined(n)&&(t+="e"+n),t}function c(e){if(o.$isEmpty(e))return e;var t=l(e),n=parseFloat(t.replace(r,"."));return o.$viewValue!==t&&(o.$setViewValue(t),o.$render()),n}var d=t(a.uiScientificNotationMask)(e);isNaN(d)&&(d=i);var h=n(d);o.$formatters.push(l),o.$parsers.push(c),o.$validators.max=function(e){return o.$isEmpty(e)||e<Number.MAX_VALUE}}}}var s=e("string-mask");r.$inject=["$locale","$parse"],t.exports=r},{"string-mask":3}],24:[function(e,t,n){"use strict";var r=e("string-mask");t.exports=function(){return{restrict:"A",require:"ngModel",link:function(e,t,n,s){function i(e){if(s.$isEmpty(e))return e;var t=e.replace(/[^0-9]/g,"").slice(0,u)||"";return(l.apply(t)||"").replace(/[^0-9]$/,"")}var a="00:00:00";angular.isDefined(n.uiTimeMask)&&"short"===n.uiTimeMask&&(a="00:00");var o=a.length,u=a.replace(":","").length,l=new r(a);s.$formatters.push(i),s.$parsers.push(function(e){if(s.$isEmpty(e))return e;var t=i(e),n=t;return s.$viewValue!==t&&(s.$setViewValue(t),s.$render()),n}),s.$validators.time=function(e){if(s.$isEmpty(e))return!0;var t=e.toString().split(/:/).filter(function(e){return!!e}),n=parseInt(t[0]),r=parseInt(t[1]),i=parseInt(t[2]||0);return e.toString().length===o&&n<24&&r<60&&i<60}}}}},{"string-mask":3}],25:[function(e,t,n){"use strict";var r=e("string-mask"),s=angular.module("ui.utils.masks.helpers",[]);t.exports=s.name,s.factory("PreFormatters",[function(){function e(e){if("0"===e)return"0";var t=e.replace(/^-/,"").replace(/^0*/,"");return t.replace(/[^0-9]/g,"")}function t(t,n){return e(parseFloat(t).toFixed(n))}return{clearDelimitersAndLeadingZeros:e,prepareNumberToFormatter:t}}]).factory("NumberMasks",[function(){return{viewMask:function(e,t,n){var s="#"+n+"##0";if(e>0){s+=t;for(var i=0;i<e;i++)s+="0"}return new r(s,{reverse:!0})},modelMask:function(e){var t="###0";if(e>0){t+=".";for(var n=0;n<e;n++)t+="0"}return new r(t,{reverse:!0})}}}])},{"string-mask":3}],26:[function(e,t,n){"use strict";var r=e("string-mask"),s=e("mask-factory"),i=new r("(*************"),a=new r("+00-00-000-000000");t.exports=s({clearValue:function(e){return e.toString().replace(/[^0-9]/g,"")},format:function(e){var t;return t=e.length<11?i.apply(e)||"":a.apply(e),t.trim().replace(/[^0-9]$/,"")},validations:{usPhoneNumber:function(e){return e&&e.toString().length>9}}})},{"mask-factory":"mask-factory","string-mask":3}],27:[function(e,t,n){"use strict";var r=angular.module("ui.utils.masks.us",[e("../helpers")]).directive("uiUsPhoneNumber",e("./phone/us-phone"));t.exports=r.name},{"../helpers":25,"./phone/us-phone":26}],"mask-factory":[function(e,t,n){"use strict";t.exports=function(e){return function(){return{restrict:"A",require:"ngModel",link:function(t,n,r,s){s.$formatters.push(function(t){if(s.$isEmpty(t))return t;var n=e.clearValue(t);return e.format(n)}),s.$parsers.push(function(t){if(s.$isEmpty(t))return t;var n=e.clearValue(t),r=e.format(n);if(s.$viewValue!==r&&(s.$setViewValue(r),s.$render()),angular.isUndefined(e.getModelValue))return n;var i=typeof s.$modelValue;return e.getModelValue(r,i)}),angular.forEach(e.validations,function(e,t){s.$validators[t]=function(t,n){return s.$isEmpty(t)||e(t,n)}})}}}}},{}],validators:[function(e,t,n){"use strict";t.exports={maxNumber:function(e,t,n){var r=parseFloat(n,10);return e.$isEmpty(t)||isNaN(r)||t<=r},minNumber:function(e,t,n){var r=parseFloat(n,10);return e.$isEmpty(t)||isNaN(r)||t>=r}}},{}]},{},[4]);