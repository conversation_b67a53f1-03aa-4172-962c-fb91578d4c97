angular.module("validation",["validation.provider","validation.directive"]),angular.module("validation.provider",[]),angular.module("validation.directive",["validation.provider"]),function(){function e(){var e,t,i,n,r,a=this,s=function(a){e=a,t=e.get("$rootScope"),i=e.get("$http"),n=e.get("$q"),r=e.get("$timeout")},o={},l=null,u={};this.setExpression=function(e){return angular.extend(o,e),a},this.getExpression=function(e){return o[e]},this.setDefaultMsg=function(e){return angular.extend(u,e),a},this.getDefaultMsg=function(e){return u[e]},this.setValidMethod=function(e){l=e},this.getValidMethod=function(){return l},this.setErrorHTML=function(e){if(e.constructor===Function)return a.getErrorHTML=e,a},this.getErrorHTML=function(e){return'<span class="validation-invalid">'+e+"</span>"},this.setSuccessHTML=function(e){if(e.constructor===Function)return a.getSuccessHTML=e,a},this.getSuccessHTML=function(e){return'<span class="validation-valid">'+e+"</span>"},this.showSuccessMessage=!0,this.showErrorMessage=!0,this.checkValid=function(e){return!(!e||!e.$valid)},this.validate=function(e){var i=n.defer(),s=0;if(void 0===e)return console.error("This is not a regular Form name scope"),i.reject("This is not a regular Form name scope"),i.promise;if(e.validationId)t.$broadcast(e.$name+"submit-"+e.validationId,s++);else if(e.constructor===Array)for(var o in e)t.$broadcast(e[o].$name+"submit-"+e[o].validationId,s++);else for(var l in e)"$"!==l[0]&&e[l].hasOwnProperty("$dirty")&&t.$broadcast(l+"submit-"+e[l].validationId,s++);return i.promise.success=function(e){return i.promise.then(function(t){e(t)}),i.promise},i.promise.error=function(e){return i.promise.then(null,function(t){e(t)}),i.promise},r(function(){a.checkValid(e)?i.resolve("success"):i.reject("error")}),i.promise},this.validCallback=null,this.invalidCallback=null,this.resetCallback=null,this.reset=function(e){if(void 0===e)return void console.error("This is not a regular Form name scope");if(e.validationId)t.$broadcast(e.$name+"reset-"+e.validationId);else if(e.constructor===Array)for(var i in e)t.$broadcast(e[i].$name+"reset-"+e[i].validationId);else for(var n in e)"$"!==n[0]&&e[n].hasOwnProperty("$dirty")&&t.$broadcast(n+"reset-"+e[n].validationId)},this.addMsgElement=function(e){var t=e.next(".val-msg");if(!t||!t.length)return e.after('<span class="val-msg"></span>')},this.getMsgElement=function(e){return e.next(".val-msg")},this.$get=["$injector",function(e){return s(e),{setValidMethod:this.setValidMethod,getValidMethod:this.getValidMethod,setErrorHTML:this.setErrorHTML,getErrorHTML:this.getErrorHTML,setSuccessHTML:this.setSuccessHTML,getSuccessHTML:this.getSuccessHTML,setExpression:this.setExpression,getExpression:this.getExpression,setDefaultMsg:this.setDefaultMsg,getDefaultMsg:this.getDefaultMsg,showSuccessMessage:this.showSuccessMessage,showErrorMessage:this.showErrorMessage,checkValid:this.checkValid,validate:this.validate,validCallback:this.validCallback,invalidCallback:this.invalidCallback,resetCallback:this.resetCallback,reset:this.reset,addMsgElement:this.addMsgElement,getMsgElement:this.getMsgElement}}]}angular.module("validation.provider").provider("$validation",e)}.call(this),function(){function e(e){var t=e.get("$validation"),i=e.get("$timeout"),n=e.get("$parse");return{link:function(e,r,a){var s=n(a.validationReset)(e);i(function(){r.on("click",function(e){e.preventDefault(),t.reset(s)})})}}}angular.module("validation.directive").directive("validationReset",e),e.$inject=["$injector"]}.call(this),function(){function e(e){var t=e.get("$validation"),i=e.get("$timeout"),n=e.get("$parse");return{priority:1,require:"?ngClick",link:function(e,r,a){var s=n(a.validationSubmit)(e);i(function(){r.off("click"),r.on("click",function(i){i.preventDefault(),t.validate(s).success(function(){n(a.ngClick)(e)})})})}}}angular.module("validation.directive").directive("validationSubmit",e),e.$inject=["$injector"]}.call(this),function(){function e(e){function t(e,t,i){for(var n=document.querySelectorAll("*[validation-group="+t+"]"),r=0,a=n.length;r<a;r++){var s=n[r],o=s.form.name,l=s.name;e[o][l].$setValidity(l,i)}}function i(e){var t={};return e&&e.length>0?(t=e[0],angular.isObject(t)||(t={result:t,message:""})):t={result:!1,message:""},t}var n=e.get("$validation"),r=e.get("$q"),a=e.get("$timeout"),s=e.get("$compile"),o=e.get("$parse"),l={},u=function(e,t,i,r,a,l,u){var c,d=t||n.getDefaultMsg(i).success,g=o(l.validCallback),v=l.messageId,f=l.validationGroup;return c=v||f?angular.element(document.querySelector("#"+(v||f))):n.getMsgElement(e),e.attr("no-validation-message")?c.css("display","none"):n.showSuccessMessage&&d?(d=angular.isFunction(d)?d(e,l,u):d,c.html("").append(s(n.getSuccessHTML(d,e,l))(r)),c.css("display","")):c.css("display","none"),a.$setValidity(a.$name,!0),g(r,{message:d}),n.validCallback&&n.validCallback(e),!0},c=function(e,t,i,r,a,l,u){var c,d=t||n.getDefaultMsg(i).error,g=o(l.invalidCallback),v=l.messageId,f=l.validationGroup;return c=v||f?angular.element(document.querySelector("#"+(v||f))):n.getMsgElement(e),e.attr("no-validation-message")?c.css("display","none"):n.showErrorMessage&&d?(d=angular.isFunction(d)?d(e,l,u):d,c.html("").append(s(n.getErrorHTML(d,e,l))(r)),c.css("display","")):c.css("display","none"),a.$setValidity(a.$name,!1),g(r,{message:d}),n.invalidCallback&&n.invalidCallback(e),!1},d=function(e){var t=l[e];return Object.keys(t).some(function(e){return t[e]})},g={},v=function(e,a,s,o,g,f){var m=g.slice(0),h=m[0].trim(),$=h.indexOf("="),p=$===-1?h:h.substr(0,$),M=$===-1?null:h.substr($+1),b=m.slice(1),y=p+"SuccessMessage",E=p+"ErrorMessage",k=n.getExpression(p),V=s.validationGroup;null!==f&&void 0!==f||(f="");var S={success:function(t){return u(a,t||s[y],p,e,o,s,M),!b.length||v(e,a,s,o,b,f)},error:function(t){return c(a,t||s[E],p,e,o,s,M)}};if(void 0===k)return console.error('You are using undefined validator "%s"',p),b.length?v(e,a,s,o,b,f):void 0;if(k.constructor===Function)return r.all([n.getExpression(p)(f,e,a,s,M)]).then(function(n){var r=i(n),a=r.message;return r.result?(V&&(l[V][o.$name]=!0,t(e,V,!0)),S.success(a)):V?(l[V][o.$name]=!1,d(V)?void t(e,V,!0):(t(e,V,!1),S.error(a))):S.error(a)},function(){return S.error()});if(k.constructor!==RegExp)return S.error();if(void 0!==f&&null!==f){if(n.getExpression(p).test(f))return V&&(l[V][o.$name]=!0,t(e,V,!0)),S.success();if(!V)return S.error();if(l[V][o.$name]=!1,!d(V))return t(e,V,!1),S.error();t(e,V,!0)}},f=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)},m=function(){return f()+f()+f()+f()};return{restrict:"A",require:"ngModel",link:function(e,t,i,r){var s="false"!==i.useViewValue,o=i.validator,u=i.messageId,c=i.validationGroup,d=i.validMethod,f=i.ngModel,h=function(){},$=o.split(","),p=r.validationId=m(),M=null,b=void 0;return"boolean"==typeof i.initialValidity&&(b=i.initialValidity),i.$observe("validator",function(e){$=e.split(",")}),c&&(l[c]||(l[c]={}),l[c][r.$name]=!1),u||c||n.addMsgElement(t),r.$setValidity(r.$name,b),e.$on(r.$name+"reset-"+p,function(){h(),a(function(){r.$setViewValue(M),r.$setPristine(),r.$setValidity(r.$name,void 0),r.$render(),u||c?angular.element(document.querySelector("#"+(u||c))).html(""):n.getMsgElement(t).html(""),n.resetCallback&&n.resetCallback(t)})}),d=angular.isUndefined(d)?n.getValidMethod():d,e.$on(r.$name+"submit-"+p,function(n,o){var l=s?r.$viewValue:r.$modelValue,u=!1;u=v(e,t,i,r,$,l),"submit"===d&&(h(),h=e.$watch(function(){return e.$eval(f)},function(n,a){n!==a&&(void 0!==n&&null!==n||(n=""),u=v(e,t,i,r,$,n))}));var c=function(e){e?delete g[o]:(g[o]=t[0],a(function(){g[Math.min.apply(null,Object.keys(g))].focus()},0))};u&&u.constructor===Object?u.then(c):c(u)}),"blur"===d?void t.bind("blur",function(){var n=e.$eval(f);"$apply"!==e.$root.$$phase?e.$apply(function(){v(e,t,i,r,$,n)}):v(e,t,i,r,$,n)}):void("submit"!==d&&"submit-only"!==d&&(e.$watch(function(){return e.$eval(f)},function(a){if(r.$pristine&&r.$viewValue)M=r.$viewValue||"",r.$setViewValue(r.$viewValue);else if(r.$pristine)return void(u||c?angular.element(document.querySelector("#"+(u||c))).html(""):n.getMsgElement(t).html(""));v(e,t,i,r,$,a)}),a(function(){i.$observe("noValidationMessage",function(e){var i;i=u||c?angular.element(document.querySelector("#"+(u||c))):n.getMsgElement(t),"true"===e||e===!0?i.css("display","none"):"false"!==e&&e!==!1||i.css("display","block")})})))}}}angular.module("validation.directive").directive("validator",e),e.$inject=["$injector"]}.call(this);