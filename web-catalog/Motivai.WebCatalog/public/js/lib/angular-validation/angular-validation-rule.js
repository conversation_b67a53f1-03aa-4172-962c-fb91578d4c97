(function() {
  angular
    .module('validation.rule', ['validation'])
    .config(['$validationProvider', function($validationProvider) {
      var expression = {
        required: function(value) {
          return !!value;
        },
        nonrequired: function(value) {
          return true;
        },
        url: /((([A-Za-z]{3,9}:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)/,
        email: /^([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$/,
        optionalEmail: /^(([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?))?$/,
        number: /^\d+$/,
        cpf: /^(\d{3}\.?\d{3}\.?\d{3}-?\d{2})$/,
        cnpj: /^(\d{2}\.?\d{3}\.?\d{3}\/?\d{4}-?\d{2})$/,
        cpfCnpj: /^((\d{3}\.?\d{3}\.?\d{3}-?\d{2})|(\d{2}\.?\d{3}\.?\d{3}\/?\d{4}-?\d{2}))$/,
        optionalCpfCnpj: /^((\d{3}\.?\d{3}\.?\d{3}-?\d{2})|(\d{2}\.?\d{3}\.?\d{3}\/?\d{4}-?\d{2}))?$/,
        date: /^((\d{2}\/\d{2}\/\d{4})|(\d{4}-\d{2}-\d{2})|(\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}:\d{2}))$/,
        datebr: /^(\d{2}\/\d{2}\/\d{4})$/,
        cep: /^\d{5}-?\d{3}$/,
        optionalPhone: /^((\(?\d{2}\)?\s?\d{4,5}-?\d{4})?)$/,
        phone: /^(\(?\d{2}\)?\s?\d{4,5}-?\d{4})$/,
        phoneNumber: /^(\(?(\d{2})?\)?\s?\d{4}-?\d{4,5})$/,
        cellphone: /^(\(?\d{2}\)?\s?\d{4}-?\d{5})$/,
        optionalCellphone: /^(\(?\d{2}\)?\s?\d{4}-?\d{5})?$/,
        minlength: function(value, scope, element, attrs, param) {
          return value && value.length >= param;
        },
        maxlength: function(value, scope, element, attrs, param) {
          return !value || value.length <= param;
        }
      };

      var defaultMsg = {
        required: {
          error: 'Campo obrigat&oacute;rio',
          success: ''
        },
        nonrequired: {
          error: '',
          success: ''
        },
        url: {
          error: 'Campo deve ser uma URL',
          success: ''
        },
        email: {
          error: 'E-mail inv&aacute;lido',
          success: ''
        },
        optionalEmail: {
          error: 'E-mail inv&aacute;lido',
          success: ''
        },
        number: {
          error: 'S&oacute; &eacute; permitido n&uacute;meros',
          success: ''
        },
        minlength: {
          error: 'This should be longer',
          success: ''
        },
        maxlength: {
          error: 'This should be shorter',
          success: ''
        },
        phone: {
          error: 'N&uacute;mero inv&aacute;lido',
          success: ''
        },
        cellphone: {
          error: 'N&uacute;mero de celular inv&aacute;lido',
          success: ''
        },
        optionalCellphone: {
          error: 'N&uacute;mero de celular inv&aacute;lido',
          success: ''
        },
        phoneNumber: {
          error: 'N&uacute;mero inv&aacute;lido',
          success: ''
        },
        optionalPhone: {
          error: 'N&uacute;mero inv&aacute;lido',
          success: ''
        },
        cpf: {
          error: 'CPF inv&aacute;lido',
          success: ''
        },
        cnpj: {
          error: 'CNPJ inv&aacute;lido',
          success: ''
        },
        cpfCnpj: {
          error: 'CPF/CNPJ inv&aacute;lido',
          success: ''
        },
        optionalCpfCnpj: {
          error: 'CPF/CNPJ inv&aacute;lido',
          success: ''
        },
        cep: {
          error: 'CEP inv&aacute;lido',
          success: ''
        },
        date: {
          error: 'Data inv&aacute;lida',
          success: ''
        },
        datebr: {
          error: 'Data inv&aacute;lida',
          success: ''
        }
      };
      $validationProvider.setExpression(expression).setDefaultMsg(defaultMsg);
    }]);
}).call(this);
