angular.module("uiMask",[]).directive("uiMask",[function(){var e={9:/\d/,A:/[a-zA-Z]/,"*":/[a-zA-Z0-9]/};return{priority:100,require:"ngModel",restrict:"A",link:function(t,n,i,r){function u(e){return angular.isDefined(e)?(p(e),j?(c(),void s()):l()):l()}function a(e){return j?(O=d(e||""),q=h(O),r.$setValidity("mask",q),q&&(_=O),q&&O.length?g(O):void 0):e}function o(e){return j?(O=d(e||""),q=h(O),viewValue=O.length?g(O):"",r.$viewValue=viewValue,r.$setValidity("mask",q),""===O&&void 0!==r.$error.required&&r.$setValidity("required",!1),q&&(_=O),q?O:void 0):e}function l(){return j=!1,f(),angular.isDefined(K)?n.attr("placeholder",K):n.removeAttr("placeholder"),angular.isDefined(N)?n.attr("maxlength",N):n.removeAttr("maxlength"),n.val(r.$modelValue),r.$viewValue=r.$modelValue,!1}function c(){O=D=d(r.$modelValue||""),R=z=g(O),q=h(O),viewValue=q&&O.length?R:"",i.maxlength&&n.attr("maxlength",2*x[x.length-1]),n.attr("placeholder",S),n.val(viewValue),r.$viewValue=viewValue}function s(){return!!H||(n.bind("blur",v),n.bind("mousedown mouseup",m),n.bind("input keyup click",y),void(H=!0))}function f(){return!H||(n.unbind("blur",v),n.unbind("mousedown",m),n.unbind("mouseup",m),n.unbind("input",y),n.unbind("keyup",y),n.unbind("click",y),void(H=!1))}function h(e){return!e.length||e.length>=E}function d(e){var t="",n=A.slice();return e=e.toString(),angular.forEach(M,function(t,n){e=e.replace(t,"")}),angular.forEach(e.split(""),function(e,i){n.length&&n[0].test(e)&&(t+=e,n.shift())}),t}function g(e){var t="",n=x.slice();return angular.forEach(S.split(""),function(i,r){e.length&&r===n[0]?(t+=e.charAt(0)||"_",e=e.substr(1),n.shift()):t+=i}),t}function p(t){var n=0;if(x=[],A=[],S="","string"==typeof t){E=0;var i=!1;angular.forEach(t.split(""),function(t,r){e[t]?(x.push(n),S+="_",A.push(e[t]),n++,i||E++):"?"===t?i=!0:(S+=t,n++)})}x.push(x.slice().pop()+1),M=S.replace(/[_]+/g,"_").replace(/([^_]+)([a-zA-Z0-9])([^_])/g,"$1$2_$3").split("_"),j=x.length>1}function v(e){T=0,Z=0,q&&0!==O.length||(R="",n.val(""),t.$apply(function(){r.$setViewValue("")}))}function m(e){"mousedown"===e.type?n.bind("mouseout",b):n.unbind("mouseout",b)}function b(e){Z=k(this),n.unbind("mouseout",b)}function y(e){e=e||{};var i=e.which,u=e.type;if(16===i||91===i)return!0;var a,o=n.val(),l=z,c=d(o),s=D,f=!1,h=V(this)||0,p=T||0,v=h-p,m=x[0],b=x[c.length]||x.slice().shift(),y=k(this),A=Z||0,S=y>0,M=A>0,_=o.length>l.length||A&&o.length>l.length-A,E=o.length<l.length||A&&o.length===l.length-A,O=i>=37&&i<=40&&e.shiftKey,R=37===i,q=8===i||"keyup"!==u&&E&&v===-1,j=46===i||"keyup"!==u&&E&&0===v&&!M,H=(R||q||"click"===u)&&h>m;if(Z=y,O||S&&("click"===u||"keyup"===u))return!0;if("input"===u&&E&&!M&&c===s){for(;q&&h>m&&!$(h);)h--;for(;j&&h<b&&x.indexOf(h)===-1;)h++;var K=x.indexOf(h);c=c.substring(0,K)+c.substring(K+1),f=!0}for(a=g(c),z=a,D=c,n.val(a),f&&t.$apply(function(){r.$setViewValue(c)}),_&&h<=m&&(h=m+1),H&&h--,h=h>b?b:h<m?m:h;!$(h)&&h>m&&h<b;)h+=H?-1:1;(H&&h<b||_&&!$(p))&&h++,T=h,w(this,h)}function $(e){return x.indexOf(e)>-1}function V(e){if(void 0!==e.selectionStart)return e.selectionStart;if(document.selection){e.focus();var t=document.selection.createRange();return t.moveStart("character",-e.value.length),t.text.length}}function w(e,t){if(0===e.offsetWidth||0===e.offsetHeight)return!0;if(e.setSelectionRange)e.focus(),e.setSelectionRange(t,t);else if(e.createTextRange){var n=e.createTextRange();n.collapse(!0),n.moveEnd("character",t),n.moveStart("character",t),n.select()}}function k(e){return void 0!==e.selectionStart?e.selectionEnd-e.selectionStart:document.selection?document.selection.createRange().text.length:void 0}var x,A,S,M,_,E,O,R,q,z,D,T,Z,j=!1,H=!1,K=i.placeholder,N=i.maxlength;i.$observe("uiMask",u),r.$formatters.push(a),r.$parsers.push(o),n.bind("mousedown mouseup",m),Array.prototype.indexOf||(Array.prototype.indexOf=function(e){"use strict";if(null===this)throw new TypeError;var t=Object(this),n=t.length>>>0;if(0===n)return-1;var i=0;if(arguments.length>1&&(i=Number(arguments[1]),i!==i?i=0:0!==i&&i!==1/0&&i!==-(1/0)&&(i=(i>0||-1)*Math.floor(Math.abs(i)))),i>=n)return-1;for(var r=i>=0?i:Math.max(n-Math.abs(i),0);r<n;r++)if(r in t&&t[r]===e)return r;return-1})}}}]);