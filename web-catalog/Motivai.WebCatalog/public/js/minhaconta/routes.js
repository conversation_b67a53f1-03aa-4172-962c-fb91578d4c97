angular.module("platformApp")
  .config(['$routeProvider', '$locationProvider', function ($routeProvider, $locationProvider) {
    $locationProvider.hashPrefix('');

    $routeProvider.when("/principal", {
        templateUrl: "MinhaConta/Template/minhaconta-principal",
        controller: "resumoCtrl"
      })
      .when("/cadastro", {
        templateUrl: "MinhaConta/Template/minhaconta-cadastro",
        controller: "cadastroCtrl"
      })
      .when("/enderecos", {
        templateUrl: "MinhaConta/Template/minhaconta-enderecos",
        controller: "enderecosCtrl"
      })
      .when("/novo-endereco", {
        templateUrl: "MinhaConta/Template/minhaconta-enderecos-editar",
        controller: "edicaoEnderecoCtrl"
      })
      .when("/editar-endereco/:enderecoId", {
        templateUrl: "MinhaConta/Template/minhaconta-enderecos-editar",
        controller: "edicaoEnderecoCtrl"
      })
      .when("/senha", {
        templateUrl: "MinhaConta/Template/minhaconta-senha",
        controller: "alterarSenhaCtrl"
      })
      .when("/dados-dupla-autenticacao", {
        templateUrl: "MinhaConta/Template/minhaconta-dados-dupla-autenticacao",
        controller: "dadosDuplaAutenticacaoCtrl"
      })
      .when("/pedidos", {
        templateUrl: "MinhaConta/Template/minhaconta-pedidos",
        controller: "pedidosCtrl"
      })
      .when("/pedido/:pedido", {
        templateUrl: "MinhaConta/Template/minhaconta-pedido-detalhes",
        controller: "detalhesPedidoCtrl"
      })
      .when("/pedido/cartao/:pedido", {
        templateUrl: "MinhaConta/Template/minhaconta-pedido-cartao-detalhes",
        controller: "detalhesPedidoCartaoCtrl"
      })
      .when("/pedido/cashback/:pedido", {
        templateUrl: "MinhaConta/Template/minhaconta-pedido-cashback-detalhes",
        controller: "detalhesPedidoCashbackCtrl"
      })
      .when("/pedido/recarga/:pedido", {
        templateUrl: "MinhaConta/Template/minhaconta-pedido-recarga-detalhes",
        controller: "detalhesPedidoRecargaCtrl"
      })
      .when("/pedido/pague-contas/:pedido", {
        templateUrl: "MinhaConta/Template/minhaconta-pedido-pague-contas-detalhes",
        controller: "detalhesPedidoPagueContasCtrl"
      })
      .when("/extrato", {
        templateUrl: "MinhaConta/Template/minhaconta-extrato",
        controller: "extratoCtrl"
      })
      .when("/expiracao", {
        templateUrl: "MinhaConta/Template/minhaconta-expiracao",
        controller: "pontosExpirarCtrl"
      })
      .when("/bloqueados", {
        templateUrl: "MinhaConta/Template/minhaconta-bloqueados",
        controller: "pontosBloqueadosCtrl"
      })
      .when("/fale-conosco", {
        templateUrl: "MinhaConta/Template/minhaconta-faleconosco"
      })
      .when("/duvidas", {
        templateUrl: "MinhaConta/Template/minhaconta-duvidas"
      })
      .when("/comprar", {
        templateUrl: "/ComprarPontos/MinhaContaFormulario",
        controller: "comprarPontosController"
      })
      .when("/auth", {
        templateUrl: "MinhaConta/Template/minhaconta-registration-mfa-validation",
        controller: "RegistrationMfaValidationCtrl"
      })
      .otherwise({
        redirectTo: "/principal"
      });
  }])
  .run(function ($rootScope, $location) {
    $rootScope.$on('$routeChangeStart', function (event, next, current) {
      if (next.originalPath) {
        pageView('/minhaconta' + next.originalPath);
      }
    });
  });
