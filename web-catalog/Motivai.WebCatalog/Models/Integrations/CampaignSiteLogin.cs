using Motivai.SharedKernel.Helpers;

namespace Motivai.WebCatalog.Models.Integrations
{
    public class CampaignSiteLogin
    {
        public string Session { get; set; }
        public string Callcenter { get; set; }

        public string Token { get; set; }
        public string User { get; set; }
        public string Campaign { get; set; }

        public string Timezone { get; set; }

        public string AccountOperatorId { get; set; }
        public string AccountOperatorLoginId { get; set; }
        public string OperatorMigrated { get; set; }
        public string OperatorMigratedType { get; set; }
        public string RedirectUrl { get; set; }
        public string ProductId { get; set; }

        public bool IsAccountOperator()
        {
            return !string.IsNullOrEmpty(AccountOperatorId) && !string.IsNullOrEmpty(AccountOperatorLoginId);
        }

        public void Validate()
        {
            Token.ForNullOrEmpty("Token de segurança inválido.");
            User.ForNullOrEmpty("Usuário inválido.");
        }

        public bool HasProductId()
        {
            return !string.IsNullOrEmpty(ProductId);
        }

        public bool IsMigratedOperator()
        {
            return !string.IsNullOrEmpty(this.OperatorMigrated);
        }
    }
}
