using System;
using Motivai.SharedKernel.Helpers.Cryptography;

namespace Motivai.WebCatalog.Models.Integrations
{
    public class CallcenterIntegrationLogin
    {
        public string campaign { get; set; }
        public string user { get; set; }
        public string participant { get; set; }
        public string callcenter { get; set; }
        ///<summary>
        /// Token encriptado, formato:
        /// {adminUserId}|{userId}|{campaignId}|{Expiration}
        ///</summary>
        public string token { get; set; }

        public bool DecryptAndValidate(ICryptography cryptography)
        {
            if (string.IsNullOrEmpty(campaign))
                return false;
            if (string.IsNullOrEmpty(user))
                return false;
            if (string.IsNullOrEmpty(participant))
                return false;
            if (string.IsNullOrEmpty(callcenter))
                return false;
            if (string.IsNullOrEmpty(token))
                return false;

            campaign = cryptography.Decrypt(campaign);
            user = cryptography.Decrypt(user);
            participant = cryptography.Decrypt(participant);
            callcenter = cryptography.Decrypt(callcenter);

            try {
                var decrypted = cryptography.Decrypt(token);
                var parts = decrypted.Split("|");
                if (callcenter != parts[0])
                    return false;
                if (user != parts[1])
                    return false;
                if (campaign != parts[2])
                    return false;
                var time = DateTime.Parse(parts[3]);
                return time >= DateTime.UtcNow;
            }
            catch
            {
                return false;
            }
        }
    }
}