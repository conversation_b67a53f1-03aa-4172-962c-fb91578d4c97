using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;

namespace Motivai.WebCatalog.Models.Integrations
{
	public class LoginSsoEndingRequest
	{
		public string Origin { get; set; }
		public Guid Token { get; set; }
		public string Timezone { get; set; }
		public ConnectionInfo ConnectionInfo { get; set; }

		public static LoginSsoEndingRequest Of(Guid token, string sessionOrigin, string timezone, ConnectionInfo connectionInfo)
		{
			return new LoginSsoEndingRequest
			{
				Origin = sessionOrigin,
				Token = token,
				Timezone = timezone,
				ConnectionInfo = connectionInfo
			};
		}
	}
}