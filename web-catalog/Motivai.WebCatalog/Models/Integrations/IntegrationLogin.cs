using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Security;

namespace Motivai.WebCatalog.Models.Integrations
{
	public class IntegrationLogin
	{
		public string Origin { get; set; }

		///<summary>
		/// Token de autenticação.
		/// Formato: <Token Cliente>.<Token Campanha>
		///</summary>
		public string Token { get; set; }
		///<summary>
		/// Documento do participante (CPF ou CNPJ).
		///</summary>
		public string Document { get; set; }

		public bool SkipDocumentValidation { get; set; }
		public string Login { get; set; }

		public string Name { get; set; }
		public string Rg { get; set; }

		public string CompanyName { get; set; }
		public string Telephone { get; set; }
		public string Cellphone { get; set; }
		public string Email { get; set; }

		public bool? StateInscriptionExempt { get; set; }
		public string StateInscriptionUf { get; set; }
		public string StateInscription { get; set; }

		public Address Address { get; set; }

		public string ClientUserId { get; set; }

		public ConnectionInfo ConnectionInfo { get; set; }

		public string ProductId { get; set; }

		public List<string> GroupsCodes { get; set; }

		// Armazenando temporariamente no login, posteriormente será colocado no ConnectionInfo
		public string TraceId { get; set; }

		public bool HasProductId()
		{
			return !string.IsNullOrEmpty(ProductId);
		}
	}
}