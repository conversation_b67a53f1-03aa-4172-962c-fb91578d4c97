namespace Motivai.WebCatalog.Models.Base
{
    public class ErrorModel
    {
        public string Code { get; set; }
        public string ErrorMessage { get; set; }
        public string DetailedError { get; set; }
        public string HelpMessage { get; set; }

        public static ErrorModel Of(string errorMessage, string helpMessage) {
            return Of(null, errorMessage, null, helpMessage);
        }

        public static ErrorModel Of(string errorMessage, string detailedMessage, string helpMessage) {
            return Of(null, errorMessage, detailedMessage, helpMessage);
        }

        public static ErrorModel Of(string code, string errorMessage, string detailedMessage, string helpMessage) {
            return new ErrorModel() {
                Code = code,
                ErrorMessage = errorMessage,
                DetailedError = detailedMessage,
                HelpMessage = helpMessage
            };
        }
    }
}