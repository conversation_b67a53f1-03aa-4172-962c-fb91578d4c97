﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Api;
using Motivai.SharedKernel.Helpers.Exceptions;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Motivai.WebCatalog.Models.Base {
    public class AjaxReturn {
        private static JsonSerializerSettings JSON_PROPS = new JsonSerializerSettings {
            ReferenceLoopHandling = ReferenceLoopHandling.Serialize,
            NullValueHandling = NullValueHandling.Ignore,
            ContractResolver = new DefaultContractResolver()
        };

        public AjaxReturn() {
            Success = true;
        }

        public bool Success { get; set; }
        public string Error { get; set; }
        public string Alert { get; set; }

        public void SetError(string error) {
            Success = false;
            Error = error;
        }

        public void SetError(Exception ex, string message) {
            Success = false;
            if (ex is MotivaiException) {
                Error = ex.Message;
            } else {
                Error = message;
            }
        }

        public JsonResult GetJsonResult() {
            return new JsonResult(this, JSON_PROPS);
        }

        public static JsonResult GetJsonResult(string data) {
            var ajaxReturn = new AjaxReturn<string>() {
                Success = true,
                Return = data
            };
            return ajaxReturn.GetJsonResult();
        }

        public static JsonResult GetJsonResult<T>(T data) {
            var ajaxReturn = new AjaxReturn<T>() {
                Success = true,
                Return = data
            };
            return ajaxReturn.GetJsonResult();
        }

        public static JsonResult FromError(string errorMessage) {
            var ajaxReturn = new AjaxReturn<string>() {
                Success = false,
                Error = errorMessage
            };
            return ajaxReturn.GetJsonResult();
        }

        public static async Task<AjaxReturn> Execute(Task func, string defaultMessageForException, string alert = null) {
            return await Execute(func, defaultMessageForException, null, alert);
        }

        public static async Task<AjaxReturn> Execute(Task func, string defaultMessageForException = null, string pathToRedictOnSuccess = null, string alert = null) {
            var ajaxreturn = new AjaxReturn();
            try {
                await func;
                if (!String.IsNullOrEmpty(alert))
                    ajaxreturn.Alert = alert;
            } catch (MotivaiException ex) {
                var msg = String.IsNullOrEmpty(defaultMessageForException) ? ex.Message : defaultMessageForException;
                ajaxreturn.SetError(msg);
                if (ex.IsLoggable())
                    await ExceptionLoggerMiddleware.HandleException(ex.InnerException != null ? ex.InnerException : ex, ex.Message);
            } catch (Exception ex) {
                var msg = String.IsNullOrEmpty(defaultMessageForException) ? ex.Message : defaultMessageForException;
                ajaxreturn.SetError(msg);
                var gpEx = MotivaiException.ofException(ex.Message, ex);
                await ExceptionLoggerMiddleware.HandleException(gpEx);
            }
            return ajaxreturn;
        }
    }

    public class AjaxReturn<T> : AjaxReturn {
        public T Return { get; set; }
        public string Redirect { get; set; }
        public bool HasRedirect {
            get { return !string.IsNullOrEmpty(Redirect); }
        }

        ///<summary>
        /// Retorna true se a chamada foi executada com sucesso e tem um retorno não-nulo.
        ///</summary>
        public bool HasNonNullReturn() {
            return Success && Return != null;
        }

        ///<summary>
        /// Se a chamada foi executada com sucesso então retorna senão lança o erro retornado na chamada.
        ///</summary>
        public T GetReturnOrError() {
            if (Success)
                return Return;
            throw new MotivaiException(Error);
        }

        public static async Task<AjaxReturn<T>> Execute(Task<T> func, string defaultMessageForException = null, string messageAlert = null) {
            return await ExecuteAndReturnRedirect(func, null, defaultMessageForException, messageAlert);
        }

        public static async Task<AjaxReturn<T>> ExecuteAndReturnRedirect(Task<T> func, string pathToRedictOnSuccess,
                string defaultMessageForException = null, string messageAlert = null) {
            var ajaxReturn = new AjaxReturn<T>();
            try {
                ajaxReturn.Return = await func;
                if (!String.IsNullOrEmpty(messageAlert))
                    ajaxReturn.Alert = messageAlert;
                if (!string.IsNullOrEmpty(pathToRedictOnSuccess))
                    ajaxReturn.Redirect = pathToRedictOnSuccess;
            } catch (MotivaiException ex) {
                var msg = String.IsNullOrEmpty(defaultMessageForException) ? ex.Message : defaultMessageForException;
                if (ex.IsLoggable()) {
                    await ExceptionLoggerMiddleware.HandleException(ex.InnerException != null ? ex.InnerException : ex, ex.Message);
                }
                ajaxReturn.SetError(msg);
            } catch (Exception ex) {
                var msg = String.IsNullOrEmpty(defaultMessageForException) ? ex.Message : defaultMessageForException;
                ajaxReturn.SetError(msg);
                var gpEx = MotivaiException.ofException(ex.Message, ex);
                await ExceptionLoggerMiddleware.HandleException(gpEx);
            }
            return ajaxReturn;
        }
    }
}