using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Helpers.Exceptions;
namespace Motivai.WebCatalog.Models.Recaptcha
{
    public class RecaptchaResponse
    {
        private Dictionary<string, string> ERROR_CODES = new Dictionary<string, string>() {
            { "missing-input-secret", "The secret parameter is missing." },
            { "invalid-input-secret",  "The secret parameter is invalid or malformed." },
            { "missing-input-response", "The response parameter is missing." },
            { "invalid-input-response", "The response parameter is invalid or malformed." },
            { "bad-request", "The request is invalid or malformed." },
            { "timeout-or-duplicate", "The response is no longer valid: either is too old or has been used previously."}
        };


        public bool Success { get; set; }
        public string Hostname { get; set; }
        public string Challenge_ts { get; set; }
        public List<string> ErrorCodes { get; set; }

        internal bool IsSuccess()
        {
            return Success;
        }

        internal void ValidateHostName(string host)
        {
            if (!host.Equals(Hostname))
            {
                throw MotivaiException.ofValidation("Host do reCaptcha inválido");
            }
        }

        internal string GetErrorsMessages()
        {
            var errors = "";
            ErrorCodes.ForEach(code =>
            {
                if (ERROR_CODES.ContainsKey(code))
                {
                    errors += ERROR_CODES.GetValueOrDefault(code, "") + ". ";
                }
            });

            return errors;
        }
    }
}