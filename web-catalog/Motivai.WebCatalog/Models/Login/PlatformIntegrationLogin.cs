using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.WebCatalog.Models.Login
{
    public class PlatformIntegrationLogin
    {
        public string Origin { get; set; }
        public string SessionId { get; set; }
        public Guid UserId { get; set; }
        public Guid? AccountOperatorId { get; set; }
        public Guid? AccountOperatorLoginId { get; set; }

        public bool OperatorMigrated { get; set; }
        public string OperatorMigratedType { get; set; }
        public string RedirectUrl { get; set; }

        public string Timezone { get; set; }

        public ConnectionInfo ConnectionInfo { get; set; }

        public static PlatformIntegrationLogin OfUser(Guid userId, string timezone, ConnectionInfo connectionInfo)
        {
            return new PlatformIntegrationLogin()
            {
                Origin = "Site Campanha para Catálogo",
                UserId = userId,
                Timezone = timezone,
                ConnectionInfo = connectionInfo
            };
        }

        public static PlatformIntegrationLogin OfAccountOperator(Guid userId, Guid accountOperatorId, Guid accountOperatorLoginId, string timezone, ConnectionInfo connectionInfo)
        {
            if (accountOperatorId == Guid.Empty || accountOperatorLoginId == Guid.Empty)
            {
                throw MotivaiException.ofValidation("Sessão do operador da conta inválida.");
            }
            return new PlatformIntegrationLogin()
            {
                Origin = "Site Campanha para Catálogo",
                UserId = userId,
                AccountOperatorId = accountOperatorId,
                AccountOperatorLoginId = accountOperatorLoginId,
                Timezone = timezone,
                ConnectionInfo = connectionInfo
            };
        }
    }
}
