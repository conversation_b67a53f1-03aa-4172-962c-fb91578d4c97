using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.WebCatalog.Models.Login {
    public class CallcenterLogin {
        public string Origin { get; set; }
        public Guid CallcenterUserId { get; set; }
        public string CallcenterUsername { get; set; }

        public Guid UserId { get; set; }
        public Guid ParticipantId { get; set; }

        public ConnectionInfo ConnectionInfo { get; set; }

        public static CallcenterLogin NewSession(Guid callcenterUserId, string callcenterUsername,
                Guid userId, Guid participantId, ConnectionInfo connectionInfo)
        {
            if (userId == Guid.Empty)
                throw MotivaiException.ofValidation("Participante inválido.");

            return new CallcenterLogin() {
                Origin = "Callcenter Catálogo",
                CallcenterUserId = callcenterUserId,
                CallcenterUsername = callcenterUsername,
                UserId = userId,
                ParticipantId = participantId,
                ConnectionInfo = connectionInfo
            };
        }
    }
}