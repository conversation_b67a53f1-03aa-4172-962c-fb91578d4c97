using System;
using Motivai.SharedKernel.Helpers.Cryptography;

namespace Motivai.WebCatalog.Models.Login {
    public class PasswordRecoverySession {
        public string Origin { get; set; }
        public DateTime ExpireAt { get; set; }

        public Guid UserId { get; set; }
        public Guid ParticipantId { get; set; }

        public Guid AccountOperatorId { get; set; }
        public Guid AccountOperatorLoginId { get; set; }

        public string Email { get; set; }
        public string Cellphone { get; set; }

        public string SendForm { get; set; }
        public string SecurityToken { get; set; }
        public string EnteredToken { get; set; }

        public string NewPassword { get; set; }
        public string ConfirmPassword { get; set; }

        public bool IsValid() {
            return ExpireAt > DateTime.UtcNow;
        }

        public bool HasEnteredCorrectToken() {
            return Hashing.ValidateHash(EnteredToken, SecurityToken);
        }

        public static PasswordRecoverySession OfContact(PasswordRecoveryContact participant) {
            return new PasswordRecoverySession() {
                ExpireAt = DateTime.UtcNow.AddMinutes(10),
                UserId = participant.UserId,
                ParticipantId = participant.ParticipantId,
                AccountOperatorId = participant.AccountOperatorId,
                AccountOperatorLoginId = participant.AccountOperatorLoginId,
                Email = participant.Email,
                Cellphone = participant.MobilePhone
            };
        }
    }
}