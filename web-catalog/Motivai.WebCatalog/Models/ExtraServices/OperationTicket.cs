using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums.ExtraServices;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Cryptography;

namespace Motivai.WebCatalog.Models.ExtraServices
{
	public class OperationTicket
	{
		public string CatalogExtraServiceId { get; set; }
		///<summary>
		/// Parceiro de pague contas da consulta.
		///</summary>
		public BillPaymentPartner BillDetailsQueryPartner { get; set; }
		///<summary>
		/// Parceiro de pague contas usado na liquidação.
		///</summary>
		public BillPaymentPartner BillPaymentPartner { get; set; }
		public bool RequiresConfirmation { get; set; }

		public string Protocol { get; set; }
		public string ProofPayment { get; set; }
		public DateTime OperationDate { get; set; }
		public DateTime FinishDate { get; set; }

		public string OperatorName { get; set; }
		public decimal? RechargeValue { get; set; }
		public decimal? RechargeCost { get; set; }
		public decimal? UpdatedBalance { get; set; }

		public string ConfirmationToken { get; set; }
	}

	public class ConfirmationTicket
	{
		public Guid UserId { get; set; }
		public Guid ParticipantId { get; set; }
		public AccountOperator AccountOperator { get; set; }
		public LocationInfo LocationInfo { get; set; }

		///<summary>
		/// Parceiro de pague contas da consulta.
		///</summary>
		public BillPaymentPartner BillDetailsQueryPartner { get; set; }
		///<summary>
		/// Parceiro de pague contas usado na liquidação.
		///</summary>
		public BillPaymentPartner BillPaymentPartner { get; set; }

		// Dados de Pague Contas
		public string Assignor { get; set; }
		public string BarCode { get; set; }
		public decimal BillingAmount { get; set; }

		// Dados de Recarga
		public string CellphoneDdd { get; set; }
		public string CellphoneNumber { get; set; }
		public string OperatorName { get; set; }
		public decimal RechargeValue { get; set; }

		// Dados Gerais
		public Amount ParticipantCost { get; set; }
		public string ProofPayment { get; set; }

		// Dados de Confirmação
		public string CatalogExtraServiceId { get; set; }
		public string Protocol { get; set; }
		public bool Confirm { get; set; }


		// Tokens encriptados
		public string ConfirmationToken { get; set; }
		public string Token { get; set; }

		public void Validate()
		{
			CatalogExtraServiceId.ForNullOrEmpty("Transação inválida.");
			Protocol.ForNullOrEmpty("Protocolo inválido.");
		}

		public static string CreateConfirmationToken(ICryptography cryptography, OperationTicket ticket)
		{
			var token = cryptography.Encrypt(string.Format("{0}|{1}", ticket.CatalogExtraServiceId, ticket.Protocol));
			ticket.CatalogExtraServiceId = null;
			return token;
		}

		public void ReadToken(ICryptography cryptography)
		{
			var splits = cryptography.Decrypt(ConfirmationToken).Split('|');
			this.CatalogExtraServiceId = splits[0];
			this.Protocol = splits[1];
		}
	}

	public class ConfirmationResult
	{
		public string Protocol { get; set; }
		public bool IsConfirmed { get; set; }
		public decimal? UpdatedBalance { get; set; }
	}
}