using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.WebCatalog.Models.ExtraServices {
    public class MobileRecharge {
        public Guid UserId { get; set; }
        public Guid ParticipantId { get; set; }
        public AccountOperator AccountOperator { get; set; }
        public LocationInfo LocationInfo { get; set; }

        public string CellphoneDdd { get; set; }
        public string CellphoneNumber { get; set; }

        public string Operator { get; set; }
        public string OperatorName { get; set; }
        public string RechargeValue { get; set; }

        public MobileOperatorOption Option { get; set; }

        public void Validate() {
            if (string.IsNullOrEmpty(CellphoneDdd))
                throw MotivaiException.ofValidation("Preencha o DDD do celular.");
            else if (CellphoneDdd.Length != 2)
                throw MotivaiException.ofValidation("DDD do celular inválido.");

            if (string.IsNullOrEmpty(CellphoneNumber))
                throw MotivaiException.ofValidation("Preencha o número do celular.");
            CellphoneNumber = CellphoneNumber.Replace("-", "");
            if (CellphoneNumber.Length < 8 || CellphoneNumber.Length > 9)
                throw MotivaiException.ofValidation("Número do celular inválido.");

            if (string.IsNullOrEmpty(Operator))
                throw MotivaiException.ofValidation("Seleciona a operadora.");
            if (string.IsNullOrEmpty(RechargeValue))
                throw MotivaiException.ofValidation("Selecione o valor de recarga.");
        }
    }
}