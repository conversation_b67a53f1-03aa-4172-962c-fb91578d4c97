using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.WebCatalog.Models.Addresses;

namespace Motivai.WebCatalog.Models.ExtraServices.Cards {
    public class PrepaidCardOrder {
        public string Id { get; set; }
        public string OrderNumber { get; set; }
        public string Status { get; set; }
        public string ResumedStatus { get; set; }
        public string Timezone { get; set; }

        public PrepaidCardType CardType { get; set; }
        public Guid CampaignId { get; set; }
        public Guid UserId { get; set; }
        public Guid ParticipantId { get; set; }
        public AccountOperator AccountOperator { get; set; }
        public LocationInfo LocationInfo { get; set; }

        public string PrepaidCardId { get; set; }
        public bool IssueNewCard { get; set; }
        public bool ReissueCard { get; set; }
        public PrepaidCard Card { get; set; }

        public decimal PointsToTransfer { get; set; }
        public decimal TotalFeesAmount { get; set; }
        public decimal CreditAmount { get; set; }
        public Amount OrderTotalCost { get; set; }
        public DetailedFees detailedFees { get; set; }
        public AddressModel ShippingAddress { get; set; }
        public bool RegulationAccepted { get; set; }
    }

    public class DetailedFees {
        public decimal PlasticCost { get; set; }
        public decimal ShippingCost { get; set; }
        public decimal ChargeCost { get; set; }
        public decimal ServiceFee { get; set; }
    }
}