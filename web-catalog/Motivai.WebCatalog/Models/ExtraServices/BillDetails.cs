using System;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums.ExtraServices;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Models.Session;

namespace Motivai.WebCatalog.Models.ExtraServices {
	public class BillDetails {
		public string BarCode { get; set; }
		public string ServiceType { get; set; }
		public string Assignor { get; set; }
		public decimal BillingAmount { get; set; }
		public DateTime? DueDate { get; set; }
		public long PartnerCorrelationId { get; set; }

		public RegisterData RegisterData { get; set; }
		public string SettleDate { get; set; }
		public string NextSettle { get; set; }

		///<summary>
		/// Parceiro de pague contas da consulta.
		///</summary>
		public BillPaymentPartner BillDetailsQueryPartner { get; set; }
		///<summary>
		/// Parceiro de pague contas usado na liquidação.
		///</summary>
		public BillPaymentPartner BillPaymentPartner { get; set; }

		public bool? FilledManually { get; set; }

		public bool CanSelectDueDate
		{
			get
			{
				return !DueDate.HasValue;
			}
		}

		public DateTime? MinimumScheduledPaymentDate { get; set; }
		public DateTime? MaximumScheduledPaymentDate { get; set; }
		public DateTime? ScheduledPaymentDate { get; set; }

		public decimal? PointsFactor { get; set; }
		public Amount ParticipantCost { get; set; }
		public decimal ParticipantCostPoints { get; set; }

		public string Token { get; set; }
		public Guid? UserId { get; set; }
		public Guid? ParticipantId { get; set; }
		public string UserDocument { get; set; }

		public AccountOperator AccountOperator { get; set; }
		public LocationInfo LocationInfo { get; set; }

		public void SetParticipantInfo(UserPrincipal participant)
		{
			UserId = participant.UserId;
			ParticipantId = participant.ParticipantId;
			UserDocument = participant.Document;
			if (participant.IsAccountOperator())
			{
				AccountOperator = participant.GetAccountOperator();
			}
		}

		public void Validate()
		{
			if (string.IsNullOrEmpty(BarCode))
				throw MotivaiException.ofValidation("Informe o código de barras.");
			if (string.IsNullOrEmpty(Token))
				throw MotivaiException.ofValidation("Conta para pagamento inválida, por favor, digite o código de barras novamente.");
		}

		public void CreateToken(ICryptography cryptography)
		{
			Token = cryptography.Encrypt(string.Format("{0}-{1}-{2}", PointsFactor ?? 1,
				ParticipantCost.GetCurrencyOrZero(), ParticipantCost.GetPointsOrZero()));
			ParticipantCostPoints = ParticipantCost.GetPointsOrZero();
			PointsFactor = null;
			ParticipantCost = null;
		}

		public void DecodeToken(ICryptography cryptography)
		{
			if (String.IsNullOrEmpty(Token)) return;
			var parts = cryptography.Decrypt(Token).Split('-');
			decimal pointsFactor = 0;
			if (!decimal.TryParse(parts[0], out pointsFactor))
				pointsFactor = 1;
			PointsFactor = pointsFactor;
			ParticipantCost = Amount.Of(decimal.Parse(parts[1]), decimal.Parse(parts[2]));
		}

		public static Amount ExtractCostFromToken(ICryptography cryptography, string token)
		{
			if (string.IsNullOrEmpty(token)) return null;
			var parts = cryptography.Decrypt(token).Split('-');
			return Amount.Of(decimal.Parse(parts[1]), decimal.Parse(parts[2]));
		}
	}

	public class RegisterData
	{
		/// Cpf ou Cnpj do Beneficiário.
		/// </summary>
		/// <returns></returns>
		public string DocumentRecipient { get; set; }

		/// Cpf ou Cnpj do pagador.
		/// </summary>
		/// <returns></returns>
		public string DocumentPayer { get; set; }

		/// <summary>
		/// Define a linha digitável a ser consultada.
		/// </summary>
		/// <returns></returns>
		public string Digitable { get; set; }

		/// Data de baixa do boleto.
		/// </summary>
		/// <returns></returns>
		public DateTime? PayDueDate { get; set; }

		/// Próximo dia útil.
		/// </summary>
		/// <returns></returns>
		public DateTime? NextBusinessDay { get; set; }

		/// Data de vencimento do registro.
		/// </summary>
		/// <returns></returns>
		public DateTime? DueDateRegister { get; set; }

		/// Permissão de alteração do valor do boleto.
		/// </summary>
		/// <returns></returns>
		public bool AllowChangeValue { get; set; }

		/// Nome do beneficiário.
		/// </summary>
		/// <returns></returns>
		public string Recipient { get; set; }

		/// Nome do pagador.
		/// </summary>
		/// <returns></returns>
		public string Payer { get; set; }

		/// Valor do desconto calculado.
		/// </summary>
		/// <returns></returns>
		public double DiscountValue { get; set; }

		/// Valor juros já calculado.
		/// </summary>
		/// <returns></returns>
		public double InterestValueCalculated { get; set; }

		/// Valor máximo permitido para pagamento do título.
		/// </summary>
		/// <returns></returns>
		public double MaxValue { get; set; }

		/// Valor mínimo permitido para pagamento do título.
		/// </summary>
		/// <returns></returns>
		public double MinValue { get; set; }

		/// Valor multa já calculado.
		/// </summary>
		/// <returns></returns>
		public double FineValueCalculated { get; set; }

		/// <summary>
		/// Define o valor nominal do título a ser pago
		/// </summary>
		/// <returns></returns>
		public double OriginalValue { get; set; }

		/// Valor atualizado a ser pago do título.
		/// </summary>
		/// <returns></returns>
		public double TotalUpdated { get; set; }
		/// Valor total de descontos e abatimentos.
		/// </summary>
		/// <returns></returns>
		public double TotalWithDiscount { get; set; }

		/// Valor total de descontos e abatimentos.
		/// </summary>
		/// <returns></returns>
		public double TotalWithAdditional { get; set; }
	}

}