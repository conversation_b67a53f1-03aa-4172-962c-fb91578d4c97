using System;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.WebCatalog.Models.ExtraServices {
    public class MobileOperatorOption {
        public string Description { get; set; }
        public string Validate { get; set; }

        public decimal? PointsFactor { get; set; }
        public decimal RechargeCost { get; set; }
        public Amount ParticipantCost { get; set; }

        public string CreateToken(ICryptography cryptography) {
            return cryptography.Encrypt(string.Format("{0}-{1}-{2}-{3}", (PointsFactor ?? 1), RechargeCost,
                ParticipantCost.GetCurrencyOrZero(), ParticipantCost.GetPointsOrZero()));
        }

        public static MobileOperatorOption FromToken(ICryptography cryptography, string token) {
            if (string.IsNullOrEmpty(token))
                throw MotivaiException.ofValidation("Opção de recarga inválida.");
            var parts = cryptography.Decrypt(token).Split('-');
            return new MobileOperatorOption() {
                PointsFactor = decimal.Parse(parts[0]),
                RechargeCost = decimal.Parse(parts[1]),
                ParticipantCost = Amount.Of(decimal.Parse(parts[2]), decimal.Parse(parts[3]))
            };
        }
    }
}