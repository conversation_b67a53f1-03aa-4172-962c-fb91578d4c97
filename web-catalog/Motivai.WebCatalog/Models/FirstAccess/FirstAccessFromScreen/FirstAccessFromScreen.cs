using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Motivai.WebCatalog.Models.FirstAccess.FirstAccessFromScreen
{
    public class FirstAccessFromScreen
    {
        public string RegulationId { get; set; }
        public string Version { get; set; }
        public string AcceptedTerms { get; set; }

        public bool IsAccepted
        {
            get
            {
                return AcceptedTerms == "on";
            }
        }
    }



}