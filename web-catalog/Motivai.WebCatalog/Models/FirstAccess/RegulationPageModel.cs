using System;

namespace Motivai.WebCatalog.Models.FirstAccess
{
    public class RegulationPageModel
    {
        public bool Disabled { get; set; }
        public bool SkipFirstAccessRegistrationData { get; set; }
        public string RegulationId { get; set; }
        public string Version { get; set; }
        public bool AcceptanceRequired { get; set; }
        public string AcceptedTerms { get; set; }
        public string Regulation { get; set; }

        public static RegulationPageModel DisabledPage()
        {
            return new RegulationPageModel()
            {
                Disabled = true
            };
        }
    }
}