using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.Users;
using Motivai.WebCatalog.Models.FirstAccess.CampaignAcceptanceResult.CampaignAcceptancesResult;

namespace Motivai.WebCatalog.Models.FirstAccess.FirstAccessAcceptancesModel
{
    public class FirstAccessAcceptancesModel
    {
        public bool KeepFirstAccess { get; set; }
        public bool AcceptedCampaignCommunications { get; set; }
		public bool AcceptedPartnerCommunications { get; set; }
        public CampaignAcceptancesResult CampaignAcceptancesResult {get; set;}
		public ParticipantAuthenticationMfaSettings AuthenticationMfaSettings { get; set; }

		public LocationInfo LocationInfo { get; set; }

        public FirstAccessAcceptancesModel(bool keepFirstAccess, CampaignAcceptancesResult campaignAcceptanceResult, LocationInfo location)
        {
            KeepFirstAccess = keepFirstAccess;
            CampaignAcceptancesResult = campaignAcceptanceResult;
            LocationInfo = location;
            AcceptedCampaignCommunications = false;
            AcceptedPartnerCommunications = false;
        }
    }
}