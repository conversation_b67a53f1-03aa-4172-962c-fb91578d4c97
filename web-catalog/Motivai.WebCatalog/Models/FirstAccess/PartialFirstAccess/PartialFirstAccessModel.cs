using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.Users;
using Motivai.WebCatalog.Models.FirstAccess.CampaignAcceptanceResult.CampaignAcceptancesResult;

namespace Motivai.WebCatalog.Models.FirstAccess.PartialFirstAccess
{
    public class PartialFirstAccessModel
    {
        public bool Accepted { get; set; }
        public bool AcceptedCampaignCommunications { get; set; }
        public bool acceptedPartnerCommunications { get; set; }
        public CampaignAcceptancesResult CampaignAcceptancesResult { get; set; }
        public ParticipantAuthenticationMfaSettings AuthenticationMfaSettings { get; set; }
        public dynamic UserMetadata { get; set; }
        public LocationInfo LocationInfo { get; set; }
    }
}