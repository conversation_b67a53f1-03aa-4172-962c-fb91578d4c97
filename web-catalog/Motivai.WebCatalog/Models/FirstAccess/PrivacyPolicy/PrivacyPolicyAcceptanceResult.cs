using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.WebCatalog.Services.Terms;

namespace Motivai.WebCatalog.Models.FirstAccess
{
    public class PrivacyPolicyAcceptanceResult
    {
        public string ContentId { get; set; }
        public string Version { get; set; }
        public bool Accepted { get; set; }

        public bool EssentialsCookies { get; set; }
        public bool AnalyticsCookies { get; set; }

        public string Timezone { get; set; }

        public PrivacyPolicyAcceptanceResult Of(string ContentId, string Version, bool EssentialsCookies, bool AnalyticsCookies, bool Accepted)
        {
            this.ContentId = ContentId;
            this.Version = Version;
            this.Accepted = Accepted;
            this.EssentialsCookies = EssentialsCookies;
            this.AnalyticsCookies = AnalyticsCookies;

            return this;
        }

        public void Validate()
        {
            if (!this.Accepted || !this.EssentialsCookies)
                throw new PrivacyPolicyNotAcceptedException();
        }

        public PrivacyPolicyAcceptanceResult EncryptSensitiveData(ICryptography cryptography)
        {
            this.ContentId = cryptography.Encrypt(this.ContentId);
            this.Version = cryptography.Encrypt(this.Version);
            return this;
        }

        public void DecryptSensitiveData(ICryptography cryptography)
        {
            this.ContentId = cryptography.Decrypt(this.ContentId);
            this.Version = cryptography.Decrypt(this.Version);
        }
    }
}
