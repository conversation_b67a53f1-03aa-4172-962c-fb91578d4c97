using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;

namespace Motivai.WebCatalog.Models.FirstAccess
{
    public class PrivacyPolicyChangeModel
    {
        public PrivacyPolicyAcceptanceResult PrivacyPolicyResult { get; set; }
        public AccountOperator AccountOperator { get; set; }
        public LocationInfo LocationInfo { get; set; }

        public static PrivacyPolicyChangeModel Of(PrivacyPolicyAcceptanceResult privacyPolicyAcceptanceResult,
            LocationInfo locationInfo, AccountOperator accountOperator)
        {
            return new PrivacyPolicyChangeModel
            {
                PrivacyPolicyResult = privacyPolicyAcceptanceResult,
                AccountOperator = accountOperator,
                LocationInfo = locationInfo
            };
        }
    }
}