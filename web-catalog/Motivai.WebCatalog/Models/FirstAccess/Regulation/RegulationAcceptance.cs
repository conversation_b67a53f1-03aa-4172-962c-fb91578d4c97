
using System.Collections.Generic;

namespace Motivai.WebCatalog.Models.FirstAccess.Regulation
{
    public class RegulationAcceptanceResult
    {
        public string RegulationId { get; set; }
        public string Version { get; set; }
        public IList<AcceptanceTerms> AcceptanceTerms { get; set; }

        public RegulationAcceptanceResult(string regulationId, string version)
        {
            Version = version;
            RegulationId = regulationId;
        }

        public RegulationAcceptanceResult()
        {

        }
    }
}