using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Models.FirstAccess.CampaignAcceptanceResult.CampaignAcceptancesResult;

namespace Motivai.WebCatalog.Models.FirstAccess
{
    public class FirstAccessDataModel
	{
		public string Name { get; set; }
		public bool KeepFirstAccess { get; set; }

		#region Pessoa Física
		public bool IsPessoaFisica { get; set; }
		public string Cpf { get; set; }
		public string Rg { get; set; }
		public string BirthDate { get; set; }
		public string MaritalStatus { get; set; }
		public string Gender { get; set; }
		#endregion

		#region Pessoa Jurídica
		public bool IsPessoaJuridica { get; set; }
		public string Cnpj { get; set; }
		public string CompanyName { get; set; }
		public bool StateInscriptionExempt { get; set; }
		public string StateInscription { get; set; }
		public string StateInscriptionUf { get; set; }
		public string ReceiverDocument { get; set; }
		#endregion

		public Address HomeAddress { get; set; }
		public Address CommercialAddress { get; set; }
		public Contact Contact { get; set; }

		public string PhotoUrl { get; set; }
		public bool GpInf { get; set; }
		public bool GpPartnerInf { get; set; }

		public string Password { get; set; }
		public string PasswordConfirmation { get; set; }

		public bool FillDocument { get; set; }

		public AccountRepresentative AccountRepresentative { get; set; }

		public dynamic Metadata { get; set; }

		public bool AcceptedCampaignCommunications { get; set; }
		public bool AcceptedPartnerCommunications { get; set; }
		public CampaignAcceptancesResult CampaignAcceptancesResult { get; set; }
		// public ParticipantAuthenticationMfaSettings AuthenticationMfaSettings { get; set; }
		public LocationInfo LocationInfo { get; set; }

		public static FirstAccessDataModel FromParticipantDataAndAddress(ParticipantDataModel participantData, Address homeAddress)
		{
			return new FirstAccessDataModel()
			{
				IsPessoaFisica = participantData.Type == PersonType.Fisica,
				IsPessoaJuridica = participantData.Type == PersonType.Juridica,

				Name = participantData.Name,

				Cpf = participantData.Cpf,
				Rg = participantData.Rg,
				MaritalStatus = participantData.MaritalStatus,
				Gender = participantData.Gender,
				BirthDate = participantData.BirthDate,

				Cnpj = participantData.Cnpj,
				CompanyName = participantData.CompanyName,
				StateInscriptionExempt = participantData.StateInscriptionExempt,
				StateInscription = participantData.StateInscription,
				StateInscriptionUf = participantData.StateInscriptionUf,
				GpInf = participantData.GpInf,
				GpPartnerInf = participantData.GpPartnerInf,
				Contact = participantData.Contact,
				HomeAddress = homeAddress,
				AccountRepresentative = participantData.AccountRepresentative,
				FillDocument = participantData.FillDocument
			};
		}

		public string GetDocument()
		{
			if (string.IsNullOrEmpty(Cpf))
				return Cnpj;
			return Cpf;
		}

		public void Validate(ParticipantData participantSettings)
		{

			if (IsPessoaFisica)
			{
				ValidatePF(participantSettings);
			}
			else
			{
				ValidatePJ(participantSettings);
			}

			// Dados default
			ValidateAddresses(participantSettings);

			if (participantSettings.EnableTelephones || participantSettings.EnableEmails)
			{
				ValidateContact(participantSettings);
			}
		}

		private void ValidatePF(ParticipantData participantSettings)
		{
			Name.ForNullOrEmptyDefaultMessage("Nome");
			Cpf.ForNullOrEmptyDefaultMessage("CPF");
			if (participantSettings.PersonalData.Rg.Required)
				Rg.ForNullOrEmptyDefaultMessage("RG");
			if (participantSettings.PersonalData.BirthDate.Required)
				BirthDate.ForNullOrEmptyDefaultMessage("Data de nascimento");
			if (participantSettings.PersonalData.Gender.Required)
				Gender.ForNullOrEmptyDefaultMessage("Sexo");
			if (participantSettings.PersonalData.MaritalStatus.Required)
				MaritalStatus.ForNullOrEmptyDefaultMessage("Estado civil");
		}

		private void ValidatePJ(ParticipantData participantSettings)
		{
			Cnpj.ForNullOrEmptyDefaultMessage("CNPJ");
			if (participantSettings.PersonalData.CompanyName.Required)
				CompanyName.ForNullOrEmptyDefaultMessage("Razão social");
			if (participantSettings.PersonalData.CompanyName.Required)
				CompanyName.ForNullOrEmptyDefaultMessage("Razão social");

			if (!StateInscriptionExempt)
			{
				if (participantSettings.PersonalData.StateInscription.Required)
					StateInscription.ForNullOrEmptyDefaultMessage("Inscrição estadual");
				if (participantSettings.PersonalData.StateInscriptionUf.Required)
					StateInscriptionUf.ForNullOrEmptyDefaultMessage("Estado da inscrição estadual");
			}
		}

		private void ValidateAddresses(ParticipantData participantSettings)
		{
			if (participantSettings.EnableHomeAddress)
			{
				if (HomeAddress == null)
					throw MotivaiException.ofValidation("Endereço residencial é obrigatório.");
				HomeAddress.AddressName = "Casa";
				HomeAddress.Validate();
			}
			else
			{
				HomeAddress = null;
			}

			if (participantSettings.EnableBusinessAddress)
			{
				if (CommercialAddress != null)
					CommercialAddress.AddressName = "Trabalho";
			}
			else
			{
				CommercialAddress = null;
			}
		}

		private void ValidateContact(ParticipantData participantSettings)
		{
			if (Contact == null)
				throw MotivaiException.ofValidation("Contato é obrigatório.");

			if (Contact != null)
			{
				if (participantSettings.Telephones.MainPhone.Required)
					Contact.MainPhone.ForNullOrEmptyDefaultMessage("Telefone principal");
				if (participantSettings.Telephones.HomePhone.Required)
					Contact.HomePhone.ForNullOrEmptyDefaultMessage("Telefone residencial");
				if (participantSettings.Telephones.BusinessPhone.Required)
					Contact.CommercialPhone.ForNullOrEmptyDefaultMessage("Telefone comercial");
				if (participantSettings.Telephones.TalkTo.Required)
					Contact.TalkTo.ForNullOrEmptyDefaultMessage("Falar com");
				if (participantSettings.Telephones.MobilePhone.Required)
					Contact.MobilePhone.ForNullOrEmptyDefaultMessage("Celular");
				if (participantSettings.Telephones.PhoneCarrier.Required)
					Contact.MobileOperator.ForNullOrEmptyDefaultMessage("Operadora");
				if (participantSettings.Emails.MainEmail.Required)
					Contact.MainEmail.ForNullOrEmptyDefaultMessage("E-mail principal");
				if (participantSettings.Emails.PersonalEmail.Required)
					Contact.PersonalEmail.ForNullOrEmptyDefaultMessage("E-mail pessoal");
				if (participantSettings.Emails.BusinessEmail.Required)
					Contact.CommercialEmail.ForNullOrEmptyDefaultMessage("E-mail comercial");
			}
		}
	}
}