using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.WebCatalog.Models.FirstAccess.Regulation;

namespace Motivai.WebCatalog.Models.FirstAccess.CampaignAcceptanceResult.CampaignAcceptancesResult
{
    public class CampaignAcceptancesResult
    {
        public PrivacyPolicyAcceptanceResult PrivacyPolicy { get; set; }
        public RegulationAcceptanceResult RegulationAcceptance { get; set; }
        public CampaignAcceptancesResult(PrivacyPolicyAcceptanceResult privacyPolicyAcceptanceResult, RegulationAcceptanceResult regulationAcceptance)
        {
            PrivacyPolicy = privacyPolicyAcceptanceResult;
            RegulationAcceptance = regulationAcceptance;
        }
    }
}