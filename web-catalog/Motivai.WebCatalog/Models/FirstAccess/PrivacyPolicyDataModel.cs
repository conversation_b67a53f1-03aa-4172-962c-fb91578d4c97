using Motivai.SharedKernel.Helpers.Cryptography;

namespace Motivai.WebCatalog.Models.FirstAccess
{
	public class PrivacyDataModel
	{
		public string ContentId { get; set; }
		public string Version { get; set; }
		public string Content { get; set; }
		public string CookieAcceptanceContent { get; set; }
		public bool SkipFirstAccessRegistrationData {get; set;}

		public PrivacyDataModel(dynamic p)
		{
			this.ContentId = p.contentId?.ToString();
			this.Version = p.version?.ToString();
			this.Content = p.content?.ToString();
			this.CookieAcceptanceContent = p.cookieAcceptanceContent?.ToString();
		}

		public PrivacyDataModel EncryptSensitiveData(ICryptography _cryptography)
		{
			this.ContentId = _cryptography.Encrypt(this.ContentId);
			this.Version = _cryptography.Encrypt(this.Version);
			return this;
		}

		public PrivacyDataModel DecryptSensitiveData(ICryptography _cryptography)
		{
			this.ContentId = _cryptography.Decrypt(this.ContentId);
			this.Version = _cryptography.Decrypt(this.Version);
			return this;
		}
	}
}