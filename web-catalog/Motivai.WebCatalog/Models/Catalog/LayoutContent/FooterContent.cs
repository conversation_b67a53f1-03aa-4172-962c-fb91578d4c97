using System;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;

namespace Motivai.WebCatalog.Models.Catalog.LayoutContent {
    public class FooterContent {
        public string AttendancePeriod { get; set; }
        public string AttendancePhoneNumber { get; set; }
        public string AttendanceMobileNumber { get; set; }
        public string AttendanceEmail { get; set; }
        public bool EnablePolicy { get; set; }
        public bool EnableShippingPolicy { get; set; }
        public bool EnableRegulation { get; set; }
        public bool EnableFaq { get; set; }
        public bool EnableContact { get; set; }

        public static FooterContent BuildFromSettings(CampaignCatalogSettings catalogSettings) {
            return new FooterContent() {
                AttendancePeriod = catalogSettings.AttendancePeriod,
                AttendancePhoneNumber = catalogSettings.AttendancePhoneNumber,
                AttendanceMobileNumber = catalogSettings.AttendanceMobileNumber,
                AttendanceEmail = catalogSettings.AttendanceEmail,
                EnablePolicy = catalogSettings.PagesSettings.EnablePolicy,
                EnableShippingPolicy = catalogSettings.PagesSettings.EnableShippingPolicy,
                EnableRegulation = catalogSettings.PagesSettings.EnableRegulation,
                EnableFaq = catalogSettings.PagesSettings.EnableFaq,
                EnableContact = catalogSettings.PagesSettings.EnableContact
            };
        }
    }
}