using System;
using System.Collections.Generic;
using Motivai.WebCatalog.Models.Product;

namespace Motivai.WebCatalog.Models.Catalog
{
    public class SearchModel
    {
        public int Total { get; set; }

        public List<ProductShowcaseModel> Result { get; set; }

        public List<Item> Partners { get; set; }
        public List<Item> Departments { get; set; }
        public List<Item> Categories { get; set; }
        public List<Item> Subcategories { get; set; }

        public List<Item> Manufacturers { get; set; }
        public List<Item> Colors { get; set; }
        public List<Item> Voltages { get; set; }

        public bool HasResult()
        {
            return Result != null && Result.Count > 0;
        }
    }

    public class Item
    {
        public string Description { get; set; }
        public int Count { get; set; }
    }
}