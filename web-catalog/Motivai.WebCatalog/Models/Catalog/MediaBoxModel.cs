using Motivai.SharedKernel.Domain.Enums;
using Motivai.WebCatalog.Models.Product;

namespace Motivai.WebCatalog.Models.Catalog {
    public class MediaBoxModel {
        public string Id { get; set; }
        public string ContentType { get; set; }

        public string Name { get; set; }
        public short Position { get; set; }
        public bool OpenInNewTab { get; set; }

        public string Link { get; set; }
        public string ImageUrl { get; set; }
        public string MediumImageUrl { get; set; }
        public string SmallImageUrl { get; set; }

        public ProductShowcaseModel FeaturedProduct { get; set; }

        public string Identifier { get; set; }
        public bool Communication { get; set; }
        public CommunicationLocation Location { get; set; }
        public bool? AlwaysShowModal { get; set; }
        public string Message { get; set; }
    }
}