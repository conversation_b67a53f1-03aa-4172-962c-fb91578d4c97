using System;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Strings;

namespace Motivai.WebCatalog.Models.Catalog
{
	public class ContactUsModel
	{
		public Guid? UserId { get; set; }

		public string Name { get; set; }
		public string Document { get; set; }

		public string Email { get; set; }
		public string Phone { get; set; }
		public string Cellphone { get; set; }

		public string Subject { get; set; }
		public string Message { get; set; }

		public void Validate()
		{
			Name.ForNullOrEmpty("Nome é obrigatório");
			Document = Extractor.RemoveMasks(Document);
			if (!Cpf.IsCpf(Document) && !Cnpj.IsCnpj(Document)) {
				throw MotivaiException.ofValidation("CPF/CNPJ inválido.");
			}
			Email.ForNullOrEmpty("E-mail é obrigatório");
			Subject.ForNullOrEmpty("Assunto é obrigatório");
			if (string.IsNullOrEmpty(Phone) && string.IsNullOrEmpty(Cellphone))
			{
				throw MotivaiException.ofValidation("Preencha o telefone ou o celular.");
			}
			Message.ForNullOrEmpty("Mensagem é obrigatória");
		}
	}
}