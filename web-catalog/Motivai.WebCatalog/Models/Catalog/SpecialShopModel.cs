using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Extensions;
using Motivai.WebCatalog.Models.Product;

namespace Motivai.WebCatalog.Models.Catalog
{
	public class SpecialShopModel
	{
		public string Id { get; set; }
		public string Origin { get; set; }
		public string Name { get; set; }
		public string PrincipalBannerImageUrl { get; set; }
		public string LinkBannerImageUrl { get; set; }
		public List<ProductShowcaseModel> Products { get; set; }
		public string Url { get; set; }

		public string PageLinkUrl { get; set; }
		public string PageContent { get; set; }
		public dynamic ViewParametrization { get; set; }

		public void EncodeUrl()
		{
			this.Name = this.Name.Replace("”", "\"").Replace("–", "-").Replace("’", "'");
			this.Url = string.Format("{0}/{1}", Id, Name.EncodeToUrl());
			if (Origin == "GENERAL_PLATFORM")
			{
				Origin = "P";
			}
			else
			{
				Origin = "C";
			}
		}

		public bool HasProducts()
		{
			return !Products.IsNullOrEmpty();
		}
	}
}