using System;

namespace Motivai.WebCatalog.Models.Catalog
{
    public class SearchFilterModel
    {
        /// Filtro de pesquisa
        public string q { get; set; }
        public string[] p { get; set; }
        /// Departamentos
        public string[] d { get; set; }
        /// Categorias
        public string[] c { get; set; }
        /// Subcategorias
        public string[] sb { get; set; }
        public string[] f { get; set; }
        /// Cores
        public string[] cr { get; set; }
        /// Voltagens
        public string[] v { get; set; }
        /// Ordenadores
        public string srt { get; set; }
        /// Inicio da paginacao
        public int? fr { get; set; }
        /// Tamanho da paginacao
        public int? tk { get; set; }
        /// Pontos De
        public decimal? pd { get; set; }
        /// Pontos Ate
        public decimal? pa { get; set; }

        public bool HasAtLeastOneDepartment()
        {
            return d != null && d.Length > 0;
        }

        public bool HasAtLeastOneCategory()
        {
            return c != null && c.Length > 0;
        }
    }
}