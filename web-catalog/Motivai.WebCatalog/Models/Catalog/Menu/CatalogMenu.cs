using System;
using System.Collections.Generic;
using System.Linq;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Features.Segmentations;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Extensions;

namespace Motivai.WebCatalog.Models.Catalog.Menu {
    public class CatalogMenu {
        public bool Hide { get; set; }
        public List<MenuItem> Menu { get; set; }
    }

    public class MenuItem {
        public bool ShowDepartment { get; set; }
        public string Name { get; set; }
        public bool Highlighted { get; set; }
        public int Position { get; set; }
        public string Url { get; set; }
        public string Icon { get; set; }
        public MenuItemType Type { get; set; }
        public List<SubmenuItem> Submenus { get; set; }
        public SegmentationParametrization Segmentations { get; set; }

		public static MenuItem Of(MenuItemType type, string name, string url)
		{
			return new MenuItem()
			{
				ShowDepartment = true,
				Name = name,
				Url = url,
				Type = type
			};
		}

        public static MenuItem OfMigratedOperator(MenuItemType type, string name, string redirectUrl)
        {
            return new MenuItem()
			{
				ShowDepartment = true,
				Name = name,
				Url = redirectUrl,
				Type = type
			};
        }

        public static MenuItem From(MenuModel menu) {
            var item = new MenuItem() {
                ShowDepartment = menu.IsDepartment() || menu.IsSpecialShop(),
                Name = menu.Name,
                Highlighted = menu.Highlighted,
                Position = menu.Position,
                Icon = menu.Icon,
                Url = menu.Url,
                Type = menu.Type,
                Segmentations = menu.Segmentations
            };
            if (!menu.Submenus.IsNullOrEmpty()) {
                item.Submenus = menu.Submenus
                    .Select(i => SubmenuItem.From(i))
                    .OrderBy(s => s.Name)
                    .ToList();
            }
            return item;
        }

        public bool HasSubMenus()
        {
            return !Submenus.IsNullOrEmpty();
        }

        public bool HasSegmentations()
        {
            return this.Segmentations != null;
        }
    }

    public class SubmenuItem {
        public string Name { get; set; }
        public string Url { get; set; }
        public bool Hide { get; set; }
        public MenuItemType Type { get; set; }

        public static SubmenuItem From(MenuModel item) {
            return new SubmenuItem() {
                Name = item.Name,
                Url = item.Url,
                Type = item.Type
            };
        }
    }
}
