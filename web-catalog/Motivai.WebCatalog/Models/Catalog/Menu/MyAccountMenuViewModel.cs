using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.WebCatalog.Models.Pages;

namespace Motivai.WebCatalog.Models.Catalog.Menu
{
	public class MyAccountMenuViewModel : CatalogPageViewModel
	{
		public bool CanLoggedParticipantAccessMenuService { get; set; }

		public bool EnableParticipantData { get { return CampaignSettings.Pages.EnableParticipantData; } }
		public bool EnableExtract { get { return CampaignSettings.Pages.EnableExtract; } }
		public bool EnablePassword { get { return CampaignSettings.Pages.EnablePassword; } }
		public bool EnableAddresses { get { return CampaignSettings.Pages.EnableAddresses; } }
		public bool EnableOrders { get { return CampaignSettings.Pages.EnableOrders; } }
		public bool EnablePointsToExpireAndBlocked { get { return CampaignSettings.Pages.EnablePointsToExpireAndBlocked; } }
		public bool AllowBuyPoints { get { return CampaignSettings.Parametrizations.AllowBuyPoints; } }
		public bool EnableFaq { get { return CampaignSettings.Pages.EnableFaq; } }
		public bool EnableAuthenticationMfa { get { return CampaignSettings.Parametrizations.EnableAuthenticationMfa; } }

		private MyAccountMenuViewModel(CampaignSettingsModel campaignSettings) : base(campaignSettings) { }

		public static MyAccountMenuViewModel InitViewModel(CampaignSettingsModel campaignSettings, bool canLoggedParticipantAccessMenuService)
		{
			return new MyAccountMenuViewModel(campaignSettings)
			{
				CanLoggedParticipantAccessMenuService = canLoggedParticipantAccessMenuService
			};
		}
	}
}
