using System;
using System.Collections.Generic;
using System.Linq;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Models.Pages;
using Motivai.WebCatalog.Models.Partner;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Motivai.WebCatalog.Models.Product {
    public class ProductDetailsModel {
        public string Id { get; set; }
        public string BuId { get; set; }
        public string ElasticsearchId { get; set; }
        public string Name { get; set; }
        public bool Active { get; set; }
        public string Description { get; set; }

        public bool Offline { get; set; }

        public ProcessType ProcessType { get; set; }
        public ProductType ProductType { get; set; }
        public ProductLayoutType LayoutType { get; set; }

        public string PartnerId { get; set; }
        public string Partner { get; set; }
        public PartnerSettingsModel PartnerSettings { get; set; }
        public Guid? FactoryId { get; set; }
        public string FactoryName { get; set; }

        public string Manufacturer { get; set; }

        public string DepartmentId { get; set; }
        public string Department { get; set; }
        public string CategoryId { get; set; }
        public string Category { get; set; }
        public Guid? SubcategoryId { get; set; }
        public string Subcategory { get; set; }

        public string Information { get; set; }
        public List<KeyValue> Characteristics { get; set; }
        public List<KeyValue> TechnicalSpecifications { get; set; }

        public List<Ranking> Rankings { get; set; }

        public Attributes Attributes { get; set; }
        // Dados do SKU principal ou selecionado
        public Guid SkuId { get; set; }
        public bool OnlyOneSku { get; set; }
        public string SkuCode { get; set; }
        public string SkuIntegrationCode { get; set; }
        public string SkuEan { get; set; }

        // Atributos
        public string SkuModel { get; set; }
        public string SkuSize { get; set; }
        public string SkuColor { get; set; }
        public string SkuVoltage { get; set; }

        public List<Images> Images { get; set; }
        public string FirstImage { get; set; }
        public string FirstZoomImage { get; set; }

        public CatalogSettings CatalogSettings { get; set; }
        public bool Available { get; set; }
        public int Quantity { get; set; }
        public Guid? StockParticipantGroupId { get; set; }
        public SkuPrice Prices { get; set; }

        public decimal? AvailableBalance { get; set; }
        // Custo dos pontos necessário para completar o valor do produto
        public decimal? PointsNeededPurchaseCost { get; set; }

        public bool OccurredError { get; set; }
        public string ErrorDescription { get; set; }

        public string Url { get; set; }

        public List<CustomAttribute> CustomAttributes { get; set; }
        public bool EnableCustomPromotionalPrice  { get; set; }

        public decimal GetSalePrice() {
            if (ShowPriceWithPurchaseCost) {
                return AvailableBalance.Value;
            }
            return Prices.Price;
        }

        public bool ShowPriceWithPurchaseCost {
            get {
                return AvailableBalance.HasValue && PointsNeededPurchaseCost.HasValue && PointsNeededPurchaseCost.Value > 0;
            }
        }

        public bool HasFactory() {
            return FactoryId.HasValue && FactoryId.Value != Guid.Empty;
        }

        public void EncodeUrl() {
            this.Url = String.Format("{0}/{1}", ElasticsearchId, Name.EncodeToUrl());
        }

        public bool HasSubcategory() {
            return SubcategoryId != null && SubcategoryId != Guid.Empty;
        }

        public bool HasCharacteristicsOrTechnicalSpecs() {
            return (Characteristics != null && Characteristics.Count > 0)
                || (TechnicalSpecifications != null && TechnicalSpecifications.Count > 0);
        }

        public bool IsAvailable() {
            return Available && Prices != null;
        }

        public bool IsPromotional() {
            return IsAvailable() && Prices.Promotional;
        }

        public decimal GetSalePricePoints() {
            return Prices.Price;
        }

        public void SetMainImage() {
            if (Images == null || Images.Count == 0) {
                FirstImage = FirstZoomImage = "/assets/img/imgError.png";
            } else {
                var image = Images.FirstOrDefault(i => i.IsMasterImage());
                if (image == null)
                    image = Images.FirstOrDefault();
                FirstImage = image.GetLargest();
                FirstZoomImage = image.GetImageForZoom();
                Images = Images.OrderBy(i => i.Order).ToList();
            }
        }

        public bool CanBuyByRankings(List<Guid> participantRankings) {
            if (Rankings != null) {
                return Rankings.Select(r => Guid.Parse(r.Id))
                    .Intersect(participantRankings).Any();
            }
            return true;
        }

        public bool HasRankings() {
            return Rankings != null && Rankings.Count > 0;
        }

        public bool HasCustomAttributes() {
            return CustomAttributes != null && CustomAttributes.Count > 0;
        }

        public string ProductModelLabel
		{
			get
			{
				return CatalogSettings == null || string.IsNullOrWhiteSpace(CatalogSettings.ProductModelLabel) ? "Modelo" : CatalogSettings.ProductModelLabel;
			}
		}

         public string ProductVoltageLabel
		{
			get
			{
				return CatalogSettings == null || string.IsNullOrWhiteSpace(CatalogSettings.ProductVoltageLabel) ? "Voltagens" : CatalogSettings.ProductVoltageLabel;
			}
		}

         public string ProductColorLabel
		{
			get
			{
				return CatalogSettings == null ||  string.IsNullOrWhiteSpace(CatalogSettings.ProductColorLabel) ? "Cor" : CatalogSettings.ProductColorLabel;
			}
		}

         public string ProductSizeLabel
		{
			get
			{
				return CatalogSettings == null || string.IsNullOrWhiteSpace(CatalogSettings.ProductSizeLabel) ? "Tamanho" : CatalogSettings.ProductSizeLabel;
			}
		}

         public string ProductInformationTabTitle
		{
			get
			{
				return CatalogSettings == null || string.IsNullOrWhiteSpace(CatalogSettings.ProductInformationTabTitle) ? "Informações sobre o produto" : CatalogSettings.ProductInformationTabTitle;
			}
		}

         public string ProductTechnicalSpecificationsTabTitle
		{
			get
			{
				return  CatalogSettings == null || string.IsNullOrWhiteSpace(CatalogSettings.ProductTechnicalSpecificationsTabTitle) ? "Especificações técnicas do produto" : CatalogSettings.ProductTechnicalSpecificationsTabTitle;
			}
		}

    }

    public class SkuPrice {
        public bool Promotional { get; set; }
        public decimal? PriceFrom { get; set; }
        public decimal Price { get; set; }
        public decimal PriceCurrency { get; set; }
        public CoinName CoinName { get; set; }
        public DetailedPrice DetailedPrice { get; set; }
        public bool DynamicPrice { get; set; }

        ///<summary>
        /// Indicação do responsável para atribuição do preço dinamico
        ///</summary>
        [BsonRepresentation(BsonType.String)]
        public PriceSettersType? DynamicPricingSetter { get; set; }

        public decimal? DynamicPriceMinimumValue { get; set; }

        public decimal? DynamicPriceMaximumValue { get; set; }

        public Amount GetUnitPriceForCart() {
            if (DynamicPrice && !IsDynamicPriceDefinedByParticipant()) {
                return Amount.Zero();
            }
            if (DynamicPrice && IsDynamicPriceDefinedByParticipant()) {
                return Amount.Of(Price, Price);
            }

            return GetSalePrice();
        }

        public bool IsDynamicPriceDefinedByParticipant() {
            return DynamicPricingSetter ==  PriceSettersType.PARTICIPANT;
        }

        public Amount GetSalePrice() {
            return Amount.Of(PriceCurrency, Price);
        }
    }

    public class Attributes {
        public string SkuCode { get; set; }
        public string[] Models { get; set; }
        public string[] Sizes { get; set; }
        public string[] Colors { get; set; }
        public string[] Voltages { get; set; }
        public List<CustomAttribute> CustomAttributes { get; set; }
        public bool DynamicPrice { get; set; }

        ///<summary>
        /// Indicação do responsável para atribuição do preço dinamico
        ///</summary>
        [BsonRepresentation(BsonType.String)]
        public PriceSettersType? DynamicPricingSetter { get; set; }

        public decimal? DynamicPriceMinimumValue { get; set; }

        public decimal? DynamicPriceMaximumValue { get; set; }

        public bool IsDynamicPriceDefinedByParticipant() {
            return DynamicPrice && DynamicPricingSetter ==  PriceSettersType.PARTICIPANT;
        }



        public bool HasModels() {
            return Models != null && Models.Length > 0;
        }

        public bool HasSizes() {
            return Sizes != null && Sizes.Length > 0;
        }

        public bool HasColors() {
            return Colors != null && Colors.Length > 0;
        }

        public bool HasVoltages() {
            return Voltages != null && Voltages.Length > 0;
        }
    }
}
