namespace Motivai.WebCatalog.Models.Product
{
    public class AvailabilityConsultModel
    {
        ///<summary>
        /// ElasticsearchId
        ///</summary>
        public string Id { get; set; }
        ///<summary>
        /// Código SKU
        ///</summary>
        public string Md { get; set; }
        ///<summary>
        /// Quantidade
        ///</summary>
        public int Qt { get; set; }
        ///<summary>
        /// Preço definido pelo participante em um produto com preço dinâmico
        ///</summary>
        public string PriceDefinedByParticipant { get; set; }
    }
}