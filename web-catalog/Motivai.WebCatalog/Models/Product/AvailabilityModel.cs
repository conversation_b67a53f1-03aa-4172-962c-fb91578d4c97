using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Motivai.WebCatalog.Models.Product
{
    public class AvailabilityModel
    {
        public bool Available { get; set; }
        public bool Promotional { get; set; }
        public decimal? PriceFrom { get; set; }
        public decimal Price { get; set; }
        public CoinName CoinName { get; set; }
        public bool DynamicPrice { get; set; }

        ///<summary>
        /// Indicação do responsável para atribuição do preço dinamico
        ///</summary>
        [BsonRepresentation(BsonType.String)]
        public PriceSettersType? DynamicPricingSetter { get; set; }

        public decimal? DynamicPriceMinimumValue { get; set; }

        public decimal? DynamicPriceMaximumValue { get; set; }

    }
}