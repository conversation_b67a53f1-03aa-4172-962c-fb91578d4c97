using System;
using Motivai.SharedKernel.Helpers;

namespace Motivai.WebCatalog.Models.Product {
    public class Marketplace {
        public string ElasticId { get; set; }
        public string Name { get; set; }
        public string Store { get; set; }
        public decimal Price { get; set; }
        public string Url { get; set; }

        public decimal? AvailableBalance { get; set; }
        public decimal? PointsNeededPurchaseCost { get; set; }

        public void EncodeUrl() {
            this.Url = String.Format("{0}/{1}", ElasticId, Name.EncodeToUrl());
        }
        public bool ShowPriceWithPurchaseCost {
            get {
                return AvailableBalance.HasValue && PointsNeededPurchaseCost.HasValue && PointsNeededPurchaseCost.Value > 0;
            }
        }
    }
}