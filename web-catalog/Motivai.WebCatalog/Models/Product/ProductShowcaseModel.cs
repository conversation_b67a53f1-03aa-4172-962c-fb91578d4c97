using System.Collections.Generic;

using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Helpers;

namespace Motivai.WebCatalog.Models.Product
{
    public class ProductShowcaseModel
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string ImageUrl { get; set; }
        public string Url { get; set; }

        public bool? DynamicPrice { get; set; }
        public string DynamicPriceDescription { get; set; }

        public bool Available { get; set; }
        public decimal? PriceFrom { get; set; }
        public decimal Price { get; set; }
        public bool Promotional { get; set; }

        public decimal? AvailableBalance { get; set; }
        // Custo dos pontos necessário para completar o valor do produto
        public decimal? PointsNeededPurchaseCost { get; set; }

        public CoinName CoinName { get; set; }
        public List<Ranking> Rankings { get; set; }

        public bool IsPromotional()
        {
            return Promotional && PriceFrom.HasValue;
        }

        public bool ShowPriceWithPurchaseCost
        {
            get
            {
                return AvailableBalance.HasValue && PointsNeededPurchaseCost.HasValue;
            }
        }

        public void EncodeUrl()
        {
            this.Name = this.Name.Replace("”", "\"").Replace("–", "-").Replace("’", "'");
            this.Url = string.Format("{0}/{1}", Id, Name.EncodeToUrl());
            if (string.IsNullOrEmpty(ImageUrl))
            {
                ImageUrl = "/assets/img/imgError.png";
            }
        }
    }
}