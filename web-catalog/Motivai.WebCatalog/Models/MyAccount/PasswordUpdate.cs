using System;
using System.Linq;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;

namespace Motivai.WebCatalog.Models.MyAccount
{
    public class PasswordUpdate
    {
        public string OldPassword { get; set; }
        public string NewPassword { get; set; }
        public string ConfirmPassword { get; set; }

        public static PasswordUpdate Of(string oldPassword, string newPassword, string confirmPassword)
        {
            return new PasswordUpdate()
            {
                OldPassword = oldPassword,
                NewPassword = newPassword,
                ConfirmPassword = confirmPassword
            };
        }

        public void Validate()
        {
            OldPassword.ForNullOrEmpty("Por favor, preencha a senha atual");
            NewPassword.ForNullOrEmpty("Por favor, preencha a nova atual");
            ConfirmPassword.ForNullOrEmpty("Por favor, preenha o confirmar a nova senha");

            if (NewPassword.Length < 6)
            {
                throw MotivaiException.ofValidation("A nova senha deve ter no mínimo 6 caracteres");
            }

            if (!NewPassword.Equals(ConfirmPassword))
            {
                throw MotivaiException.ofValidation("A confirmação de senha deve ser igual a nova senha.");
            }

            if (!NewPassword.Any(char.IsDigit))
            {
                throw MotivaiException.ofValidation("A nova senha deve conter no mínimo uma número");
            }

            if (!NewPassword.Any(char.IsLower))
            {
                throw MotivaiException.ofValidation("A nova senha deve conter no mínimo uma letra minúscula");
            }

            if (!NewPassword.Any(char.IsUpper))
            {
                throw MotivaiException.ofValidation("A nova senha deve conter no mínimo uma letra maiúscula");
            }
        }
    }
}