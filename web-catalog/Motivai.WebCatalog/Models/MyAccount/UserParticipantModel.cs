using System;
using Motivai.SharedKernel.Domain.Enums;

namespace Motivai.WebCatalog.Models.MyAccount
{
    public class UserParticipantModel
    {
        public Guid SessionId { get; set; }
        public Guid UserId { get; set; }
        public Guid ParticipantId { get; set; }
        public string Document { get; set; }
        public PersonType Type { get; set; }
        public string Timezone { get; set; }
        public bool Active { get; set; }
        public string Email { get; set; }
        public string Name { get; set; }
        public string PictureUrl { get; set; }
        public decimal Balance { get; set; }
        public bool FirstAccess { get; set; }
        public Guid CampaignId { get; set; }
        public string CampaignUrl { get; set; }
        public Guid? RankingId { get; set; }

        ///<summary>
        /// Dados do operador da conta.
        ///</summary>
        public Guid? AccountOperatorId { get; set; }
        public Guid? AccountOperatorLoginId { get; set; }
        public string AccountOperatorDocument { get; set; }
        public string AccountOperatorEmail { get; set; }
        public AuthenticationAccess AuthenticationAccess { get; set; }
        public string RedirectUrl { get; set; }

        ///<summary>
        /// Flags da autenticação MFA.
        ///</summary>
        public bool NeedAuthenticationMfa { get; set; }
        public bool NeedSetupAuthenticationMfa { get; set; }

        public bool NeedAcceptPrivacyPolicy { get; set; }

        public void setRedirectUrl(string redirectUrl)
        {
            this.RedirectUrl = redirectUrl;
        }
    }

    public class AuthenticationAccess
    {
		public string AuthenticationType { get; set; }
		public bool Migrated { get; set; }
		public DateTime MigrationDate { get; set; }
	}
}
