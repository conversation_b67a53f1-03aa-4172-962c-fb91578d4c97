using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;

namespace Motivai.WebCatalog.Models.MyAccount {
	// Extrato de Pontos
	public class ExtractModel {
		public CoinName CoinName { get; set; }
		public List<ExtractRegister> Transactions { get; set; }
		public string TotalAmount { get; set; }
	}

	public class ExtractRegister {
		public DateTime ProcessingDate { get; set; }
		public string Description { get; set; }
		public string Amount { get; set; }
		public dynamic ExtraData { get; set; }
	}
}