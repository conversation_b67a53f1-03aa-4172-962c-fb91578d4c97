using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;

namespace Motivai.WebCatalog.Models.MyAccount
{
    public class BlockedPointsSummary
    {
        public CoinName CoinName { get; set; }
        public List<BlockedPointsDetails> BlockedPoints { get; set; }
        public string TotalPoints { get; set; }
    }

    public class BlockedPointsDetails
    {
        public string Description { get; set; }
        public string ProcessingDate { get; set; }
        public string Amount { get; set; }
    }
}