using System;

namespace Motivai.WebCatalog.Models.MyAccount
{
    public class BalanceResumeModel
    {
        public DateTime? LastAccess { get; set; }
        public decimal Balance { get; set; }
        public PointsSummary ExpiringPoints { get; set; }
        public PointsSummary BlockedPoints { get; set; }
    }

    public class PointsSummary
    {
        public DateTime Date { get; set; }
        public decimal Points { get; set; }

        public static PointsSummary Zero()
        {
            return new PointsSummary()
            {
                Date = DateTime.UtcNow,
                Points = 0
            };
        }
    }
}