using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;

namespace Motivai.WebCatalog.Models.MyAccount
{
    public class MechanicsSummary
    {
        public CoinName CoinName { get; set; }
        public List<MechanicDetails> Mechanics { get; set; }
        public decimal TotalPoints { get; set; }

        public static MechanicsSummary Zero()
        {
            return new MechanicsSummary()
            {
                TotalPoints = 0,
                Mechanics = new List<MechanicDetails>()
            };
        }
    }

    public class MechanicDetails
    {
        public string Description { get; set; }
        public decimal TotalPoints { get; set; }
        public decimal AvailablePoints { get; set; }
        public decimal BlockedPoints { get; set; }
        public decimal UsedPoints { get; set; }
        public decimal ExpiringPoints { get; set; }
        public DateTime? ExpirationDate { get; set; }
    }
}