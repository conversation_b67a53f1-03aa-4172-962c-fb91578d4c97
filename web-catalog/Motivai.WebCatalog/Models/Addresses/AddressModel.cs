using System;
using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Strings;
using Motivai.SharedKernel.Helpers.Values;
using Newtonsoft.Json;

namespace Motivai.WebCatalog.Models.Addresses
{
	public class AddressModel
	{
		public string Id { get; set; }

		public bool MainAddress { get; set; }
		public bool Integration { get; set; }
		public bool FilledManually { get; set; }

		public string AddressName { get; set; }
		public string Cep { get; set; }
		public string Street { get; set; }
		public string Number { get; set; }
		public string Complement { get; set; }
		public string Neighborhood { get; set; }
		public string City { get; set; }
		public string State { get; set; }
		public string InitialState { get; set; }
		public string Reference { get; set; }

		[JsonProperty("cepSearchError")]
		public bool? CepSearchError { get; set; }
		[JsonProperty("cepSearchMessage")]
		public string CepSearchMessage { get; set; }

		public string Uf
		{
			get
			{
				return this.InitialState;
			}
			set
			{
				this.InitialState = value;
			}
		}

		public ReceiverModel Receiver { get; set; }

		public void Validate()
		{
			AddressName.ForNullOrEmptyDefaultMessage("Nome do endereço");
			Cep.ForNullOrEmptyDefaultMessage("CEP");
			Number.ForNullOrEmptyDefaultMessage("Número");
			if (FilledManually)
			{
				Street.ForNullOrEmptyDefaultMessage("Logradouro");
				Neighborhood.ForNullOrEmptyDefaultMessage("Bairro");
			}
			Complement.ForNullOrEmptyDefaultMessage("Complemento");
			City.ForNullOrEmptyDefaultMessage("Cidade");
			State.ForNullOrEmptyDefaultMessage("Estado");
			Reference.ForNullOrEmptyDefaultMessage("Ponto de referência");

			if (string.IsNullOrEmpty(InitialState) && State != null)
			{
				InitialState = BrazilUfHelper.GetUfByStateName(State);
			}
			if (string.IsNullOrEmpty(State) && InitialState != null)
			{
				State = BrazilUfHelper.GetStateNameByUf(InitialState);
			}

			if (Receiver != null)
				Receiver.Validate();
		}

		public static AddressModel Of(Address address)
		{
			if (address == null) return new AddressModel();
			return new AddressModel()
			{
				Id = address.Id.ToString(),
				MainAddress = address.MainAddress,
				Integration = address.CreatedByIntegration,
				FilledManually = address.IsFilledManually(),
				AddressName = address.AddressName,
				Cep = address.Cep,
				Street = address.Street,
				Number = address.Number,
				Complement = address.Complement,
				Neighborhood = address.Neighborhood,
				City = address.City,
				State = address.State,
				InitialState = address.InitialState,
				Reference = address.Reference,
				Receiver = new ReceiverModel()
				{
					Name = address.ReceiverName,
					Email = address.ReceiverEmail,
					Telephone = address.ReceiverPhone,
					Cellphone = address.ReceiverMobilePhone,
					Cpf = address.ReceiverCpf
				}
			};
		}

		public Address ToEntity()
		{
			var address = new Address()
			{
				FilledManually = this.FilledManually,
				AddressName = this.AddressName,
				MainAddress = this.MainAddress,
				Cep = this.Cep,
				Street = this.Street,
				Number = this.Number,
				Complement = this.Complement,
				Neighborhood = this.Neighborhood,
				City = this.City,
				State = this.State,
				InitialState = this.InitialState,
				Reference = this.Reference,
				CepSearchError = this.CepSearchError,
				CepSearchMessage = this.CepSearchMessage
			};
			if (this.Receiver != null)
			{
				address.ReceiverName = this.Receiver.Name;
				address.ReceiverEmail = this.Receiver.Email;
				address.ReceiverPhone = this.Receiver.Telephone;
				address.ReceiverMobilePhone = this.Receiver.Cellphone;
				address.ReceiverCpf = this.Receiver.Cpf;
			}
			return address;
		}
	}

	public class ReceiverModel
	{
		public string Name { get; set; }
		public string Cpf { get; set; }
		public string Telephone { get; set; }
		public string Cellphone { get; set; }
		public string Email { get; set; }

		public string GetTelefoneFormatado()
		{
			return TelephoneHelper.FormatNumber(Telephone);
		}

		public string GetCelularFormatado()
		{
			return TelephoneHelper.FormatNumber(Cellphone);
		}

		public string GetDddTelefone()
		{
			if (String.IsNullOrEmpty(Telephone))
				return null;
			return Telephone.Substring(0, 2);
		}

		public string GetNumeroTelefone()
		{
			if (String.IsNullOrEmpty(Telephone))
				return null;
			return Telephone.Substring(2);
		}

		public string GetDddCelular()
		{
			return Cellphone.Substring(0, 2);
		}

		public string GetNumeroCelular()
		{
			return Cellphone.Substring(2);
		}

		private void RemoveMasks()
		{
			Cpf = Extractor.RemoveMasks(Cpf);
			Telephone = Extractor.RemoveMasks(Telephone);
			Cellphone = Extractor.RemoveMasks(Cellphone);
		}

		public void Validate()
		{
			RemoveMasks();

			if (String.IsNullOrEmpty(Name))
				throw MotivaiException.ofValidation("Nome é obrigatório.");

			if (String.IsNullOrEmpty(Cpf))
				throw MotivaiException.ofValidation("CPF é obrigatório.");
			if (!Motivai.SharedKernel.Domain.ValuesObject.Cpf.IsCpf(Cpf))
				throw MotivaiException.ofValidation("CPF inválido.");

			if(!String.IsNullOrEmpty(Telephone) && !ValueValidator.IsTelefone(Telephone)) {
				throw MotivaiException.ofValidation("Número de telefone inválido.");
			}

			if (!ValueValidator.IsCelular(Cellphone))
				throw MotivaiException.ofValidation("Número de celular inválido.");

			if (!ValueValidator.IsEmail(Email))
				throw MotivaiException.ofValidation("E-mail inválido.");
		}
	}
}
