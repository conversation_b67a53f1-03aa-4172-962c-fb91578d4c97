using Motivai.SharedKernel.Domain.Entities.References.Contacts;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;

namespace Motivai.WebCatalog.Models.Addresses {
    public class AddressUpdate : Address {
        public AccountOperator AccountOperator { get; set; }
        public LocationInfo LocationInfo { get; set; }

        public Address ToEntity() {
            var address = new Address();
            address.CopyFrom(this);
            return address;
        }

        public static AddressUpdate FromModel(AddressModel addressModel) {
            return new AddressUpdate {
                FilledManually = addressModel.FilledManually,
                AddressName = addressModel.AddressName,
                MainAddress = addressModel.MainAddress,
                Cep = addressModel.Cep,
                Street = addressModel.Street,
                Number = addressModel.Number,
                Complement = addressModel.Complement,
                Neighborhood = addressModel.Neighborhood,
                City = addressModel.City,
                State = addressModel.State,
                InitialState = addressModel.InitialState,
                Reference = addressModel.Reference,
                CepSearchError = addressModel.CepSearchError,
                CepSearchMessage = addressModel.CepSearchMessage,
                ReceiverName = addressModel.Receiver.Name,
                ReceiverEmail = addressModel.Receiver.Email,
                ReceiverPhone = addressModel.Receiver.Telephone,
                ReceiverMobilePhone = addressModel.Receiver.Cellphone,
                ReceiverCpf = addressModel.Receiver.Cpf
            };
        }
    }
}