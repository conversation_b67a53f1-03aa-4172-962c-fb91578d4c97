using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.WebCatalog.Models.PointsPurchase;

namespace Motivai.WebCatalog.Models.Pages.ComprarPontos
{
    public class PointsPurchaseViewModel : CatalogPageViewModel
    {
        public PaymentsOptionsModel PaymentOptions { get; set; }

        public PointsPurchaseViewModel(CampaignSettingsModel campaignSettings, PaymentsOptionsModel paymentOptions)
            : base(campaignSettings)
        {
            PaymentOptions = paymentOptions;
        }
    }
}