using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;

namespace Motivai.WebCatalog.Models.Pages.Pedido
{
	public class ResumoViewModel : CatalogPageViewModel
	{
		public OrderConfirmationPageModel OrderConfirmationPageModel { get; set; }

		public ResumoViewModel(CampaignSettingsModel campaignSettings, OrderConfirmationPageModel orderConfirmationPageModel)
			: base(campaignSettings)
		{
			OrderConfirmationPageModel = orderConfirmationPageModel;
		}
	}
}