using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Helpers.Extensions;

namespace Motivai.WebCatalog.Models.Pages
{
	public class FirstAccessPageModel
	{
		public bool SkipFirstAccessRegistrationData { get; set; }

		public bool CanUploadPicture { get; set; }
		public bool HasNewsletters { get; set; }
		public bool RequiredAddressDetails { get; set; }
		public bool HasRegulation { get; set; }
		public bool HasAuthenticationMfa { get; }
		public bool EnableMetadataFields { get; set; }

		public bool EnableAccountRepresentative { get; set; }
		public bool EnableDocumentFieldAtFirstAccess { get; set; }
		public bool? EnableCardRegisterAtFirstAccess { get; set; }
		public ParticipantData ParticipantSettings { get; set; }


		public bool EnableMetadata
		{
			get
			{
				return EnableMetadataFields && ParticipantSettings.EnableMetadataAtFirstAccess
					&& !ParticipantSettings.Metadata.IsNullOrEmpty();
			}
		}

		public FirstAccessPageModel(CampaignSettingsModel settings, CampaignCatalogSettings catalogSettings, ParticipantData firstAccessSettings)
		{
			this.CanUploadPicture = settings.Parametrizations.CanUploadPicture;
			this.HasNewsletters = settings.Parametrizations.HasNewsletters;
			this.RequiredAddressDetails = settings.Parametrizations.RequiredShippingAddressDetails;
			this.HasRegulation = catalogSettings.PagesSettings.EnableRegulation;
			this.HasAuthenticationMfa = settings.Parametrizations.EnableAuthenticationMfa;
			this.EnableMetadataFields = settings.Parametrizations.EnableMetadataFields;
			this.ParticipantSettings = firstAccessSettings;
			this.EnableAccountRepresentative = settings.Parametrizations.EnableAccountRepresentative;
			this.EnableDocumentFieldAtFirstAccess = settings.Parametrizations.EnableDocumentFieldAtFirstAccess;
			this.EnableCardRegisterAtFirstAccess = settings.Parametrizations.EnableCardRegisterAtFirstAccess;
		}
	}
}