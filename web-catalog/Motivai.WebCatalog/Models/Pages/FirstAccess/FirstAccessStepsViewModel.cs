using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;

namespace Motivai.WebCatalog.Models.Pages.FirstAccess
{
	public class FirstAccessStepsViewModel
	{
		public CampaignSettingsModel CampaignSettings { get; set; }
		public FirstAccessSteps CurrentStep { get; set; }

		public dynamic PageModel { get; set; }

		private int _index;
		public FirstAccessStepsViewModel(CampaignSettingsModel campaignSettings, FirstAccessSteps currentStep, object model = null)
		{
			CampaignSettings = campaignSettings;
			CurrentStep = currentStep;
			PageModel = model;
		}

		public bool IsRegulationStepActive
		{
			get
			{
				return CampaignSettings.Pages.EnableRegulation;
			}
		}

		public bool IsRegulationStep
		{
			get
			{
				return CurrentStep == FirstAccessSteps.REGULATION;
			}
		}

		public bool IsPrivacyPolicyStep
		{
			get
			{
				return CurrentStep == FirstAccessSteps.PRIVACY_POLICY;
			}
		}

		public bool IsRegistrationDataStepActive
		{
			get
			{
				return CampaignSettings.Parametrizations.SkipFirstAccessRegistrationData != true;
			}
		}

		public bool IsRegistrationDataStep
		{
			get
			{
				return CurrentStep == FirstAccessSteps.REGISTRATION_DATA;
			}
		}

		public bool IsCardStepActive
		{
			get
			{
				return CampaignSettings.Parametrizations.EnableCardRegisterAtFirstAccess ?? false;
			}
		}

		public bool IsCardStep
		{
			get
			{
				return CurrentStep == FirstAccessSteps.CARD;
			}
		}


		public int GetAndIncrement()
		{
			return ++_index;
		}
	}

	public enum FirstAccessSteps
	{
		START,
		REGULATION,
		PRIVACY_POLICY,
		REGISTRATION_DATA,
		CARD
	}
}