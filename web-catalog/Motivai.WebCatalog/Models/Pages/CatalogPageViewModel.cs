using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;

namespace Motivai.WebCatalog.Models.Pages
{
	public class CatalogPageViewModel
	{
		public CampaignSettingsModel CampaignSettings { get; set; }
		public MarketplaceViewStringsParametrization ViewStrings { get; set; }

		public bool IsMarketplace
		{
			get
			{
				return CampaignSettings.IsMarketplace();
			}
		}

		public bool IsRewards
		{
			get
			{
				return CampaignSettings.IsRewards();
			}
		}

		public ParametrizationsModel Parametrizations
		{
			get
			{
				return CampaignSettings.Parametrizations;
			}
		}

		public CatalogPageViewModel(CampaignSettingsModel campaignSettings)
		{
			CampaignSettings = campaignSettings;
			ViewStrings = new MarketplaceViewStringsParametrization(campaignSettings);
		}

		public static CatalogPageViewModel OfCampaignSettings(CampaignSettingsModel campaignSettings)
		{
			return new CatalogPageViewModel(campaignSettings);
		}
	}
}