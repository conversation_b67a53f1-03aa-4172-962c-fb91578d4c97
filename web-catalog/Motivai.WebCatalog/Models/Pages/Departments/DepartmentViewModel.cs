using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.WebCatalog.Models.Catalog;
using Motivai.WebCatalog.Models.MyAccount;

namespace Motivai.WebCatalog.Models.Pages.Departments
{
	public class DepartmentViewModel : CatalogPageViewModel
	{
		public DepartmentModel Department { get; set; }

		public DepartmentViewModel(CampaignSettingsModel campaignSettings, DepartmentModel department)
			: base(campaignSettings)
		{
			Department = department;
		}
	}
}