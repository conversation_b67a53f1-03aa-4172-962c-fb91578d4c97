using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.WebCatalog.Models.Catalog;
using Motivai.WebCatalog.Models.MyAccount;

namespace Motivai.WebCatalog.Models.Pages.Departments
{
	public class CategoryViewModel : CatalogPageViewModel
	{
		public CategoryModel Category { get; set; }

		public CategoryViewModel(CampaignSettingsModel campaignSettings, CategoryModel category)
			: base(campaignSettings)
		{
			Category = category;
		}
	}
}