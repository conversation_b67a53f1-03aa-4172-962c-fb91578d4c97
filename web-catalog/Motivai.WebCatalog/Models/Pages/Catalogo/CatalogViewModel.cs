using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.WebCatalog.Models.Catalog;

namespace Motivai.WebCatalog.Models.Pages.Catalogo
{
	public class CatalogViewModel : CatalogPageViewModel
	{
		public SearchModel Search { get; set; }

		public CatalogViewModel(CampaignSettingsModel campaignSettings, SearchModel search)
			: base(campaignSettings)
		{
			Search = search;
		}
	}
}