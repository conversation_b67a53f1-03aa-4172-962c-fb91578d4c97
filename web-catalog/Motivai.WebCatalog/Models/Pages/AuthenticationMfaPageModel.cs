using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Users;

namespace Motivai.WebCatalog.Models.Pages
{
    public class AuthenticationMfaPageModel
    {
        public string Email { get; set; }
        public string MobilePhone { get; set; }
        public ParticipantAuthenticationMfaFormat AuthenticationMfaFormat { get; set; }
        public bool FromValidate { get; set; }
        public CampaignSettingsModel CampaignSettings { get; set; }

        public bool IsEmailOnly() {
            return AuthenticationMfaFormat == ParticipantAuthenticationMfaFormat.EMAIL_ONLY;
        }
    }
}