using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;

namespace Motivai.WebCatalog.Models.Pages
{
	public class MarketplaceViewStringsParametrization
	{
		public bool IsMarketplace { get; set; }

		public MarketplaceViewStringsParametrization(CampaignSettingsModel campaignSettingsModel)
		{
			this.IsMarketplace = campaignSettingsModel.IsMarketplace();
		}

		public string BalanceDescription
		{
			get
			{
				return IsMarketplace ? "Saldo" : "Pontos";
			}
		}

		public string BalanceDescriptionLowerCase
		{
			get
			{
				return IsMarketplace ? "saldo" : "pontos";
			}
		}

		public string OrderTotalDescription
		{
			get
			{
				return IsMarketplace ? "Valor" : "Pontos";
			}
		}

		public string LastOrdersDescription
		{
			get
			{
				return IsMarketplace ? "Últimas Compras" : "Últimos Resgates";
			}
		}

		public string BuyActionDescription
		{
			get
			{
				return IsMarketplace ? "Comprar" : "Resgatar";
			}
		}

		public string BuyActionDescriptionLowerCase
		{
			get
			{
				return IsMarketplace ? "comprar" : "resgatar";
			}
		}

		public string BuyActionTypeDescription
		{
			get
			{
				return IsMarketplace ? "Compra" : "Resgate";
			}
		}
		public string BuyActionTypeDescriptionLowercase
		{
			get
			{
				return IsMarketplace ? "compra" : "resgate";
			}
		}

		public string MostRedeemedProductsDescriptionLowerCase
		{
			get
			{
				return IsMarketplace ? "comprados" : "resgatados";
			}
		}

		public string QuickBuyDescription
		{
			get
			{
				return IsMarketplace ? "Compra Rápida" : "Resgate Rápido";
			}
		}

		public string RangePointsDescription
		{
			get
			{
				return IsMarketplace ? "Preço" : "Pontuação";
			}
		}

		public string BuyerDescription
		{
			get
			{
				return IsMarketplace ? "comprador" : "premiado";
			}
		}

		public string BalancePurchaseActionDescription
		{
			get
			{
				return IsMarketplace ? "Adicionar" : "Comprar";
			}
		}

		public string BalancePurchaseActionDescriptionLowerCase
		{
			get
			{
				return IsMarketplace ? "adicionar" : "comprar";
			}
		}
	}
}