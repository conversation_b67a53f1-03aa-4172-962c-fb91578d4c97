using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.WebCatalog.Models.Catalog;
using Motivai.WebCatalog.Models.MyAccount;

namespace Motivai.WebCatalog.Models.Pages.Produto
{
	public class ProductViewModel : CatalogPageViewModel
	{
		public ProductPageModel Product { get; set; }

		public ProductViewModel(CampaignSettingsModel campaignSettings, ProductPageModel product)
			: base(campaignSettings)
		{
			Product = product;
		}
	}
}
