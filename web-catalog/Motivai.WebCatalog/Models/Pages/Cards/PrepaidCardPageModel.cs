using System;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.WebCatalog.Models.Pages.Cards;

namespace Motivai.WebCatalog.Models.Pages.Cards
{
	public class PrepaidCardPageModel
	{
		public PrepaidCardType CardType { get; set; }
		public bool AllowChangeShippingAddress { get; set; }
		public PrepaidCardParametrizations Parametrizations { get; set; }

		public MarketplaceViewStringsParametrization ViewStrings { get; set; }

		public string CardTypeDescription
		{
			get
			{
				return CardType == PrepaidCardType.WITHDRAWABLE ? "Com Saque" : "Sem Saque";
			}
		}

		public bool IsDisableShippingAddress
		{
			get
			{
				if (this.Parametrizations == null)
				{
					return false;
				}
				return this.Parametrizations.DisableShippingAddress.HasValue && this.Parametrizations.DisableShippingAddress.Value;
			}
		}

		public static PrepaidCardPageModel Of(PrepaidCardType cardType, CampaignSettingsModel campaignSettings,
			PrepaidCardParametrizations parametrizations)
		{
			return new PrepaidCardPageModel()
			{
				CardType = cardType,
				AllowChangeShippingAddress = campaignSettings.Parametrizations.AllowChangeShippingAddress,
				Parametrizations = parametrizations,
				ViewStrings = new MarketplaceViewStringsParametrization(campaignSettings)
			};
		}
	}
}