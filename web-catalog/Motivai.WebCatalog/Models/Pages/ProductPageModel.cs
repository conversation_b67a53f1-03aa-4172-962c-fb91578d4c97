using System;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.WebCatalog.Models.Product;

namespace Motivai.WebCatalog.Models.Pages
{
    public class ProductPageModel {
        public bool Rewards { get; set; }
        public bool Marketplace { get; set; }
        public string CoinPrefix { get; set; }
        public string CoinSufix { get; set; }

        public bool HasShippingCalculation { get; set; }
        public bool CanSelectShippingAddress { get; set; }
        public bool CanSelectFactory { get; set; }
        public bool CanQuicklyBuy { get; set; }
        public bool EnableMatchingByEan { get; set; }

        public MarketplaceViewStringsParametrization ViewStrings { get; set; }

        public ProductDetailsModel Product { get; private set; }

        public ProductPageModel(ProductDetailsModel product) {
            this.Product = product;
        }

        public void SetParameters(CampaignSettingsModel settings) {
            this.Rewards = settings.IsRewards();
            this.Marketplace = settings.IsMarketplace();

            this.HasShippingCalculation = settings.Parametrizations.EnableShippingCalculation;
            if (Product.ProductType != ProductType.ValeVirtual) {
                this.CanSelectShippingAddress = settings.Parametrizations.EnableSelectShippingAddress;
            }
            this.CanSelectFactory = settings.Parametrizations.EnableSelectProductFactory;
            this.CanQuicklyBuy = settings.Parametrizations.EnableQuickRedeem;
            this.EnableMatchingByEan = settings.Parametrizations.EnableMatchingByEan;
        }
    }
}