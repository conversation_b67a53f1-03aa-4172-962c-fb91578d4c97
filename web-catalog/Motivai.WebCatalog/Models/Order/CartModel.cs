using System;
using System.Collections.Generic;
using System.Linq;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Generators;
using Motivai.WebCatalog.Models.Addresses;
using Motivai.WebCatalog.Models.Partner;
using Motivai.WebCatalog.Models.Product;
using Motivai.WebCatalog.Models.Session;

namespace Motivai.WebCatalog.Models.Order
{
    /// <summary>
    /// Carrinho de compras do participante.
    /// </summary>
    public class CartModel
	{
		public Guid OrderId { get; set; }
		public CampaignType Type { get; set; }
		public string InternalOrderNumber { get; set; }
		public DateTime CreationDate { get; set; }
		public int? TimezoneOffset { get; set; }
		public string Timezone { get; set; }

		public Guid CampaignId { get; set; }
		public Guid UserId { get; set; }
		public Guid ParticipantId { get; set; }
		public AccountOperator AccountOperator { get; set; }
		public Guid SessionId { get; set; }
		public string SessionTrackingId { get; set; }
		public LocationInfo LocationInfo { get; set; }

		public string DiscountCoupon { get; set; }
		public Amount Discount { get; set; }

		public List<ChildCart> ChildrenCarts { get; set; }
		public bool RequiresAllChildren { get; set; }

		public string Cep { get; set; }
		public AddressModel ShippingAddress { get; set; }
		public ParticipantDataModel Participant { get; set; }
		public int? EstimatedDeliveryDays { get; set; }

		// Atributos para pedidos utilizando o Call Center
		public Guid? CallCenterUserId { get; set; }
		public DateTime? SessionStartDate { get; set; }
		public DateTime? SessionEndDate { get; set; }

		public bool OccurredError { get; set; }

		// Define se a mensagem será mostrada ao carregar o carrinho
		public bool HasShownError { get; set; }
		public string ErrorMessage { get; set; }
		public string Notification { get; set; }
		public List<string> Notifications { get; set; }

		// Código de segurança para confirmar o pedido
		public bool? TokenConfirmed { get; set; }
		public string ConfirmationToken { get; set; }

		public string ShippingPolicyContentId { get; set; }
		public string ShippingPolicyVersion { get; set; }

		CartModel()
		{
			this.ChildrenCarts = new List<ChildCart>();
		}

		public static CartModel For(Guid campaignId, CampaignType type, UserPrincipal participant)
		{
			return new CartModel()
			{
				SessionId = participant.SessionId,
				CampaignId = campaignId,
				Type = type,
				CreationDate = DateTime.UtcNow,
				UserId = participant.UserId,
				ParticipantId = participant.ParticipantId
			};
		}

		public bool HasOccurredError()
		{
			return OccurredError || HasAnyChildCartWithError();
		}

		public bool HasAnyChildCartWithError()
		{
			if (ChildrenCarts == null) return false;
			return ChildrenCarts.Any(c => c.HasOccurredError());
		}

		public bool HasErrorToShow()
		{
			return HasOccurredError() && !HasShownError;
		}

		public string GetErrorDescription()
		{
			if (!string.IsNullOrEmpty(ErrorMessage))
				return ErrorMessage;
			return GetChildCartErrorMessage();
		}

		public string GetChildCartErrorMessage()
		{
			if (ChildrenCarts == null) return null;
			return ChildrenCarts.Where(c => c.OccurredError)
				.Select(c => c.ErrorMessage)
				.FirstOrDefault();
		}

		public void AddNotification(string message)
		{
			if (this.Notifications == null)
				this.Notifications = new List<string>();
			this.Notifications.Add(message);
		}

		public void ClearNotifications()
		{
			this.Notifications = null;
		}

		public void VerifyErrors()
		{
			// limpa apenas o erro geral para verificar os carrinho dos parceiros
			ClearCartError();

			if (HasOccurredError())
			{
				OccurredError = true;
				HasShownError = false;
			}
			if (OccurredError && string.IsNullOrEmpty(ErrorMessage))
				ErrorMessage = GetChildCartErrorMessage();
			// Valida se tem algum produto com problema
			ChildrenCarts.ForEach(cp => cp.Products.ForEach(i =>
			{
				if (i.OccurredError)
					throw MotivaiException.ofValidation("Um item do carrinho está com algum problema, por favor, verique antes de inicar o resgate.");
			}));
		}

		public void AcceptShippingPolicy(string contentId, string version)
		{
			ShippingPolicyContentId = contentId;
			ShippingPolicyVersion = version;
		}

		public bool IsFromCallCenter()
		{
			return CallCenterUserId.HasValue && CallCenterUserId.Value != Guid.Empty;
		}

		public void AddProduct(ProductDetailsModel product, int quantity)
		{
			if (product == null) return;

			this.ValidateCartItemAddition(product, quantity);

			var itemGrouperId = this.GetItemGrouperId(product);

			// Os vales estarão no carrinho do parceiro
			var childCart = this.GetPartnerCartBy(itemGrouperId);
			if (childCart == null)
			{
				childCart = new ChildCart()
				{
					ItemGrouperId = itemGrouperId
				};
				ChildrenCarts.Add(childCart);
			}
			childCart.AddProduct(CartItem.FromItem(product, quantity));
		}

		private void ValidateCartItemAddition(ProductDetailsModel product, int quantity = 1)
        {
            if (product.PartnerSettings == null || !product.PartnerSettings.CartItemLimitEnabled)
				return;

			var itemGrouperId = this.GetItemGrouperId(product);
			var childCart = this.GetPartnerCartBy(itemGrouperId);
			if (childCart == null)
				return;

			var hasCartItemLimit = product.PartnerSettings.CartItemLimit.HasValue;

			if (childCart.Products != null && hasCartItemLimit && childCart.Products.Count + 1 > product.PartnerSettings.CartItemLimit)
				throw MotivaiException.ofValidation($"O limite máximo de produtos deste parceiro no seu carrinho é {product.PartnerSettings.CartItemLimit} produtos.");

			var itemQuantity = 0;
			var item = childCart.Products?.FirstOrDefault(p => p.ProductId == Guid.Parse(product.Id) && p.SkuId == product.SkuId);
			if (item != null)
				itemQuantity = item.Quantity;

			if (hasCartItemLimit && itemQuantity + quantity > product.PartnerSettings.CartItemLimit)
				throw MotivaiException.ofValidation($"Você pode adicionar no máximo {product.PartnerSettings.CartItemLimit} deste produto no seu carrinho.");
        }

		private Guid GetItemGrouperId(ProductDetailsModel product)
		{
			Guid itemGrouperId = Guid.Empty;
			if (Type.HasMarketplace())
			{
				itemGrouperId = Guid.Parse(product.PartnerId);
			}
			else if (product.HasFactory())
			{
				itemGrouperId = product.FactoryId.Value;
			}
			else
			{
				throw MotivaiException.ofValidation("Produto sem fábrica/distribuidora.");
			}
			return itemGrouperId;
		}

		private ChildCart GetPartnerCartBy(Guid itemGrouperId)
		{
			return ChildrenCarts?.FirstOrDefault(c => c.ItemGrouperId == itemGrouperId);
		}

		public void RemoveItem(string itemId)
		{
			ChildrenCarts.ForEach(c =>
			{
				c.Products.RemoveAll(i => i.ItemId == itemId);
			});
			ChildrenCarts.RemoveAll(cp => cp.Products.Count == 0);
			if (ChildrenCarts.Count == 0)
			{
				ClearPartnersCartsErrors();
			}
		}

		public List<CartItem> GetProducts()
		{
			var products = ChildrenCarts.SelectMany(c => c.Products).ToList();
			return products ?? new List<CartItem>();
		}

		public IQueryable<CartItem> GetProductsAsQueryable()
		{
			return ChildrenCarts.SelectMany(c => c.Products).AsQueryable();
		}

		public void UpdateItemQuantityIfAllowed(string itemId, int quantity)
		{
			ChildrenCarts.ForEach(c =>
			{
				var item = c.Products.FirstOrDefault(i => i.ItemId == itemId);
				if (item != null)
				{
					var hasCartItemLimit = item.PartnerSettings != null && item.PartnerSettings.CartItemLimit.HasValue;
					if (item.PartnerSettings != null && item.PartnerSettings.CartItemLimitEnabled && hasCartItemLimit && quantity > item.PartnerSettings.CartItemLimit)
						throw MotivaiException.ofValidation($"Você pode adicionar no máximo {item.PartnerSettings.CartItemLimit} quantidade deste produto no seu carrinho.");

					item.Quantity = quantity;
				}
			});
		}

		public bool IsEmpty()
		{
			return !ChildrenCarts.Any(c => c.Products.Any());
		}

		public void SetShippingAddress(AddressModel address)
		{
			this.ShippingAddress = address;
			if (address != null)
				this.Cep = address.Cep;
		}

		public int GetTotalItems()
		{
			return ChildrenCarts.Sum(c => c.Products.Count);
		}

		public decimal GetProductsSubtotalPoints()
		{
			return ChildrenCarts.Sum(c => c.GetProductsSubtotalPoints());
		}

		public decimal GetTotalShippingCostPoints()
		{
			var costPoints = ChildrenCarts.Select(c => c.ShippingCost).Aggregate(Amount.Of(0, 0), (sc, acc) => sc + acc).Points;
			return costPoints ?? 0;
		}

		public decimal GetCartTotal()
		{
			return ChildrenCarts.Sum(c => c.GetTotalPoints()) - Discount.GetPointsOrZero();
		}

		public bool HasShippingAddress()
		{
			return ShippingAddress != null && !string.IsNullOrEmpty(ShippingAddress.Id);
		}

		public void SetError(string errorMessage)
		{
			OccurredError = true;
			HasShownError = false;
			ErrorMessage = errorMessage;
		}

		public void SetErrorIfMotivaiException(Exception ex, string errorMessage)
		{
			if (ex is MotivaiException)
			{
				SetError(ex.Message);
			}
			else
			{
				SetError(errorMessage);
			}
		}

		public void ClearCartError()
		{
			OccurredError = false;
			HasShownError = true;
			ErrorMessage = null;
		}

		public void ClearPartnersCartsErrors()
		{
			ClearCartError();
			if (ChildrenCarts == null) return;
			ChildrenCarts.ForEach(pc => pc.ClearErrors());
		}

		public void StoreToken(string token)
		{
			this.TokenConfirmed = false;
			if (string.IsNullOrEmpty(token))
				throw MotivaiException.ofValidation("Código de segurança inválido.");
			this.ConfirmationToken = token;
		}

		public bool WasTokenConfirmed()
		{
			return this.TokenConfirmed == true;
		}

		public void ValidateToken(string token)
		{
			this.TokenConfirmed = this.ConfirmationToken == token;
			if (!WasTokenConfirmed())
				throw MotivaiException.ofValidation("Código de segurança inválido.");
		}

		public void SetParticipantInfo(ParticipantDataModel participantDataModel)
		{
			this.Participant = participantDataModel;
			if (this.HasShippingAddress() && (string.IsNullOrEmpty(ShippingAddress.Receiver?.Cpf) ||
				Cpf.IsCpf(participantDataModel.Documento)))
			{
				ShippingAddress.Receiver = new ReceiverModel()
				{
					Cpf = participantDataModel.Documento?.Length == 11 ? participantDataModel.Documento : "",
					Name = participantDataModel.Nome,
					Telephone = participantDataModel.Telefone?.Length == 10 ? participantDataModel.Telefone : "",
					Cellphone = participantDataModel.Celular,
					Email = participantDataModel.Email
				};
			}
		}
	}

	/// <summary>
	/// Carrinho de compras por parceiros.
	/// </summary>
	public class ChildCart
	{
		public Guid ItemGrouperId { get; set; }
		public string ItemGrouperName { get; set; }

		public Amount ShippingCost { get; set; }
		public DetailedShippingCost DetailedShippingCost { get; set; }
		public int? EstimatedDeliveryDays { get; set; }
		public bool OccurredError { get; set; }
		public string ErrorMessage { get; set; }
		public PartnerOrderStatus? Status { get; set; }

		public List<CartItem> Products { get; set; }
		public List<VirtualItem> Vouchers { get; set; }

		public ChildCart()
		{
			this.Products = new List<CartItem>();
		}

		public bool HasOccurredError()
		{
			if (!OccurredError && Products.Any(p => p.OccurredError))
			{
				this.OccurredError = true;
			}
			return OccurredError;
		}

		public void SetError(string errorMessage)
		{
			this.OccurredError = true;
			this.ErrorMessage = errorMessage;
		}

		public void ClearErrors()
		{
			Products.ForEach(p =>
			{
				p.Notifications = null;
				p.OccurredError = false;
				p.ErrorMessage = null;
			});
		}

		public void AddProduct(CartItem item)
		{
			var existingItem = Products.FirstOrDefault(p => p.ProductId == item.ProductId && p.SkuId == item.SkuId);
			if (existingItem == null)
			{
				Products.Add(item);
			}
			else
			{
				// Verifica se o produto para adicionar tem atributos iguais
				if (existingItem.HasCustomAttributes() != item.HasCustomAttributes())
				{
					throw MotivaiException.ofValidation("O produto já existe no carrinho com informações extras diferentes do preenchido.");
				}
				else if (existingItem.HasCustomAttributes())
				{
					existingItem.CustomAttributes.ForEach(currentAttr =>
					{
						var addingAttr = item.GetCustomAttributeByIdentifier(currentAttr.GetIdentifier());
						if (addingAttr == null || addingAttr.Value != currentAttr.Value)
							throw MotivaiException.ofValidation("O produto já existe no carrinho com informações extras diferentes do preenchido.");
					});
				}
				existingItem.Quantity++;
			}
		}

		public decimal GetProductsSubtotalPoints()
		{
			return Products.Sum(i => i.GetSubtotalPoints());
		}

		public decimal GetTotalPoints()
		{
			return GetProductsSubtotalPoints() + ShippingCost.GetPointsOrZero();
		}
	}

	/// <summary>
	/// Item do carrinho de compra.
	/// </summary>
	public class CartItem
	{
		public string ItemId { get; set; }
		public Guid? PartnerId { get; set; }
		public Guid? FactoryId { get; set; }
		public string PartnerName { get; set; }
		public PartnerSettingsModel PartnerSettings { get; set; }
		public Guid ProductId { get; set; }
		public Guid SkuId { get; set; }
		public string ElasticId { get; set; }
		public bool Offline { get; set; }
		public ProcessType ProcessType { get; set; }
		public ProductType ProductType { get; set; }
		public ProductLayoutType LayoutType { get; set; }
		public string ProductName { get; set; }
		public string Image { get; set; }
		public List<Guid> Rankings { get; set; }

		public string SkuCode { get; set; }
		public string SkuIntegrationCode { get; set; }
		public string SkuModel { get; set; }
		public string SkuSize { get; set; }
		public string SkuColor { get; set; }
		public string SkuVoltage { get; set; }
		public List<CustomAttribute> CustomAttributes { get; set; }
		public string ShippingHint { get; set; }
		public string DynamicPriceDescription { get; set; }
		public string ProductModelLabel { get; set; }
		public string ProductSizeLabel { get; set; }
		public string ProductVoltageLabel { get; set; }
		public string ProductColorLabel { get; set; }
		public string ProductInformationTabTitle { get; set; }
		public string ProductTechnicalSpecificationsTabTitle { get; set; }
		public string DepartmentId { get; set; }
		public string CategoryId { get; set; }
		public Guid? SubcategoryId { get; set; }

		public bool Available { get; set; }
		public Guid? StockParticipantGroupId { get; set; }
		public int Quantity { get; set; }
		public bool DynamicPrice { get; set; }
        public bool IsDynamicPriceDefinedByParticipant { get; set; }
		public PriceSettersType? DynamicPricingSetter { get; set; }
		public decimal? DynamicPriceMinimumValue { get; set; }
		public decimal? DynamicPriceMaximumValue { get; set; }

		public bool Priced { get; set; }
		public Amount UnitPrices { get; set; }
		public DetailedPrice DetailedPrice { get; set; }
		public Amount Discount { get; set; }
		public bool PriceUpdate { get; set; }

		public Amount ShippingCost { get; set; }
		public DetailedShippingCost DetailedShippingCost { get; set; }
		public int? EstimatedDeliveryDays { get; set; }

		public string VoucherLink { get; set; }
		public string TrackingCode { get; set; }
		public string TrackingLink { get; set; }
		public List<dynamic> TrackingEvents { get; set; }

		public OrderItemStatus Status { get; set; }
		public bool OccurredError { get; set; }
		public string ErrorMessage { get; set; }

		public bool CanBePriced { get; set; }
		public List<string> Notifications { get; set; }
        public bool EnableCustomPromotionalPrice  { get; set; }

		public static CartItem FromItem(ProductDetailsModel product, int quantity = 1)
		{
			return new CartItem()
			{
				ItemId = AlphanumericGenerator.GenerateId16(product.SkuId),
				FactoryId = product.FactoryId,
				PartnerId = string.IsNullOrEmpty(product.PartnerId) ? default(Guid?) : Guid.Parse(product.PartnerId),
				PartnerName = product.Partner,
				PartnerSettings = product.PartnerSettings,
				ProductId = Guid.Parse(product.Id),
				ElasticId = product.ElasticsearchId,
				Offline = product.Offline,
				ProcessType = product.ProcessType,
				ProductType = product.ProductType,
				LayoutType = product.LayoutType,
				ProductName = product.Name,
				SkuId = product.SkuId,
				SkuCode = product.SkuCode,
				SkuIntegrationCode = product.SkuIntegrationCode,
				SkuModel = product.SkuModel,
				SkuSize = product.SkuSize,
				SkuColor = product.SkuColor,
				SkuVoltage = product.SkuVoltage,
				CustomAttributes = product.HasCustomAttributes() ? product.CustomAttributes : null,
				DepartmentId = product.DepartmentId,
				CategoryId = product.CategoryId,
				SubcategoryId = product.SubcategoryId,
				Image = product.FirstImage,
				Available = product.Available,
				StockParticipantGroupId = product.StockParticipantGroupId,
				Quantity = product.Quantity,
				DynamicPrice = product.Prices.DynamicPrice,
				IsDynamicPriceDefinedByParticipant = product.Prices.IsDynamicPriceDefinedByParticipant(),
				DynamicPricingSetter = product.Prices.DynamicPricingSetter,
				DynamicPriceMinimumValue = product.Prices.DynamicPriceMinimumValue,
				DynamicPriceMaximumValue = product.Prices.DynamicPriceMaximumValue,
				UnitPrices = product.Prices.GetUnitPriceForCart(),
				DetailedPrice = product.Prices.DetailedPrice,
				Rankings = product.Rankings?.Select(r => Guid.Parse(r.Id)).ToList(),
				ShippingHint = product.CatalogSettings?.ShippingHint,
				DynamicPriceDescription = product.CatalogSettings?.DynamicPriceDescription,
				ProductModelLabel = string.IsNullOrEmpty(product.CatalogSettings?.ProductModelLabel) ? product.CatalogSettings?.ProductModelLabel : product.CatalogSettings?.ProductModelLabel,
				ProductColorLabel = string.IsNullOrEmpty(product.CatalogSettings?.ProductColorLabel) ? "Cor" : product.CatalogSettings?.ProductColorLabel,
				ProductInformationTabTitle = string.IsNullOrEmpty(product.CatalogSettings?.ProductInformationTabTitle) ? "Especificações do produto" : product.CatalogSettings?.ProductInformationTabTitle,
				ProductSizeLabel = string.IsNullOrEmpty(product.CatalogSettings?.ProductSizeLabel) ? "Tamanho" : product.CatalogSettings.ProductSizeLabel,
				ProductTechnicalSpecificationsTabTitle = string.IsNullOrEmpty(product.CatalogSettings?.ProductTechnicalSpecificationsTabTitle) ? "Especificações Técnicas do produto" : product.CatalogSettings?.ProductTechnicalSpecificationsTabTitle,
				ProductVoltageLabel = string.IsNullOrEmpty(product.CatalogSettings?.ProductVoltageLabel) ? "Voltagem" : product.CatalogSettings?.ProductVoltageLabel,
				EnableCustomPromotionalPrice = product.EnableCustomPromotionalPrice
			};
		}

		public bool HasCustomAttributes()
		{
			return CustomAttributes != null && CustomAttributes.Count > 0;
		}

		public CustomAttribute GetCustomAttributeByIdentifier(string identifier)
		{
			if (!HasCustomAttributes()) return null;
			return CustomAttributes.FirstOrDefault(c => c.GetIdentifier() == identifier);
		}

		public decimal GetSubtotalCurrency()
		{
			if (Status == OrderItemStatus.Canceled || Status == OrderItemStatus.Refunded)
				return 0;
			return Quantity * UnitPrices.GetCurrency();
		}

		public decimal GetSubtotalPoints()
		{
			if (Status == OrderItemStatus.Canceled || Status == OrderItemStatus.Refunded)
				return 0;
			return Quantity * UnitPrices.GetPoints();
		}

		public string GetEstimatedDeliveryDays()
		{
			return EstimatedDeliveryDays.HasValue && EstimatedDeliveryDays.Value > 0 ? EstimatedDeliveryDays.Value + " dia(s)" : null;
		}

		public DateTime? GetEstimatedDeliveryDate()
		{
			if (!EstimatedDeliveryDays.HasValue || EstimatedDeliveryDays.Value <= 0) return null;
			return DateTime.UtcNow.AddDays(EstimatedDeliveryDays.Value);
		}

		// Usado no _header.cshtml
		public string GetEncodedUrl()
		{
			var productName = this.ProductName.Replace("”", "\"").Replace("–", "-").Replace("’", "'");
			return String.Format("{0}/{1}", ElasticId, productName.EncodeToUrl());
		}

		public bool HasNotification()
		{
			return Notifications != null && Notifications.Any();
		}

		public Guid GetPartnerId()
		{
			return PartnerId ?? FactoryId ?? Guid.Empty;
		}
	}

	public class VirtualItem
	{
		public DateTime? CreateDate { get; set; }
		public string Name { get; set; }
		public string Link { get; set; }
		public bool Notificated { get; set; }
		public string Status { get; set; }
		public string SkuCode { get; set; }
		public DateTime? NotificationDate { get; set; }
	}
}
