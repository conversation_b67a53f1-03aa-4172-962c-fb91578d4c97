using System;
using System.Collections.Generic;
using Motivai.SharedKernel.Domain.ValuesObject;

namespace Motivai.WebCatalog.Models.Order {
    public class DetailsShippingCostModel {
        public decimal ShippingCost { get; set; }
        public string EstimatedDelivery { get; set; }
        public List<ProductFactory> Factories { get; set; }

        public static DetailsShippingCostModel From(ShippingCostResult shippingPrice) {
            return new DetailsShippingCostModel() {
                ShippingCost = shippingPrice.Prices.GetPointsOrZero(),
                EstimatedDelivery = shippingPrice.EstimatedDeliveryDays,
                Factories = shippingPrice.Factories
            };
        }
    }

    public class ShippingCostResult {
        public Guid? ModalityId { get; set; }
        public Amount Prices { get; set; }
        public string EstimatedDeliveryDays { get; set; }
        public bool OccurredError { get; set; }
        public string ErrorMessage { get; set; }
        public List<ProductFactory> Factories { get; set; }
    }

    public class ProductFactory {
        public string CompanyId { get; set; }
        public string FactoryId { get; set; }
        public string FactoryName { get; set; }
    }
}