using System;
using System.Collections.Generic;
using System.Linq;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.WebCatalog.Models.Addresses;

namespace Motivai.WebCatalog.Models.Order {
    public class FactoryOrder {
        public string Token { get; set; }

        public string OrderId { get; set; }
        public string ItemGrouperId { get; set; }
        public string FactoryName { get; set; }

        public string OrderNumber { get; set; }
        public DateTime OrderDate { get; set; }
        public string Timezone { get; set; }
        public bool RequiresAllChildren { get; set; }

        public AddressModel ShippingAddress { get; set; }
        public dynamic Buyer { get; set; }

        public decimal ShippingCost { get; set; }
        public decimal Subtotal { get; set; }
        public decimal Total { get; set; }

        public bool OccurredError { get; set; }
        public string ErrorDescription { get; set; }

        public List<FactoryItem> Items { get; set; }
        public bool CanBePriced { get; set; }

        public FactoryOrder(){}

        public FactoryOrder(Guid factoryId, CartModel cart) {
            OrderId = cart.OrderId.ToString();
            if (!string.IsNullOrEmpty(cart.InternalOrderNumber)) {
                OrderNumber = cart.InternalOrderNumber;
                OrderDate = cart.CreationDate;
                Timezone = cart.Timezone;
            }
            Subtotal = cart.GetProductsSubtotalPoints();
            ShippingCost = cart.GetTotalShippingCostPoints();
            Total = cart.GetCartTotal();
            OccurredError = cart.OccurredError;
            ErrorDescription = cart.ErrorMessage;
            if (!cart.OccurredError && cart.HasAnyChildCartWithError()) {
                OccurredError = true;
                ErrorDescription = cart.GetChildCartErrorMessage();
            }
            RequiresAllChildren = cart.RequiresAllChildren;

            var childOrder = cart.ChildrenCarts.FirstOrDefault(c => c.ItemGrouperId == factoryId);
            ItemGrouperId = childOrder.ItemGrouperId.ToString();
            FactoryName = childOrder.Products
                .Where(p => p.PartnerName != null)
                .Select(p => p.PartnerName)
                .FirstOrDefault();
            Items = cart.GetProducts().Select(i => new FactoryItem(i)).ToList();
            CanBePriced = Items.Any(i => i.CanBePriced);

            if (cart.ShippingAddress != null) {
                ShippingAddress = new AddressModel() {
                    AddressName = cart.ShippingAddress.AddressName,
                    Cep = cart.ShippingAddress.Cep,
                    Street = cart.ShippingAddress.Street,
                    Number = cart.ShippingAddress.Number,
                    Complement = cart.ShippingAddress.Complement,
                    Neighborhood = cart.ShippingAddress.Neighborhood,
                    City = cart.ShippingAddress.City,
                    State = cart.ShippingAddress.State,
                    Reference = cart.ShippingAddress.Reference,
                    Receiver = cart.ShippingAddress.Receiver
                };
                if (ShippingAddress.Receiver != null) {
                    if (!string.IsNullOrEmpty(cart.ShippingAddress.Receiver.Cpf)) {
                        ShippingAddress.Receiver.Cpf = new Cpf(cart.ShippingAddress.Receiver.Cpf).GetCpfFormatado();
                    }
                    ShippingAddress.Receiver.Telephone = cart.ShippingAddress.Receiver.GetTelefoneFormatado();
                    ShippingAddress.Receiver.Cellphone = cart.ShippingAddress.Receiver.GetCelularFormatado();
                }
            }
            if (cart.Participant != null && !string.IsNullOrEmpty(cart.Participant.Nome)) {
                Buyer = new {
                    Name = cart.Participant.Nome,
                    Email = cart.Participant.Email,
                    Tellphone = cart.Participant.GetTelefoneFormatado(),
                    Cellphone = cart.Participant.GetCelularFormatado()
                };
            }
        }

        public class FactoryItem {
            public string ProductId { get; set; }
            public string SkuId { get; set; }
            public string Description { get; set; }

            public string PartnerName { get; set; }
            public string Image { get; set; }

            public string SkuCode { get; set; }
            public string SkuModel { get; set; }
            public string SkuSize { get; set; }
            public string SkuColor { get; set; }
            public string SkuVoltage { get; set; }
            public List<CustomAttribute> CustomAttributes { get; set; }

            public string ShippingHint { get; set; }
            public string DynamicPriceDescription { get; set; }

            public int Quantity { get; set; }
            public bool DynamicPrice { get; set; }
            public decimal UnitPrice { get; set; }
            public decimal ShippingCost { get; set; }

            public OrderItemStatus Status { get; set; }
            public bool CanBePriced { get; set; }

            public bool OccurredError { get; set; }
            public string ErrorMessage { get; set; }

            public FactoryItem(CartItem item) {
                ProductId = item.ProductId.ToString();
                SkuId = item.SkuId.ToString();
                Description = item.ProductName;
                PartnerName = item.PartnerName;
                Image = item.Image;
                SkuCode = item.SkuCode;
                SkuModel = item.SkuModel;
                SkuSize = item.SkuSize;
                SkuColor = item.SkuColor;
                SkuVoltage = item.SkuVoltage;
                CustomAttributes = item.CustomAttributes;
                ShippingHint = item.ShippingHint;
                DynamicPriceDescription = item.DynamicPriceDescription;
                Quantity = item.Quantity;
                DynamicPrice = item.DynamicPrice;
                UnitPrice = item.UnitPrices.GetCurrencyOrZero();
                ShippingCost = item.ShippingCost.GetCurrencyOrZero();
                Status = item.Status;
                CanBePriced = item.CanBePriced;
                OccurredError = item.OccurredError;
                ErrorMessage = item.ErrorMessage;
            }
        }
    }
}