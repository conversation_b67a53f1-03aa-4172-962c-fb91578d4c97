using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Values;

namespace Motivai.WebCatalog.Models.Order {
    public class ParticipantDataModel {
        public string Id { get; set; }
        public string Nome { get; set; }
        public string Documento { get; set; }
        public string Email { get; set; }
        public string Telefone { get; set; }
        public string Celular { get; set; }

        public string GetTelefoneFormatado() {
            return string.IsNullOrEmpty(Telefone) ? null : TelephoneHelper.FormatNumber(Telefone);
        }

        public string GetCelularFormatado() {
            return string.IsNullOrEmpty(Celular) ? null : TelephoneHelper.FormatNumber(Celular);
        }
    }
}