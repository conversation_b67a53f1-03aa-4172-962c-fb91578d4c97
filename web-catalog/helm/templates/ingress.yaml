{{- if .Values.ingress.enabled }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "application.fullname" . }}
  namespace: {{ include "application.namespace" . }}
  labels:
    {{- include "application.labels" . | nindent 4 }}
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:677276094924:certificate/a5d055b8-f8a9-4ccd-ab1b-a49d4de3ddac
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/security-groups: sg-0c4cc1ea13dd7ab0f
    alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-TLS-1-2-Ext-2018-06
    alb.ingress.kubernetes.io/subnets: subnet-014a27fec26aa1699,subnet-021dbe58d7c208cef,subnet-0e6a8725974aabb64
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
spec:
  rules:
    - {{- if .Values.ingress.dns }}
      host: {{ .Values.ingress.dns }}
      {{- end }}
      http:
        paths:
          - backend:
              service:
                name: {{ include "application.fullname" . }}
                port:
                  number: {{ .Values.service.port }}
            path: /*
            pathType: ImplementationSpecific
  {{- if .Values.ingress.dns }}
  tls:
    - hosts:
        - {{ .Values.ingress.dns }}
  {{- end }}
{{- end }}
