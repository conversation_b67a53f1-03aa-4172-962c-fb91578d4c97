FROM mcr.microsoft.com/dotnet/core/sdk:2.1-stretch AS build
WORKDIR /src

ARG GH_USERNAME
ARG GH_TOKEN

ENV GH_USERNAME=${GH_USERNAME}
ENV GH_TOKEN=${GH_TOKEN}

COPY NuGet.Config ./
COPY Motivai.WebCatalog.sln ./
COPY ./Motivai.WebCatalog/ Motivai.WebCatalog/

RUN dotnet restore ./Motivai.WebCatalog/Motivai.WebCatalog.csproj

WORKDIR /src/Motivai.WebCatalog
RUN dotnet build Motivai.WebCatalog.csproj -c Release -o /app/build
RUN dotnet publish Motivai.WebCatalog.csproj -c Release -o /app/publish

FROM mcr.microsoft.com/dotnet/core/aspnet:2.1-stretch-slim AS base
WORKDIR /app
EXPOSE 80
COPY --from=build /app/publish .
ENTRYPOINT ["dotnet", "Motivai.WebCatalog.dll"]
